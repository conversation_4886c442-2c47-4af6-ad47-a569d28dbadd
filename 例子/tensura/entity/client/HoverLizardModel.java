package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HoverLizardEntity;
import com.github.manasmods.tensura.entity.variant.HoverLizardVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class HoverLizardModel extends AnimatedGeoModel<HoverLizardEntity> {
   public ResourceLocation getModelResource(HoverLizardEntity lizard) {
      return new ResourceLocation("tensura", "geo/hover_lizard.geo.json");
   }

   public ResourceLocation getTextureResource(HoverLizardEntity lizard) {
      return (ResourceLocation)HoverLizardVariant.LOCATION_BY_VARIANT.get(lizard.getVariant());
   }

   public ResourceLocation getAnimationResource(HoverLizardEntity lizard) {
      return new ResourceLocation("tensura", "animations/hover_lizard.animation.json");
   }

   public void setCustomAnimations(HoverLizardEntity lizard, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(lizard, instanceId, customPredicate);
      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      IBone headSaddle = this.getAnimationProcessor().getBone("headSaddle");
      if (lizard.m_6254_() == saddle.isHidden()) {
         saddle.setHidden(!lizard.m_6254_());
         headSaddle.setHidden(saddle.isHidden());
      }

      IBone chestBag;
      if (lizard.m_6254_()) {
         chestBag = this.getAnimationProcessor().getBone("SaddleHandle");
         if (lizard.m_20160_() == chestBag.isHidden()) {
            chestBag.setHidden(!lizard.m_20160_());
         }
      }

      chestBag = this.getAnimationProcessor().getBone("Bag");
      if (lizard.m_30502_() == chestBag.isHidden()) {
         chestBag.setHidden(!lizard.m_30502_());
      }

      if (!lizard.m_5803_() && lizard.getMiscAnimation() == 0) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Face");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationZ(extraData.netHeadYaw * -0.017453292F);
         }
      }

   }
}
