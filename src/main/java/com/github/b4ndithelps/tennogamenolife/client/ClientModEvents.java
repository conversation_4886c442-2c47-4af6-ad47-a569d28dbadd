package com.github.b4ndithelps.tennogamenolife.client;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.model.XiubiArmorModel;
import com.github.b4ndithelps.tennogamenolife.client.model.XiubiHaloModel;
import com.github.b4ndithelps.tennogamenolife.client.renderer.entity.XiubiRenderer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.entity.ExMachinaBulletRenderer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.PlayerExMachinaArmorLayer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.PlayerExMachinaHaloLayer;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.player.PlayerRenderer;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.EntityRenderersEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 客户端MOD事件处理器
 * 负责注册客户端相关的内容，如渲染器和模型层
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class ClientModEvents {

    /**
     * 注册实体渲染器
     */
    @SubscribeEvent
    public static void onRegisterEntityRenderers(EntityRenderersEvent.RegisterRenderers event) {
        // 注册休比异世界人的渲染器
        event.registerEntityRenderer(TenreincarnationEntities.XIUBI.get(), XiubiRenderer::new);

        // 注册机铠种之械子弹的渲染器
        event.registerEntityRenderer(TenreincarnationEntities.EX_MACHINA_BULLET.get(), ExMachinaBulletRenderer::new);
    }

    /**
     * 注册模型层
     */
    @SubscribeEvent
    public static void onRegisterLayerDefinitions(EntityRenderersEvent.RegisterLayerDefinitions event) {
        // 注册休比光环模型层
        event.registerLayerDefinition(XiubiHaloModel.LAYER_LOCATION, XiubiHaloModel::createBodyLayer);

        // 注册休比机铠装饰模型层
        event.registerLayerDefinition(XiubiArmorModel.LAYER_LOCATION, XiubiArmorModel::createBodyLayer);
    }

    /**
     * 添加玩家渲染层
     */
    @SubscribeEvent
    public static void onAddLayers(EntityRenderersEvent.AddLayers event) {
        // 为默认和slim玩家模型添加装饰层
        addLayersToPlayerRenderer(event, "default");
        addLayersToPlayerRenderer(event, "slim");
    }

    /**
     * 为指定的玩家渲染器添加装饰层
     */
    private static void addLayersToPlayerRenderer(EntityRenderersEvent.AddLayers event, String skinType) {
        var renderer = event.getSkin(skinType);

        if (renderer instanceof PlayerRenderer playerRenderer) {
            // 创建装饰模型
            XiubiArmorModel armorModel = new XiubiArmorModel(
                event.getEntityModels().bakeLayer(XiubiArmorModel.LAYER_LOCATION)
            );
            
            XiubiHaloModel haloModel = new XiubiHaloModel(
                event.getEntityModels().bakeLayer(XiubiHaloModel.LAYER_LOCATION)
            );
            
            // 添加装饰渲染层
            playerRenderer.addLayer(new PlayerExMachinaHaloLayer(playerRenderer, haloModel));
            playerRenderer.addLayer(new PlayerExMachinaArmorLayer(playerRenderer, armorModel));
        }
    }
}
