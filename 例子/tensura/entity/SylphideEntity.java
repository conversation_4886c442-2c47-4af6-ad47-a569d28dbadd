package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.navigator.NoSpinFlightPathNavigator;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.beam.ElectroBlastProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.LightningSphereProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WindBladeProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WindSphereProjectile;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.SpawnPlacements;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.SpawnPlacements.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.NaturalSpawner;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class SylphideEntity extends GreaterSpiritEntity {
   public SylphideEntity(EntityType<? extends SylphideEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.GREEN, BossBarOverlay.NOTCHED_20)).m_7005_(true);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 40.0D).m_22268_(Attributes.f_22276_, 400.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : SylphideEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new SylphideEntity.SylphideAttackGoal(this));
      this.f_21345_.m_25352_(3, new GreaterSpiritEntity.FollowHinataGoal(this, 1.0D, HinataSakaguchiEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new GreaterSpiritEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 12));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{SylphideEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void switchNavigator(boolean onLand) {
      if (!onLand && !this.m_5803_()) {
         this.f_21342_ = new GreaterSpiritEntity.GreaterMoveControl();
         this.f_21344_ = new NoSpinFlightPathNavigator(this, this.f_19853_);
         this.wasFlying = false;
      } else {
         this.f_21342_ = new TensuraTamableEntity.SleepMoveControl() {
            public void m_8126_() {
               if (SylphideEntity.this.getMiscAnimation() < 6 || SylphideEntity.this.getMiscAnimation() == 10) {
                  super.m_8126_();
               }
            }
         };
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = true;
      }

   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (pSource.m_7639_() instanceof AquaFrogEntity) {
         return false;
      } else {
         if (DamageSourceHelper.isLightningDamage(pSource)) {
            pAmount *= 0.05F;
         } else if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         } else {
            pAmount *= this.getPhysicalAttackInput(pSource);
         }

         boolean hurt = super.m_6469_(pSource, pAmount);
         if (hurt) {
            Entity var5 = pSource.m_7639_();
            if (var5 instanceof LivingEntity) {
               LivingEntity damageSource = (LivingEntity)var5;
               if (!damageSource.m_6084_()) {
                  return true;
               }

               if (damageSource instanceof Player) {
                  Player player = (Player)damageSource;
                  if (player.m_7500_() || player.m_5833_()) {
                     return true;
                  }
               }

               List<FeatheredSerpentEntity> list = this.f_19853_.m_6443_(FeatheredSerpentEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
                  return !entity.m_21824_();
               });
               if (!list.isEmpty()) {
                  list.forEach((frog) -> {
                     frog.m_6710_(damageSource);
                  });
               }
            }
         }

         return hurt;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof FeatheredSerpentEntity) {
         FeatheredSerpentEntity serpent = (FeatheredSerpentEntity)entity;
         return serpent.m_21824_() == this.m_21824_();
      } else if (entity instanceof SylphideEntity) {
         SylphideEntity sylphide = (SylphideEntity)entity;
         return sylphide.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         this.m_7311_(Math.min(60, this.m_20094_()));
      } else if (this.m_6084_() && this.f_19797_ % 20 == 0) {
         TensuraParticleHelper.addParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 2.0D);
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if ((this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2) && this.miscAnimationTicks == 5) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               shootWindSphere(this);
               this.m_5496_(SoundEvents.f_11862_, 7.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if ((this.getMiscAnimation() == 3 || this.getMiscAnimation() == 4) && this.miscAnimationTicks == 10) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               shootWindBlade(this);
               this.m_5496_(SoundEvents.f_12317_, 7.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() != 5) {
               if (this.getMiscAnimation() == 9) {
                  this.m_21573_().m_26573_();
                  target = this.m_5448_();
                  if (target != null) {
                     this.m_7618_(Anchor.EYES, target.m_146892_());
                  }

                  if (this.miscAnimationTicks == 10) {
                     this.electroBlast();
                  }

                  if (this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 35) {
                     this.m_5496_(SoundEvents.f_12089_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 10) {
                  this.m_21573_().m_26573_();
                  target = this.m_5448_();
                  if (target != null) {
                     this.m_7618_(Anchor.EYES, target.m_146892_());
                  }

                  if (this.miscAnimationTicks >= 30) {
                     this.aerialBlade();
                     this.m_5496_(SoundEvents.f_12317_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                     this.m_5496_(SoundEvents.f_12089_, 3.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 6) {
                  this.m_21573_().m_26573_();
                  if (this.miscAnimationTicks >= 30) {
                     this.combust();
                     this.m_5496_(SoundEvents.f_12317_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 7) {
                  this.m_21573_().m_26573_();
                  switch(this.miscAnimationTicks) {
                  case 10:
                     this.summonFeatheredSerpent(3, 7);
                     break;
                  case 20:
                     this.summonFeatheredSerpent(5, 10);
                     break;
                  case 30:
                     this.summonFeatheredSerpent(7, 15);
                  }

                  this.m_5496_(SoundEvents.f_12052_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 20 && this.miscAnimationTicks <= 50) {
                  this.lightningBall();
               } else {
                  this.setMagicID(0);
               }

               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      default:
         var10000 = 20;
         break;
      case 5:
         var10000 = 67;
         break;
      case 6:
         var10000 = 60;
         break;
      case 7:
         var10000 = 45;
         break;
      case 8:
         var10000 = 30;
         break;
      case 9:
         var10000 = 40;
         break;
      case 10:
         var10000 = 70;
      }

      return var10000;
   }

   public static void shootWindSphere(LivingEntity entity) {
      WindSphereProjectile sphere = new WindSphereProjectile(entity.f_19853_, entity);
      sphere.setSize(0.75F);
      sphere.setSkill(SkillUtils.getSkillOrNull(entity, (ManasSkill)ExtraSkills.WIND_MANIPULATION.get()));
      float angle = 0.017453292F * entity.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      sphere.m_7678_(entity.m_20185_() + xOffset, entity.m_20188_() - 0.2D, entity.m_20189_() + zOffset, entity.m_146908_(), entity.m_146909_());
      sphere.setDamage((float)entity.m_21133_(Attributes.f_22281_));
      sphere.setSpiritAttack(true);
      sphere.setSpeed(1.5F);
      sphere.m_20242_(true);
      sphere.shootFromRot(entity.m_20154_());
      entity.f_19853_.m_7967_(sphere);
   }

   public static void shootWindBlade(LivingEntity entity) {
      WindBladeProjectile blade = new WindBladeProjectile(entity.f_19853_, entity);
      blade.setSkill(SkillUtils.getSkillOrNull(entity, (ManasSkill)SpiritualMagics.WIND_BLADE.get()));
      float angle = 0.017453292F * entity.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      blade.m_7678_(entity.m_20185_() + xOffset, entity.m_20188_() - 0.2D, entity.m_20189_() + zOffset, entity.m_146908_(), entity.m_146909_());
      blade.setDamage((float)entity.m_21133_(Attributes.f_22281_) * 1.5F);
      blade.setSpiritAttack(true);
      blade.setSpeed(2.0F);
      blade.m_20242_(true);
      blade.shootFromRot(entity.m_20154_());
      entity.f_19853_.m_7967_(blade);
   }

   private void lightningBall() {
      int ballId = this.getMagicID();
      if (ballId == 0) {
         LightningSphereProjectile sphere = new LightningSphereProjectile(this.f_19853_, this);
         sphere.setSize(0.1F);
         sphere.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
         sphere.setSpiritAttack(true);
         sphere.setMpCost(500.0D);
         sphere.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.WIND_MANIPULATION.get()).orElse((Object)null));
         sphere.setExplosionRadius(4.0F);
         sphere.m_146884_(this.m_146892_().m_82520_(0.0D, 4.0D, 0.0D));
         sphere.setOwnerOffset(new Vec3(0.0D, 3.0D, 0.0D));
         sphere.setLookDistance(30.0F);
         sphere.setDelayTick(30);
         sphere.m_20242_(true);
         sphere.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 200, 1, false, false, false));
         sphere.setEffectRange(3.0F);
         this.m_9236_().m_7967_(sphere);
         this.setMagicID(sphere.m_19879_());
      } else {
         Entity entity = this.m_9236_().m_6815_(ballId);
         if (entity instanceof LightningSphereProjectile) {
            LightningSphereProjectile ball = (LightningSphereProjectile)entity;
            if (ball.getDelayTick() > 0) {
               ball.setSize(ball.getSize() + 0.1F);
            }
         } else {
            this.setMagicID(0);
            this.lightningBall();
         }
      }

      this.m_5496_(SoundEvents.f_12521_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
   }

   private void electroBlast() {
      ElectroBlastProjectile beam = new ElectroBlastProjectile(this.f_19853_, this);
      beam.setFollowingOwner(true);
      beam.setSize(1.0F);
      beam.setLife(25);
      float damage = (float)(this.m_21133_(Attributes.f_22281_) * 2.0D);
      beam.setDamage(damage);
      beam.setExplosionRadius(2.0F);
      beam.setRange(20.0F);
      beam.m_146884_(this.m_146892_());
      beam.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)SpiritualMagics.ELECTRO_BLAST.get()));
      beam.setMpCost(2000.0D);
      this.f_19853_.m_7967_(beam);
   }

   public void aerialBlade() {
      if (this.miscAnimationTicks % 10 == 0) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 5.0F, 1.0F);
         int radius = 4;
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, (double)(radius + 2));
         BlockPos ahead = result.m_82425_();
         Vec3 aheadVec = new Vec3((double)ahead.m_123341_(), (double)ahead.m_123342_(), (double)ahead.m_123343_());
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 4.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 5.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, ParticleTypes.f_123766_, 4.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, ParticleTypes.f_123766_, 5.0D);
         AABB thisInflation = this.m_20191_().m_82400_((double)radius);
         List<LivingEntity> aroundList = this.m_9236_().m_6443_(LivingEntity.class, thisInflation, (living) -> {
            return !living.m_7306_(this) && living.m_6084_() && !living.m_7307_(this);
         });

         LivingEntity target;
         for(Iterator var7 = aroundList.iterator(); var7.hasNext(); target.f_19864_ = true) {
            target = (LivingEntity)var7.next();
            target.m_20256_(aheadVec.m_82546_(target.m_20182_()).m_82541_().m_82490_(2.0D));
         }

         AABB box = (new AABB(ahead)).m_82400_((double)radius);
         List<LivingEntity> list = this.m_9236_().m_6443_(LivingEntity.class, box.m_82367_(thisInflation), (living) -> {
            return !living.m_7306_(this) && living.m_6084_() && !living.m_7307_(this);
         });
         if (!list.isEmpty()) {
            float damage = (float)this.m_21133_(Attributes.f_22281_);
            Iterator var10 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var10.hasNext()) {
                     return;
                  }

                  target = (LivingEntity)var10.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               target.m_6469_(TensuraDamageSources.elementalAttack("tensura.wind_attack", this, true), damage);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 3.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
            }
         }
      }
   }

   public void combust() {
      if (this.miscAnimationTicks % 10 == 0) {
         TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123796_, this.m_20185_(), this.m_20188_(), this.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.2D, true);
         TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.LIGHTNING_EFFECT.get(), this.m_20185_(), this.m_20188_(), this.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.2D, true);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 3.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.GUST.get(), 4.0D);
         AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 10.0D);
         List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, this::shouldAttack);
         if (!list.isEmpty()) {
            float damage = (float)(this.m_21133_(Attributes.f_22281_) * 0.5D);
            DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 100.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.WIND_MANIPULATION.get())).setNotTensuraMagic();
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               LivingEntity target = (LivingEntity)var5.next();
               target.m_6469_(damageSource, damage);
               target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 200, 2, false, false, false));
               target.m_20334_(0.0D, 0.1D, 0.0D);
               SkillHelper.knockBack(this, target, 2.0F);
            }

         }
      }
   }

   private void summonFeatheredSerpent(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         FeatheredSerpentEntity serpent = new FeatheredSerpentEntity((EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get(), serverLevel);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            BlockPos blockpos = new BlockPos(i1, j1, k1);
            EntityType<?> entitytype = (EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get();
            Type type = SpawnPlacements.m_21752_(entitytype);
            if (NaturalSpawner.m_47051_(type, serverLevel, blockpos, entitytype)) {
               serpent.m_6034_((double)i1, (double)j1, (double)k1);
               if (serverLevel.m_45784_(serpent) && serverLevel.m_45786_(serpent)) {
                  serpent.m_6518_(serverLevel, serverLevel.m_6436_(serpent.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                  serpent.m_6710_(this.m_5448_());
                  serverLevel.m_47205_(serpent);
                  serpent.setSummonerUUID(this.getSummonerUUID());
                  TensuraParticleHelper.addServerParticlesAroundSelf(serpent, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(serpent, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 2.0D);
                  break;
               }
            }
         }

      }
   }

   public MagicElemental getElemental() {
      return MagicElemental.WIND;
   }

   public Item getElementalCore() {
      return (Item)TensuraMaterialItems.ELEMENT_CORE_WIND.get();
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 29) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         this.spawnDeathParticles();
      }

   }

   protected void spawnDeathParticles() {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SMALL_GUST.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.GUST.get(), 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 2.0D);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_215671_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_215675_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_215672_;
   }

   public boolean shouldStand() {
      if (this.m_21525_()) {
         return true;
      } else if (this.getMiscAnimation() == 6) {
         return true;
      } else if (this.getMiscAnimation() == 7) {
         return true;
      } else if (this.getMiscAnimation() == 8) {
         return true;
      } else {
         return !this.m_5803_() && !this.m_21825_() ? this.m_20096_() : false;
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.relax", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_()) {
         if (this.getMiscAnimation() == -1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.idle_train", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.stay_by", EDefaultLoopTypes.LOOP));
         }
      } else if (event.isMoving() && !this.m_21525_()) {
         if (this.m_21660_() && !this.m_20096_() && this.getMiscAnimation() != 6 && this.getMiscAnimation() != 7 && this.getMiscAnimation() != 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.fly_fast", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.fly", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.wind_sphere_right", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.wind_sphere_left", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.wind_blade_right", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.wind_blade_left", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.lightning_sphere", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.aerial_blade_burst", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 7) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.summon", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.death", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 9) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.electro_blast", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 10) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sylphide.aerial_blade", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static class SylphideAttackGoal extends Goal {
      private final SylphideEntity sylphide;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private int attackCooldown;

      public SylphideAttackGoal(SylphideEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.sylphide = entity;
      }

      public boolean m_8036_() {
         if (this.sylphide.m_21827_()) {
            return false;
         } else if (this.sylphide.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.sylphide.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      public void m_8037_() {
         LivingEntity target = this.sylphide.m_5448_();
         if (target != null && target.m_6084_()) {
            this.sylphide.setFlying(true);
            if (this.orbitTime < this.maxOrbitTime) {
               if (this.sylphide.getMiscAnimation() == 0) {
                  ++this.orbitTime;
                  --this.attackCooldown;
               }

               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(15.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.sylphide.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
               if (this.isTimeToAttack()) {
                  this.resetAttackCooldown();
                  this.sylphide.setMiscAnimation(this.randomAttack((double)this.sylphide.m_20270_(target)));
                  this.sylphide.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else {
               this.orbitTime = 0;
               if (this.canSummonSerpents()) {
                  this.sylphide.setMiscAnimation(7);
               }
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 80 + this.sylphide.m_217043_().m_188503_(80);
         this.sylphide.m_21561_(true);
         this.attackCooldown = 0;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private boolean canSummonSerpents() {
         if (this.sylphide.m_21824_()) {
            return false;
         } else {
            List<FeatheredSerpentEntity> list = this.sylphide.f_19853_.m_45971_(FeatheredSerpentEntity.class, TargetingConditions.m_148353_().m_26883_(20.0D).m_148355_().m_26893_(), this.sylphide, this.sylphide.m_20191_().m_82400_(20.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  FeatheredSerpentEntity entity = (FeatheredSerpentEntity)var2.next();
                  entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false));
               }
            }

            return this.sylphide.f_19796_.m_188503_(3) + 1 > list.size();
         }
      }

      private void resetAttackCooldown() {
         this.attackCooldown = this.m_183277_(30);
      }

      private boolean isTimeToAttack() {
         return this.attackCooldown <= 0;
      }

      private int randomAttack(double distance) {
         if (distance < 10.0D) {
            if ((double)this.sylphide.f_19796_.m_188501_() <= 0.1D) {
               return 6;
            }

            if ((double)this.sylphide.f_19796_.m_188501_() <= 0.2D) {
               return 10;
            }
         }

         if (distance < 20.0D && (double)this.sylphide.f_19796_.m_188501_() <= 0.3D) {
            return 9;
         } else if ((double)this.sylphide.f_19796_.m_188501_() <= 0.3D) {
            return 5;
         } else if ((double)this.sylphide.f_19796_.m_188501_() <= 0.5D) {
            return this.sylphide.m_217043_().m_188499_() ? 3 : 4;
         } else {
            return this.sylphide.m_217043_().m_188499_() ? 1 : 2;
         }
      }
   }
}
