package com.github.b4ndithelps.tennogamenolife.client;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.model.XiubiHaloModel;
import com.github.b4ndithelps.tennogamenolife.client.model.XiubiArmorModel;
import com.github.b4ndithelps.tennogamenolife.client.renderer.entity.XiubiRenderer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.PlayerExMachinaArmorLayer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.PlayerExMachinaHaloLayer;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.ExMachinaDecorativeManager;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.IExMachinaDecorativeRace;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.player.PlayerRenderer;
import net.minecraftforge.client.event.ClientPlayerNetworkEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.EntityRenderersEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 客户端事件处理器
 * 负责注册客户端相关的内容，如渲染器
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID, value = Dist.CLIENT)
public class ClientEvents {

    /**
     * 客户端玩家tick事件
     */
    @SubscribeEvent
    public static void onClientPlayerTick(TickEvent.PlayerTickEvent event) {
        if (event.side.isClient() && event.phase == TickEvent.Phase.END) {
            // 检查玩家是否是ExMachina种族
            Race race = TensuraPlayerCapability.getRace(event.player);
            if (race instanceof IExMachinaDecorativeRace) {
                // 更新装饰状态
                ExMachinaDecorativeManager.updateDecorativeState(event.player);
            }
        }
    }

    /**
     * 玩家离开事件 - 清理数据
     */
    @SubscribeEvent
    public static void onPlayerLogout(ClientPlayerNetworkEvent.LoggingOut event) {
        // 检查玩家是否为null，避免空指针异常
        if (event.getPlayer() != null) {
            // 清理玩家装饰数据
            ExMachinaDecorativeManager.cleanupPlayerData(event.getPlayer().getUUID());
        }
    }
}
