package com.github.manasmods.tensura.registry.command;

import com.github.manasmods.tensura.command.argument.RaceArgument;
import com.github.manasmods.tensura.command.argument.SkillArgument;
import net.minecraft.commands.synchronization.ArgumentTypeInfo;
import net.minecraft.commands.synchronization.ArgumentTypeInfos;
import net.minecraft.commands.synchronization.SingletonArgumentInfo;
import net.minecraft.core.Registry;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraArgumentTypes {
   private static final DeferredRegister<ArgumentTypeInfo<?, ?>> registry;
   private static final RegistryObject<SingletonArgumentInfo<SkillArgument>> SKILL_ARGUMENT;
   private static final RegistryObject<SingletonArgumentInfo<RaceArgument>> RACE_ARGUMENT;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_235724_, "tensura");
      SKILL_ARGUMENT = registry.register("skill", () -> {
         return (SingletonArgumentInfo)ArgumentTypeInfos.registerByClass(SkillArgument.class, SingletonArgumentInfo.m_235451_(SkillArgument::skill));
      });
      RACE_ARGUMENT = registry.register("race", () -> {
         return (SingletonArgumentInfo)ArgumentTypeInfos.registerByClass(RaceArgument.class, SingletonArgumentInfo.m_235451_(RaceArgument::race));
      });
   }
}
