package com.github.manasmods.tensura.entity.magic.breath;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.MolecularManipulationSkill;
import com.github.manasmods.tensura.ability.skill.unique.DegenerateSkill;
import com.github.manasmods.tensura.ability.skill.unique.StarvedSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.entity.BeehiveBlockEntity;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity;
import net.minecraft.world.level.block.entity.BeehiveBlockEntity.BeeReleaseStatus;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class PredatorMistProjectile extends BreathEntity {
   private boolean consumeProjectile;
   private int blockMode;

   public PredatorMistProjectile(EntityType<? extends PredatorMistProjectile> entityType, Level level) {
      super(entityType, level);
      this.m_20242_(true);
   }

   public PredatorMistProjectile(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.PREDATOR_MIST.get(), level);
      this.m_5602_(entity);
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.m_9236_().m_5776_()) {
         this.consumeBlocks();
      }

   }

   protected boolean canCollide(Entity entity) {
      if (this.isConsumeProjectile()) {
         return super.canCollide(entity);
      } else {
         boolean var10000;
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            if (super.canCollide(living)) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }

   protected boolean canDevour(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         if (instance.getSkill() == this.getSkill().getSkill()) {
            return false;
         } else if (instance.getSkill() instanceof StarvedSkill) {
            return true;
         } else if (instance.getSkill() instanceof DegenerateSkill && this.m_9236_().m_46469_().m_46207_(TensuraGameRules.RIMURU_MODE)) {
            return true;
         } else {
            ManasSkill var3 = instance.getSkill();
            if (!(var3 instanceof Skill)) {
               return false;
            } else {
               Skill devouredSkill = (Skill)var3;
               return devouredSkill.getType().equals(Skill.SkillType.INTRINSIC) || devouredSkill.getType().equals(Skill.SkillType.COMMON) || devouredSkill.getType().equals(Skill.SkillType.EXTRA) || devouredSkill.getType().equals(Skill.SkillType.RESISTANCE);
            }
         }
      } else {
         return false;
      }
   }

   protected void m_5790_(EntityHitResult entityHitResult) {
      if (!this.f_19853_.m_5776_()) {
         Entity entity = entityHitResult.m_82443_();
         if (this.isConsumeProjectile() && entity instanceof Projectile) {
            Projectile projectile = (Projectile)entity;
            this.devourProjectile(projectile);
         } else if (entity.m_6084_()) {
            if (entity instanceof LivingEntity) {
               LivingEntity target = (LivingEntity)entity;
               if (target instanceof Player) {
                  Player player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               DamageSource damageSource = TensuraDamageSources.DEVOURED;
               Entity var6 = this.m_37282_();
               LivingEntity owner;
               if (var6 instanceof LivingEntity) {
                  owner = (LivingEntity)var6;
                  damageSource = TensuraDamageSources.devour(owner);
               }

               if (entity.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), 10.0F)) {
                  var6 = this.m_37282_();
                  if (var6 instanceof LivingEntity) {
                     owner = (LivingEntity)var6;
                     if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                        SkillHelper.drainMP(target, owner, 100.0D, false);
                     }

                     if (target.m_6084_()) {
                        if ((double)target.m_217043_().m_188501_() < 0.1D) {
                           this.devourRandomSkill(target, owner);
                        }
                     } else {
                        this.devourAllSkills(target, owner);
                        this.devourEP(target, owner, 0.5F);
                        if (owner instanceof Player) {
                           Player player = (Player)owner;
                           List<ItemEntity> list = owner.f_19853_.m_45976_(ItemEntity.class, AABB.m_165882_(target.m_20182_(), 2.0D, 2.0D, 2.0D));
                           Iterator var8 = list.iterator();

                           while(var8.hasNext()) {
                              ItemEntity item = (ItemEntity)var8.next();
                              if (this.addItemToSpatialStorage(player, item.m_32055_())) {
                                 item.m_146870_();
                              } else if (player.m_36356_(item.m_32055_())) {
                                 item.m_146870_();
                              } else {
                                 item.m_20219_(owner.m_20182_());
                              }
                           }
                        }
                     }

                  }
               }
            }
         }
      }
   }

   protected void devourProjectile(Projectile projectile) {
      projectile.m_146870_();
      Entity var3 = this.m_37282_();
      if (var3 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var3;
         ItemStack scale;
         if (projectile.m_6095_() == TensuraEntityTypes.TEMPEST_SCALE.get() && owner instanceof Player) {
            Player player = (Player)owner;
            scale = ((Item)TensuraMobDropItems.CHARYBDIS_SCALE.get()).m_7968_();
            if (!this.addItemToSpatialStorage(player, scale) && !player.m_36356_(scale)) {
               SkillHelper.dropItem(player, player.m_217043_(), scale, 20, 1.0F);
            }
         } else if (projectile instanceof AbstractArrow) {
            AbstractArrow arrow = (AbstractArrow)projectile;
            if (arrow.f_36705_ == Pickup.ALLOWED && owner instanceof Player) {
               Player player = (Player)owner;
               scale = arrow.m_7941_();
               if (!this.addItemToSpatialStorage(player, scale) && !player.m_36356_(scale)) {
                  SkillHelper.dropItem(player, player.m_217043_(), scale, 20, 1.0F);
               }
            }
         }

         if (projectile instanceof TensuraProjectile) {
            TensuraProjectile tensuraProjectile = (TensuraProjectile)projectile;
            ManasSkillInstance skill = tensuraProjectile.getSkill();
            if (skill != null && this.canDevour(skill)) {
               SkillPlunderEvent event = new SkillPlunderEvent(projectile.m_37282_(), owner, false, skill.getSkill());
               if (!MinecraftForge.EVENT_BUS.post(event)) {
                  if (SkillUtils.learnSkill(owner, event.getSkill(), this.getSkill().getRemoveTime())) {
                     if (owner instanceof Player) {
                        Player player = (Player)owner;
                        player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     }

                     owner.m_9236_().m_6263_((Player)null, owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }

               }
            }
         }
      }
   }

   protected void devourRandomSkill(LivingEntity target, LivingEntity owner) {
      if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
         List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter(this::canDevour).toList();
         if (!collection.isEmpty()) {
            ManasSkillInstance instance = (ManasSkillInstance)collection.get(target.m_217043_().m_188503_(collection.size()));
            SkillPlunderEvent event = new SkillPlunderEvent(target, owner, false, instance.getSkill());
            if (!MinecraftForge.EVENT_BUS.post(event)) {
               if (SkillUtils.learnSkill(owner, event.getSkill(), this.getSkill().getRemoveTime())) {
                  if (owner instanceof Player) {
                     Player player = (Player)owner;
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }

                  owner.m_9236_().m_6263_((Player)null, owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

            }
         }
      }
   }

   protected void devourAllSkills(LivingEntity target, LivingEntity owner) {
      if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
         List<ManasSkillInstance> targetSkills = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter(this::canDevour).toList();
         Iterator var4 = targetSkills.iterator();

         while(var4.hasNext()) {
            ManasSkillInstance targetInstance = (ManasSkillInstance)var4.next();
            if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0) {
               SkillPlunderEvent event = new SkillPlunderEvent(target, owner, false, targetInstance.getSkill());
               if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(owner, event.getSkill(), this.getSkill().getRemoveTime())) {
                  if (owner instanceof Player) {
                     Player player = (Player)owner;
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }

                  owner.m_9236_().m_6263_((Player)null, owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }

      }
   }

   protected void devourEP(LivingEntity target, LivingEntity owner, float amountToMax) {
      if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(owner);
         Optional<ManasSkillInstance> predator = storage.getSkill(this.skill.getSkill());
         if (!predator.isEmpty()) {
            ManasSkillInstance instance = (ManasSkillInstance)predator.get();
            if (!instance.isTemporarySkill()) {
               CompoundTag tag = instance.getOrCreateTag();
               CompoundTag predationList;
               if (tag.m_128441_("predationList")) {
                  predationList = (CompoundTag)tag.m_128423_("predationList");
                  if (predationList == null) {
                     return;
                  }

                  String targetID = EntityType.m_20613_(target.m_6095_()).toString();
                  if (predationList.m_128441_(targetID)) {
                     return;
                  }

                  predationList.m_128379_(targetID, true);
                  instance.markDirty();
               } else {
                  predationList = new CompoundTag();
                  predationList.m_128379_(EntityType.m_20613_(target.m_6095_()).toString(), true);
                  tag.m_128365_("predationList", predationList);
                  instance.markDirty();
               }

               storage.syncChanges();
               double EP = Math.min(SkillUtils.getEPGain(target, owner), (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get() / (double)amountToMax);
               if (target instanceof Player) {
                  Player playerTarget = (Player)target;
                  if (TensuraGameRules.canEpSteal(target.m_9236_())) {
                     int minEP = TensuraGameRules.getMinEp(target.m_9236_());
                     if (minEP > 0) {
                        EP -= (double)minEP;
                     }

                     if (EP <= 0.0D) {
                        return;
                     }

                     SkillHelper.gainMaxMP(owner, EP * (double)amountToMax);
                     TensuraEPCapability.setSkippingEPDrop(target, true);
                     this.saveMagiculeIntoStorage(owner, EP * (double)(1.0F - amountToMax));
                     TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                        cap.setBaseMagicule(cap.getBaseMagicule() - EP / 2.0D, playerTarget);
                        cap.setBaseAura(cap.getBaseAura() - EP / 2.0D, playerTarget);
                     });
                     TensuraPlayerCapability.sync(playerTarget);
                  }
               } else {
                  SkillHelper.gainMaxMP(owner, EP * (double)amountToMax);
                  this.saveMagiculeIntoStorage(owner, EP * (double)(1.0F - amountToMax));
                  SkillHelper.reduceEP(target, owner, 1.0D, true, true);
                  TensuraEPCapability.setSkippingEPDrop(target, true);
               }

            }
         }
      }
   }

   protected void saveMagiculeIntoStorage(LivingEntity owner, double amount) {
      ManasSkillInstance instance = this.getSkillInstance(owner);
      if (instance != null) {
         CompoundTag tag = instance.getOrCreateTag();
         tag.m_128347_("MpStomach", tag.m_128459_("MpStomach") + amount);
         instance.markDirty();
      }
   }

   @Nullable
   protected ManasSkillInstance getSkillInstance(LivingEntity owner) {
      SkillStorage storage = SkillAPI.getSkillsFrom(owner);
      Optional<ManasSkillInstance> optional = storage.getSkill(this.skill.getSkill());
      return (ManasSkillInstance)optional.orElse((Object)null);
   }

   protected boolean addItemToSpatialStorage(Player player, ItemStack stack) {
      ManasSkillInstance instance = this.getSkillInstance(player);
      if (instance == null) {
         return false;
      } else {
         ManasSkill var5 = instance.getSkill();
         if (var5 instanceof ISpatialStorage) {
            ISpatialStorage spatialStorage = (ISpatialStorage)var5;
            return spatialStorage.addItemToSpatialStorage(instance, player, stack);
         } else {
            return false;
         }
      }
   }

   protected void consumeBlocks() {
      if (TensuraGameRules.canSkillGrief(this.m_9236_())) {
         Entity var2 = this.m_37282_();
         if (var2 instanceof Player) {
            Player player = (Player)var2;
            int blockMode = this.getBlockMode();
            if (blockMode != 1) {
               float range = 0.34906584F;

               for(int i = 0; i < 5; ++i) {
                  Vec3 lookAngle = this.m_37282_().m_20154_().m_82541_().m_82496_(this.m_9236_().f_46441_.m_188501_() * range * 2.0F - range).m_82524_(this.m_9236_().f_46441_.m_188501_() * range * 2.0F - range).m_82535_(this.m_9236_().f_46441_.m_188501_() * range * 2.0F - range);
                  if (blockMode == 2 || blockMode == 4) {
                     this.breakBlocks(player, lookAngle);
                  }

                  if (blockMode == 3 || blockMode == 4) {
                     this.consumeFluid(player, lookAngle);
                  }
               }

            }
         }
      }
   }

   protected void breakBlocks(Player player, Vec3 lookAngle) {
      BlockHitResult result = this.m_9236_().m_45547_(new ClipContext(player.m_146892_(), player.m_146892_().m_82549_(lookAngle.m_82490_((double)this.getLength())), Block.OUTLINE, Fluid.NONE, this));
      if (result.m_6662_() == Type.BLOCK) {
         BlockPos pos = result.m_82425_();
         BlockState state = this.f_19853_.m_8055_(pos);
         if (state.m_204336_(TensuraTags.Blocks.SKILL_UNOBTAINABLE)) {
            return;
         }

         if (state.m_60734_().m_155943_() <= -1.0F) {
            return;
         }

         SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), pos);
         if (MinecraftForge.EVENT_BUS.post(preGrief)) {
            return;
         }

         state.m_60734_().m_5707_(this.f_19853_, pos, state, player);
         if (MolecularManipulationSkill.isMultiBlock(state)) {
            this.f_19853_.m_46953_(pos, true, player);
         } else {
            label65: {
               BlockEntity blockentity = this.f_19853_.m_7702_(pos);
               if (blockentity instanceof BeehiveBlockEntity) {
                  BeehiveBlockEntity beehiveblockentity = (BeehiveBlockEntity)blockentity;
                  beehiveblockentity.m_58748_(player, state, BeeReleaseStatus.EMERGENCY);
               }

               if (blockentity instanceof ShulkerBoxBlockEntity) {
                  ShulkerBoxBlockEntity shulker = (ShulkerBoxBlockEntity)blockentity;
                  if (!shulker.m_7983_()) {
                     this.f_19853_.m_46953_(pos, !player.m_7500_(), player);
                     break label65;
                  }
               }

               ItemStack stack = new ItemStack(state.m_60734_());
               if (!this.addItemToSpatialStorage(player, stack) && !player.m_36356_(stack)) {
                  player.m_36176_(stack, false);
               }

               this.f_19853_.m_46953_(pos, false, player);
            }
         }

         MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), pos));
         List<ItemEntity> list = player.f_19853_.m_45976_(ItemEntity.class, AABB.m_165882_(Vec3.m_82512_(pos), 2.0D, 2.0D, 2.0D));
         Iterator var12 = list.iterator();

         while(var12.hasNext()) {
            ItemEntity item = (ItemEntity)var12.next();
            if (this.addItemToSpatialStorage(player, item.m_32055_())) {
               item.m_146870_();
            } else if (player.m_36356_(item.m_32055_())) {
               item.m_146870_();
            } else {
               item.m_20219_(player.m_20182_());
            }
         }
      }

   }

   protected void consumeFluid(Player player, Vec3 lookAngle) {
      ClipContext context = new ClipContext(player.m_146892_(), player.m_146892_().m_82549_(lookAngle.m_82490_((double)this.getLength())), Block.OUTLINE, Fluid.ANY, this);
      BlockHitResult result = this.m_9236_().m_45547_(context);
      if (result.m_6662_() == Type.BLOCK) {
         BlockPos pos = result.m_82425_();
         BlockState state = this.f_19853_.m_8055_(pos);
         if (!state.m_60819_().m_76178_()) {
            SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), pos);
            if (MinecraftForge.EVENT_BUS.post(preGrief)) {
               return;
            }

            if (state.m_60819_().m_76170_()) {
               TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                  if (state.m_60819_().m_205070_(FluidTags.f_13131_)) {
                     cap.setWaterPoint(cap.getWaterPoint() + 1.0D);
                     TensuraSkillCapability.sync(player);
                  } else if (state.m_60819_().m_205070_(FluidTags.f_13132_)) {
                     cap.setLavaPoint(cap.getLavaPoint() + 1.0D);
                     TensuraSkillCapability.sync(player);
                  }

               });
            }

            if (!state.m_60713_(Blocks.f_49990_) && !state.m_60713_(Blocks.f_49991_)) {
               if (state.m_61138_(BlockStateProperties.f_61362_) && (Boolean)state.m_61143_(BlockStateProperties.f_61362_)) {
                  this.f_19853_.m_7731_(pos, (BlockState)state.m_61124_(BlockStateProperties.f_61362_, false), 11);
               }
            } else {
               this.f_19853_.m_7731_(pos, Blocks.f_50016_.m_49966_(), 11);
            }

            MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), pos));
         }
      }

   }

   public void spawnParticle() {
      BreathPart[] var1 = this.parts;
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         BreathPart part = var1[var3];
         TensuraParticleHelper.addParticlesAroundSelf(part, ParticleTypes.f_123765_, 0.7D);
      }

   }

   public boolean isConsumeProjectile() {
      return this.consumeProjectile;
   }

   public void setConsumeProjectile(boolean consumeProjectile) {
      this.consumeProjectile = consumeProjectile;
   }

   public int getBlockMode() {
      return this.blockMode;
   }

   public void setBlockMode(int blockMode) {
      this.blockMode = blockMode;
   }
}
