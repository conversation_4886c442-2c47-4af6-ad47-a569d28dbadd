package com.github.manasmods.tensura.integration.jei;

import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.util.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import mezz.jei.api.constants.VanillaTypes;
import mezz.jei.api.gui.builder.IRecipeLayoutBuilder;
import mezz.jei.api.gui.drawable.IDrawable;
import mezz.jei.api.gui.ingredient.IRecipeSlotsView;
import mezz.jei.api.helpers.IGuiHelper;
import mezz.jei.api.recipe.IFocusGroup;
import mezz.jei.api.recipe.RecipeIngredientRole;
import mezz.jei.api.recipe.RecipeType;
import mezz.jei.api.recipe.category.IRecipeCategory;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;

public class KilnMixingRecipeCategory implements IRecipeCategory<KilnMixingRecipe> {
   private static final ResourceLocation TEXTURE = new ResourceLocation("tensura", "textures/gui/kiln/jei_mixing.png");
   private static final Component TITLE = Component.m_237115_("tensura.jei.mixing.title");
   static final ResourceLocation UID = new ResourceLocation("tensura", "kiln/mixing");
   private final IDrawable background;
   private final IDrawable icon;

   public KilnMixingRecipeCategory(IGuiHelper guiHelper) {
      this.icon = guiHelper.createDrawableIngredient(VanillaTypes.ITEM_STACK, ((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get()).m_7968_());
      this.background = guiHelper.createDrawable(TEXTURE, 0, 0, 177, 86);
   }

   public RecipeType<KilnMixingRecipe> getRecipeType() {
      return TensuraJeiPlugin.KILN_MIXING_RECIPE;
   }

   public Component getTitle() {
      return TITLE;
   }

   public IDrawable getBackground() {
      return this.background;
   }

   public IDrawable getIcon() {
      return this.icon;
   }

   public void setRecipe(IRecipeLayoutBuilder builder, KilnMixingRecipe recipe, IFocusGroup focuses) {
      builder.addSlot(RecipeIngredientRole.OUTPUT, 80, 36).addItemStack(recipe.getOutput());
   }

   public void draw(KilnMixingRecipe recipe, IRecipeSlotsView recipeSlotsView, PoseStack stack, double mouseX, double mouseY) {
      Iterator var8 = TensuraData.getKilnMoltenMaterials().iterator();

      while(var8.hasNext()) {
         KilnMoltenMaterial moltenMaterial = (KilnMoltenMaterial)var8.next();
         if (!recipe.getLeftInput().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.getMoltenType().equals(recipe.getLeftInput())) {
            RenderUtils.renderMoltenMaterial(stack, moltenMaterial, recipe.getLeftInputAmount(), 144);
         }

         if (!recipe.getRightInput().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.getMoltenType().equals(recipe.getRightInput())) {
            RenderUtils.renderMoltenMaterial(stack, moltenMaterial, recipe.getRightInputAmount(), 144);
         }
      }

   }

   public List<Component> getTooltipStrings(KilnMixingRecipe recipe, IRecipeSlotsView recipeSlotsView, double mouseX, double mouseY) {
      ArrayList<Component> tooltip = new ArrayList();
      Iterator var8 = TensuraData.getKilnMoltenMaterials().iterator();

      while(var8.hasNext()) {
         KilnMoltenMaterial moltenMaterial = (KilnMoltenMaterial)var8.next();
         if (moltenMaterial.isRightBar()) {
            if (this.isHovering(145, 6, 13, 74, mouseX, mouseY) && !recipe.getRightInput().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.isRightBar() && moltenMaterial.getMoltenType().equals(recipe.getRightInput())) {
               tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getRightInputAmount() / 4.0F, 36));
            }
         } else if (!recipe.getLeftInput().equals(KilnMixingRecipe.EMPTY) && this.isHovering(18, 6, 13, 74, mouseX, mouseY) && moltenMaterial.getMoltenType().equals(recipe.getLeftInput())) {
            tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getLeftInputAmount(), 144));
         }
      }

      return tooltip;
   }

   protected boolean isHovering(int pX, int pY, int pWidth, int pHeight, double pMouseX, double pMouseY) {
      return pMouseX >= (double)(pX - 1) && pMouseX < (double)(pX + pWidth + 1) && pMouseY >= (double)(pY - 1) && pMouseY < (double)(pY + pHeight + 1);
   }
}
