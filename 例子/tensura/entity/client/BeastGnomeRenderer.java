package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BeastGnomeEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import java.util.Arrays;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class BeastGnomeRenderer extends GeoEntityRenderer<BeastGnomeEntity> {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_0.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_1.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_2.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_3.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_4.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_5.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_6.png"), new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome_7.png")};

   public BeastGnomeRenderer(Context renderManager) {
      super(renderManager, new BeastGnomeModel());
   }

   public ResourceLocation getTextureLocation(BeastGnomeEntity instance) {
      return (ResourceLocation)Arrays.stream(TEXTURES).toList().get(instance.f_19797_ / 2 % TEXTURES.length);
   }

   public RenderType getRenderType(BeastGnomeEntity beast, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (beast.isShrunk()) {
         stack.m_85841_(0.1F, 0.1F, 0.1F);
      }

      return RenderType.m_110473_(this.getTextureLocation(beast));
   }

   public void render(BeastGnomeEntity pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      this.f_114477_ = pEntity.isShrunk() ? 0.15F : 1.5F;
      super.render(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
   }
}
