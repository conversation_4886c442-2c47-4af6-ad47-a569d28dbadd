package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.magic.misc.ChaosEaterProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.blaze3d.vertex.PoseStack.Pose;
import com.mojang.math.Matrix3f;
import com.mojang.math.Matrix4f;
import com.mojang.math.Vector3f;
import java.util.Arrays;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.blockentity.BeaconRenderer;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

public class ChaosEaterRenderer extends EntityRenderer<ChaosEaterProjectile> {
   public static final ModelLayerLocation CHAOS_EATER = new ModelLayerLocation(new ResourceLocation("tensura", "chaos_eater"), "main");
   private final ModelPart eater;

   public ChaosEaterRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(CHAOS_EATER);
      this.eater = modelpart.m_171324_("Ball");
   }

   public ResourceLocation getTextureLocation(ChaosEaterProjectile instance) {
      ResourceLocation[] resourceLocations = instance.getTextureLocation();
      return resourceLocations == null ? new ResourceLocation("tensura", "textures/blank_texture.png") : (ResourceLocation)Arrays.stream(resourceLocations).toList().get(instance.f_19797_ % resourceLocations.length);
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      partdefinition.m_171599_("Ball", CubeListBuilder.m_171558_().m_171514_(0, 32).m_171488_(-3.0F, -3.0F, -5.3333F, 6.0F, 6.0F, 6.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 6.0F, 0.3333F));
      return LayerDefinition.m_171565_(meshdefinition, 48, 44);
   }

   public void render(ChaosEaterProjectile entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !entity.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         float scale = entity.getVisualSize();
         poseStack.m_85841_(scale, scale, scale);
         Vec3 motion = entity.m_20184_();
         float xRot = -((float)(Mth.m_14136_(motion.m_165924_(), motion.f_82480_) * 57.2957763671875D) - 90.0F);
         float yRot = -((float)(Mth.m_14136_(motion.f_82481_, motion.f_82479_) * 57.2957763671875D) + 90.0F);
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(yRot));
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(xRot));
         float f = (float)entity.f_19797_ + partialTicks;
         poseStack.m_85836_();
         float swirlX = Mth.m_14089_(0.05F * f * 2.0F) * 90.0F;
         float swirlY = Mth.m_14031_(0.05F * f * 2.0F) * 90.0F;
         float swirlZ = Mth.m_14089_(0.05F * f * 2.0F + 5464.0F) * 90.0F;
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(swirlX * 0.45F));
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(swirlY * 0.45F));
         poseStack.m_85845_(Vector3f.f_122227_.m_122240_(swirlZ * 0.45F));
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(entity)));
         this.eater.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(swirlZ));
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(swirlX));
         poseStack.m_85845_(Vector3f.f_122227_.m_122240_(swirlY));
         poseStack.m_85849_();
         super.m_7392_(entity, yaw, partialTicks, poseStack, bufferSource, light);
         if (entity.m_37282_() != null) {
            renderRay(entity, entity.m_37282_(), partialTicks, poseStack, bufferSource, 15728880, 0.3F, 38, 23, 19, 255);
            renderRay(entity, entity.m_37282_(), partialTicks, poseStack, bufferSource, 15728880, 0.4F, 78, 2, 9, 30);
         }

      }
   }

   public static void renderRay(ChaosEaterProjectile eater, Entity owner, float partialTick, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, float size, int red, int green, int blue, int alpha) {
      float gloveHalfWidth = eater.m_20205_() / 2.0F;
      Vec3 offset = owner.m_146892_().m_82549_(eater.getStartOffset());
      double x = offset.m_7096_();
      double y = offset.m_7098_();
      double z = offset.m_7094_();
      float xDist = (float)(x - Mth.m_14139_((double)partialTick, eater.f_19854_, eater.m_20185_()));
      float yDist = (float)(y - Mth.m_14139_((double)partialTick, eater.f_19855_, eater.m_20186_()) - (double)gloveHalfWidth);
      float zDist = (float)(z - Mth.m_14139_((double)partialTick, eater.f_19856_, eater.m_20189_()));
      float f = Mth.m_14116_(xDist * xDist + zDist * zDist);
      float f1 = Mth.m_14116_(xDist * xDist + yDist * yDist + zDist * zDist);
      poseStack.m_85836_();
      poseStack.m_85837_(0.0D, (double)gloveHalfWidth, 0.0D);
      poseStack.m_85845_(Vector3f.f_122225_.m_122270_((float)(-Math.atan2((double)zDist, (double)xDist)) - 1.5707964F));
      poseStack.m_85845_(Vector3f.f_122223_.m_122270_((float)(-Math.atan2((double)f, (double)yDist)) - 1.5707964F));
      VertexConsumer vertexConsumer = bufferSource.m_6299_(RenderType.m_110473_(BeaconRenderer.f_112102_));
      float f2 = 0.0F - ((float)eater.f_19797_ + partialTick) * 0.01F;
      float f3 = f1 / 32.0F - ((float)eater.f_19797_ + partialTick) * 0.01F;
      int i = 8;
      float f4 = 0.0F;
      float f5 = 0.25F;
      float f6 = 0.0F;
      Pose pose = poseStack.m_85850_();
      Matrix4f matrix4f = pose.m_85861_();
      Matrix3f matrix3f = pose.m_85864_();

      for(int j = 1; j <= i; ++j) {
         float f7 = Mth.m_14031_((float)j * 6.2831855F / (float)i) * size;
         float f8 = Mth.m_14089_((float)j * 6.2831855F / (float)i) * size;
         float f9 = (float)j / (float)i;
         vertexConsumer.m_85982_(matrix4f, f4, f5, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f6, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f4, f5, f1).m_6122_(red, green, blue, alpha).m_7421_(f6, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, f1).m_6122_(red, green, blue, alpha).m_7421_(f9, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f9, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         f4 = f7;
         f5 = f8;
         f6 = f9;
      }

      poseStack.m_85849_();
   }
}
