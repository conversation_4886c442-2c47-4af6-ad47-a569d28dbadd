package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.MetalSlimeEntity;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.entity.variant.SlimeVariant;
import java.util.Calendar;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class SlimeModel extends AnimatedGeoModel<SlimeEntity> {
   private boolean santaHat;

   protected SlimeModel() {
      Calendar calendar = Calendar.getInstance();
      if (calendar.get(2) + 1 == 12 && calendar.get(5) >= 24 && calendar.get(5) <= 26) {
         this.santaHat = true;
      }

   }

   public ResourceLocation getModelResource(SlimeEntity slime) {
      return new ResourceLocation("tensura", "geo/slime.geo.json");
   }

   public ResourceLocation getTextureResource(SlimeEntity slime) {
      if (slime.getClass() == MetalSlimeEntity.class) {
         return SlimeRenderer.shouldBeGolden(slime) ? new ResourceLocation("tensura", "textures/entity/slime/slime_metal_golden.png") : new ResourceLocation("tensura", "textures/entity/slime/slime_metal.png");
      } else {
         return slime.m_8077_() && "jeb_".equals(slime.m_7755_().getString()) ? new ResourceLocation("tensura", "textures/entity/slime/slime_no_color.png") : (ResourceLocation)SlimeVariant.LOCATION_BY_VARIANT.get(slime.getVariant());
      }
   }

   public ResourceLocation getAnimationResource(SlimeEntity slime) {
      return new ResourceLocation("tensura", "animations/slime.animation.json");
   }

   public void setCustomAnimations(SlimeEntity slime, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(slime, instanceId, customPredicate);
      this.getAnimationProcessor().getBone("HeadArmor").setHidden(true);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (slime.isChested() == chest.isHidden()) {
         chest.setHidden(!slime.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (slime.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!slime.isSaddled());
      }

      boolean santaHat = this.shouldShowSantaHat(slime);
      IBone hat = this.getAnimationProcessor().getBone("SantaHat");
      if (santaHat == hat.isHidden()) {
         hat.setHidden(!santaHat);
      }

   }

   private boolean shouldShowSantaHat(SlimeEntity slime) {
      if (slime.isSaddled()) {
         return false;
      } else {
         return !slime.m_6844_(EquipmentSlot.HEAD).m_41619_() ? false : this.santaHat;
      }
   }
}
