package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import java.util.function.Consumer;
import net.minecraft.advancements.Advancement;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraftforge.event.entity.player.AdvancementEvent.AdvancementEarnEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraAdvancementsHandler {
   private static Player player;
   private static Advancement advancement;

   @SubscribeEvent
   public static void advancementReward(AdvancementEarnEvent event) {
      player = event.getEntity();
      advancement = event.getAdvancement();
      action(TensuraAdvancementsHelper.Advancements.REINCARNATED, (player) -> {
         grantExperience(1);
      });
      action(TensuraAdvancementsHelper.Advancements.OBTAIN_HIHIIROKANE_HOE, (player) -> {
         grantExperience(55);
      });
      action(TensuraAdvancementsHelper.Advancements.GETCHA_LEATHERS, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.LEATHER_GEAR.get()), 1);
      });
      action(new ResourceLocation("story/smelt_iron"), (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.IRON_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.GOLD_RUSH, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.GOLD_GEAR.get()), 1);
      });
      action(new ResourceLocation("story/mine_diamond"), (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.DIAMOND_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.BELIEVE_T0_FLY, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.WINGED_SHOES.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.ACQUIRE_SILVERWARE, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.SILVER_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.GETCHA_BETTER_LEATHERS, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.LOW_MAGISTEEL, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.HIGH_MAGISTEEL, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.MITHRIL, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.MITHRIL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.ORICHALCUM, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.ORICHALCUM_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.PURE_MAGISTEEL, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.ADAMANTITE, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.ADAMANTITE_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.HIHIIROKANE, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.VIGILANT, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.SHELL_LIZARD, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.HISS_TORY, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.ARACHNOPHOBIC, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.WEB_GUN.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.GOODNIGHT_SPIDER, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR.get()), 1);
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.SPIDER_BOWS.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.RULER_OF_THE_SKIES, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR.get()), 1);
      });
      action(TensuraAdvancementsHelper.Advancements.RULER_OF_MONSTERS, (player) -> {
         giveItem((ItemLike)((ItemLike)TensuraSmithingSchematicItems.DARK_SET.get()), 1);
      });
   }

   private static void action(ResourceLocation ifThisAdvancement, Consumer<Player> thenDo) {
      if (advancement.m_138327_().equals(ifThisAdvancement)) {
         thenDo.accept(player);
      }

   }

   private static void grantExperience(int points) {
      player.m_6756_(points);
   }

   private static void giveItem(ItemLike item, int quantity) {
      for(int i = 0; i < quantity; ++i) {
         ItemStack stack = item.m_5456_().m_7968_();
         if (!player.m_36356_(stack)) {
            player.m_36176_(stack, false);
         }
      }

   }

   private static void giveItem(ItemStack item, int quantity) {
      for(int i = 0; i < quantity; ++i) {
         if (!player.m_36356_(item)) {
            player.m_36176_(item, false);
         }
      }

   }
}
