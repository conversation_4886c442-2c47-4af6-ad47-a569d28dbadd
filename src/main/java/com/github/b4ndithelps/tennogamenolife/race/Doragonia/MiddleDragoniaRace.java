package com.github.b4ndithelps.tennogamenolife.race.Doragonia;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 中阶龙
 */
public class MiddleDragoniaRace extends Race {

    // 种族基础属性
    private double baseHealth = 180.0;          // 基础生命值
    private double baseAttackDamage = 3.0;     // 基础攻击伤害
    private double baseAttackSpeed = 4.3;      // 基础攻击速度
    private double knockbackResistance = 0.6;  // 击退抗性
    private double jumpHeight = 1.2;           // 跳跃高度
    private double movementSpeed = 0.13;       // 移动速度
    private double sprintSpeed = 0.16;         // 冲刺速度
    private double auraMin = 1600.0;           // 最小灵气值
    private double auraMax = 2500.0;           // 最大灵气值
    private double startingMagiculeMin = 800.0; // 最小初始魔素值
    private double startingMagiculeMax = 1200.0; // 最大初始魔素值

    private float playerSize = 2.5f;           // 玩家大小

    public MiddleDragoniaRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        // 暂不实现种族能力
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        double ep = Math.min(TensuraPlayerCapability.getBaseEP(player) * 50.0D / 55000.0D, 50.0D);
        double boss = Math.min(TensuraStats.getBossKilled(player) * 50 / 5, 50);
        return ep + boss;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        list.add(Component.translatable("tensura.evolution_menu.boss_kill_requirement"));
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.HIGHER_DRAGONIA.get());
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.HIGHER_DRAGONIA.get();
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null; // 暂无觉醒进化
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null; // 暂无丰收节进化
    }
} 