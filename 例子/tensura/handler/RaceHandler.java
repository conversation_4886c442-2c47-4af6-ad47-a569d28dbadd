package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.api.race.AdvancedHitbox;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.network.play2server.SlimeJumpReleasedPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.race.slime.SlimeRace;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.block.LiquidBlock;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.EntityJoinLevelEvent;
import net.minecraftforge.event.entity.EntityEvent.Size;
import net.minecraftforge.event.entity.living.LivingFallEvent;
import net.minecraftforge.event.entity.living.LivingEvent.LivingJumpEvent;
import net.minecraftforge.event.entity.player.PlayerSetSpawnEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class RaceHandler {
   @SubscribeEvent
   public static void resizeOnLogin(EntityJoinLevelEvent e) {
      if (!e.getLevel().m_5776_()) {
         Entity var2 = e.getEntity();
         if (var2 instanceof Player) {
            Player player = (Player)var2;
            player.m_6210_();
            TensuraPlayerCapability.sync(player);
         }
      }
   }

   @SubscribeEvent
   public static void resizeOnSizeChange(Size e) {
      Entity var2 = e.getEntity();
      if (var2 instanceof Player) {
         Player player = (Player)var2;
         boolean shouldChange = false;
         EntityDimensions size = e.getNewSize();
         float eyeHeight = e.getNewEyeHeight();
         Race race = TensuraPlayerCapability.getRace(player);
         float height;
         if (race != null) {
            if (RaceHelper.getRaceSize(race) != 1.0F) {
               height = RaceHelper.getRaceSize(race);
               size = size.m_20388_(height);
               eyeHeight *= height;
               shouldChange = true;
            }

            if (race instanceof AdvancedHitbox) {
               AdvancedHitbox hitbox = (AdvancedHitbox)race;
               size = size.m_20390_(hitbox.getHitboxWidthModifier(), hitbox.getHitboxHeightModifier());
               eyeHeight *= hitbox.getHitboxHeightModifier();
               shouldChange = true;
            }
         }

         height = TensuraEffectsCapability.getHeight(player);
         if (height != 1.0F && height != 0.0F) {
            size = size.m_20390_(1.0F, height);
            eyeHeight *= height;
            shouldChange = true;
         }

         float skillSize = RaceHelper.getSkillSizeMultiplier(player);
         if (skillSize != 1.0F) {
            size = size.m_20388_(skillSize);
            eyeHeight *= skillSize;
            shouldChange = true;
         }

         if (shouldChange) {
            e.setNewSize(size);
            e.setNewEyeHeight(eyeHeight);
         }

      }
   }

   @SubscribeEvent
   public static void onSetSpawn(PlayerSetSpawnEvent e) {
      Player player = e.getEntity();
      Race race = TensuraPlayerCapability.getRace(player);
      if (race != null) {
         if ((e.getSpawnLevel() == TensuraDimensions.LABYRINTH || e.getSpawnLevel() == TensuraDimensions.HELL) && race.getRespawnDimension() != e.getSpawnLevel()) {
            e.setCanceled(true);
         }

      }
   }

   @SubscribeEvent
   public static void onSlimeTakeFallDamage(LivingFallEvent e) {
      if (!e.getEntity().f_19853_.m_5776_()) {
         LivingEntity var2 = e.getEntity();
         if (var2 instanceof Player) {
            Player player = (Player)var2;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.getRace() instanceof SlimeRace) {
                  e.setDistance(Math.max(e.getDistance(), 0.0F));
                  e.setDamageMultiplier(0.5F);
               }

            });
         }
      }
   }

   @SubscribeEvent
   public static void onSlimeJump(LivingJumpEvent e) {
      if (!e.getEntity().f_19853_.m_5776_()) {
         LivingEntity var2 = e.getEntity();
         if (var2 instanceof Player) {
            Player player = (Player)var2;
            if (!player.m_5833_()) {
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  if (cap.getRace() instanceof SlimeRace) {
                     if (!SlimeJumpReleasedPacket.jumpChargeData.containsKey(player.m_20148_())) {
                        if (player.m_6047_() && !isInFluid(player) && player.m_20096_()) {
                           player.m_20334_(player.m_20184_().f_82479_, 0.0D, player.m_20184_().f_82481_);
                        }
                     } else {
                        float jumpCharge = (Float)SlimeJumpReleasedPacket.jumpChargeData.get(player.m_20148_());
                        Vec3 look = player.m_20154_().m_82541_();
                        float modifier = 3.3000002F;
                        look = look.m_82542_((double)(3.5000002F * jumpCharge), (double)(3.3000002F * jumpCharge), (double)(3.5000002F * jumpCharge));
                        look = look.m_82520_(0.0D, player.m_20184_().f_82480_, 0.0D);
                        player.m_20256_(look);
                        player.f_19864_ = true;
                        SlimeJumpReleasedPacket.jumpChargeData.remove(player.m_20148_());
                     }
                  }

               });
            }
         }
      }
   }

   public static boolean isInFluid(LivingEntity entity) {
      return entity.m_20072_() || entity.m_20077_() || entity.m_9236_().m_8055_(entity.m_20183_()).m_60734_() instanceof LiquidBlock;
   }
}
