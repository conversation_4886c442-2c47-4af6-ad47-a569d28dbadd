package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.IfritCloneEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.SalamanderEntity;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class FireBoltProjectile extends TensuraProjectile {
   private int impactParticleCount = 1;
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_5.png")};

   public FireBoltProjectile(EntityType<? extends FireBoltProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(0.5F);
   }

   public FireBoltProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.FIRE_BOLT.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(0.5F);
   }

   protected void m_7380_(@NotNull CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("trailLoops", this.getImpactParticleCount());
   }

   protected void m_7378_(@NotNull CompoundTag compound) {
      super.m_7378_(compound);
      this.setImpactParticleCount(compound.m_128451_("trailLoops"));
   }

   public String getMagic() {
      return "tensura.fire_attack";
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   protected void m_5790_(@NotNull EntityHitResult result) {
      if (!(result.m_82443_() instanceof IfritEntity) && !(result.m_82443_() instanceof IfritCloneEntity) && !(result.m_82443_() instanceof SalamanderEntity)) {
         super.m_5790_(result);
      }
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.RED_FIRE.get(), x, y, z, 5 * this.getImpactParticleCount(), 0.1D, 0.1D, 0.1D, 0.25D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123762_, x, y, z, 5 * this.getImpactParticleCount(), 0.1D, 0.1D, 0.1D, 0.25D, true);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20184_();
      double d0 = this.m_20185_() - vec3.f_82479_;
      double d1 = this.m_20186_() - vec3.f_82480_;
      double d2 = this.m_20189_() - vec3.f_82481_;

      for(int i = 0; i < 4; ++i) {
         Vec3 motion = this.vec3Random().m_82490_(0.10000000149011612D).m_82546_(this.m_20184_().m_82490_(0.10000000149011612D));
         Vec3 pos = this.vec3Random().m_82490_(0.20000000298023224D);
         this.f_19853_.m_7106_((ParticleOptions)(this.f_19853_.f_46441_.m_188500_() < 0.3D ? ParticleTypes.f_123762_ : (ParticleOptions)TensuraParticles.RED_FIRE.get()), d0 + pos.f_82479_, d1 + 0.5D + pos.f_82480_, d2 + pos.f_82481_, motion.f_82479_, motion.f_82480_, motion.f_82481_);
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11892_);
   }

   public int getImpactParticleCount() {
      return this.impactParticleCount;
   }

   public void setImpactParticleCount(int impactParticleCount) {
      this.impactParticleCount = impactParticleCount;
   }
}
