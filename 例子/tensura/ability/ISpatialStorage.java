package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.menu.SpatialStorageMenu;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundSpatialStorageOpenPacket;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerContainerEvent.Open;
import net.minecraftforge.network.PacketDistributor;

public interface ISpatialStorage {
   SpatialStorageContainer getSpatialStorage(ManasSkillInstance var1);

   default void openSpatialStorage(LivingEntity entity, ManasSkillInstance instance) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         player.m_6915_();
         player.m_9217_();
         player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         ManasSkill skill = instance.getSkill();
         SpatialStorageContainer container = this.getSpatialStorage(instance);
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundSpatialStorageOpenPacket(player.m_19879_(), player.f_8940_, container.m_6643_(), container.m_6893_(), SkillUtils.getSkillId(skill)));
         player.f_36096_ = new SpatialStorageMenu(player.f_8940_, player.m_150109_(), player, container, skill);
         player.m_143399_(player.f_36096_);
         MinecraftForge.EVENT_BUS.post(new Open(player, player.f_36096_));
      }

   }

   default boolean addItemToSpatialStorage(ManasSkillInstance instance, LivingEntity entity, ItemStack stack) {
      SpatialStorageContainer container = this.getSpatialStorage(instance);
      if (container.m_19183_(stack)) {
         container.m_19173_(stack);
         this.saveContainer(instance, entity, container);
         return true;
      } else {
         this.saveContainer(instance, entity, container);
         return false;
      }
   }

   default void setItemInSpatialStorage(ManasSkillInstance instance, LivingEntity entity, ItemStack stack, int slot) {
      SpatialStorageContainer container = this.getSpatialStorage(instance);
      container.m_6836_(slot, stack);
      this.saveContainer(instance, entity, container);
   }

   default void moveItemsToSpatialStorage(ManasSkillInstance from, ManasSkillInstance to, LivingEntity entity, boolean openNewStorage) {
      ISpatialStorage newStorage = (ISpatialStorage)to.getSkill();
      SpatialStorageContainer container = this.getSpatialStorage(from);
      if (!container.m_7983_()) {
         for(int i = 0; i < container.m_6643_(); ++i) {
            ItemStack stack = container.m_8020_(i);
            if (!stack.m_41619_() && !newStorage.addItemToSpatialStorage(to, entity, stack) && entity instanceof Player) {
               Player player = (Player)entity;
               if (!player.m_36356_(stack)) {
                  player.m_36176_(stack, false);
               }
            }
         }

         container.m_6211_();
         from.getOrCreateTag().m_128473_("SpatialStorage");
         from.markDirty();
      }

      if (openNewStorage) {
         newStorage.openSpatialStorage(entity, to);
      }

   }

   default void dropAllItems(ManasSkillInstance instance, Player player) {
      SpatialStorageContainer container = this.getSpatialStorage(instance);
      if (!container.m_7983_()) {
         for(int i = 0; i < container.m_6643_(); ++i) {
            ItemStack stack = container.m_8020_(i);
            if (!stack.m_41619_() && !player.m_36356_(stack)) {
               player.m_36176_(stack, false);
            }
         }

         container.m_6211_();
         instance.getOrCreateTag().m_128473_("SpatialStorage");
         instance.markDirty();
      }

   }

   default void saveContainer(ManasSkillInstance instance, LivingEntity entity, SpatialStorageContainer container) {
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128365_("SpatialStorage", container.m_7927_());
      instance.markDirty();
      SkillAPI.getSkillsFrom(entity).syncChanges();
   }
}
