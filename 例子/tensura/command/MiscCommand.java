package com.github.manasmods.tensura.command;

import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.google.common.collect.ImmutableList;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.SimpleCommandExceptionType;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class MiscCommand {
   private static final SimpleCommandExceptionType ERROR_CLEAR_EVERYTHING_FAILED = new SimpleCommandExceptionType(Component.m_237115_("commands.effect.clear.everything.failed"));

   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("effect").requires((stack) -> {
         return stack.m_6761_(2);
      })).then(Commands.m_82127_("clear").then(((LiteralArgumentBuilder)Commands.m_82127_("ability").executes((context) -> {
         return clearEffects((CommandSourceStack)context.getSource(), ImmutableList.of(((CommandSourceStack)context.getSource()).m_81374_()));
      })).then(Commands.m_82129_("targets", EntityArgument.m_91460_()).executes((context) -> {
         return clearEffects((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"));
      })))));
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("despawn").requires((stack) -> {
         return stack.m_6761_(2);
      })).then(Commands.m_82129_("targets", EntityArgument.m_91460_()).executes((context) -> {
         List<? extends Entity> entities = EntityArgument.m_91461_(context, "targets").stream().toList();
         if (entities.size() == 1) {
            Entity entityx = (Entity)entities.get(0);
            if (entityx instanceof Player) {
               ((CommandSourceStack)context.getSource()).m_81352_(Component.m_237110_("tensura.command.despawn.fail", new Object[]{entityx.m_7755_()}));
            } else {
               entityx.m_146870_();
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.despawn", new Object[]{entityx.m_7755_()}).m_130940_(ChatFormatting.DARK_GREEN), true);
            }

            return 1;
         } else {
            int i = 0;
            Iterator var3 = entities.iterator();

            while(var3.hasNext()) {
               Entity entity = (Entity)var3.next();
               if (!(entity instanceof Player)) {
                  entity.m_146870_();
                  ++i;
               }
            }

            ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.command.despawn.all", new Object[]{i}).m_130940_(ChatFormatting.DARK_GREEN), true);
            return 1;
         }
      })));
   }

   private static int clearEffects(CommandSourceStack pSource, Collection<? extends Entity> pTargets) throws CommandSyntaxException {
      int i = 0;
      Iterator var3 = pTargets.iterator();

      while(var3.hasNext()) {
         Entity entity = (Entity)var3.next();
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            if (SkillMobEffect.removeAllEffects(living)) {
               ++i;
            }
         }
      }

      if (i == 0) {
         throw ERROR_CLEAR_EVERYTHING_FAILED.create();
      } else {
         if (pTargets.size() == 1) {
            pSource.m_81354_(Component.m_237110_("tensura.command.clear_skill_effect.success.single", new Object[]{((Entity)pTargets.iterator().next()).m_5446_()}), true);
         } else {
            pSource.m_81354_(Component.m_237110_("tensura.command.clear_skill_effect.success.multiple", new Object[]{pTargets.size()}), true);
         }

         return i;
      }
   }
}
