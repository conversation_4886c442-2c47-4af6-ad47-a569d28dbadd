package com.github.b4ndithelps.tennogamenolife.command;

import com.github.b4ndithelps.tennogamenolife.util.DamageTestManager;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;

/**
 * 处理 /aqzl 命令
 */
public class JorCommand {
    
    /**
     * 注册命令
     */
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(
            Commands.literal("aqzl")
                .requires(source -> source.hasPermission(0)) // 设置权限级别（0为所有玩家可用）
                .then(Commands.argument("enabled", BoolArgumentType.bool())
                    .executes(context -> executeJorCommand(
                        context, 
                        BoolArgumentType.getBool(context, "enabled")
                    ))
                )
        );
    }
    
    /**
     * 执行命令
     */
    private static int executeJorCommand(CommandContext<CommandSourceStack> context, boolean enabled) {
        CommandSourceStack source = context.getSource();
        
        // 检查命令执行者是否为玩家
        if (source.getEntity() instanceof ServerPlayer player) {
            // 设置玩家的伤害测试模式
            DamageTestManager.setDamageTestMode(player.getUUID(), enabled);
            
            // 发送反馈消息
            String message = enabled ? 
                "§a伤害测试模式已启用，你的攻击伤害将会在聊天栏显示" : 
                "§c伤害测试模式已禁用";
            source.sendSuccess(Component.literal(message), false);
            
            return 1; // 命令执行成功
        } else {
            // 不是玩家执行的命令
            source.sendFailure(Component.literal("§c只有玩家才能使用此命令"));
            return 0; // 命令执行失败
        }
    }
} 