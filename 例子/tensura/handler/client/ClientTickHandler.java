package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.effect.FearEffect;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.LivingEntityRenderer;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.TickEvent.Phase;
import net.minecraftforge.event.TickEvent.PlayerTickEvent;
import net.minecraftforge.event.entity.living.LivingEvent.LivingTickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
public class ClientTickHandler {
   private static final float PARTICLE_SIZE = 0.2F;

   @SubscribeEvent
   public static void clientTick(PlayerTickEvent e) {
      if (e.phase != Phase.END) {
         if (!e.side.isServer()) {
            Player player = e.player;
            if (!player.m_5833_()) {
               FearEffect.onClientTick(player, 10.0F);
            }
         }
      }
   }

   @SubscribeEvent
   public static void onLivingTick(LivingTickEvent e) {
      LivingEntity entity = e.getEntity();
      if (entity.f_19853_.f_46443_) {
         if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.BLACK_FLAME.get())) {
            Vec3 right = getArmPosition(entity, HumanoidArm.RIGHT).m_82520_(0.0D, 0.10000000149011612D, 0.0D);
            Vec3 left = getArmPosition(entity, HumanoidArm.LEFT).m_82520_(0.0D, 0.10000000149011612D, 0.0D);
            spawnParticle(entity.f_19853_, left, (ParticleOptions)TensuraParticles.BLACK_FIRE.get());
            spawnParticle(entity.f_19853_, right, (ParticleOptions)TensuraParticles.BLACK_FIRE.get());
         }

      }
   }

   public static void spawnParticle(Level level, Vec3 pos, ParticleOptions options) {
      int count = Math.round(2.0F);

      for(int i = 0; i < count; ++i) {
         double theta = RandomSource.m_216327_().m_188500_() * 3.141592653589793D * 2.0D;
         double phi = RandomSource.m_216327_().m_188500_() * 3.141592653589793D;
         Vec3 direction = new Vec3(Math.sin(phi) * Math.cos(theta), Math.sin(phi) * Math.sin(theta), Math.cos(phi));
         Vec3 start = pos.m_82549_(direction.m_82490_(0.20000000298023224D));
         level.m_6493_(options, true, start.f_82479_, start.f_82480_, start.f_82481_, 0.0D, 0.0D, 0.0D);
      }

   }

   public static Vec3 getArmPosition(LivingEntity entity, HumanoidArm arm) {
      Minecraft mc = Minecraft.m_91087_();
      EntityRenderDispatcher dispatcher = mc.m_91290_();
      EntityRenderer<?> renderer = dispatcher.m_114382_(entity);
      if (renderer instanceof LivingEntityRenderer) {
         LivingEntityRenderer<?, ?> living = (LivingEntityRenderer)renderer;
         EntityModel var7 = living.m_7200_();
         if (var7 instanceof HumanoidModel) {
            HumanoidModel<?> humanoid = (HumanoidModel)var7;
            Vec3 var10000;
            switch(arm) {
            case RIGHT:
               var10000 = transform3rdPerson(new Vec3(0.0D, -0.7D, 0.0D), new Vec3((double)humanoid.f_102811_.f_104203_, (double)humanoid.f_102811_.f_104204_, (double)humanoid.f_102811_.f_104205_), entity, HumanoidArm.RIGHT, mc.getPartialTick());
               break;
            case LEFT:
               var10000 = transform3rdPerson(new Vec3(0.0D, -0.7D, 0.0D), new Vec3((double)humanoid.f_102812_.f_104203_, (double)humanoid.f_102812_.f_104204_, (double)humanoid.f_102812_.f_104205_), entity, HumanoidArm.LEFT, mc.getPartialTick());
               break;
            default:
               throw new IncompatibleClassChangeError();
            }

            return var10000;
         }
      }

      return Vec3.f_82478_;
   }

   private static Vec3 transform3rdPerson(Vec3 pos, Vec3 angles, LivingEntity entity, HumanoidArm arm, float partialTicks) {
      Vec3 var10000;
      double var10001;
      float var10003;
      label31: {
         var10000 = rotateRoll(pos, (float)(-angles.f_82481_)).m_82496_((float)(-angles.f_82479_)).m_82524_((float)(-angles.f_82480_));
         var10001 = (double)(0.0586F * (arm == HumanoidArm.RIGHT ? -6.0F : 6.0F));
         if (entity.m_6144_()) {
            if (!(entity instanceof Player)) {
               break label31;
            }

            Player player = (Player)entity;
            if (!player.m_150110_().f_35935_) {
               break label31;
            }
         }

         var10003 = 0.0F;
         return var10000.m_82520_(var10001, (double)(1.3F - var10003), -0.05000000074505806D).m_82524_(-Mth.m_14179_(partialTicks, entity.f_20884_, entity.f_20883_) * 0.017453292F).m_82520_(Mth.m_14139_((double)partialTicks, entity.f_19790_, entity.m_20185_()), Mth.m_14139_((double)partialTicks, entity.f_19791_, entity.m_20186_()), Mth.m_14139_((double)partialTicks, entity.f_19792_, entity.m_20189_()));
      }

      var10003 = 0.3F;
      return var10000.m_82520_(var10001, (double)(1.3F - var10003), -0.05000000074505806D).m_82524_(-Mth.m_14179_(partialTicks, entity.f_20884_, entity.f_20883_) * 0.017453292F).m_82520_(Mth.m_14139_((double)partialTicks, entity.f_19790_, entity.m_20185_()), Mth.m_14139_((double)partialTicks, entity.f_19791_, entity.m_20186_()), Mth.m_14139_((double)partialTicks, entity.f_19792_, entity.m_20189_()));
   }

   private static Vec3 rotateRoll(Vec3 pos, float roll) {
      float f = Mth.m_14089_(roll);
      float f1 = Mth.m_14031_(roll);
      double d0 = pos.f_82479_ * (double)f - pos.f_82480_ * (double)f1;
      double d1 = pos.f_82480_ * (double)f + pos.f_82479_ * (double)f1;
      double d2 = pos.f_82481_;
      return new Vec3(d0, d1, d2);
   }
}
