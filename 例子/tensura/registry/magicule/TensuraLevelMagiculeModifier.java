package com.github.manasmods.tensura.registry.magicule;

import com.github.manasmods.tensura.data.pack.LevelMagiculeModifier;
import java.util.function.Supplier;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.IForgeRegistry;
import net.minecraftforge.registries.RegistryBuilder;

public class TensuraLevelMagiculeModifier {
   public static final ResourceKey<Registry<LevelMagiculeModifier>> REGISTRY_KEY = ResourceKey.m_135788_(new ResourceLocation("tensura", "magicule/modifier/level"));
   private static final DeferredRegister<LevelMagiculeModifier> REGISTRY;
   private static final Supplier<IForgeRegistry<LevelMagiculeModifier>> REGISTRY_SUPPLIER;

   public static void init(IEventBus modEventBus) {
      REGISTRY.register(modEventBus);
   }

   static {
      REGISTRY = DeferredRegister.create(REGISTRY_KEY, "tensura");
      REGISTRY_SUPPLIER = REGISTRY.makeRegistry(() -> {
         return (new RegistryBuilder()).dataPackRegistry(LevelMagiculeModifier.CODEC, LevelMagiculeModifier.CODEC);
      });
   }
}
