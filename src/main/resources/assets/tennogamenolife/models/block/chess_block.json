{"credit": "Made with Blockbench", "textures": {"0": "tennogamenolife:item/chess", "particle": "tennogamenolife:item/chess"}, "elements": [{"from": [6.9, 0, 6.7], "to": [9.5, 2, 9.3], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [0, 6, 3, 8], "texture": "#0"}, "east": {"uv": [6, 0, 9, 2], "texture": "#0"}, "south": {"uv": [6, 2, 9, 4], "texture": "#0"}, "west": {"uv": [3, 6, 6, 8], "texture": "#0"}, "up": {"uv": [3, 3, 0, 0], "texture": "#0"}, "down": {"uv": [3, 3, 0, 6], "texture": "#0"}}}, {"from": [6.5, 0, 6.3], "to": [9.9, 1, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [11, 0, 14, 1], "texture": "#0"}, "east": {"uv": [11, 1, 14, 2], "texture": "#0"}, "south": {"uv": [11, 2, 14, 3], "texture": "#0"}, "west": {"uv": [11, 3, 14, 4], "texture": "#0"}, "up": {"uv": [6, 3, 3, 0], "texture": "#0"}, "down": {"uv": [6, 3, 3, 6], "texture": "#0"}}}, {"from": [7.2, 2, 7], "to": [9.2, 4, 9], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [6, 4, 8, 6], "texture": "#0"}, "east": {"uv": [6, 6, 8, 8], "texture": "#0"}, "south": {"uv": [0, 8, 2, 10], "texture": "#0"}, "west": {"uv": [2, 8, 4, 10], "texture": "#0"}, "up": {"uv": [6, 10, 4, 8], "texture": "#0"}, "down": {"uv": [10, 4, 8, 6], "texture": "#0"}}}, {"from": [7.5, 4, 7.3], "to": [8.9, 7.2, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [0, 12, 1, 15], "texture": "#0"}, "east": {"uv": [1, 12, 2, 15], "texture": "#0"}, "south": {"uv": [2, 12, 3, 15], "texture": "#0"}, "west": {"uv": [3, 12, 4, 15], "texture": "#0"}, "up": {"uv": [5, 15, 4, 14], "texture": "#0"}, "down": {"uv": [15, 4, 14, 5], "texture": "#0"}}}, {"from": [7, 7.2, 6.8], "to": [9.4, 7.5, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [4, 12, 6, 13], "texture": "#0"}, "east": {"uv": [12, 4, 14, 5], "texture": "#0"}, "south": {"uv": [12, 5, 14, 6], "texture": "#0"}, "west": {"uv": [6, 12, 8, 13], "texture": "#0"}, "up": {"uv": [8, 10, 6, 8], "texture": "#0"}, "down": {"uv": [10, 6, 8, 8], "texture": "#0"}}}, {"from": [7.3, 7.5, 7.1], "to": [9.1, 7.8, 8.9], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [12, 6, 14, 7], "texture": "#0"}, "east": {"uv": [12, 7, 14, 8], "texture": "#0"}, "south": {"uv": [8, 12, 10, 13], "texture": "#0"}, "west": {"uv": [12, 8, 14, 9], "texture": "#0"}, "up": {"uv": [10, 10, 8, 8], "texture": "#0"}, "down": {"uv": [11, 0, 9, 2], "texture": "#0"}}}, {"from": [7.3, 8.2, 7.1], "to": [9.1, 9.9, 8.9], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [9, 2, 11, 4], "texture": "#0"}, "east": {"uv": [0, 10, 2, 12], "texture": "#0"}, "south": {"uv": [2, 10, 4, 12], "texture": "#0"}, "west": {"uv": [4, 10, 6, 12], "texture": "#0"}, "up": {"uv": [12, 6, 10, 4], "texture": "#0"}, "down": {"uv": [8, 10, 6, 12], "texture": "#0"}}}, {"from": [7, 9.9, 6.8], "to": [9.4, 10.4, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [12, 9, 14, 10], "texture": "#0"}, "east": {"uv": [10, 12, 12, 13], "texture": "#0"}, "south": {"uv": [12, 10, 14, 11], "texture": "#0"}, "west": {"uv": [12, 11, 14, 12], "texture": "#0"}, "up": {"uv": [12, 8, 10, 6], "texture": "#0"}, "down": {"uv": [10, 10, 8, 12], "texture": "#0"}}}, {"from": [7.5, 10.4, 7.2], "to": [8.9, 10.6, 8.6], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [5, 14, 6, 15], "texture": "#0"}, "east": {"uv": [14, 5, 15, 6], "texture": "#0"}, "south": {"uv": [6, 14, 7, 15], "texture": "#0"}, "west": {"uv": [14, 6, 15, 7], "texture": "#0"}, "up": {"uv": [8, 15, 7, 14], "texture": "#0"}, "down": {"uv": [15, 7, 14, 8], "texture": "#0"}}}, {"from": [7.4, 7.8, 7.2], "to": [9, 8.2, 8.8], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [12, 12, 14, 13], "texture": "#0"}, "east": {"uv": [4, 13, 6, 14], "texture": "#0"}, "south": {"uv": [6, 13, 8, 14], "texture": "#0"}, "west": {"uv": [8, 13, 10, 14], "texture": "#0"}, "up": {"uv": [12, 10, 10, 8], "texture": "#0"}, "down": {"uv": [12, 10, 10, 12], "texture": "#0"}}}, {"from": [8, 10.4, 7.7], "to": [8.4, 12.1, 8.1], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [10, 13, 11, 15], "texture": "#0"}, "east": {"uv": [11, 13, 12, 15], "texture": "#0"}, "south": {"uv": [12, 13, 13, 15], "texture": "#0"}, "west": {"uv": [13, 13, 14, 15], "texture": "#0"}, "up": {"uv": [9, 15, 8, 14], "texture": "#0"}, "down": {"uv": [15, 8, 14, 9], "texture": "#0"}}}, {"from": [7.3, 11.1, 7.7], "to": [9, 11.5, 8.1], "rotation": {"angle": 0, "axis": "y", "origin": [8.1, 0, 7.9]}, "faces": {"north": {"uv": [14, 0, 16, 1], "texture": "#0"}, "east": {"uv": [9, 14, 10, 15], "texture": "#0"}, "south": {"uv": [14, 1, 16, 2], "texture": "#0"}, "west": {"uv": [14, 9, 15, 10], "texture": "#0"}, "up": {"uv": [16, 3, 14, 2], "texture": "#0"}, "down": {"uv": [16, 3, 14, 4], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"translation": [0, 3, -0.5]}, "firstperson_righthand": {"rotation": [-22.75, 0, 0], "translation": [1, 3.5, 0]}, "firstperson_lefthand": {"rotation": [-22.75, 0, 0], "translation": [1, 3.5, 0]}, "ground": {"rotation": [-51.35, -28.06, 28.6], "translation": [0, 1, 0]}}, "groups": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}]}