package com.github.manasmods.tensura.entity.magic.breath;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.Pose;
import net.minecraftforge.entity.PartEntity;

public class BreathPart extends PartEntity<BreathEntity> {
   public final BreathEntity breath;
   public final String id;
   private final EntityDimensions size;

   public BreathPart(BreathEntity breath, String name, float width, float height) {
      super(breath);
      this.breath = breath;
      this.id = name;
      this.size = EntityDimensions.m_20395_(width, height);
      this.m_6210_();
   }

   protected void m_8097_() {
   }

   protected void m_7378_(CompoundTag compoundTag) {
   }

   protected void m_7380_(CompoundTag compoundTag) {
   }

   public boolean m_7306_(Entity entity) {
      return this == entity || this.breath == entity;
   }

   public EntityDimensions m_6972_(Pose pose) {
      return this.size;
   }

   public boolean m_142391_() {
      return false;
   }
}
