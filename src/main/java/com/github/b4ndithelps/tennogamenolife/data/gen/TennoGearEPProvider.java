package com.github.b4ndithelps.tennogamenolife.data.gen;

import com.github.manasmods.manascore.api.data.gen.CustomDataProvider;
import com.github.manasmods.tensura.data.pack.GearEPCount;
import com.google.gson.JsonElement;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.data.event.GatherDataEvent;
import org.jetbrains.annotations.NotNull;

import java.util.function.BiConsumer;
import java.util.function.Supplier;

public class TennoGearEPProvider extends CustomDataProvider {
    public TennoGearEPProvider(GatherDataEvent event) {
        super("gear/ep", event.getGenerator());
    }

    @NotNull
    @Override
    public String getName() {
        return "Tenno Gear EP";
    }

    @Override
    protected void run(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
        GearEPCount.of(new ResourceLocation("tennogamenolife:sickle"), 5200, 1000000, 0.03D).build<PERSON>son(consumer);
    }
} 