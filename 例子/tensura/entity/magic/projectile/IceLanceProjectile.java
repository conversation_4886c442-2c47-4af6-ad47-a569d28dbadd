package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;

public class IceLanceProjectile extends WaterBallProjectile {
   public IceLanceProjectile(EntityType<? extends IceLanceProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public IceLanceProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.ICE_LANCE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/icicle.png")};
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11983_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SNOWFLAKE.get(), x, y, z, 15, 0.1D, 0.1D, 0.1D, 0.1D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()), x, y, z, 15, 0.1D, 0.1D, 0.1D, 0.1D, true);
   }

   public void flyingParticles() {
      for(int i = 0; i < 1; ++i) {
         double speed = 0.05D;
         double dx = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         double dy = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         double dz = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         this.f_19853_.m_7106_((ParticleOptions)(this.f_19853_.f_46441_.m_188500_() < 0.3D ? new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()) : (ParticleOptions)TensuraParticles.SNOWFLAKE.get()), this.m_20185_() + dx, this.m_20186_() + dy, this.m_20189_() + dz, dx, dy, dz);
      }

   }
}
