package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class SolarGrenadeProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/solar_grenade/solar_grenade_6.png")};

   public SolarGrenadeProjectile(EntityType<? extends SolarGrenadeProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(0.4F);
   }

   public SolarGrenadeProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.SOLAR_GRENADE.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(0.4F);
   }

   public String getMagic() {
      return "tensura.light_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   public void explosion(double x, double y, double z) {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 2.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_((double)this.getEffectRange()), (entityData) -> {
         return this.m_37282_() == null || !entityData.m_7307_(this.m_37282_()) && !entityData.m_7306_(this.m_37282_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var8 = livingEntityList.iterator();

         while(var8.hasNext()) {
            LivingEntity target = (LivingEntity)var8.next();
            DamageSource damagesource = TensuraDamageSources.indirectElementalAttack("tensura.light_attack", this, this.m_37282_(), true);
            if (target.m_6469_(DamageSourceHelper.addSkillAndCost(damagesource, this.getMpCost(), this.getSkill()), 30.0F)) {
               if (this.getMobEffect() != null) {
                  target.m_7292_(this.getMobEffect());
               }

               target.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 300, 1, false, false, false));
            }
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123747_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
   }
}
