package com.github.manasmods.tensura.capability.smithing;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.util.INBTSerializable;
import net.minecraftforge.registries.ForgeRegistries;

public interface ISmithingCapability extends INBTSerializable<CompoundTag> {
   boolean hasSchematic(ResourceLocation var1);

   default boolean hasSchematic(Item schematic) {
      return this.hasSchematic((ResourceLocation)Objects.requireNonNull(ForgeRegistries.ITEMS.getKey(schematic)));
   }

   default boolean hasSchematic(ItemStack schematic) {
      return this.hasSchematic(schematic.m_41720_());
   }

   void unlockSchematic(ResourceLocation var1);

   default void unlockSchematic(Item schematic) {
      this.unlockSchematic((ResourceLocation)Objects.requireNonNull(ForgeRegistries.ITEMS.getKey(schematic)));
   }

   default void unlockSchematic(ItemStack schematic) {
      this.unlockSchematic(schematic.m_41720_());
   }

   default boolean hasSchematics(List<ResourceLocation> requiredSchematics) {
      Iterator var2 = requiredSchematics.iterator();

      ResourceLocation schematic;
      do {
         if (!var2.hasNext()) {
            return true;
         }

         schematic = (ResourceLocation)var2.next();
      } while(this.hasSchematic(schematic));

      return false;
   }

   void clearSchematics();
}
