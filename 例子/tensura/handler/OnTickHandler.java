package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.world.chunk.LevelChunkTickEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.magicule.MagiculeAPI;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.entity.magic.misc.WarpPortalEntity;
import com.github.manasmods.tensura.event.EnergyRegenerateTickEvent;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestDimensionUpdatePacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import java.util.List;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent.Phase;
import net.minecraftforge.event.TickEvent.PlayerTickEvent;
import net.minecraftforge.event.entity.living.LivingEvent.LivingTickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class OnTickHandler {
   public static final int SLEEP_TICK = 20;
   public static final int ENERGY_REGEN = 10;
   public static final int SPIRITUAL_REGEN = 20;

   @SubscribeEvent
   static void onPlayerTick(PlayerTickEvent event) {
      if (event.phase.equals(Phase.END)) {
         Player player = event.player;
         if (!player.m_9236_().m_5776_()) {
            if (player.f_19797_ % 10 == 0) {
               if (player.f_19797_ % 20 == 0) {
                  respawnColossus(player, player.m_9236_());
                  TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                     if (cap.getSpiritCooldown() != 0) {
                        cap.setSpiritCooldown(Math.max(0, cap.getSpiritCooldown() - 1));
                        TensuraSkillCapability.sync(player);
                     }
                  });
               }

               TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
                  float height = RaceHelper.getSkillSizeMultiplier(player);
                  if (height != cap.getHeightUpdate()) {
                     cap.setHeightUpdate(height);
                     player.m_6210_();
                     TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                        return player;
                     }), new RequestDimensionUpdatePacket(player.m_19879_()));
                     TensuraEffectsCapability.sync(player);
                  }
               });
            }
         }
      }
   }

   @SubscribeEvent
   static void onTick(LivingTickEvent event) {
      LivingEntity entity = event.getEntity();
      Level level = entity.m_9236_();
      if (!level.m_5776_()) {
         if (level.m_7654_() != null) {
            fallOffLabyrinth(entity, level);
            if (entity instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)entity;
               InsanityEffect.onServerTick(player);
            }

            if (level.m_7654_().m_129921_() % 20 == 0) {
               spiritualRegeneration(entity);
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  Race race = TensuraPlayerCapability.getRace(player);
                  if (race != null) {
                     race.raceTick(player);
                  }
               }
            }

            if (level.m_7654_().m_129921_() % 10 == 0) {
               if (!MinecraftForge.EVENT_BUS.post(new EnergyRegenerateTickEvent(entity))) {
                  energyRegen(entity);
               }

               TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                  if (cap.getWarpPortalTime() > 0) {
                     List<WarpPortalEntity> list = level.m_6443_(WarpPortalEntity.class, entity.m_20191_().m_82400_(0.20000000298023224D), (portal) -> {
                        return portal.f_19797_ > 20 || portal.getDestination() != null;
                     });
                     if (list.isEmpty()) {
                        cap.setWarpPortalTime(-1);
                        TensuraEffectsCapability.sync(entity);
                     }
                  }
               });
            }
         }
      }
   }

   private static void spiritualRegeneration(LivingEntity entity) {
      double multiplier;
      if (entity instanceof Player) {
         Player player = (Player)entity;
         Race race = TensuraPlayerCapability.getRace(player);
         multiplier = race != null ? race.getSpiritualHealthMultiplier() : 1.0D;
      } else if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.HERO_BOSS)) {
         multiplier = 10.0D;
      } else if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.SPIRITUAL)) {
         multiplier = 5.0D;
      } else {
         multiplier = 1.0D;
      }

      TensuraEPCapability.healSpiritualHealth(entity, 2.0D * multiplier);
      TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
         if (!(cap.getSeveranceAmount() <= 0.0D)) {
            if ((Integer)TensuraConfig.INSTANCE.skillsConfig.severanceRemoveSec.get() != -1) {
               cap.setSeveranceRemoveSec(cap.getSeveranceRemoveSec() - 1);
               if (cap.getSeveranceRemoveSec() <= 0) {
                  cap.setSeveranceAmount(0.0D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123760_, 1.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123760_, 2.0D);
               }

            }
         }
      });
   }

   private static void fallOffLabyrinth(LivingEntity entity, Level level) {
      if (level instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)level;
         if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (player.m_7500_() || player.m_5833_()) {
                  return;
               }
            }

            if (!((double)entity.m_20097_().m_123342_() > LabyrinthSaveData.get(serverLevel.m_7654_().m_129783_()).getStartFallOffY())) {
               entity.m_183634_();
               entity.m_19877_();
               entity.m_20219_(LabyrinthSaveData.get(serverLevel.m_7654_().m_129783_()).getFallOffPos());
               entity.m_5496_(SoundEvents.f_11852_, 1.0F, 1.0F);
            }
         }
      }
   }

   private static void respawnColossus(Player player, Level level) {
      if (level instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)level;
         if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
            if (serverLevel.m_46469_().m_46207_(TensuraGameRules.COLOSSUS_RESPAWN)) {
               LabyrinthSaveData saveData = LabyrinthSaveData.get(serverLevel.m_7654_().m_129783_());
               if (!saveData.isHavingColossus()) {
                  if (!TensuraEffectsCapability.isColossusWon(player)) {
                     Vec3 colossusPos = saveData.getColossusPos();
                     if (!(player.m_20238_(colossusPos) > 1600.0D)) {
                        ElementalColossusEntity colossus = new ElementalColossusEntity(serverLevel, colossusPos, MobSpawnType.TRIGGERED);
                        level.m_7967_(colossus);
                        saveData.setHavingColossus(true);
                        TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123812_);
                        TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123767_);
                        TensuraParticleHelper.addServerParticlesAroundSelf(colossus, ParticleTypes.f_123767_, 2.0D);
                        colossus.m_9236_().m_6263_((Player)null, colossus.m_20185_(), colossus.m_20186_(), colossus.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 2.0F, 1.0F);
                        colossus.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     }
                  }
               }
            }
         }
      }
   }

   @SubscribeEvent
   static void onChunkTick(LevelChunkTickEvent e) {
      LevelChunk chunk = e.getChunk();
      if (!chunk.m_62953_().m_5776_()) {
         if (e.phase == Phase.END) {
            MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(chunk);
            ((MagiculeChunkCapabilityImpl)cap).tick();
         }
      }
   }

   public static void energyRegen(LivingEntity entity) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            if (cap.getRace() != null) {
               if (!player.m_150110_().f_35934_ && (cap.getAura() < 0.0D || cap.getMagicule() < 0.0D)) {
                  player.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, player.m_21233_());
               } else {
                  boolean shouldSync = false;
                  if (cap.getSleepMode() > 0) {
                     cap.setSleepMode(cap.getSleepMode() - 1);
                     player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SLEEP_MODE.get(), 20, 0, false, false, false));
                     shouldSync = true;
                  } else if (player.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
                     player.m_21195_((MobEffect)TensuraMobEffects.SLEEP_MODE.get());
                  }

                  if (cap.getMagicule() <= 0.0D) {
                     cap.setSleepMode(20);
                     if (cap.getMagicule() < 0.0D) {
                        player.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, player.m_21233_() * 10.0F);
                        return;
                     }
                  }

                  double maxAura = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
                  double maxMagicule = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                  int difference = (int)(MagiculeAPI.getMagicule(player) / ((maxMagicule + maxAura) * 4.0D));
                  if (difference > getMagiculePoisonResistance(player) && !player.m_5833_() && !player.m_7500_() && (player.m_9236_().m_46472_() != TensuraDimensions.HELL || cap.getRace().getRespawnDimension() != TensuraDimensions.HELL)) {
                     player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGICULE_POISON.get(), 100, difference - 1, false, false));
                  }

                  double extraAP;
                  if (cap.isSpiritualForm() && !player.m_7500_() && !player.m_5833_() && player.m_9236_().m_46472_() != TensuraDimensions.HELL && player.m_9236_().m_46472_() != TensuraDimensions.LABYRINTH) {
                     cap.setMagicule(Math.max(cap.getMagicule() - 115.0D, -1.0D));
                     shouldSync = true;
                  } else {
                     extraAP = MagiculeAPI.getMagicule(player) * (Double)TensuraConfig.INSTANCE.magiculeConfig.manaRegen.get();
                     double extraMP;
                     if (cap.getMagicule() > maxMagicule) {
                        if (cap.getMagicule() > maxMagicule * 1.25D) {
                           extraMP = cap.getMagicule() - maxMagicule * 1.25D;
                           int poisonLevel = (int)(extraMP / (maxMagicule * 0.25D));
                           player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGICULE_POISON.get(), 100, poisonLevel, false, false));
                        }

                        cap.setMagicule(Math.max(cap.getMagicule() - (Double)TensuraConfig.INSTANCE.magiculeConfig.auraRegen.get(), maxMagicule));
                        shouldSync = true;
                     } else if (cap.getMagicule() < maxMagicule) {
                        extraMP = cap.getMagicule() + SkillHelper.mpRegen(player, maxMagicule, extraAP);
                        cap.setMagicule(Math.min(extraMP, maxMagicule));
                        shouldSync = true;
                     }
                  }

                  if (cap.getAura() <= 0.0D) {
                     auraPenalties(player);
                  }

                  if (cap.getAura() < maxAura) {
                     extraAP = cap.getAura() + SkillHelper.apRegen(player, maxAura, (Double)TensuraConfig.INSTANCE.magiculeConfig.auraRegen.get());
                     cap.setAura(Math.min(extraAP, maxAura));
                     shouldSync = true;
                  } else if (cap.getAura() > maxAura) {
                     if (cap.getAura() > maxAura * 1.25D) {
                        extraAP = cap.getAura() - maxAura * 1.25D;
                        int insaneLevel = 1 + 2 * (int)(extraAP / (maxAura * 0.25D));
                        player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSANITY.get(), 100, insaneLevel, false, false));
                     }

                     cap.setAura(Math.max(cap.getAura() - (Double)TensuraConfig.INSTANCE.magiculeConfig.auraRegen.get(), maxAura));
                     shouldSync = true;
                  }

                  if (shouldSync) {
                     TensuraPlayerCapability.sync(player);
                  }

               }
            }
         });
      } else {
         TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
            if (!(cap.getEP() < 0.0D) && !(cap.getCurrentEP() < 0.0D)) {
               if (cap.getCurrentEP() < cap.getEP()) {
                  double mana = SkillHelper.mpRegen(entity, cap.getEP(), MagiculeAPI.getMagicule(entity) * 0.01D);
                  double newEP = cap.getCurrentEP() + SkillHelper.apRegen(entity, cap.getEP(), 2.0D) + mana;
                  cap.setCurrentEP(entity, Math.min(newEP, cap.getEP()));
               }
            } else {
               entity.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, entity.m_21233_() * 10.0F);
            }

         });
      }
   }

   private static int getMagiculePoisonResistance(Player player) {
      int level = 0;
      if (TensuraEPCapability.isMajin(player)) {
         ++level;
      }

      if (SkillUtils.isSkillToggled(player, (ManasSkill)ResistanceSkills.MAGIC_RESISTANCE.get())) {
         level += 2;
      }

      return level;
   }

   private static void auraPenalties(Player player) {
      player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 100, 2, false, false));
      player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 100, 2, false, false));
      player.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 100, 2, false, false));
      player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 100, 2, false, false));
   }
}
