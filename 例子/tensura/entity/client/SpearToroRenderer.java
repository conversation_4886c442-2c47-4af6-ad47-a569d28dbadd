package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SpearToroEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class SpearToroRenderer extends GeoEntityRenderer<SpearToroEntity> {
   public SpearToroRenderer(Context renderManager) {
      super(renderManager, new SpearToroModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(SpearToroEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/spear_toro/spear_toro.png");
   }

   public RenderType getRenderType(SpearToroEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (entity.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
