package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.BufferBuilder;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.Tesselator;
import com.mojang.blaze3d.vertex.VertexFormat.Mode;
import java.util.function.Predicate;
import net.minecraft.client.Minecraft;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.LevelRenderer;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.core.BlockPos;
import net.minecraft.core.SectionPos;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.level.ChunkPos;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.client.event.RenderLevelStageEvent.Stage;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
public class ClientBlockOutlineHandler {
   private static PoseStack poseStack;
   private static BufferBuilder bufferBuilder;

   @SubscribeEvent
   public static void handleOreSightEnchantment(RenderLevelStageEvent event) {
      if (event.getStage() == Stage.AFTER_PARTICLES) {
         LocalPlayer player = Minecraft.m_91087_().f_91074_;
         if (player != null) {
            MobEffectInstance dangerDetection = player.m_21124_((MobEffect)TensuraMobEffects.DANGER_DETECTION.get());
            if (dangerDetection != null && dangerDetection.m_19564_() == 0) {
               highLightBlocks(event, player, 15, (state) -> {
                  return state.m_204336_(TensuraTags.Blocks.TRAP_BLOCKS);
               }, new Vec3(0.89D, 0.1D, 0.1D));
               highLightBlocks(event, player, 15, (state) -> {
                  return state.m_204336_(TensuraTags.Blocks.TREASURE_BLOCKS);
               }, new Vec3(1.0D, 1.0D, 0.0D));
            }

            poseStack = null;
            bufferBuilder = null;
         }
      }
   }

   private static void highLightBlocks(RenderLevelStageEvent event, LocalPlayer player, int radius, Predicate<BlockState> predicate, Vec3 color) {
      poseStack = event.getPoseStack();
      poseStack.m_85836_();
      RenderSystem.m_157427_(GameRenderer::m_172811_);
      RenderSystem.m_69465_();
      Tesselator tesselator = Tesselator.m_85913_();
      bufferBuilder = tesselator.m_85915_();
      bufferBuilder.m_166779_(Mode.DEBUG_LINES, DefaultVertexFormat.f_85815_);
      drawLines(player, radius, predicate, color);
      tesselator.m_85914_();
      poseStack.m_85849_();
      RenderSystem.m_69482_();
      RenderType.m_110463_().m_110188_();
   }

   private static void drawLines(LocalPlayer localPlayer, int radius, Predicate<BlockState> predicate, Vec3 color) {
      Vec3 camera = Minecraft.m_91087_().f_91063_.m_109153_().m_90583_();
      BlockPos startPosition = localPlayer.m_20183_();
      ChunkPos currentChunkPosition = new ChunkPos(startPosition);
      LevelChunk currentChunk = null;
      int minChunkX = startPosition.m_123341_() - radius;
      int maxChunkX = startPosition.m_123341_() + radius;
      int minChunkY = Math.max(localPlayer.m_9236_().m_141937_(), startPosition.m_123342_() - radius);
      int maxChunkY = Math.min(localPlayer.m_9236_().m_151558_(), startPosition.m_123342_() + radius);
      int minChunkZ = startPosition.m_123343_() - radius;
      int maxChunkZ = startPosition.m_123343_() + radius;
      boolean foundSection = false;
      MutableBlockPos mutablePosition = BlockPos.f_121853_.m_122032_();

      for(int x = minChunkX; x <= maxChunkX; ++x) {
         for(int z = minChunkZ; z <= maxChunkZ; ++z) {
            int sectionX = SectionPos.m_123171_(x);
            int sectionZ = SectionPos.m_123171_(z);
            if (currentChunk == null || currentChunkPosition.f_45578_ != sectionX || currentChunkPosition.f_45579_ != sectionZ) {
               currentChunkPosition = new ChunkPos(sectionX, sectionZ);
               currentChunk = localPlayer.m_9236_().m_6325_(sectionX, sectionZ);
            }

            foundSection = false;

            for(int y = maxChunkY; y >= minChunkY; --y) {
               foundSection = true;
               mutablePosition.m_122178_(x, y, z);
               BlockState state = currentChunk.m_8055_(mutablePosition);
               if (predicate.test(state)) {
                  float xMin = (float)((double)x - camera.m_7096_());
                  float yMin = (float)((double)y - camera.m_7098_());
                  float zMin = (float)((double)z - camera.m_7094_());
                  float xMax = (float)((double)(1 + x) - camera.m_7096_());
                  float yMax = (float)((double)(1 + y) - camera.m_7098_());
                  float zMax = (float)((double)(1 + z) - camera.m_7094_());
                  LevelRenderer.m_109608_(poseStack, bufferBuilder, (double)xMin, (double)yMin, (double)zMin, (double)xMax, (double)yMax, (double)zMax, (float)color.m_7096_(), (float)color.m_7098_(), (float)color.m_7094_(), 1.0F);
               }
            }

            if (!foundSection && z != maxChunkZ) {
               z = Math.min(maxChunkZ, SectionPos.m_123223_(SectionPos.m_123171_(z) + 1));
            }
         }

         if (!foundSection && x != maxChunkX) {
            x = Math.min(maxChunkX, SectionPos.m_123223_(SectionPos.m_123171_(x) + 1));
         }
      }

   }
}
