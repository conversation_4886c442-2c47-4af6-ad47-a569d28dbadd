package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import java.util.List;
import net.minecraft.core.Direction;
import net.minecraft.core.Holder;
import net.minecraft.core.Registry;
import net.minecraft.data.worldgen.placement.PlacementUtils;
import net.minecraft.data.worldgen.placement.VegetationPlacements;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.levelgen.VerticalAnchor;
import net.minecraft.world.level.levelgen.blockpredicates.BlockPredicate;
import net.minecraft.world.level.levelgen.placement.BiomeFilter;
import net.minecraft.world.level.levelgen.placement.CountPlacement;
import net.minecraft.world.level.levelgen.placement.EnvironmentScanPlacement;
import net.minecraft.world.level.levelgen.placement.HeightRangePlacement;
import net.minecraft.world.level.levelgen.placement.InSquarePlacement;
import net.minecraft.world.level.levelgen.placement.PlacedFeature;
import net.minecraft.world.level.levelgen.placement.PlacementModifier;
import net.minecraft.world.level.levelgen.placement.RarityFilter;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraPlacedFeatures {
   private static final DeferredRegister<PlacedFeature> registry;
   public static RegistryObject<PlacedFeature> SAKURA_TREE_CHECKED;
   public static RegistryObject<PlacedFeature> SAKURA_TREE_LARGE_CHECKED;
   public static RegistryObject<PlacedFeature> SAKURA_FOREST_CHECKED;
   public static RegistryObject<PlacedFeature> PALM_TREE_CHECKED;
   public static final RegistryObject<PlacedFeature> HIPOKUTE_GRASS_PLACED;
   public static final RegistryObject<PlacedFeature> ORE_SILVER;
   public static final RegistryObject<PlacedFeature> ORE_SILVER_LARGE;
   public static final RegistryObject<PlacedFeature> ORE_SILVER_BURIED;
   public static final RegistryObject<PlacedFeature> ORE_MAGIC;
   public static final RegistryObject<PlacedFeature> ORE_MAGIC_BURIED;
   public static final RegistryObject<PlacedFeature> ROCKY_SPIKE;
   public static final RegistryObject<PlacedFeature> HELL_BLOCK_BLOB;
   public static final RegistryObject<PlacedFeature> FLOATING_DEBRIS;

   private static List<PlacementModifier> commonOrePlacement(int amount, HeightRangePlacement placement) {
      return orePlacement(CountPlacement.m_191628_(amount), placement);
   }

   private static List<PlacementModifier> rareOrePlacement(int chance, HeightRangePlacement placement) {
      return orePlacement(RarityFilter.m_191900_(chance), placement);
   }

   private static List<PlacementModifier> orePlacement(PlacementModifier placementModifier, HeightRangePlacement heightRangePlacement) {
      return List.of(placementModifier, InSquarePlacement.m_191715_(), heightRangePlacement, BiomeFilter.m_191561_());
   }

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_194567_, "tensura");
      SAKURA_TREE_CHECKED = registry.register("sakura_tree_checked", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.SAKURA_TREE.getHolder().get(), List.of(PlacementUtils.m_206493_((Block)TensuraBlocks.SAKURA_SAPLING.get())));
      });
      SAKURA_TREE_LARGE_CHECKED = registry.register("sakura_tree_large_checked", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.SAKURA_TREE_LARGE.getHolder().get(), List.of(PlacementUtils.m_206493_((Block)TensuraBlocks.SAKURA_SAPLING.get())));
      });
      SAKURA_FOREST_CHECKED = registry.register("sakura_forest_checked", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.SAKURA_FOREST.getHolder().get(), VegetationPlacements.m_195479_(PlacementUtils.m_195364_(10, 0.1F, 1)));
      });
      PALM_TREE_CHECKED = registry.register("palm_tree_checked", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.PALM_TREE.getHolder().get(), List.of(PlacementUtils.m_206493_((Block)TensuraBlocks.PALM_SAPLING.get()), RarityFilter.m_191900_(1), CountPlacement.m_191628_(3), InSquarePlacement.m_191715_(), PlacementUtils.f_195356_));
      });
      HIPOKUTE_GRASS_PLACED = registry.register("hipokute_grass_placed", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.HIPOKUTE_GRASS.getHolder().get(), List.of(RarityFilter.m_191900_(20), CountPlacement.m_191628_(20), InSquarePlacement.m_191715_(), PlacementUtils.f_195356_, EnvironmentScanPlacement.m_191657_(Direction.DOWN, BlockPredicate.m_190432_(), BlockPredicate.f_190393_, 10), BiomeFilter.m_191561_()));
      });
      ORE_SILVER = registry.register("ore_silver", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ORE_SILVER_SMALL.getHolder().get(), commonOrePlacement(3, HeightRangePlacement.m_191692_(VerticalAnchor.m_158922_(-64), VerticalAnchor.m_158922_(32))));
      });
      ORE_SILVER_LARGE = registry.register("ore_silver_large", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ORE_SILVER_LARGE.getHolder().get(), rareOrePlacement(4, HeightRangePlacement.m_191680_(VerticalAnchor.m_158930_(-24), VerticalAnchor.m_158930_(56))));
      });
      ORE_SILVER_BURIED = registry.register("ore_silver_buried", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ORE_SILVER_BURIED.getHolder().get(), commonOrePlacement(3, HeightRangePlacement.m_191692_(VerticalAnchor.m_158922_(-64), VerticalAnchor.m_158922_(32))));
      });
      ORE_MAGIC = registry.register("ore_magic", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ORE_MAGIC.getHolder().get(), rareOrePlacement(2, HeightRangePlacement.m_191692_(VerticalAnchor.m_158922_(-64), VerticalAnchor.m_158922_(10))));
      });
      ORE_MAGIC_BURIED = registry.register("ore_magic_buried", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ORE_MAGIC_BURIED.getHolder().get(), commonOrePlacement(3, HeightRangePlacement.m_191692_(VerticalAnchor.m_158922_(-64), VerticalAnchor.m_158922_(10))));
      });
      ROCKY_SPIKE = registry.register("rocky_spike", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.ROCKY_SPIKE.getHolder().get(), List.of(new PlacementModifier[]{CountPlacement.m_191628_(3), InSquarePlacement.m_191715_(), PlacementUtils.f_195352_, BiomeFilter.m_191561_()}));
      });
      HELL_BLOCK_BLOB = registry.register("hell_blob_block", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.HELL_BLOCK_BLOB.getHolder().get(), List.of(new PlacementModifier[]{RarityFilter.m_191900_(5), InSquarePlacement.m_191715_(), PlacementUtils.f_195352_, BiomeFilter.m_191561_()}));
      });
      FLOATING_DEBRIS = registry.register("floating_debris", () -> {
         return new PlacedFeature((Holder)TensuraConfiguredFeatures.FLOATING_DEBRIS.getHolder().get(), List.of(new PlacementModifier[]{RarityFilter.m_191900_(4), InSquarePlacement.m_191715_(), PlacementUtils.f_195352_, BiomeFilter.m_191561_()}));
      });
   }
}
