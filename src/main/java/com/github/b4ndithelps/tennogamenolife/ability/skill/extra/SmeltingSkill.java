package com.github.b4ndithelps.tennogamenolife.ability.skill.extra;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.ability.skill.unique.ChainMiningSkill;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.SkillHelper;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.PickaxeItem;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.item.crafting.SmeltingRecipe;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.SimpleContainer;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.Optional;
import java.util.UUID;

/**
 * 冶炼者技能 - 附加烈焰的镐子，将万物烧制！
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID)
public class SmeltingSkill extends Skill {

    public SmeltingSkill() {
        super(SkillType.EXTRA);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/extra/smelting.png");
    }

    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        // 检查玩家是否拥有并精通了连锁挖矿技能
        Optional<ManasSkillInstance> chainMiningSkill = SkillAPI.getSkillsFrom(entity)
            .getSkill(TenreincarnationSkill.CHAIN_MINING.get());

        if (chainMiningSkill.isEmpty()) {
            return false; // 没有连锁挖矿技能
        }

        // 检查连锁挖矿技能是否达到100%精通
        return chainMiningSkill.get().isMastered(entity);
    }

    @Override
    public double learningCost() {
        return 3.0; // 学习成本
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        // 检查是否精通
        if (instance.isMastered(entity)) {
            return 5.0; // 精通后消耗5魔素
        } else {
            return 10.0; // 普通消耗10魔素
        }
    }

    @Override
    public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
        return true; // 可以切换开关
    }



    /**
     * 检查玩家是否开启了冶炼者技能
     */
    public static boolean isSmeltingEnabled(Player player) {
        // 首先检查玩家是否学会了冶炼者技能
        SmeltingSkill skill = TenreincarnationSkill.SMELTING.get();
        Optional<ManasSkillInstance> skillInstance = SkillAPI.getSkillsFrom(player).getSkill(skill);

        if (skillInstance.isEmpty()) {
            return false; // 没有学会技能
        }

        // 检查技能是否被激活（打勾）
        return skillInstance.get().isToggled();
    }

    /**
     * 获取方块的冶炼结果
     */
    public static ItemStack getSmeltingResult(Level level, BlockState blockState) {
        if (!(level instanceof ServerLevel serverLevel)) {
            return ItemStack.EMPTY;
        }

        // 获取方块对应的物品
        Block block = blockState.getBlock();
        ItemStack blockItem = new ItemStack(block.asItem());
        
        if (blockItem.isEmpty()) {
            return ItemStack.EMPTY;
        }

        // 查找冶炼配方
        Optional<SmeltingRecipe> recipe = serverLevel.getRecipeManager()
            .getRecipeFor(RecipeType.SMELTING,
                new SimpleContainer(blockItem),
                serverLevel);

        if (recipe.isPresent()) {
            return recipe.get().getResultItem();
        }

        return ItemStack.EMPTY;
    }

    /**
     * 增加冶炼技能精通度
     * 每10次冶炼增加1点精通度（相当于每次0.1）
     */
    private static void addSmeltingMastery(ManasSkillInstance instance, Player player, SmeltingSkill skill) {
        if (player.isCreative()) {
            return; // 创造模式不增加精通度
        }

        // 使用NBT标签记录冶炼次数
        var tag = instance.getOrCreateTag();
        int smeltCount = tag.getInt("smeltCount");
        smeltCount++;
        tag.putInt("smeltCount", smeltCount);

        // 每10次冶炼增加1点精通度
        if (smeltCount % 10 == 0) {
            skill.addMasteryPoint(instance, player, 1);
        }

        instance.markDirty();
    }

    /**
     * 处理单个方块的冶炼掉落
     * 这个方法会被连锁挖矿调用，也会被单独挖矿调用
     */
    public static void handleSmeltedDrop(Level level, BlockPos pos, BlockState blockState, Player player, ItemStack tool) {
        // 获取冶炼结果
        ItemStack smeltedResult = getSmeltingResult(level, blockState);

        if (!smeltedResult.isEmpty()) {
            // 获取技能实例
            SmeltingSkill skill = TenreincarnationSkill.SMELTING.get();
            Optional<ManasSkillInstance> skillInstance = SkillAPI.getSkillsFrom(player).getSkill(skill);

            if (skillInstance.isPresent()) {
                ManasSkillInstance instance = skillInstance.get();

                // 检查魔素是否足够并消耗
                double epCost = skill.magiculeCost(player, instance);
                if (SkillHelper.outOfMagicule(player, epCost)) {
                    // 魔素不足，使用正常掉落
                    Block.dropResources(blockState, level, pos, null, player, tool);
                    return;
                }

                // 掉落冶炼后的物品
                Block.popResource(level, pos, smeltedResult.copy());

                // 增加精通度（每10个方块增加1点精通度，相当于每个方块0.1）
                addSmeltingMastery(instance, player, skill);
            } else {
                // 没有技能实例，使用正常掉落
                Block.dropResources(blockState, level, pos, null, player, tool);
            }
        } else {
            // 没有冶炼配方，使用正常掉落
            Block.dropResources(blockState, level, pos, null, player, tool);
        }
    }


    /**
     * 监听单独挖矿的方块破坏事件（非连锁挖矿）
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        Player player = event.getPlayer();
        if (player == null || player.level.isClientSide) {
            return;
        }

        // 检查玩家是否开启了冶炼者技能
        if (!isSmeltingEnabled(player)) {
            return;
        }

        // 检查玩家手中的工具是否是镐子
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof PickaxeItem)) {
            return;
        }

        // 检查是否是连锁挖矿触发的（如果是，则由连锁挖矿处理）
        UUID playerId = player.getUUID();
        if (ChainMiningSkill.isPlayerMiningActive(playerId)) {
            return; // 连锁挖矿会自己处理冶炼
        }

        BlockPos pos = event.getPos();
        Level level = player.getLevel();
        BlockState blockState = event.getState();

        // 获取技能实例检查魔素
        SmeltingSkill skill = TenreincarnationSkill.SMELTING.get();
        Optional<ManasSkillInstance> skillInstance = SkillAPI.getSkillsFrom(player).getSkill(skill);

        if (skillInstance.isEmpty()) {
            return; // 没有技能实例
        }

        ManasSkillInstance instance = skillInstance.get();

        // 获取冶炼结果
        ItemStack smeltedResult = getSmeltingResult(level, blockState);

        if (!smeltedResult.isEmpty()) {
            // 检查魔素是否足够并消耗
            double epCost = skill.magiculeCost(player, instance);
            if (SkillHelper.outOfMagicule(player, epCost)) {
                return; // 魔素不足，让原始事件正常处理
            }

            // 取消原始掉落物
            event.setCanceled(true);

            // 移除方块
            level.removeBlock(pos, false);

            // 掉落冶炼后的物品
            Block.popResource(level, pos, smeltedResult.copy());

            // 增加精通度（每10个方块增加1点精通度，相当于每个方块0.1）
            addSmeltingMastery(instance, player, skill);
        }
    }

}
