package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.DirewolfEntity;
import com.github.manasmods.tensura.entity.variant.DirewolfVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class DirewolfModel extends AnimatedGeoModel<DirewolfEntity> {
   public ResourceLocation getModelResource(DirewolfEntity object) {
      return new ResourceLocation("tensura", "geo/direwolf.geo.json");
   }

   public ResourceLocation getTextureResource(DirewolfEntity instance) {
      return DirewolfRenderer.getLocation(instance);
   }

   public ResourceLocation getAnimationResource(DirewolfEntity tiger) {
      return new ResourceLocation("tensura", "animations/direwolf.animation.json");
   }

   public void setCustomAnimations(DirewolfEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      IBone chests = this.getAnimationProcessor().getBone("Chests");
      if (entity.isChested() == chests.isHidden()) {
         chests.setHidden(!entity.isChested());
      }

      IBone scarEye = this.getAnimationProcessor().getBone("ScarEye");
      if (entity.isAlpha() == scarEye.isHidden()) {
         scarEye.setHidden(!entity.isAlpha());
      }

      IBone upperHorn = this.getAnimationProcessor().getBone("UpperHorn");
      if (this.showUpperHorn(entity) == upperHorn.isHidden()) {
         upperHorn.setHidden(!this.showUpperHorn(entity));
      }

      IBone lowerHorn = this.getAnimationProcessor().getBone("LowerHorn");
      if (this.showLowerHorn(entity) == lowerHorn.isHidden()) {
         lowerHorn.setHidden(!this.showLowerHorn(entity));
      }

      if (!entity.m_5803_()) {
         if (entity.getMiscAnimation() != 1 && entity.getMiscAnimation() < 4) {
            EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
            IBone head = this.getAnimationProcessor().getBone("Head");
            if (head != null) {
               float pitch = entity.m_21825_() ? extraData.headPitch - 35.0F : extraData.headPitch;
               head.setRotationX(pitch * 0.017453292F);
               head.setRotationY(extraData.netHeadYaw * 0.017453292F);
            }

         }
      }
   }

   private boolean showUpperHorn(DirewolfEntity entity) {
      if (entity.getVariant() == DirewolfVariant.TEMPEST_STAR_WOLF) {
         return true;
      } else {
         return entity.getVariant() == DirewolfVariant.STAR_WOLF;
      }
   }

   private boolean showLowerHorn(DirewolfEntity entity) {
      return entity.getVariant() == DirewolfVariant.TEMPEST_STAR_WOLF;
   }
}
