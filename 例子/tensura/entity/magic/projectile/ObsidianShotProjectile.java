package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;

public class ObsidianShotProjectile extends StoneShotProjectile {
   public ObsidianShotProjectile(EntityType<? extends ObsidianShotProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public ObsidianShotProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.OBSIDIAN_SHOT.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/obsidian_shot.png")};
   }

   protected void hitEntity(Entity entity) {
      super.hitEntity(entity);
      if (entity instanceof LivingEntity) {
         LivingEntity pLivingEntity = (LivingEntity)entity;
         StoneShotProjectile.breakTargetArmor(pLivingEntity, 10);
      }
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50080_.m_49966_()), x, y, z, 10, 0.08D, 0.08D, 0.08D, 0.1D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.f_19853_.m_7106_(new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50080_.m_49966_()), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);
   }
}
