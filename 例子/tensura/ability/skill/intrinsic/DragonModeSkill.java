package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class DragonModeSkill extends Skill implements Transformation {
   public DragonModeSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_21023_((MobEffect)TensuraMobEffects.DRAGON_MODE.get());
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_((MobEffect)TensuraMobEffects.DRAGON_MODE.get());
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.failedToActivate(entity, (MobEffect)TensuraMobEffects.DRAGON_MODE.get())) {
         if (!entity.m_21023_((MobEffect)TensuraMobEffects.DRAGON_MODE.get())) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(1200);
            entity.m_21153_(entity.m_21223_() * 2.0F);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setMagicule(cap.getMagicule() * 2.0D);
                  cap.setAura(cap.getAura() * 2.0D);
                  TensuraPlayerCapability.sync(player);
               });
            }

            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11894_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.DRAGON_MODE.get(), this.isMastered(instance, entity) ? 7200 : 3600, 0, false, false, false));
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123759_, 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123813_, 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 2.0D);
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
         } else {
            entity.m_21195_((MobEffect)TensuraMobEffects.DRAGON_MODE.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
