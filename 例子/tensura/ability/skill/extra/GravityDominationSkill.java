package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class GravityDominationSkill extends Skill {
   public GravityDominationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (DamageSourceHelper.isGravityDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 4.0F);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Player player;
      if (entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get())) {
         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else if (entity instanceof Player) {
         player = (Player)entity;
         if (!player.m_7500_() && !player.m_5833_()) {
            if (!SkillHelper.outOfMagicule(player, instance)) {
               if (player.m_150110_().f_35936_) {
                  player.m_150110_().f_35936_ = false;
                  player.m_150110_().f_35935_ = false;
               } else {
                  this.addMasteryPoint(instance, entity);
                  player.m_150110_().f_35936_ = true;
                  player.m_150110_().f_35935_ = true;
               }

               player.m_6885_();
               player.m_6330_(SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      }
   }
}
