package com.github.b4ndithelps.tennogamenolife.ability.skill.unique;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import net.minecraft.client.Minecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraft.network.chat.Component;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.LevelRenderer;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 连锁砍伐独特技能
 * 当玩家开启技能后使用斧头砍伐木头时，会连锁砍伐相同类型的木头
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID)
public class ChainFellingSkill extends Skill {

    // 基础配置
    private final double magiculeCost = 100.0; // 每次连锁砍伐消耗的魔素

    // 存储每个玩家的技能激活状态
    private static final Map<UUID, Boolean> playerActiveStates = new HashMap<>();
    
    // 存储将被连锁砍伐的方块位置，用于渲染预览
    private static Set<BlockPos> highlightedBlocks = new HashSet<>();

    public ChainFellingSkill() {
        super(SkillType.UNIQUE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/unique/chain_felling.png");
    }

    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        return false; // 无EP要求
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return magiculeCost;
    }

    /**
     * 当技能按键被按下时触发
     */
    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (entity instanceof Player player) {
            UUID playerId = player.getUUID();

            // 切换该玩家的技能激活状态
            boolean currentState = playerActiveStates.getOrDefault(playerId, false);
            boolean newState = !currentState;
            playerActiveStates.put(playerId, newState);

            // 清除高亮方块
            highlightedBlocks.clear();

            // 播放音效
            Level level = entity.getLevel();
            level.playSound(
                null, entity.getX(), entity.getY(), entity.getZ(),
                newState ? SoundEvents.BEACON_ACTIVATE : SoundEvents.BEACON_DEACTIVATE,
                SoundSource.PLAYERS, 0.6F, 1.0F
            );

            // 通知玩家
            if (player instanceof ServerPlayer) {
                String messageKey = newState ? "skill.tennogamenolife.chain_felling.enabled" : "skill.tennogamenolife.chain_felling.disabled";
                player.displayClientMessage(Component.translatable(messageKey), true);
            }
        }
    }

    /**
     * 静态方法，处理方块破坏事件
     * 这样可以不通过实例直接调用
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        // 获取玩家
        Player player = event.getPlayer();
        if (player == null) return;

        // 检查该玩家的技能是否激活
        UUID playerId = player.getUUID();
        if (!playerActiveStates.getOrDefault(playerId, false)) return;
        
        // 检查玩家手中的工具是否是斧头
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof AxeItem)) return;
        
        // 获取当前破坏的方块信息
        BlockPos pos = event.getPos();
        Level level = player.getLevel();
        BlockState targetState = event.getState();
        Block targetBlock = targetState.getBlock();
        
        // 检查方块是否是木头
        if (!isWood(targetState, mainHandItem)) return;
        
        // 获取技能实例
        ChainFellingSkill skill = (ChainFellingSkill) TenreincarnationSkill.CHAIN_FELLING.get();
        
        // 尝试获取玩家的技能实例
        SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
            // 如果魔素不足，不执行连锁砍伐
            if (SkillHelper.outOfMagicule(player, instance)) {
                if (player instanceof ServerPlayer) {
                    player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_felling.no_magicule"), true);
                }
                return;
            }
            
            // 执行连锁砍伐
            // 1. 找到所有连接的相同木头方块
            int maxBlocks = instance.isMastered(player) ? 64 : 32; // 树木通常很多，增加连锁上限
            Set<BlockPos> chainedBlocks = findChainedBlocks(level, pos, targetBlock, maxBlocks);
            
            // 2. 移除起始位置（已经由原始事件处理）
            chainedBlocks.remove(pos);
            
            // 3. 如果找到了连锁方块，消耗魔素
            if (!chainedBlocks.isEmpty()) {
                // 消耗魔素（只有真正进行了连锁砍伐才消耗）
                // 由于消耗魔素的机制是通过冷却时间来实现的，所以设置一个短暂的冷却
                // 这样就会触发魔素消耗
                instance.setCoolDown(1);
                
                // 获取斧头的附魔效果
                int fortuneLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_FORTUNE, mainHandItem);
                int silkTouchLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.SILK_TOUCH, mainHandItem);
                
                // 4. 破坏找到的方块
                for (BlockPos blockPos : chainedBlocks) {
                    // 检查方块是否可以被当前工具破坏
                    BlockState state = level.getBlockState(blockPos);
                    
                    // 跳过不是木头的方块
                    if (!isWood(state, mainHandItem)) continue;
                    
                    // 使用斧头破坏方块，同时应用附魔效果
                    if (level instanceof ServerLevel serverLevel) {
                        // 使用适当的方法来模拟正确的掉落物
                        Block block = state.getBlock();
                        
                        // 应用时运或精准采集
                        if (silkTouchLevel > 0) {
                            // 精准采集 - 直接掉落方块本身
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        } else {
                            // 时运 - 增加掉落数量
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        }
                        
                        // 破坏方块但不生成掉落物(因为我们已经手动处理了掉落物)
                        level.removeBlock(blockPos, false);
                    }
                    
                    // 对工具造成耐久损耗
                    if (!player.isCreative()) {
                        mainHandItem.hurtAndBreak(1, player, (p) -> p.broadcastBreakEvent(player.getUsedItemHand()));
                        
                        // 如果工具损坏了，终止连锁砍伐
                        if (mainHandItem.isEmpty()) break;
                    }
                }
                
                // 播放连锁砍伐成功的声音
                level.playSound(null, player.getX(), player.getY(), player.getZ(), 
                    SoundEvents.WOOD_BREAK, SoundSource.BLOCKS, 0.5F, 0.8F);
                
                // 增加技能熟练度 (每次连锁砍伐增加1点熟练度，连锁的方块越多，增加的越多)
                if (!player.isCreative()) {
                    // 熟练度增加量 = 基础值1 + 额外连锁方块数量 / 10（向下取整，最小为1）
                    int masteryToAdd = 1 + Math.max(1, chainedBlocks.size() / 10);
                    addSkillMasteryPoints(instance, player, masteryToAdd);
                }
            }
        });
    }

    /**
     * 检查方块是否是木头
     */
    private static boolean isWood(BlockState state, ItemStack tool) {
        // 检查方块是否适合用斧头砍伐
        float destroySpeed = state.getDestroySpeed(null, BlockPos.ZERO);
        
        // 方块硬度为-1表示无法破坏
        if (destroySpeed < 0) return false;
        
        // 使用标签系统检查方块是否属于木头类型
        return state.is(net.minecraft.tags.BlockTags.LOGS) || 
               state.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_AXE) ||
               // 对于其他情况，尝试获取方块破坏速度
               tool.getItem().getDestroySpeed(tool, state) > 1.0F;
    }
    
    /**
     * 增加技能熟练度（静态方法版本）
     */
    private static void addSkillMasteryPoints(ManasSkillInstance instance, LivingEntity entity, int amount) {
        if (instance.isMastered(entity)) return; // 如果已经熟练了，不再增加
        
        int currentMastery = instance.getMastery();
        int maxMastery = instance.getMaxMastery();
        
        int newMastery = Math.min(currentMastery + amount, maxMastery);
        instance.setMastery(newMastery);
        
        // 如果达到熟练，通知玩家
        if (newMastery >= maxMastery && entity instanceof Player player) {
            player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_felling.mastered"), true);
        }
    }

    /**
     * 查找连锁的相同类型方块
     */
    private static Set<BlockPos> findChainedBlocks(Level level, BlockPos startPos, Block targetBlock, int maxBlocks) {
        Set<BlockPos> result = new HashSet<>();
        Set<BlockPos> visited = new HashSet<>();
        List<BlockPos> queue = new ArrayList<>();
        
        // 初始化队列
        queue.add(startPos);
        visited.add(startPos);
        
        // 广度优先搜索相邻的相同方块
        while (!queue.isEmpty() && result.size() < maxBlocks) {
            BlockPos current = queue.remove(0);
            
            // 如果是相同类型的方块，加入结果集
            if (level.getBlockState(current).getBlock() == targetBlock) {
                result.add(current);
                
                // 检查所有26个相邻方块（3x3x3立方体，排除中心点）
                for (int x = -1; x <= 1; x++) {
                    for (int y = -1; y <= 1; y++) {
                        for (int z = -1; z <= 1; z++) {
                            // 跳过自身
                            if (x == 0 && y == 0 && z == 0) continue;
                            
                            BlockPos neighbor = current.offset(x, y, z);
                            if (!visited.contains(neighbor)) {
                                visited.add(neighbor);
                                if (level.getBlockState(neighbor).getBlock() == targetBlock) {
                                    queue.add(neighbor);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 客户端Tick事件，用于更新高亮方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;
        
        Minecraft mc = Minecraft.getInstance();
        Player player = mc.player;
        
        // 清除旧的高亮方块
        highlightedBlocks.clear();
        
        // 如果玩家不存在，直接返回
        if (player == null) return;

        // 检查该玩家的技能是否激活
        UUID playerId = player.getUUID();
        if (!playerActiveStates.getOrDefault(playerId, false)) return;
        
        // 检查玩家手中是否有斧头
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof AxeItem)) return;
        
        // 获取玩家看向的方块
        HitResult hitResult = mc.hitResult;
        if (hitResult == null || hitResult.getType() != HitResult.Type.BLOCK) return;
        
        BlockHitResult blockHit = (BlockHitResult) hitResult;
        BlockPos hitPos = blockHit.getBlockPos();
        Level level = player.getLevel();
        BlockState hitState = level.getBlockState(hitPos);
        
        // 如果玩家看向的方块是木头
        if (isWood(hitState, mainHandItem)) {
            Block targetBlock = hitState.getBlock();
            
            // 预览连锁砍伐的方块
            ChainFellingSkill skill = (ChainFellingSkill) TenreincarnationSkill.CHAIN_FELLING.get();
            SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
                int maxBlocks = instance.isMastered(player) ? 64 : 32;
                highlightedBlocks = findChainedBlocks(level, hitPos, targetBlock, maxBlocks);
            });
        }
    }
    
    /**
     * 渲染事件，用于高亮连锁砍伐的方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onRenderLevel(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_TRANSLUCENT_BLOCKS) return;
        if (highlightedBlocks.isEmpty()) return;
        
        Minecraft mc = Minecraft.getInstance();
        PoseStack poseStack = event.getPoseStack();
        
        // 获取相机位置
        double cameraX = mc.gameRenderer.getMainCamera().getPosition().x;
        double cameraY = mc.gameRenderer.getMainCamera().getPosition().y;
        double cameraZ = mc.gameRenderer.getMainCamera().getPosition().z;
        
        // 设置渲染状态
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.disableTexture();
        
        // 获取顶点缓冲区
        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer builder = bufferSource.getBuffer(RenderType.lines());
        
        // 渲染每个方块的轮廓 - 使用绿色高亮表示木头
        for (BlockPos pos : highlightedBlocks) {
            poseStack.pushPose();
            poseStack.translate(pos.getX() - cameraX, pos.getY() - cameraY, pos.getZ() - cameraZ);
            
            // 绘制方块边缘 - 绿色轮廓更符合树木主题
            LevelRenderer.renderLineBox(
                poseStack,
                builder,
                0, 0, 0, 1, 1, 1,
                0.0f, 1.0f, 0.0f, 0.5f // RGBA: 绿色，50%透明
            );
            
            poseStack.popPose();
        }
        
        // 结束渲染
        bufferSource.endBatch(RenderType.lines());
        RenderSystem.enableTexture();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }
} 