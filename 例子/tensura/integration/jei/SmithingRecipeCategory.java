package com.github.manasmods.tensura.integration.jei;

import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import mezz.jei.api.constants.VanillaTypes;
import mezz.jei.api.gui.builder.IRecipeLayoutBuilder;
import mezz.jei.api.gui.drawable.IDrawable;
import mezz.jei.api.helpers.IGuiHelper;
import mezz.jei.api.recipe.IFocusGroup;
import mezz.jei.api.recipe.RecipeIngredientRole;
import mezz.jei.api.recipe.RecipeType;
import mezz.jei.api.recipe.category.IRecipeCategory;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;

public class SmithingRecipeCategory implements IRecipeCategory<SmithingBenchRecipe> {
   private static final Component TITLE = Component.m_237115_("tensura.jei.smithing.title");
   static final ResourceLocation UID = new ResourceLocation("tensura", "smithing");
   private final IDrawable background;
   private final IDrawable icon;

   public SmithingRecipeCategory(IGuiHelper guiHelper) {
      this.icon = guiHelper.createDrawableIngredient(VanillaTypes.ITEM_STACK, ((Item)TensuraBlocks.Items.SMITHING_BENCH.get()).m_7968_());
      this.background = guiHelper.createBlankDrawable(140, 20);
   }

   public RecipeType<SmithingBenchRecipe> getRecipeType() {
      return TensuraJeiPlugin.SMITHING_RECIPE;
   }

   public Component getTitle() {
      return TITLE;
   }

   public IDrawable getBackground() {
      return this.background;
   }

   public IDrawable getIcon() {
      return this.icon;
   }

   public void setRecipe(IRecipeLayoutBuilder builder, SmithingBenchRecipe recipe, IFocusGroup focuses) {
      builder.addSlot(RecipeIngredientRole.OUTPUT, 120, 0).addItemStack(recipe.m_8043_());
      List<ItemStack> ingredient1 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(0)).m_43908_()).peek((stack) -> {
         stack.m_41764_((Integer)recipe.getIngedientAmount().get(0));
      }).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 0, 0).addItemStacks(ingredient1);
      List<ItemStack> ingredient2 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(1)).m_43908_()).peek((stack) -> {
         stack.m_41764_((Integer)recipe.getIngedientAmount().get(1));
      }).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 20, 0).addItemStacks(ingredient2);
      List<ItemStack> ingredient3 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(2)).m_43908_()).peek((stack) -> {
         stack.m_41764_((Integer)recipe.getIngedientAmount().get(2));
      }).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 40, 0).addItemStacks(ingredient3);
      List<ItemStack> ingredient4 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(3)).m_43908_()).peek((stack) -> {
         stack.m_41764_((Integer)recipe.getIngedientAmount().get(3));
      }).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 60, 0).addItemStacks(ingredient4);
      List<ItemStack> ingredient5 = new ArrayList(Arrays.stream(((Ingredient)recipe.m_7527_().get(4)).m_43908_()).peek((stack) -> {
         stack.m_41764_((Integer)recipe.getIngedientAmount().get(4));
      }).toList());
      builder.addSlot(RecipeIngredientRole.INPUT, 80, 0).addItemStacks(ingredient5);
   }
}
