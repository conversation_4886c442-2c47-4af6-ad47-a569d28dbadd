package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraIndirectEntityDamageSource;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;

public class SpatialArrowProjectile extends TensuraProjectile {
   public SpatialArrowProjectile(EntityType<? extends SpatialArrowProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public SpatialArrowProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.SPATIAL_ARROW.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/energy_arrow.png")};
   }

   public boolean piercingBlock() {
      return true;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   protected void hitEntity(Entity entity) {
      if (entity != this.m_37282_()) {
         super.hitEntity(entity);
      }
   }

   protected void dealDamage(Entity target) {
      DamageSource damageSource = (new TensuraIndirectEntityDamageSource("arrow", this, this.m_37282_())).setSpatial().m_19366_();
      if (this.damage > 0.0F && target.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), this.getDamage())) {
         target.f_19802_ = 0;
      }

   }

   protected void playHitSound(SoundEvent sound, HitResult hitresult) {
      if (hitresult.m_6662_().equals(Type.ENTITY)) {
         super.playHitSound(sound, hitresult);
      }

   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123808_, x, y, z, 15, 0.1D, 0.1D, 0.1D, 0.1D, true);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));

      for(int i = 0; i < 2; ++i) {
         Vec3 random = this.vec3Random().m_82490_(0.10000000149011612D);
         this.m_9236_().m_7106_(ParticleTypes.f_123808_, vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, random.f_82479_, random.f_82480_, random.f_82481_);
      }

   }
}
