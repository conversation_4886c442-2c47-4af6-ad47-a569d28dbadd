package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.ThoughtAccelerationSkill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.network.protocol.game.ClientboundAnimatePacket;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class ShadowStrikerSkill extends Skill {
   protected static final UUID ACCELERATION = UUID.fromString("498bb130-f5e2-406a-9761-9e195f34c11e");

   public ShadowStrikerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 3000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 2 ? 3000.0D : 0.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_striker.ultra_acceleration");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_striker.insta_kill");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_striker.espionage");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         return instance.getMode() != 2;
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(attacker)) {
         if (instance.getMode() == 2) {
            if (!instance.onCoolDown()) {
               DamageSource source = e.getSource();
               if (source.m_7639_() == attacker) {
                  if (DamageSourceHelper.isPhysicalAttack(source)) {
                     if (!SkillHelper.outOfMagicule(attacker, instance)) {
                        LivingEntity living = e.getEntity();
                        DamageSourceHelper.directSpiritualHurt(living, attacker, 500.0F);
                        instance.setCoolDown(instance.isMastered(living) ? 5 : 10);
                        if (!living.m_6084_()) {
                           e.setCanceled(true);
                        }

                     }
                  }
               }
            }
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 3) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get(), 5, 0, false, false, false));
         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (instance.getMode() == 1) {
         if (entity.m_20096_()) {
            if (!SkillHelper.outOfAura(entity, instance)) {
               this.addMasteryPoint(instance, entity);
               int range = instance.isMastered(entity) ? 20 : 15;
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, (double)range);
               BlockPos resultPos = result.m_82425_().m_121945_(result.m_82434_());
               Vec3 vec3 = SkillHelper.getFloorPos(resultPos);
               if (!level.m_8055_(resultPos).m_60767_().m_76336_()) {
                  vec3 = SkillHelper.getFloorPos(resultPos.m_7494_());
               }

               if (level.m_8055_(resultPos).m_60713_((Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else if (!entity.m_9236_().m_6857_().m_61937_(new BlockPos(vec3.m_7096_(), vec3.m_7098_(), vec3.m_7094_()))) {
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.skill.teleport.out_border").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

               } else {
                  Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F), 0.0D);
                  Vec3 offSetToTarget = vec3.m_82546_(source);

                  for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
                     Vec3 particlePos = source.m_82549_(offSetToTarget.m_82541_().m_82490_((double)particleIndex));
                     ((ServerLevel)level).m_8767_(ParticleTypes.f_123796_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
                     TensuraParticleHelper.addServerParticlesAroundPos(entity.m_217043_(), level, particlePos, ParticleTypes.f_123766_, 3.0D);
                     TensuraParticleHelper.addServerParticlesAroundPos(entity.m_217043_(), level, particlePos, ParticleTypes.f_123766_, 2.0D);
                     AABB aabb = (new AABB(new BlockPos(particlePos))).m_82400_(Math.max(entity.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()), 2.0D));
                     List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (targetx) -> {
                        return !targetx.m_7306_(entity) && !targetx.m_7307_(entity);
                     });
                     if (!livingEntityList.isEmpty()) {
                        float bonus = instance.isMastered(entity) ? 70.0F : 0.0F;
                        float amount = (float)(entity.m_21133_(Attributes.f_22281_) * entity.m_21133_((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get()));
                        Iterator var16 = livingEntityList.iterator();

                        while(var16.hasNext()) {
                           LivingEntity target = (LivingEntity)var16.next();
                           if (target.m_6469_(this.sourceWithMP(DamageSource.m_19370_(entity), entity, instance), amount + bonus)) {
                              ItemStack stack = entity.m_21205_();
                              stack.m_41720_().m_7579_(stack, target, entity);
                              entity.m_9236_().m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_12313_, entity.m_5720_(), 1.0F, 1.0F);
                              if (level instanceof ServerLevel) {
                                 ServerLevel serverLevel = (ServerLevel)level;
                                 serverLevel.m_7726_().m_8394_(entity, new ClientboundAnimatePacket(entity, 4));
                              }
                           }
                        }
                     }
                  }

                  entity.m_183634_();
                  entity.m_19877_();
                  entity.m_20219_(vec3);
                  entity.f_19812_ = true;
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, true);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, false);
   }
}
