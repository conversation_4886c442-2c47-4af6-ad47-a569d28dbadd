package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.DynamicMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.PeacockVariant;
import com.github.manasmods.tensura.item.armor.MonsterLeatherHelmetItem;
import com.github.manasmods.tensura.item.armor.MonsterLeatherSpecialAHelmetItem;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class DragonPeacockEntity extends FLyingTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private boolean partyPeacock;
   @Nullable
   private BlockPos jukebox;
   public int hurtTicks = 0;
   private int eatingTick;
   private static final EntityDataAccessor<Boolean> HURT;
   private static final EntityDataAccessor<Boolean> EATING;
   public static final AnimationBuilder IDLE;
   public static final AnimationBuilder WALK;
   public static final AnimationBuilder FLY;
   public static final AnimationBuilder THREATEN;
   public static final AnimationBuilder EAT;
   public static final AnimationBuilder SIT;
   public static final AnimationBuilder PLAYER_GLIDE;
   public static final AnimationBuilder DANCE;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public DragonPeacockEntity(EntityType<? extends TamableAnimal> type, Level worldIn) {
      super(type, worldIn);
      this.f_21364_ = 7;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 16.0D).m_22268_(Attributes.f_22281_, 3.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(4, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(6, new EatBlockGoal(this));
      this.f_21345_.m_25352_(7, new FLyingTamableEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21345_.m_25352_(4, new DynamicMeleeAttackGoal(this, List.of((self, target, goal) -> {
         float speed = 1.7F;
         goal.shouldMoveToTarget = !target.m_5842_();
         if (this.m_20270_(target) < 20.0F) {
            if ((double)this.m_20270_(target) < 2.0D) {
               this.m_7327_(target);
               speed = 2.0F;
            } else {
               this.m_21391_(target, 70.0F, 70.0F);
            }
         }

         return speed;
      }, (self, target, goal) -> {
         return target instanceof Player ? 1.7F : 2.0F;
      })));
      this.f_21346_.m_25352_(5, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(HURT, Boolean.FALSE);
      this.f_19804_.m_135372_(EATING, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128379_("Hurt", this.isHurt());
      compound.m_128379_("Eat", this.eating());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.setHurt(compound.m_128471_("Hurt"));
      this.setEating(compound.m_128471_("Eat"));
   }

   public boolean isHurt() {
      return (Boolean)this.f_19804_.m_135370_(HURT);
   }

   public void setHurt(boolean hurt) {
      this.f_19804_.m_135381_(HURT, hurt);
   }

   public boolean eating() {
      return (Boolean)this.f_19804_.m_135370_(EATING);
   }

   public void setEating(boolean eating) {
      this.f_19804_.m_135381_(EATING, eating);
   }

   public PeacockVariant getVariant() {
      return PeacockVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(PeacockVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   protected boolean m_8028_() {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      LivingEntity living;
      if (this.m_20159_()) {
         Entity var2 = this.m_20202_();
         if (var2 instanceof LivingEntity) {
            living = (LivingEntity)var2;
            Vec3 vec3 = living.m_20184_();
            this.m_146922_(living.m_146908_());
            if (living.m_20072_() || living.m_20077_() || !(living.m_6844_(EquipmentSlot.HEAD).m_41720_() instanceof MonsterLeatherHelmetItem) && !(living.m_6844_(EquipmentSlot.HEAD).m_41720_() instanceof MonsterLeatherSpecialAHelmetItem)) {
               this.m_8127_();
               this.m_6210_();
               this.m_146884_(Vec3.m_82512_(living.m_20183_()));
            }

            if (!living.m_20096_() && !this.m_6162_() && vec3.m_7098_() < 0.0D) {
               living.m_20256_(vec3.m_82542_(1.0D, 0.65D, 1.0D));
               living.m_183634_();
            }
         }
      }

      living = this.m_5448_();
      if (living != null && living.m_6084_() && this.m_20280_(living) > 3.0D) {
         this.setFlying(true);
      }

      if (this.isHurt()) {
         ++this.hurtTicks;
         if (this.hurtTicks >= 5) {
            this.setHurt(Boolean.FALSE);
            this.hurtTicks = 0;
         }
      }

      if (this.eating()) {
         ++this.eatingTick;
         if (this.eatingTick >= 5) {
            this.setEating(Boolean.FALSE);
            this.eatingTick = 0;
         }
      }

   }

   public void m_8107_() {
      if (this.jukebox == null || !this.jukebox.m_203195_(this.m_20182_(), 3.46D) || !this.f_19853_.m_8055_(this.jukebox).m_60713_(Blocks.f_50131_)) {
         this.partyPeacock = false;
         this.jukebox = null;
      }

      super.m_8107_();
   }

   public void m_6818_(BlockPos pPos, boolean pIsPartying) {
      this.jukebox = pPos;
      this.partyPeacock = pIsPartying;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return !(this.m_20202_() instanceof LivingEntity) && !this.m_21825_() ? entitydimensions : entitydimensions.m_20390_(1.0F, 0.7F);
   }

   public double m_6049_() {
      Entity var2 = this.m_20202_();
      if (var2 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var2;
         if (living.m_21255_()) {
            return 0.0D;
         }
      }

      return 0.5D;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_20159_()) {
         Entity var3 = this.m_20202_();
         if (var3 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var3;
            LivingEntity owner = this.m_21826_();
            if (this.m_21824_() && living.equals(owner)) {
               if ((living.m_20096_() || owner.m_20184_().m_7098_() == 0.0D) && !living.m_20142_()) {
                  event.getController().setAnimation(SIT);
                  return PlayState.CONTINUE;
               }

               event.getController().setAnimation(PLAYER_GLIDE);
               return PlayState.CONTINUE;
            }
         }
      }

      if (this.m_20096_()) {
         if (this.m_21825_() && !this.isPartyPeacock()) {
            event.getController().setAnimation(SIT);
            return PlayState.CONTINUE;
         } else if (event.isMoving()) {
            event.getController().setAnimation(WALK);
            return PlayState.CONTINUE;
         } else if (this.isPartyPeacock()) {
            event.getController().setAnimation(DANCE);
            return PlayState.CONTINUE;
         } else {
            event.getController().setAnimation(IDLE);
            return PlayState.CONTINUE;
         }
      } else {
         event.getController().setAnimation(FLY);
         return PlayState.CONTINUE;
      }
   }

   private <T extends IAnimatable> PlayState hurtPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.isHurt() && !this.m_20159_()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(THREATEN);
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState eatPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.eating()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(EAT);
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "hurtController", 0.0F, this::hurtPredicate));
      data.addAnimationController(new AnimationController(this, "eatController", 0.0F, this::eatPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public boolean m_7757_(LivingEntity pTarget, LivingEntity pOwner) {
      this.setHurt(Boolean.TRUE);
      return true;
   }

   public void m_7822_(byte pId) {
      if (pId == 10 && !this.eating()) {
         this.setEating(Boolean.TRUE);
         this.m_5634_(5.0F);
      } else {
         super.m_7822_(pId);
      }

   }

   public void m_8035_() {
      super.m_8035_();
      if (this.m_6162_()) {
         this.m_146758_(60);
      }

   }

   public DragonPeacockEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      DragonPeacockEntity peacock = (DragonPeacockEntity)((EntityType)TensuraEntityTypes.DRAGON_PEACOCK.get()).m_20615_(pLevel);
      if (peacock == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            peacock.m_21816_(uuid);
            peacock.m_7105_(true);
         }

         return peacock;
      }
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      if (pOtherAnimal == this) {
         return false;
      } else if (pOtherAnimal.getClass() != this.getClass()) {
         return false;
      } else {
         DragonPeacockEntity peacock = (DragonPeacockEntity)pOtherAnimal;
         return this.m_27593_() && pOtherAnimal.m_27593_() && peacock.getVariant() != this.getVariant();
      }
   }

   public boolean m_6898_(ItemStack stack) {
      return this.m_21824_() && stack.m_204117_(TensuraTags.Items.PEACOCK_FOOD);
   }

   protected SoundEvent m_7515_() {
      return (SoundEvent)TensuraSoundEvents.PEACOCK_AMBIENT.get();
   }

   protected SoundEvent m_7975_(DamageSource source) {
      if (source.m_7639_() != null) {
         if (this.m_21826_() != null && source.m_7639_().equals(this.m_21826_())) {
            return (SoundEvent)TensuraSoundEvents.BIRD_HURT.get();
         }

         if (source.m_7640_() instanceof LivingEntity) {
            this.setHurt(Boolean.TRUE);
         }
      }

      return (SoundEvent)TensuraSoundEvents.BIRD_HURT.get();
   }

   protected SoundEvent m_5592_() {
      return (SoundEvent)TensuraSoundEvents.BIRD_DEATH.get();
   }

   public SoundSource m_5720_() {
      return SoundSource.NEUTRAL;
   }

   public boolean m_7327_(Entity entityIn) {
      if (!this.m_29443_()) {
         this.setEating(Boolean.TRUE);
      }

      return super.m_7327_(entityIn);
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else if (!this.m_21830_(player) || player.m_146895_() instanceof DragonPeacockEntity || player.m_36341_() || !player.m_21205_().m_41619_() || !(player.m_6844_(EquipmentSlot.HEAD).m_41720_() instanceof MonsterLeatherHelmetItem) && !(player.m_6844_(EquipmentSlot.HEAD).m_41720_() instanceof MonsterLeatherSpecialAHelmetItem)) {
         if (this.m_6898_(itemstack)) {
            if (this.m_21223_() < this.m_21233_()) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.SMALL_CHEW.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               this.m_5634_(2.0F);
               return InteractionResult.SUCCESS;
            }

            if (this.m_6162_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_146740_(m_216967_(-this.m_146764_()), true);
               this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.SMALL_CHEW.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         }

         if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || itemstack.m_204117_(TensuraTags.Items.PEACOCK_TAMING_FOOD);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_()) {
            if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player) && !this.m_6162_()) {
               this.commanding(player);
               return InteractionResult.SUCCESS;
            } else {
               return InteractionResult.PASS;
            }
         } else if (itemstack.m_204117_(TensuraTags.Items.PEACOCK_TAMING_FOOD)) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            if (this.f_19796_.m_188503_(3) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
               this.m_21828_(player);
               this.f_21344_.m_26573_();
               this.m_6710_((LivingEntity)null);
               this.m_21839_(true);
               this.f_19853_.m_7605_(this, (byte)7);
            } else {
               this.f_19853_.m_7605_(this, (byte)6);
            }

            return InteractionResult.SUCCESS;
         } else {
            return super.m_6071_(player, hand);
         }
      } else {
         this.m_7998_(player, true);
         this.f_21344_.m_26573_();
         this.m_6710_((LivingEntity)null);
         this.m_6210_();
         return InteractionResult.SUCCESS;
      }
   }

   public static boolean checkPeacockSpawnRules(EntityType<DragonPeacockEntity> pPeacock, ServerLevelAccessor pLevel, MobSpawnType mobSpawnType, BlockPos blockPos, RandomSource randomSource) {
      return m_186209_(pLevel, blockPos) && blockPos.m_123342_() > 40;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.dragonPeacockSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      this.setVariant(PeacockVariant.byId(this.f_19796_.m_188503_(2)));
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public boolean isPartyPeacock() {
      return this.partyPeacock;
   }

   static {
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(DragonPeacockEntity.class, EntityDataSerializers.f_135028_);
      HURT = SynchedEntityData.m_135353_(DragonPeacockEntity.class, EntityDataSerializers.f_135035_);
      EATING = SynchedEntityData.m_135353_(DragonPeacockEntity.class, EntityDataSerializers.f_135035_);
      IDLE = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.standing", EDefaultLoopTypes.LOOP);
      WALK = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.walking", EDefaultLoopTypes.LOOP);
      FLY = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.flying", EDefaultLoopTypes.LOOP);
      THREATEN = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.threatening", EDefaultLoopTypes.PLAY_ONCE);
      EAT = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.eating", EDefaultLoopTypes.PLAY_ONCE);
      SIT = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.sitting", EDefaultLoopTypes.LOOP);
      PLAYER_GLIDE = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.player_gliding", EDefaultLoopTypes.LOOP);
      DANCE = (new AnimationBuilder()).addAnimation("animation.dragon_peacock.dancing", EDefaultLoopTypes.LOOP);
   }
}
