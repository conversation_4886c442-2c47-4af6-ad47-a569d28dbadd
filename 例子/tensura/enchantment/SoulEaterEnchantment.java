package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class SoulEaterEnchantment extends EngravingEnchantment {
   public SoulEaterEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity target, int pLevel, float damage) {
      if (target.f_19802_ < 60) {
         if (target instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)target;
            DamageSourceHelper.directSpiritualHurt(living, attacker, damage * (float)pLevel);
         }

      }
   }
}
