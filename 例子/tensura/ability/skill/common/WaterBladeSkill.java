package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.skill.WaterBladeProjectile;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class WaterBladeSkill extends Skill {
   public WaterBladeSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)CommonSkills.HYDRAULIC_PROPULSION.get())) {
         return false;
      } else {
         return newEP > 10000.0D;
      }
   }

   public double learningCost() {
      return 2.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_20072_()) {
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         this.shootBlade(instance, entity);
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            if (!(cap.getWaterPoint() <= 0.0D) || cap.getSpiritLevel(MagicElemental.WATER.getId()) >= 1) {
               if (!SkillHelper.outOfMagicule(entity, instance)) {
                  cap.setWaterPoint(cap.getWaterPoint() - 1.0D);
                  this.shootBlade(instance, entity);
                  TensuraSkillCapability.sync(player);
               }
            }
         });
      }

   }

   private void shootBlade(ManasSkillInstance instance, LivingEntity entity) {
      this.addMasteryPoint(instance, entity);
      WaterBladeProjectile waterBlade = new WaterBladeProjectile(entity.m_9236_(), entity);
      waterBlade.setSpeed(5.0F);
      waterBlade.setDamage(40.0F);
      waterBlade.setMpCost(this.magiculeCost(entity, instance));
      waterBlade.setSkill(instance);
      waterBlade.setPosAndShoot(entity);
      entity.m_9236_().m_7967_(waterBlade);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12520_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
