package com.github.b4ndithelps.tennogamenolife.registry.sound;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

/**
 * 音效注册类
 */
public class TenreincarnationSounds {
    
    public static final DeferredRegister<SoundEvent> SOUND_EVENTS = 
        DeferredRegister.create(ForgeRegistries.SOUND_EVENTS, Tenreincarnation.MODID);

    // 休比音效
    public static final RegistryObject<SoundEvent> XIUBI_AMBIENT = registerSoundEvent("entity.xiubi.ambient");
    public static final RegistryObject<SoundEvent> XIUBI_HURT = registerSoundEvent("entity.xiubi.hurt");

    private static RegistryObject<SoundEvent> registerSoundEvent(String name) {
        return SOUND_EVENTS.register(name, () -> new SoundEvent(new ResourceLocation(Tenreincarnation.MODID, name)));
    }

    public static void register(IEventBus eventBus) {
        SOUND_EVENTS.register(eventBus);
    }
}
