package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.event.SkillDamageEvent.PreCalculation;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.unique.GuardianSkill;
import com.github.manasmods.tensura.capability.effects.ITensuraEffectsCapability;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.effect.skill.PresenceSenseEffect;
import com.github.manasmods.tensura.effect.template.Concealment;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.event.ForcedTeleportationEvent;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.Registry;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.common.ToolActions;
import net.minecraftforge.event.entity.EntityTeleportEvent;
import net.minecraftforge.event.entity.EntityTravelToDimensionEvent;
import net.minecraftforge.event.entity.EntityTeleportEvent.SpreadPlayersCommand;
import net.minecraftforge.event.entity.EntityTeleportEvent.TeleportCommand;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingHealEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.event.entity.living.LivingKnockBackEvent;
import net.minecraftforge.event.entity.living.MobEffectEvent;
import net.minecraftforge.event.entity.living.MobEffectEvent.Added;
import net.minecraftforge.event.entity.living.MobEffectEvent.Applicable;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerLoggedInEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class StatusEffectsHandler {
   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void applyEffectPreBarrier(PreCalculation e) {
      LivingEntity target = e.getEntity();
      if (!SkillUtils.noInteractiveMode(target)) {
         if (!GuardianSkill.onSubordinateHurt(e.getEvent())) {
            magicInterference(e.getEvent(), target);
            Entity var3 = e.getSource().m_7639_();
            if (var3 instanceof LivingEntity) {
               LivingEntity source = (LivingEntity)var3;
               Iterator var8 = List.copyOf(source.m_21220_()).iterator();

               while(var8.hasNext()) {
                  MobEffectInstance instance = (MobEffectInstance)var8.next();
                  MobEffect var6 = instance.m_19544_();
                  if (var6 instanceof DamageAction) {
                     DamageAction effect = (DamageAction)var6;
                     effect.onDamagingEntity(source, e.getEvent());
                  }
               }
            }

            Iterator var7 = List.copyOf(target.m_21220_()).iterator();

            while(var7.hasNext()) {
               MobEffectInstance instance = (MobEffectInstance)var7.next();
               MobEffect var11 = instance.m_19544_();
               if (var11 instanceof DamageAction) {
                  DamageAction effect = (DamageAction)var11;
                  effect.onBeingDamaged(e.getEvent());
                  Entity var13 = e.getSource().m_7639_();
                  if (var13 instanceof LivingEntity) {
                     LivingEntity source = (LivingEntity)var13;
                     effect.onDamagingEntity(source, e.getEvent());
                  }
               }
            }

         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onTakingDamage(LivingDamageEvent e) {
      LivingEntity target = e.getEntity();
      if (!SkillUtils.noInteractiveMode(target)) {
         Entity var3 = e.getSource().m_7639_();
         int var4;
         if (var3 instanceof LivingEntity) {
            LivingEntity source = (LivingEntity)var3;
            EquipmentSlot[] var12 = EquipmentSlot.values();
            var4 = var12.length;

            for(int var5 = 0; var5 < var4; ++var5) {
               EquipmentSlot slot = var12[var5];
               Map<Enchantment, Integer> enchantments = source.m_6844_(slot).getAllEnchantments();
               Iterator var8 = enchantments.keySet().iterator();

               while(var8.hasNext()) {
                  Enchantment enchantment = (Enchantment)var8.next();
                  if (enchantment instanceof EngravingEnchantment) {
                     EngravingEnchantment engrave = (EngravingEnchantment)enchantment;
                     e.setAmount(e.getAmount() + engrave.getDamageBonus((Integer)enchantments.get(enchantment), e.getSource(), source, target, slot, e.getAmount()));
                  }
               }
            }
         }

         EquipmentSlot[] var11 = EquipmentSlot.values();
         int var13 = var11.length;

         for(var4 = 0; var4 < var13; ++var4) {
            EquipmentSlot slot = var11[var4];
            Map<Enchantment, Integer> enchantments = target.m_6844_(slot).getAllEnchantments();
            Iterator var16 = enchantments.keySet().iterator();

            while(var16.hasNext()) {
               Enchantment enchantment = (Enchantment)var16.next();
               if (enchantment instanceof EngravingEnchantment) {
                  EngravingEnchantment engrave = (EngravingEnchantment)enchantment;
                  e.setAmount(e.getAmount() - engrave.getDamageProtection((Integer)enchantments.get(enchantment), e.getSource(), target, slot, e.getAmount()));
               }
            }
         }

         if (SkillUtils.haveSeveranceAttack(e.getSource(), target)) {
            TensuraEffectsCapability.getFrom(target).ifPresent((cap) -> {
               int removeSec = (Integer)TensuraConfig.INSTANCE.skillsConfig.severanceRemoveSec.get();
               if (removeSec != 0) {
                  cap.setSeveranceRemoveSec(removeSec);
                  cap.setSeveranceAmount(cap.getSeveranceAmount() + (double)e.getAmount());
                  TensuraEffectsCapability.sync(target);
               }
            });
         }

      }
   }

   @SubscribeEvent
   public static void onEffectAdded(MobEffectEvent e) {
      if (e instanceof Added) {
         LivingEntity entity = e.getEntity();
         MobEffectInstance instance = e.getEffectInstance();
         ResourceLocation resourceLocation = Registry.f_122823_.m_7981_(instance.m_19544_());
         if (resourceLocation != null) {
            TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
               cap.addSyncedEffect(resourceLocation, entity);
            });
         }
      }
   }

   @SubscribeEvent
   public static void onEffectRemoved(MobEffectEvent e) {
      if (!(e instanceof Added) && !(e instanceof Applicable)) {
         MobEffectInstance instance = e.getEffectInstance();
         if (instance != null) {
            LivingEntity entity = e.getEntity();
            MobEffect effect = instance.m_19544_();
            ResourceLocation resourceLocation = Registry.f_122823_.m_7981_(effect);
            if (resourceLocation != null) {
               TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                  cap.removeSyncedEffect(resourceLocation, entity);
                  cap.removeEffectSource(resourceLocation);
               });
               if (effect instanceof Concealment) {
                  TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                     cap.setPresenceConceal(0);
                  });
                  TensuraEffectsCapability.sync(entity);
               } else if (effect instanceof PresenceSenseEffect) {
                  TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                     cap.setPresenceSense(0);
                  });
                  TensuraEffectsCapability.sync(entity);
               } else if (effect == TensuraMobEffects.MIND_CONTROL.get()) {
                  LivingEntity effectSource = TensuraEffectsCapability.getEffectSource(entity, effect);
                  TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
                     if (effectSource == null || Objects.equals(cap.getTemporaryOwner(), effectSource.m_20148_())) {
                        cap.setTemporaryOwner((UUID)null);
                        UUID owner = cap.getPermanentOwner();
                        if (entity instanceof TensuraTamableEntity) {
                           TensuraTamableEntity tamable = (TensuraTamableEntity)entity;
                           tamable.resetOwner(owner);
                        } else if (entity instanceof TensuraHorseEntity) {
                           TensuraHorseEntity horse = (TensuraHorseEntity)entity;
                           horse.resetOwner(owner);
                        } else if (entity instanceof TamableAnimal) {
                           TamableAnimal animal = (TamableAnimal)entity;
                           animal.m_21816_(owner);
                           if (owner == null) {
                              animal.m_7105_(false);
                           }
                        }
                     }

                     if (effectSource != null) {
                        cap.removeNeutralTarget(effectSource.m_20148_());
                     }

                  });
                  TensuraEffectsCapability.sync(entity);
               }

            }
         }
      }
   }

   @SubscribeEvent
   public static void onKnockBack(LivingKnockBackEvent e) {
      LivingEntity entity = e.getEntity();
      TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
         if (cap.isNoKnockBack()) {
            cap.setNoKnockBack(false);
            e.setCanceled(true);
         }

      });
   }

   @SubscribeEvent
   public static void onTeleport(EntityTeleportEvent e) {
      if (!(e instanceof TeleportCommand) && !(e instanceof SpreadPlayersCommand)) {
         Entity var2 = e.getEntity();
         if (var2 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var2;
            if (MobEffectHelper.noTeleportation(living)) {
               if (!(e instanceof ForcedTeleportationEvent) && living instanceof Player) {
                  Player player = (Player)living;
                  player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               }

               e.setCanceled(true);
            }
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void onChangingDimension(EntityTravelToDimensionEvent e) {
      Entity var2 = e.getEntity();
      if (var2 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var2;
         if (MobEffectHelper.noDimensionChanging(living)) {
            if (living instanceof Player) {
               Player player = (Player)living;
               player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

            e.setCanceled(true);
         }
      }
   }

   @SubscribeEvent
   public static void onPlayerLogin(PlayerLoggedInEvent e) {
      TensuraEffectsCapability.getFrom(e.getEntity()).ifPresent((cap) -> {
         cap.setWarpPortalTime(0);
         cap.setNoKnockBack(false);
         cap.setPresenceConceal(0);
         cap.setPresenceSense(0);
      });
      TensuraEffectsCapability.sync(e.getEntity());
   }

   @SubscribeEvent
   public static void onHeal(LivingHealEvent e) {
      LivingEntity entity = e.getEntity();
      if (Float.isNaN(e.getAmount())) {
         e.setCanceled(true);
      } else if (!Float.isNaN(entity.m_21223_()) && !(entity.m_21223_() < 0.0F)) {
         if (MobEffectHelper.shouldCancelHeal(entity)) {
            e.setCanceled(Boolean.TRUE);
         }

         ITensuraEffectsCapability effectsCapability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(entity, TensuraEffectsCapability.CAPABILITY);
         if (effectsCapability != null && effectsCapability.getSeveranceAmount() > 0.0D) {
            float severedHealth = (float)((double)entity.m_21233_() - effectsCapability.getSeveranceAmount());
            if (entity.m_21223_() == severedHealth) {
               e.setCanceled(Boolean.TRUE);
            } else if (entity.m_21223_() + e.getAmount() > severedHealth) {
               if (entity.m_21223_() > severedHealth) {
                  entity.m_6469_(TensuraDamageSources.SEVERANCE_UPDATE, entity.m_21223_() - severedHealth);
                  e.setCanceled(Boolean.TRUE);
               } else {
                  e.setAmount(severedHealth - entity.m_21223_());
               }
            }
         }

         if (!e.isCanceled()) {
            if (!entity.m_9236_().m_5776_()) {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                     if (cap.isHeroEgg()) {
                        if (!RaceHelper.shouldNamingStopAwakening(player)) {
                           if (!cap.isTrueHero()) {
                              if (!cap.isTrueDemonLord()) {
                                 if (!(player.m_21223_() >= player.m_21233_() / 4.0F)) {
                                    if (RaceHelper.fightingBossForHero(player)) {
                                       cap.setTrueHero(true);
                                       RaceHelper.awakening(player, true);
                                    }

                                 }
                              }
                           }
                        }
                     }
                  });
               }
            }
         }
      } else {
         entity.revive();
         entity.m_21153_(0.0F);
         e.setCanceled(true);
         if (!(entity instanceof Player)) {
            entity.m_146870_();
         }

      }
   }

   private static void magicInterference(LivingHurtEvent e, LivingEntity target) {
      float magicInterfere = 0.0F;
      EquipmentSlot[] var3 = EquipmentSlot.values();
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         EquipmentSlot slot = var3[var5];
         if (!slot.m_20743_().equals(Type.HAND)) {
            int level = target.m_6844_(slot).getEnchantmentLevel((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get());
            if (level > 0) {
               magicInterfere = (float)((double)magicInterfere + 0.25D * (double)level);
            }
         }
      }

      if (target.m_21211_().canPerformAction(ToolActions.SHIELD_BLOCK) && target.m_21211_().getEnchantmentLevel((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get()) > 0) {
         magicInterfere = 1.0F;
      }

      DamageSource source = e.getSource();
      if (magicInterfere > 0.0F) {
         if (source.m_19378_()) {
            return;
         }

         if (!DamageSourceHelper.isTensuraMagic(source)) {
            return;
         }

         Entity var10 = e.getSource().m_7639_();
         if (var10 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var10;
            if (SkillHelper.getMP(living, false) >= SkillHelper.getMP(target, false) * 1.5D) {
               return;
            }
         }

         e.setAmount(e.getAmount() * (1.0F - magicInterfere));
      }

   }
}
