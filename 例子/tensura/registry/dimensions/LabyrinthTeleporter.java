package com.github.manasmods.tensura.registry.dimensions;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import java.util.Optional;
import java.util.function.Function;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.portal.PortalInfo;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.util.ITeleporter;
import org.jetbrains.annotations.Nullable;

public class LabyrinthTeleporter implements ITeleporter {
   public boolean isVanilla() {
      return false;
   }

   @Nullable
   public PortalInfo getPortalInfo(Entity entity, ServerLevel destWorld, Function<ServerLevel, PortalInfo> defaultPortalInfo) {
      if (destWorld.m_46472_() != TensuraDimensions.LABYRINTH) {
         if (entity instanceof ServerPlayer) {
            ServerPlayer player = (ServerPlayer)entity;
            if (player.m_8963_() == destWorld.m_46472_()) {
               BlockPos pos = player.m_8961_();
               if (pos != null) {
                  Optional<Vec3> optional = Player.m_36130_(destWorld, pos, player.m_8962_(), player.m_8964_(), true);
                  if (optional.isPresent()) {
                     pos = new BlockPos((Vec3)optional.get());
                  }
               } else {
                  pos = destWorld.m_220360_();
               }

               return new PortalInfo(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), Vec3.f_82478_, player.m_8962_(), 0.0F);
            }
         }

         return super.getPortalInfo(entity, destWorld, defaultPortalInfo);
      } else {
         LabyrinthSaveData saveData = LabyrinthSaveData.get(destWorld.m_7654_().m_129783_());
         Vec3 pos = saveData.getEntrancePos();
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (LabyrinthSaveData.isEntityPassedColossus(player) || TensuraEffectsCapability.isColossusWon(player)) {
               pos = saveData.getPassedEntrance();
            }
         }

         return new PortalInfo(pos, Vec3.f_82478_, 0.0F, 0.0F);
      }
   }
}
