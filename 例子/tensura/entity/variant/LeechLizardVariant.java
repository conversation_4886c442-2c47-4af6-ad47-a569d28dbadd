package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum LeechLizardVariant {
   GREEN(0),
   YELLOW(1),
   BLUE(2),
   TAN(3),
   WHITE(4);

   private static final LeechLizardVariant[] BY_ID = (LeechLizardVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(LeechLizardVariant::getId)).toArray((x$0) -> {
      return new LeechLizardVariant[x$0];
   });
   private final int id;
   public static final Map<LeechLizardVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(LeechLizardVariant.class), (variant) -> {
      variant.put(GREEN, new ResourceLocation("tensura", "textures/entity/leech_lizard/leech_lizard.png"));
      variant.put(YELLOW, new ResourceLocation("tensura", "textures/entity/leech_lizard/leech_lizard_yellow.png"));
      variant.put(BLUE, new ResourceLocation("tensura", "textures/entity/leech_lizard/leech_lizard_blue.png"));
      variant.put(TAN, new ResourceLocation("tensura", "textures/entity/leech_lizard/leech_lizard_tan.png"));
      variant.put(WHITE, new ResourceLocation("tensura", "textures/entity/leech_lizard/leech_lizard_white.png"));
   });

   private LeechLizardVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static LeechLizardVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static LeechLizardVariant[] $values() {
      return new LeechLizardVariant[]{GREEN, YELLOW, BLUE, TAN, WHITE};
   }
}
