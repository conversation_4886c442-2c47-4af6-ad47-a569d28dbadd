package com.github.manasmods.tensura.entity.magic.spike;

import com.github.manasmods.tensura.ability.skill.unique.AntiSkill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.NbtUtils;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.PickaxeItem;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;

public class PillarEntity extends SpikeEntity {
   private static final EntityDataAccessor<Optional<BlockState>> STATE;
   private static final EntityDataAccessor<Float> HEALTH;

   public PillarEntity(EntityType<? extends PillarEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public PillarEntity(Level pLevel, LivingEntity pOwner) {
      this((EntityType)TensuraEntityTypes.EARTH_PILLAR.get(), pLevel);
      this.setOwner(pOwner);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(HEALTH, 5.0F);
      this.f_19804_.m_135372_(STATE, Optional.of(Blocks.f_50493_.m_49966_()));
   }

   protected void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setHealth(pCompound.m_128457_("Health"));
      this.setBlockState(NbtUtils.m_129241_(pCompound.m_128469_("BlockState")));
   }

   protected void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128350_("Health", this.getHealth());
      if (this.getBlockState().isPresent()) {
         pCompound.m_128365_("BlockState", NbtUtils.m_129202_((BlockState)this.getBlockState().get()));
      }

   }

   public void setBlockState(BlockState state) {
      this.f_19804_.m_135381_(STATE, Optional.of(state));
      this.setHealth(state.m_60734_().m_155943_() * 100.0F);
   }

   public Optional<BlockState> getBlockState() {
      return (Optional)this.f_19804_.m_135370_(STATE);
   }

   public void setHealth(float pDamageTaken) {
      this.f_19804_.m_135381_(HEALTH, pDamageTaken);
   }

   public float getHealth() {
      return (Float)this.f_19804_.m_135370_(HEALTH);
   }

   public boolean shouldPushUp() {
      return true;
   }

   public void applyEffect(LivingEntity target) {
      if (this.getTickCount() <= this.getExtendingTick()) {
         DamageSource damageSource = TensuraDamageSources.indirectElementalAttack("tensura.earth_attack", this, this.getOwner(), true);
         target.m_6469_(damageSource, this.getDamage());
      }
   }

   public void onBreak() {
      if (this.getBlockState().isPresent()) {
         BlockState state = (BlockState)this.getBlockState().get();
         ParticleOptions particle = new BlockParticleOption(ParticleTypes.f_123794_, state);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, particle);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, particle);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), state.m_60734_().m_49962_(state).m_56775_(), SoundSource.PLAYERS, 1.0F, 1.0F);
      }

      this.m_146870_();
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.f_19853_.m_5776_() && !this.m_213877_()) {
            Entity var4 = pSource.m_7640_();
            if (var4 instanceof LivingEntity) {
               LivingEntity attacker = (LivingEntity)var4;
               if (((AntiSkill)UniqueSkills.ANTI_SKILL.get()).isInSlot(attacker)) {
                  pAmount = this.getHealth();
               } else if (attacker.m_21205_().m_41720_() instanceof PickaxeItem) {
                  pAmount *= 2.0F;
               }
            }

            this.setHealth(this.getHealth() - pAmount);
            this.m_5834_();
            this.m_146852_(GameEvent.f_223706_, pSource.m_7639_());
            if (this.getHealth() <= 0.0F) {
               this.onBreak();
            } else if (this.getBlockState().isPresent()) {
               BlockState state = (BlockState)this.getBlockState().get();
               TensuraParticleHelper.addServerParticlesAroundSelf(this, new BlockParticleOption(ParticleTypes.f_123794_, state));
               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), state.m_60734_().m_49962_(state).m_56778_(), this.m_5720_(), 2.0F, 1.0F);
            }
         }

         return true;
      }
   }

   static {
      STATE = SynchedEntityData.m_135353_(PillarEntity.class, EntityDataSerializers.f_135034_);
      HEALTH = SynchedEntityData.m_135353_(PillarEntity.class, EntityDataSerializers.f_135029_);
   }
}
