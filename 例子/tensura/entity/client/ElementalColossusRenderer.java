package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class ElementalColossusRenderer extends GeoEntityRenderer<ElementalColossusEntity> {
   public ElementalColossusRenderer(Context renderManager) {
      super(renderManager, new ElementalColossusModel());
      this.f_114477_ = 0.5F;
   }

   protected float getDeathMaxRotation(ElementalColossusEntity animatable) {
      return 0.0F;
   }

   public ResourceLocation getTextureLocation(ElementalColossusEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/elemental_colossus/elemental_colossus.png");
   }

   public RenderType getRenderType(ElementalColossusEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(1.5F, 1.5F, 1.5F);
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
