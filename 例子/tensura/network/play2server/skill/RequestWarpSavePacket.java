package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.menu.SpatialMenu;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestWarpSavePacket {
   private final double savedX1;
   private final double savedY1;
   private final double savedZ1;
   private final double savedX2;
   private final double savedY2;
   private final double savedZ2;
   private final double savedX3;
   private final double savedY3;
   private final double savedZ3;
   private final String savedName1;
   private final String savedName2;
   private final String savedName3;

   public RequestWarpSavePacket(FriendlyByteBuf buf) {
      this.savedX1 = buf.readDouble();
      this.savedY1 = buf.readDouble();
      this.savedZ1 = buf.readDouble();
      this.savedX2 = buf.readDouble();
      this.savedY2 = buf.readDouble();
      this.savedZ2 = buf.readDouble();
      this.savedX3 = buf.readDouble();
      this.savedY3 = buf.readDouble();
      this.savedZ3 = buf.readDouble();
      this.savedName1 = buf.m_130277_();
      this.savedName2 = buf.m_130277_();
      this.savedName3 = buf.m_130277_();
   }

   public RequestWarpSavePacket(double x1, double y1, double z1, double x2, double y2, double z2, double x3, double y3, double z3, String name1, String name2, String name3) {
      this.savedX1 = x1;
      this.savedY1 = y1;
      this.savedZ1 = z1;
      this.savedX2 = x2;
      this.savedY2 = y2;
      this.savedZ2 = z2;
      this.savedX3 = x3;
      this.savedY3 = y3;
      this.savedZ3 = z3;
      this.savedName1 = name1;
      this.savedName2 = name2;
      this.savedName3 = name3;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeDouble(this.savedX1);
      buf.writeDouble(this.savedY1);
      buf.writeDouble(this.savedZ1);
      buf.writeDouble(this.savedX2);
      buf.writeDouble(this.savedY2);
      buf.writeDouble(this.savedZ2);
      buf.writeDouble(this.savedX3);
      buf.writeDouble(this.savedY3);
      buf.writeDouble(this.savedZ3);
      buf.m_130070_(this.savedName1);
      buf.m_130070_(this.savedName2);
      buf.m_130070_(this.savedName3);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            if (player.f_36096_ instanceof SpatialMenu) {
               TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setWarp(0, this.savedX1, this.savedY1, this.savedZ1);
                  cap.setWarp(1, this.savedX2, this.savedY2, this.savedZ2);
                  cap.setWarp(2, this.savedX3, this.savedY3, this.savedZ3);
                  cap.setWarpName(0, this.savedName1);
                  cap.setWarpName(1, this.savedName2);
                  cap.setWarpName(2, this.savedName3);
               });
               TensuraSkillCapability.sync(player);
            }

         }
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
