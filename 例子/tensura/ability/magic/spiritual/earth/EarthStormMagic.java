package com.github.manasmods.tensura.ability.magic.spiritual.earth;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.StoneShotProjectile;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import java.util.Iterator;
import java.util.List;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class EarthStormMagic extends SpiritualMagic {
   public EarthStormMagic() {
      super(MagicElemental.EARTH, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 120;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 300.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         Level level = entity.m_9236_();
         if (heldTicks >= castTime) {
            if (heldTicks == castTime + 1) {
               if (SkillHelper.outOfMagicule(entity, instance)) {
                  return false;
               }

               this.addMasteryPoint(instance, entity);
            }

            if (heldTicks % 20 == 0) {
               if (heldTicks > castTime && SkillHelper.outOfMagicule(entity, 50.0D)) {
                  return false;
               }

               List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(5.0D), (living) -> {
                  return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
               });
               if (!list.isEmpty()) {
                  Iterator var8 = list.iterator();

                  label63:
                  while(true) {
                     LivingEntity target;
                     Player player;
                     do {
                        if (!var8.hasNext()) {
                           break label63;
                        }

                        target = (LivingEntity)var8.next();
                        if (!(target instanceof Player)) {
                           break;
                        }

                        player = (Player)target;
                     } while(player.m_150110_().f_35934_);

                     if ((double)target.m_217043_().m_188501_() <= 0.3D) {
                        target.m_7292_(new MobEffectInstance(MobEffects.f_19620_, 40, 0, false, false, false));
                     }

                     this.spawnStoneSpike(instance, entity, target);
                  }
               }
            }

            if (heldTicks % 2 == 0) {
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return entity;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:earth_storm"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
            }

            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return heldTicks - castTime <= (instance.isMastered(entity) ? 600 : 300);
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               this.addCastingParticle(instance, player, heldTicks);
            }

            return true;
         }
      }
   }

   private void spawnStoneSpike(ManasSkillInstance instance, LivingEntity owner, LivingEntity target) {
      StoneShotProjectile stoneShot = new StoneShotProjectile(owner.m_9236_(), owner);
      stoneShot.setSpeed(1.0F);
      stoneShot.setDamage(20.0F);
      stoneShot.setMpCost(this.magiculeCost(owner, instance));
      stoneShot.setSkill(instance);
      stoneShot.setSpiritAttack(true);
      stoneShot.m_6034_(target.m_20185_(), target.m_20188_() + 4.0D, target.m_20189_());
      owner.m_9236_().m_7967_(stoneShot);
   }
}
