package com.github.manasmods.tensura.entity.client.barrier;

import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;

public class BarrierRenderer extends EntityRenderer<BarrierEntity> {
   public static final ModelLayerLocation BARRIER = new ModelLayerLocation(new ResourceLocation("tensura", "barrier"), "main");
   private final ModelPart barrier;

   public BarrierRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(BARRIER);
      this.barrier = modelpart.m_171324_("Barrier");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      partdefinition.m_171599_("Barrier", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-8.0F, -23.0F, -8.0F, 16.0F, 16.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 24.0F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 64, 32);
   }

   public void render(BarrierEntity entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !entity.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         float radius = entity.getVisualRadius() * 2.0F;
         poseStack.m_85841_(radius, radius, radius);
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(entity)));
         this.barrier.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         super.m_7392_(entity, yaw, partialTicks, poseStack, bufferSource, light);
      }
   }

   public ResourceLocation getTextureLocation(BarrierEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/barrier/barrier.png");
   }
}
