package com.github.manasmods.tensura.registry.dimensions;

import com.github.manasmods.tensura.registry.biome.TensuraBiomes;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ImmutableList.Builder;
import java.util.List;
import net.minecraft.core.Holder;
import net.minecraft.core.Registry;
import net.minecraft.data.BuiltinRegistries;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.ai.village.poi.PoiType;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.dimension.DimensionType;
import net.minecraft.world.level.levelgen.DensityFunction;
import net.minecraft.world.level.levelgen.DensityFunctions;
import net.minecraft.world.level.levelgen.NoiseGeneratorSettings;
import net.minecraft.world.level.levelgen.NoiseRouter;
import net.minecraft.world.level.levelgen.NoiseRouterData;
import net.minecraft.world.level.levelgen.NoiseSettings;
import net.minecraft.world.level.levelgen.Noises;
import net.minecraft.world.level.levelgen.SurfaceRules;
import net.minecraft.world.level.levelgen.SurfaceRules.ConditionSource;
import net.minecraft.world.level.levelgen.SurfaceRules.RuleSource;
import net.minecraft.world.level.levelgen.synth.NormalNoise.NoiseParameters;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraDimensions {
   public static final DeferredRegister<PoiType> POI;
   public static final DeferredRegister<NoiseGeneratorSettings> NOISE;
   public static final ResourceKey<Level> LABYRINTH;
   public static final ResourceKey<DimensionType> LABYRINTH_TYPE;
   public static final RegistryObject<PoiType> LABYRINTH_PORTAL;
   public static final ResourceKey<Level> HELL;
   public static final ResourceKey<DimensionType> HELL_TYPE;
   public static final RegistryObject<NoiseGeneratorSettings> HELL_NOISE;
   public static final RegistryObject<PoiType> HELL_PORTAL;

   public static void init(IEventBus modEventBus) {
      POI.register(modEventBus);
      NOISE.register(modEventBus);
   }

   static {
      POI = DeferredRegister.create(ForgeRegistries.POI_TYPES, "tensura");
      NOISE = DeferredRegister.create(Registry.f_122878_, "tensura");
      LABYRINTH = ResourceKey.m_135785_(Registry.f_122819_, new ResourceLocation("tensura", "labyrinth"));
      LABYRINTH_TYPE = ResourceKey.m_135785_(Registry.f_122818_, LABYRINTH.m_135782_());
      LABYRINTH_PORTAL = POI.register("labyrinth_portal", () -> {
         return new PoiType(ImmutableSet.copyOf(((Block)TensuraBlocks.LABYRINTH_PORTAL.get()).m_49965_().m_61056_()), 0, 1);
      });
      HELL = ResourceKey.m_135785_(Registry.f_122819_, new ResourceLocation("tensura", "hell"));
      HELL_TYPE = ResourceKey.m_135785_(Registry.f_122818_, HELL.m_135782_());
      HELL_NOISE = NOISE.register("hell", () -> {
         return TensuraDimensions.GenerationNoises.HELL;
      });
      HELL_PORTAL = POI.register("hell_portal", () -> {
         return new PoiType(ImmutableSet.copyOf(((Block)TensuraBlocks.HELL_PORTAL.get()).m_49965_().m_61056_()), 0, 1);
      });
   }

   public static class GenerationNoises extends NoiseRouterData {
      private static final DensityFunction ZERO = DensityFunctions.m_208263_();
      public static final NoiseGeneratorSettings HELL;

      private static Holder<NoiseParameters> m_209542_(ResourceKey<NoiseParameters> key) {
         return BuiltinRegistries.f_194654_.m_206081_(key);
      }

      private static DensityFunction m_224443_(DensityFunction func, int i, int i1, int i2, int i3, double v, int i4, int i5, double v1) {
         DensityFunction func1 = DensityFunctions.m_208266_(i + i1 - i2, i + i1 - i3, 1.0D, 0.0D);
         DensityFunction toReturn = DensityFunctions.m_224030_(func1, v, func);
         DensityFunction func2 = DensityFunctions.m_208266_(i + i4, i + i5, 0.0D, 1.0D);
         toReturn = DensityFunctions.m_224030_(func2, v1, toReturn);
         return toReturn;
      }

      private static DensityFunction m_224439_(DensityFunction func, int i, int i1) {
         return m_224443_(func, i, i1, 72, -184, -23.4375D, 4, 32, -0.234375D);
      }

      private static NoiseRouter hell() {
         DensityFunction uFunc1 = DensityFunctions.m_208361_(DensityFunctions.m_208373_(DensityFunctions.m_208366_(m_209542_(Noises.f_189286_))));
         DensityFunction uFunc2 = DensityFunctions.m_208361_(DensityFunctions.m_208373_(DensityFunctions.m_208378_(m_209542_(Noises.f_189286_))));
         DensityFunction temperature = DensityFunctions.m_208361_(DensityFunctions.m_208296_(uFunc1, uFunc2, 0.25D, m_209542_(Noises.f_189269_)));
         DensityFunction vegetation = DensityFunctions.m_208361_(DensityFunctions.m_208296_(uFunc1, uFunc2, 0.25D, m_209542_(Noises.f_189278_)));
         DensityFunction continentalness = DensityFunctions.m_208361_(DensityFunctions.m_208296_(uFunc1, uFunc2, 0.25D, m_209542_(Noises.f_189279_)));
         DensityFunction erosion = DensityFunctions.m_208361_(DensityFunctions.m_208296_(uFunc1, uFunc2, 0.25D, m_209542_(Noises.f_189280_)));
         DensityFunction uFunc3 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(0.05D), DensityFunctions.m_208368_((Holder)TensuraNoises.HELL.getHolder().orElseThrow(), 0.6D, 1.0D));
         DensityFunction uFunc4 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(0.4D), uFunc3);
         DensityFunction uFunc5 = DensityFunctions.m_208363_(DensityFunctions.m_208266_(16, 128, 1.0D, 0.0D), uFunc4);
         DensityFunction uFunc6 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(-0.2D), uFunc5);
         DensityFunction uFunc7 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(0.1D), uFunc6);
         DensityFunction uFunc8 = DensityFunctions.m_208363_(DensityFunctions.m_208266_(0, 48, 0.0D, 1.0D), uFunc7);
         DensityFunction uFunc9 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(-0.1D), uFunc8);
         DensityFunction uFunc10 = DensityFunctions.m_208293_(DensityFunctions.m_208264_(-0.05D), uFunc9);
         DensityFunction finalDensity = DensityFunctions.m_208281_(DensityFunctions.m_208389_(m_224439_(uFunc10, 0, 128))).m_208234_();
         return new NoiseRouter(ZERO, ZERO, ZERO, ZERO, temperature, vegetation, continentalness, erosion, ZERO, ZERO, ZERO, finalDensity, ZERO, ZERO, ZERO);
      }

      static {
         HELL = new NoiseGeneratorSettings(NoiseSettings.m_224525_(0, 128, 2, 1), Blocks.f_152491_.m_49966_(), Blocks.f_50016_.m_49966_(), hell(), TensuraDimensions.Rules.HELL_DIMENSION, List.of(), 0, false, false, false, false);
      }
   }

   public static class Rules {
      private static final RuleSource SAND;
      private static final RuleSource RED_SAND;
      private static final RuleSource SANDSTONE;
      private static final RuleSource RED_SANDSTONE;
      private static final RuleSource STONE;
      private static final RuleSource DIORITE;
      private static final RuleSource GRANITE;
      private static final RuleSource ANDESITE;
      public static final RuleSource HELL_DIMENSION;

      private static RuleSource makeStateRule(Block pBlock) {
         return SurfaceRules.m_189390_(pBlock.m_49966_());
      }

      private static RuleSource hell() {
         ConditionSource onCeilingLayer = SurfaceRules.f_189377_;
         ConditionSource underCeilingLayer = SurfaceRules.f_189378_;
         ConditionSource topLayer = SurfaceRules.f_189375_;
         ConditionSource middleLayer = SurfaceRules.f_189376_;
         ConditionSource preBottomLayer = SurfaceRules.f_202169_;
         ConditionSource bottomLayer = SurfaceRules.f_202170_;
         ConditionSource barrens = SurfaceRules.m_189416_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_BARRENS.getKey()});
         RuleSource barrensSurface = SurfaceRules.m_198272_(new RuleSource[]{SurfaceRules.m_189394_(topLayer, STONE), SurfaceRules.m_189394_(middleLayer, GRANITE), SurfaceRules.m_189394_(bottomLayer, DIORITE), ANDESITE});
         ConditionSource spikes = SurfaceRules.m_189416_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_SPIKES.getKey()});
         RuleSource spikesSurface = SurfaceRules.m_198272_(new RuleSource[]{SurfaceRules.m_189394_(topLayer, ANDESITE), SurfaceRules.m_189394_(middleLayer, STONE), SurfaceRules.m_189394_(bottomLayer, GRANITE), DIORITE});
         ConditionSource sands = SurfaceRules.m_189416_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_SANDS.getKey()});
         RuleSource sandsSurface = SurfaceRules.m_198272_(new RuleSource[]{SurfaceRules.m_189394_(topLayer, SAND), SANDSTONE});
         ConditionSource redSands = SurfaceRules.m_189416_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_RED_SANDS.getKey()});
         RuleSource redSandSurface = SurfaceRules.m_198272_(new RuleSource[]{SurfaceRules.m_189394_(topLayer, RED_SAND), RED_SANDSTONE});
         Builder<RuleSource> builder = ImmutableList.builder();
         builder.add(SurfaceRules.m_189394_(barrens, barrensSurface));
         builder.add(SurfaceRules.m_189394_(spikes, spikesSurface));
         builder.add(SurfaceRules.m_189394_(sands, sandsSurface));
         builder.add(SurfaceRules.m_189394_(redSands, redSandSurface));
         return SurfaceRules.m_198272_((RuleSource[])builder.build().toArray((x$0) -> {
            return new RuleSource[x$0];
         }));
      }

      static {
         SAND = makeStateRule(Blocks.f_49992_);
         RED_SAND = makeStateRule(Blocks.f_49993_);
         SANDSTONE = makeStateRule(Blocks.f_50062_);
         RED_SANDSTONE = makeStateRule(Blocks.f_50394_);
         STONE = makeStateRule(Blocks.f_50069_);
         DIORITE = makeStateRule(Blocks.f_50228_);
         GRANITE = makeStateRule(Blocks.f_50122_);
         ANDESITE = makeStateRule(Blocks.f_50334_);
         HELL_DIMENSION = hell();
      }
   }
}
