package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.ItemModelProvider;
import com.github.manasmods.tensura.block.SimpleBlock;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.items.TensuraSpawnEggs;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.FenceBlock;
import net.minecraft.world.level.block.FenceGateBlock;
import net.minecraft.world.level.block.PressurePlateBlock;
import net.minecraft.world.level.block.SlabBlock;
import net.minecraft.world.level.block.StairBlock;
import net.minecraft.world.level.block.TrapDoorBlock;
import net.minecraft.world.level.block.WallBlock;
import net.minecraft.world.level.block.WoodButtonBlock;
import net.minecraftforge.client.model.generators.ItemModelBuilder;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraItemModelProvider extends ItemModelProvider {
   public TensuraItemModelProvider(GatherDataEvent gatherDataEvent) {
      super(gatherDataEvent, "tensura");
   }

   protected void generate() {
      this.generateWeapons();
      this.generateSpawnEggs();
      this.generateWoodenBlocks();
      this.generateMiscItems();
   }

   private void generateWeapons() {
      this.longSword(TensuraToolItems.WOODEN_LONG_SWORD);
      this.greatSword(TensuraToolItems.WOODEN_GREAT_SWORD);
      this.tachi(TensuraToolItems.WOODEN_TACHI);
      this.odachi(TensuraToolItems.WOODEN_ODACHI);
      this.spear(TensuraToolItems.WOODEN_SPEAR);
      this.scythe(TensuraToolItems.WOODEN_SCYTHE);
      this.longSword(TensuraToolItems.STONE_LONG_SWORD);
      this.greatSword(TensuraToolItems.STONE_GREAT_SWORD);
      this.tachi(TensuraToolItems.STONE_TACHI);
      this.odachi(TensuraToolItems.STONE_ODACHI);
      this.spear(TensuraToolItems.STONE_SPEAR);
      this.scythe(TensuraToolItems.STONE_SCYTHE);
      this.longSword(TensuraToolItems.IRON_LONG_SWORD);
      this.greatSword(TensuraToolItems.IRON_GREAT_SWORD);
      this.tachi(TensuraToolItems.IRON_TACHI);
      this.odachi(TensuraToolItems.IRON_ODACHI);
      this.spear(TensuraToolItems.IRON_SPEAR);
      this.scythe(TensuraToolItems.IRON_SCYTHE);
      this.longSword(TensuraToolItems.SILVER_LONG_SWORD);
      this.greatSword(TensuraToolItems.SILVER_GREAT_SWORD);
      this.tachi(TensuraToolItems.SILVER_TACHI);
      this.odachi(TensuraToolItems.SILVER_ODACHI);
      this.spear(TensuraToolItems.SILVER_SPEAR);
      this.scythe(TensuraToolItems.SILVER_SCYTHE);
      this.longSword(TensuraToolItems.GOLDEN_LONG_SWORD);
      this.greatSword(TensuraToolItems.GOLDEN_GREAT_SWORD);
      this.tachi(TensuraToolItems.GOLDEN_TACHI);
      this.odachi(TensuraToolItems.GOLDEN_ODACHI);
      this.spear(TensuraToolItems.GOLDEN_SPEAR);
      this.scythe(TensuraToolItems.GOLDEN_SCYTHE);
      this.longSword(TensuraToolItems.DIAMOND_LONG_SWORD);
      this.greatSword(TensuraToolItems.DIAMOND_GREAT_SWORD);
      this.tachi(TensuraToolItems.DIAMOND_TACHI);
      this.odachi(TensuraToolItems.DIAMOND_ODACHI);
      this.spear(TensuraToolItems.DIAMOND_SPEAR);
      this.scythe(TensuraToolItems.DIAMOND_SCYTHE);
      this.longSword(TensuraToolItems.NETHERITE_LONG_SWORD);
      this.greatSword(TensuraToolItems.NETHERITE_GREAT_SWORD);
      this.tachi(TensuraToolItems.NETHERITE_TACHI);
      this.odachi(TensuraToolItems.NETHERITE_ODACHI);
      this.spear(TensuraToolItems.NETHERITE_SPEAR);
      this.scythe(TensuraToolItems.NETHERITE_SCYTHE);
      this.longSword(TensuraToolItems.LOW_MAGISTEEL_LONG_SWORD);
      this.greatSword(TensuraToolItems.LOW_MAGISTEEL_GREAT_SWORD);
      this.tachi(TensuraToolItems.LOW_MAGISTEEL_TACHI);
      this.odachi(TensuraToolItems.LOW_MAGISTEEL_ODACHI);
      this.spear(TensuraToolItems.LOW_MAGISTEEL_SPEAR);
      this.scythe(TensuraToolItems.LOW_MAGISTEEL_SCYTHE);
      this.longSword(TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD);
      this.greatSword(TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD);
      this.tachi(TensuraToolItems.HIGH_MAGISTEEL_TACHI);
      this.odachi(TensuraToolItems.HIGH_MAGISTEEL_ODACHI);
      this.spear(TensuraToolItems.HIGH_MAGISTEEL_SPEAR);
      this.scythe(TensuraToolItems.HIGH_MAGISTEEL_SCYTHE);
      this.longSword(TensuraToolItems.MITHRIL_LONG_SWORD);
      this.greatSword(TensuraToolItems.MITHRIL_GREAT_SWORD);
      this.tachi(TensuraToolItems.MITHRIL_TACHI);
      this.odachi(TensuraToolItems.MITHRIL_ODACHI);
      this.spear(TensuraToolItems.MITHRIL_SPEAR);
      this.scythe(TensuraToolItems.MITHRIL_SCYTHE);
      this.longSword(TensuraToolItems.ORICHALCUM_LONG_SWORD);
      this.greatSword(TensuraToolItems.ORICHALCUM_GREAT_SWORD);
      this.tachi(TensuraToolItems.ORICHALCUM_TACHI);
      this.odachi(TensuraToolItems.ORICHALCUM_ODACHI);
      this.spear(TensuraToolItems.ORICHALCUM_SPEAR);
      this.scythe(TensuraToolItems.ORICHALCUM_SCYTHE);
      this.longSword(TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD);
      this.greatSword(TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD);
      this.tachi(TensuraToolItems.PURE_MAGISTEEL_TACHI);
      this.odachi(TensuraToolItems.PURE_MAGISTEEL_ODACHI);
      this.spear(TensuraToolItems.PURE_MAGISTEEL_SPEAR);
      this.scythe(TensuraToolItems.PURE_MAGISTEEL_SCYTHE);
      this.longSword(TensuraToolItems.ADAMANTITE_LONG_SWORD);
      this.greatSword(TensuraToolItems.ADAMANTITE_GREAT_SWORD);
      this.tachi(TensuraToolItems.ADAMANTITE_TACHI);
      this.odachi(TensuraToolItems.ADAMANTITE_ODACHI);
      this.spear(TensuraToolItems.ADAMANTITE_SPEAR);
      this.scythe(TensuraToolItems.ADAMANTITE_SCYTHE);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_SWORD);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_SHORT_SWORD);
      this.handheldHihiirokane(TensuraToolItems.HIHIIROKANE_LONG_SWORD, "item/long_sword_handheld");
      this.handheldHihiirokane(TensuraToolItems.HIHIIROKANE_GREAT_SWORD, "item/great_sword_handheld");
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_KATANA);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_KODACHI);
      this.handheldHihiirokane(TensuraToolItems.HIHIIROKANE_TACHI, "item/tachi_handheld");
      this.handheldHihiirokane(TensuraToolItems.HIHIIROKANE_ODACHI, "item/odachi_handheld");
      this.handheldHihiirokane(TensuraToolItems.HIHIIROKANE_SCYTHE, "item/scythe_handheld");
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_PICKAXE);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_AXE);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_SHOVEL);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_HOE);
      this.handheldHihiirokaneMinecraft(TensuraToolItems.HIHIIROKANE_SICKLE);
      RegistryObject<Item> item = TensuraToolItems.HIHIIROKANE_SPEAR;
      ResourceLocation texture = this.modLoc("item/" + item.getId().m_135815_());
      ItemModelBuilder spear = ((ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), this.modLoc("item/spear_handheld"))).texture("layer0", texture)).override().predicate(new ResourceLocation("inactive"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_inactive", this.modLoc("item/spear_handheld"))).texture("layer0", texture + "_inactive")).end();
      spear.override().predicate(new ResourceLocation("throwing"), 1.0F).model(((ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_throwing", this.modLoc("item/spear_handheld_throwing"))).texture("layer0", texture)).override().predicate(new ResourceLocation("inactive"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_throwing_inactive", this.modLoc("item/spear_handheld_throwing"))).texture("layer0", texture + "_inactive")).end());
      this.spear(TensuraToolItems.BEAST_HORN_SPEAR);
      this.spear(TensuraToolItems.UNICORN_HORN_SPEAR);
      this.scythe(TensuraToolItems.BLADE_TIGER_SCYTHE);
      this.bow(TensuraToolItems.SHORT_BOW, "short_bow");
      this.bow(TensuraToolItems.LONG_BOW, "long_bow");
      this.bow(TensuraToolItems.WAR_BOW, "war_bow");
      this.bow(TensuraToolItems.SHORT_SPIDER_BOW, "short_bow");
      this.bow(TensuraToolItems.SPIDER_BOW, "bow");
      this.bow(TensuraToolItems.LONG_SPIDER_BOW, "long_bow");
      this.bow(TensuraToolItems.WAR_SPIDER_BOW, "war_spider_bow");
   }

   private void generateSpawnEggs() {
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_FOLGEN);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_HINATA_SAKAGUCHI);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_KIRARA_MIZUTANI);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_KYOYA_TACHIBANA);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_MAI_FURUKI);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_MARK_LAUREN);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_SHINJI_TANIMURA);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_SHIN_RYUSEI);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_SHIZU);
      this.spawnEggItem(TensuraSpawnEggs.OTHERWORLDER_SHOGO_TAGUCHI);
      this.spawnEggItem(TensuraSpawnEggs.AKASH);
      this.spawnEggItem(TensuraSpawnEggs.AQUA_FROG);
      this.spawnEggItem(TensuraSpawnEggs.ARMOURSAURUS);
      this.spawnEggItem(TensuraSpawnEggs.ARMY_WASP);
      this.spawnEggItem(TensuraSpawnEggs.BARGHEST);
      this.spawnEggItem(TensuraSpawnEggs.BEAST_GNOME);
      this.spawnEggItem(TensuraSpawnEggs.BLACK_SPIDER);
      this.spawnEggItem(TensuraSpawnEggs.BLADE_TIGER);
      this.spawnEggItem(TensuraSpawnEggs.BULLDEER);
      this.spawnEggItem(TensuraSpawnEggs.CHARYBDIS);
      this.spawnEggItem(TensuraSpawnEggs.DIREWOLF);
      this.spawnEggItem(TensuraSpawnEggs.DRAGON_PEACOCK);
      this.spawnEggItem(TensuraSpawnEggs.ELEMENTAL_COLOSSUS);
      this.spawnEggItem(TensuraSpawnEggs.EVIL_CENTIPEDE);
      this.spawnEggItem(TensuraSpawnEggs.FEATHERED_SERPENT);
      this.spawnEggItem(TensuraSpawnEggs.GIANT_ANT);
      this.spawnEggItem(TensuraSpawnEggs.GIANT_BAT);
      this.spawnEggItem(TensuraSpawnEggs.GIANT_BEAR);
      this.spawnEggItem(TensuraSpawnEggs.GIANT_COD);
      this.spawnEggItem(TensuraSpawnEggs.GIANT_SALMON);
      this.spawnEggItem(TensuraSpawnEggs.GOBLIN);
      this.spawnEggItem(TensuraSpawnEggs.HOLY_COW);
      this.spawnEggItem(TensuraSpawnEggs.HELL_CATERPILLAR);
      this.spawnEggItem(TensuraSpawnEggs.HELL_MOTH);
      this.spawnEggItem(TensuraSpawnEggs.HORNED_BEAR);
      this.spawnEggItem(TensuraSpawnEggs.HORNED_RABBIT);
      this.spawnEggItem(TensuraSpawnEggs.HOUND_DOG);
      this.spawnEggItem(TensuraSpawnEggs.HOVER_LIZARD);
      this.spawnEggItem(TensuraSpawnEggs.IFRIT);
      this.spawnEggItem(TensuraSpawnEggs.KNIGHT_SPIDER);
      this.spawnEggItem(TensuraSpawnEggs.LANDFISH);
      this.spawnEggItem(TensuraSpawnEggs.LEECH_LIZARD);
      this.spawnEggItem(TensuraSpawnEggs.LIZARDMAN);
      this.spawnEggItem(TensuraSpawnEggs.MEGALODON);
      this.spawnEggItem(TensuraSpawnEggs.ONE_EYED_OWL);
      this.spawnEggItem(TensuraSpawnEggs.ORC);
      this.spawnEggItem(TensuraSpawnEggs.ORC_LORD);
      this.spawnEggItem(TensuraSpawnEggs.ORC_DISASTER);
      this.spawnEggItem(TensuraSpawnEggs.SALAMANDER);
      this.spawnEggItem(TensuraSpawnEggs.SISSIE);
      this.spawnEggItem(TensuraSpawnEggs.SLIME);
      this.spawnEggItem(TensuraSpawnEggs.METAL_SLIME);
      this.spawnEggItem(TensuraSpawnEggs.SUPERMASSIVE_SLIME);
      this.spawnEggItem(TensuraSpawnEggs.SPEAR_TORO);
      this.spawnEggItem(TensuraSpawnEggs.SYLPHIDE);
      this.spawnEggItem(TensuraSpawnEggs.TEMPEST_SERPENT);
      this.spawnEggItem(TensuraSpawnEggs.UNDINE);
      this.spawnEggItem(TensuraSpawnEggs.UNICORN);
      this.spawnEggItem(TensuraSpawnEggs.WAR_GNOME);
      this.spawnEggItem(TensuraSpawnEggs.WINGED_CAT);
   }

   private void generateWoodenBlocks() {
      this.singleTexture((Item)TensuraBlocks.Items.SAKURA_SAPLING.get());
      this.fenceItem(TensuraBlocks.SAKURA_FENCE, TensuraBlocks.SAKURA_PLANKS);
      this.buttonItem(TensuraBlocks.SAKURA_BUTTON, TensuraBlocks.SAKURA_PLANKS);
      this.woodenPlateItem(TensuraBlocks.SAKURA_PRESSURE_PLATE);
      this.fenceGateItem(TensuraBlocks.SAKURA_FENCE_GATE);
      this.trapdoorItem(TensuraBlocks.SAKURA_TRAPDOOR);
      this.singleTexture((Item)TensuraBlocks.Items.SAKURA_SIGN.get());
      this.singleTexture((Item)TensuraBlocks.Items.SAKURA_DOOR.get());
      this.singleTexture((Item)TensuraBlocks.Items.PALM_SAPLING.get());
      this.fenceItem(TensuraBlocks.PALM_FENCE, TensuraBlocks.PALM_PLANKS);
      this.buttonItem(TensuraBlocks.PALM_BUTTON, TensuraBlocks.PALM_PLANKS);
      this.woodenPlateItem(TensuraBlocks.PALM_PRESSURE_PLATE);
      this.fenceGateItem(TensuraBlocks.PALM_FENCE_GATE);
      this.trapdoorItem(TensuraBlocks.PALM_TRAPDOOR);
      this.singleTexture((Item)TensuraBlocks.Items.PALM_DOOR.get());
      this.singleTexture((Item)TensuraBlocks.Items.PALM_SIGN.get());
   }

   private void generateMiscItems() {
      this.cubeBlockItem(TensuraBlocks.SLIME_CHUNK_BLOCK);
      this.cubeBlockItem(TensuraBlocks.CHILLED_SLIME_BLOCK);
      this.cubeBlockItem(TensuraBlocks.SPIDER_EGG);
      this.cubeBlockItem(TensuraBlocks.UNLIT_TORCH);
      this.singleTexture((Item)TensuraBlocks.Items.MOTH_EGG.get());
      this.singleTexture((Item)TensuraBlocks.Items.UNLIT_LANTERN.get());
      this.singleTexture((Item)TensuraBlocks.Items.CHARYBDIS_CORE.get());
      this.simpleItem(TensuraBlocks.Items.STICKY_COBWEB, "block/sticky_cobweb");
      this.simpleItem(TensuraBlocks.Items.STICKY_STEEL_COBWEB, "block/sticky_steel_cobweb");
      this.simpleItem(TensuraBlocks.Items.LABYRINTH_BARRIER_BLOCK, "item/yellow_barrier");
      this.simpleItem(TensuraBlocks.Items.LABYRINTH_PORTAL, "item/pink_barrier");
      this.wallItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.wallItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.wallItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS);
      this.cubeBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.cubeBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.cubeBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK);
      this.cubeBlockItem(TensuraBlocks.SARASA_SANDSTONE);
      this.stairBlockItem(TensuraBlocks.SARASA_SANDSTONE_STAIRS);
      this.slabBlockItem(TensuraBlocks.SARASA_SANDSTONE_SLAB);
      this.wallItem(TensuraBlocks.SARASA_SANDSTONE_WALL, TensuraBlocks.SARASA_SANDSTONE);
      this.cubeBlockItem(TensuraBlocks.CUT_SARASA_SANDSTONE);
      this.slabBlockItem(TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB);
      this.cubeBlockItem(TensuraBlocks.CHISELED_SARASA_SANDSTONE);
      this.webbedBlockItem(TensuraBlocks.WEB_BLOCK);
      this.webbedBlockItem(TensuraBlocks.WEBBED_COBBLESTONE);
      this.webbedBlockItem(TensuraBlocks.WEBBED_STONE_BRICKS);
      this.webbedSlabItem(TensuraBlocks.WEB_SLAB);
      this.webbedSlabItem(TensuraBlocks.WEBBED_COBBLESTONE_SLAB);
      this.webbedSlabItem(TensuraBlocks.WEBBED_STONE_BRICK_SLAB);
      this.webbedStairsItem(TensuraBlocks.WEB_STAIRS);
      this.webbedStairsItem(TensuraBlocks.WEBBED_COBBLESTONE_STAIRS);
      this.webbedStairsItem(TensuraBlocks.WEBBED_STONE_BRICK_STAIRS);
      this.webbedWallItem(TensuraBlocks.WEBBED_COBBLESTONE_WALL);
      this.webbedWallItem(TensuraBlocks.WEBBED_STONE_BRICK_WALL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LAMP);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LAMP_BL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LAMP_BR);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LAMP_TL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LAMP_TR);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_BL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_BR);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_TL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_TR);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_CRYSTAL);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH);
      this.slabBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH_SLAB);
      this.stairBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH_STAIRS);
      this.cubeBlockItem(TensuraBlocks.LABYRINTH_PRAYING_PATH);
   }

   private void simpleItem(RegistryObject<Item> item, String location) {
      ((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), new ResourceLocation("item/generated"))).texture("layer0", new ResourceLocation("tensura", location));
   }

   private ItemModelBuilder handheldModdedItem(RegistryObject<? extends Item> item, String parent) {
      return (ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), this.modLoc(parent))).texture("layer0", new ResourceLocation("tensura", "item/" + item.getId().m_135815_()));
   }

   private ItemModelBuilder handheldMinecraftItem(RegistryObject<? extends Item> item, String parent) {
      return (ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), this.mcLoc(parent))).texture("layer0", new ResourceLocation("tensura", "item/" + item.getId().m_135815_()));
   }

   private void handheldHihiirokane(RegistryObject<? extends Item> item, String parent) {
      ResourceLocation texture = this.modLoc("item/" + item.getId().m_135815_());
      this.handheldModdedItem(item, parent).override().predicate(new ResourceLocation("inactive"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_inactive", this.modLoc(parent))).texture("layer0", texture + "_inactive"));
   }

   private void handheldHihiirokaneMinecraft(RegistryObject<? extends Item> item) {
      ResourceLocation texture = this.modLoc("item/" + item.getId().m_135815_());
      this.handheldMinecraftItem(item, "item/handheld").override().predicate(new ResourceLocation("inactive"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_inactive", this.mcLoc("item/handheld"))).texture("layer0", texture + "_inactive"));
   }

   private ItemModelBuilder odachi(RegistryObject<Item> item) {
      return this.handheldModdedItem(item, "item/odachi_handheld");
   }

   private ItemModelBuilder greatSword(RegistryObject<Item> item) {
      return this.handheldModdedItem(item, "item/great_sword_handheld");
   }

   private ItemModelBuilder tachi(RegistryObject<Item> item) {
      return this.handheldModdedItem(item, "item/tachi_handheld");
   }

   private ItemModelBuilder longSword(RegistryObject<Item> item) {
      return this.handheldModdedItem(item, "item/long_sword_handheld");
   }

   private ItemModelBuilder scythe(RegistryObject<Item> item) {
      return this.handheldModdedItem(item, "item/scythe_handheld");
   }

   private ItemModelBuilder spear(RegistryObject<Item> item) {
      ResourceLocation texture = this.modLoc("item/" + item.getId().m_135815_());
      return ((ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), this.modLoc("item/spear_handheld"))).texture("layer0", texture)).override().predicate(new ResourceLocation("throwing"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_throwing", this.modLoc("item/spear_handheld_throwing"))).texture("layer0", texture)).end();
   }

   private void bow(RegistryObject<Item> item, String bowType) {
      String path = "item/" + item.getId().m_135815_();
      ResourceLocation type = this.modLoc("item/" + bowType + "_handheld");
      ItemModelBuilder modelBuilder = ((ItemModelBuilder)((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_(), type)).texture("layer0", this.modLoc(path))).override().predicate(new ResourceLocation("pulling"), 1.0F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_pulling_0", type)).texture("layer0", this.modLoc(path + "_pulling_0"))).end();
      modelBuilder.override().predicate(new ResourceLocation("pulling"), 1.0F).predicate(new ResourceLocation("pull"), 0.65F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_pulling_1", type)).texture("layer0", this.modLoc(path + "_pulling_1")));
      modelBuilder.override().predicate(new ResourceLocation("pulling"), 1.0F).predicate(new ResourceLocation("pull"), 0.9F).model(((ItemModelBuilder)this.withExistingParent(item.getId().m_135815_() + "_pulling_2", type)).texture("layer0", this.modLoc(path + "_pulling_2")));
   }

   public void spawnEggItem(RegistryObject<Item> item) {
      this.withExistingParent("tensura:" + ForgeRegistries.ITEMS.getKey((Item)item.get()).m_135815_(), new ResourceLocation("minecraft:item/template_spawn_egg"));
   }

   public void cubeBlockItem(RegistryObject<Block> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003));
   }

   public void slabBlockItem(RegistryObject<SlabBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003));
   }

   public void stairBlockItem(RegistryObject<StairBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003));
   }

   public void woodenPlateItem(RegistryObject<PressurePlateBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003));
   }

   public void fenceGateItem(RegistryObject<FenceGateBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003));
   }

   public void trapdoorItem(RegistryObject<TrapDoorBlock> block) {
      String var10001 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/" + var10003 + "_bottom"));
   }

   public void fenceItem(RegistryObject<FenceBlock> block, RegistryObject<SimpleBlock> baseBlock) {
      ItemModelBuilder var10000 = (ItemModelBuilder)this.withExistingParent(ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_(), this.mcLoc("block/fence_inventory"));
      String var10005 = ForgeRegistries.BLOCKS.getKey((Block)baseBlock.get()).m_135815_();
      var10000.texture("texture", new ResourceLocation("tensura", "block/" + var10005));
   }

   public void buttonItem(RegistryObject<WoodButtonBlock> block, RegistryObject<SimpleBlock> baseBlock) {
      ItemModelBuilder var10000 = (ItemModelBuilder)this.withExistingParent(ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_(), this.mcLoc("block/button_inventory"));
      String var10005 = ForgeRegistries.BLOCKS.getKey((Block)baseBlock.get()).m_135815_();
      var10000.texture("texture", new ResourceLocation("tensura", "block/" + var10005));
   }

   public void wallItem(RegistryObject<WallBlock> block, RegistryObject<Block> baseBlock) {
      ItemModelBuilder var10000 = (ItemModelBuilder)this.withExistingParent(ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_(), this.mcLoc("block/wall_inventory"));
      String var10005 = ForgeRegistries.BLOCKS.getKey((Block)baseBlock.get()).m_135815_();
      var10000.texture("wall", new ResourceLocation("tensura", "block/" + var10005));
   }

   public void webbedBlockItem(RegistryObject<Block> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/variated/" + var10003 + "_0"));
   }

   public void webbedStairsItem(RegistryObject<StairBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/variated/stairs/" + var10003 + "_0"));
   }

   public void webbedSlabItem(RegistryObject<SlabBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/variated/slab/" + var10003 + "_0"));
   }

   public void webbedWallItem(RegistryObject<WallBlock> block) {
      String var10001 = "tensura:" + ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      String var10003 = ForgeRegistries.BLOCKS.getKey((Block)block.get()).m_135815_();
      this.withExistingParent(var10001, this.modLoc("block/variated/wall/" + var10003 + "_inventory"));
   }
}
