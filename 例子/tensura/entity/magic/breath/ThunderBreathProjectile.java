package com.github.manasmods.tensura.entity.magic.breath;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;

public class ThunderBreathProjectile extends BreathEntity {
   public ThunderBreathProjectile(EntityType<? extends ThunderBreathProjectile> entityType, Level level) {
      super(entityType, level);
      this.m_20242_(true);
   }

   public ThunderBreathProjectile(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.THUNDER_BREATH.get(), level);
      this.m_5602_(entity);
   }

   protected void m_5790_(EntityHitResult entityHitResult) {
      Entity entity = entityHitResult.m_82443_();
      entity.m_20254_(5);
      entity.m_6469_(DamageSourceHelper.addSkillAndCost(TensuraDamageSources.thunderBreath(this, this.m_37282_()), this.getMpCost(), this.getSkill()), this.getDamage());
   }

   public void spawnParticle() {
      Entity var2 = this.m_37282_();
      if (var2 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var2;
         Vec3 rotation = owner.m_20154_().m_82541_();
         Vec3 pos = owner.m_20182_().m_82549_(rotation.m_82490_(1.6D));
         double x = pos.f_82479_;
         double y = pos.f_82480_ + (double)(owner.m_20192_() * 0.9F);
         double z = pos.f_82481_;
         double speed = owner.m_217043_().m_188500_() * 0.35D + 0.35D;

         for(int i = 0; i < 10; ++i) {
            double ox = Math.random() * 0.3D - 0.15D;
            double oy = Math.random() * 0.3D - 0.15D;
            double oz = Math.random() * 0.3D - 0.15D;
            Vec3 randomVec = (new Vec3(Math.random() - 0.5D, Math.random() - 0.5D, Math.random() - 0.5D)).m_82541_();
            Vec3 result = rotation.m_82490_(3.0D).m_82549_(randomVec).m_82541_().m_82490_(speed);
            owner.m_9236_().m_7106_((ParticleOptions)TensuraParticles.LIGHTNING_EFFECT.get(), x + ox, y + oy, z + oz, result.f_82479_, result.f_82480_, result.f_82481_);
         }

      }
   }
}
