package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.ChatFormatting;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.core.Direction;
import net.minecraft.core.Direction.Axis;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Pose;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class HellCaterpillarRenderer extends GeoEntityRenderer<HellCaterpillarEntity> {
   public HellCaterpillarRenderer(Context renderManager) {
      super(renderManager, new HellCaterpillarModel());
      this.f_114477_ = 0.3F;
   }

   public ResourceLocation getTextureLocation(HellCaterpillarEntity instance) {
      return instance.getCocoonType() == 1 ? new ResourceLocation("tensura", "textures/entity/hell_moth_line/gehenna_caterpillar.png") : new ResourceLocation("tensura", "textures/entity/hell_moth_line/hell_caterpillar.png");
   }

   public RenderType getRenderType(HellCaterpillarEntity hellCaterpillar, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (hellCaterpillar.m_6162_()) {
         stack.m_85841_(0.3F, 0.3F, 0.3F);
      } else {
         stack.m_85841_(0.6F, 0.6F, 0.6F);
      }

      return RenderType.m_110473_(this.getTextureLocation(hellCaterpillar));
   }

   private Direction rotate(Direction attachmentFacing) {
      return attachmentFacing.m_122434_() == Axis.Y ? Direction.UP : attachmentFacing;
   }

   private void rotateForAngle(PoseStack matrixStackIn, Direction rotate, float f) {
      if (rotate.m_122434_() != Axis.Y) {
         matrixStackIn.m_85845_(Vector3f.f_122223_.m_122240_(90.0F * f));
      }

      switch(rotate) {
      case DOWN:
      case NORTH:
         matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(180.0F * f));
      case UP:
      case SOUTH:
      default:
         break;
      case WEST:
         matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(90.0F * f));
         break;
      case EAST:
         matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(-90.0F * f));
      }

   }

   protected void applyRotations(HellCaterpillarEntity entityLiving, PoseStack matrixStackIn, float ageInTicks, float rotationYaw, float partialTicks) {
      if (entityLiving.m_20159_()) {
         super.applyRotations(entityLiving, matrixStackIn, ageInTicks, rotationYaw, partialTicks);
      } else {
         float trans = entityLiving.m_6162_() ? 0.2F : 0.4F;
         Pose pose = entityLiving.m_20089_();
         float progress;
         if (pose != Pose.SLEEPING && !entityLiving.hasStartedCocoon() && !entityLiving.isCocooned()) {
            progress = (entityLiving.prevAttachChangeProgress + (entityLiving.attachChangeProgress - entityLiving.prevAttachChangeProgress) * partialTicks) * 0.2F;
            float yawMul = 0.0F;
            if (entityLiving.prevAttachDir == entityLiving.getAttachmentFacing() && entityLiving.getAttachmentFacing().m_122434_() == Axis.Y) {
               yawMul = 1.0F;
            }

            matrixStackIn.m_85845_(Vector3f.f_122225_.m_122240_(180.0F - yawMul * rotationYaw));
            matrixStackIn.m_85837_(0.0D, (double)trans, 0.0D);
            float prevProgress = 1.0F - progress;
            this.rotateForAngle(matrixStackIn, this.rotate(entityLiving.prevAttachDir), prevProgress);
            this.rotateForAngle(matrixStackIn, this.rotate(entityLiving.getAttachmentFacing()), progress);
            if (entityLiving.getAttachmentFacing() != Direction.DOWN) {
               matrixStackIn.m_85837_(0.0D, (double)trans, 0.0D);
               if (entityLiving.m_20184_().f_82480_ <= -0.0010000000474974513D) {
                  matrixStackIn.m_85845_(Vector3f.f_122224_.m_122240_(180.0F * progress));
               }

               matrixStackIn.m_85837_(0.0D, (double)(-trans), 0.0D);
            }

            matrixStackIn.m_85837_(0.0D, (double)(-trans), 0.0D);
         }

         if (entityLiving.f_20919_ > 0) {
            progress = ((float)entityLiving.f_20919_ + partialTicks - 1.0F) / 20.0F * 1.6F;
            progress = Mth.m_14116_(progress);
            if (progress > 1.0F) {
               progress = 1.0F;
            }

            matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(progress * 90.0F));
         } else if (entityLiving.m_8077_() && pose != Pose.SLEEPING) {
            String s = ChatFormatting.m_126649_(entityLiving.m_7755_().getString());
            if ("Dinnerbone".equals(s)) {
               matrixStackIn.m_85837_(0.0D, (double)(entityLiving.m_20206_() + 0.1F), 0.0D);
               matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(180.0F));
            }
         } else if (pose != Pose.SLEEPING && (entityLiving.hasStartedCocoon() || entityLiving.isCocooned())) {
            matrixStackIn.m_85837_(0.0D, (double)(entityLiving.m_20206_() + 0.1F), 0.0D);
            matrixStackIn.m_85845_(Vector3f.f_122227_.m_122240_(180.0F));
         }

      }
   }
}
