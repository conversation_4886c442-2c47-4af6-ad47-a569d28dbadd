package com.github.b4ndithelps.tennogamenolife.client;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.model.HurtDummyModel;
import com.github.b4ndithelps.tennogamenolife.client.renderer.HurtDummyRenderer;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import net.minecraft.client.renderer.entity.EntityRenderers;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.EntityRenderersEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;

/**
 * 客户端注册类，注册实体渲染器和模型
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class TenreincarnationClientSetup {
    
    /**
     * 注册实体渲染器
     */
    @SubscribeEvent
    public static void registerRenderers(FMLClientSetupEvent event) {
        event.enqueueWork(() -> {
            // 注册伤害假人渲染器
            EntityRenderers.register(TenreincarnationEntities.HURT_DUMMY.get(), HurtDummyRenderer::new);
        });
    }
    
    /**
     * 注册实体模型层
     */
    @SubscribeEvent
    public static void registerLayerDefinitions(EntityRenderersEvent.RegisterLayerDefinitions event) {
        // 注册伤害假人模型层
        event.registerLayerDefinition(HurtDummyModel.LAYER_LOCATION, HurtDummyModel::createBodyLayer);
    }
} 