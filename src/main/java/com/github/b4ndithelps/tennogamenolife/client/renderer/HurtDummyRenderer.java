package com.github.b4ndithelps.tennogamenolife.client.renderer;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.model.HurtDummyModel;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.resources.ResourceLocation;

public class HurtDummyRenderer extends MobRenderer<HurtDummyEntity, HurtDummyModel> {
    private static final ResourceLocation TEXTURE = new ResourceLocation(Tenreincarnation.MODID, "textures/entity/hurt_dummy.png");
    private static final float SCALE = 0.7F;

    public HurtDummyRenderer(EntityRendererProvider.Context context) {
        super(context, 
              new HurtDummyModel(context.bakeLayer(HurtDummyModel.LAYER_LOCATION)), 
              0.5f); 
    }
    
    @Override
    protected void scale(HurtDummyEntity entity, PoseStack poseStack, float partialTick) {
        poseStack.scale(SCALE, SCALE, SCALE);
        super.scale(entity, poseStack, partialTick);
    }

    @Override
    public ResourceLocation getTextureLocation(HurtDummyEntity entity) {
        return TEXTURE;
    }
} 