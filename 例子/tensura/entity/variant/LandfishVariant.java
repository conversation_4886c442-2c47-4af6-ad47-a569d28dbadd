package com.github.manasmods.tensura.entity.variant;

import java.util.Arrays;
import java.util.Comparator;
import net.minecraft.resources.ResourceLocation;

public enum LandfishVariant {
   GREEN(0, "green"),
   CYAN(1, "cyan"),
   <PERSON><PERSON><PERSON>(2, "blue"),
   COD(3, "cod"),
   SALMON(4, "salmon"),
   PUFFER(5, "puffer"),
   DOLPHIN(6, "dolphin"),
   GUARDIAN(7, "guardian"),
   ELDER_GUARDIAN(8, "elder_guardian");

   private static final LandfishVariant[] BY_ID = (LandfishVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(LandfishVariant::getId)).toArray((x$0) -> {
      return new LandfishVariant[x$0];
   });
   private final int id;
   private final String location;

   private LandfishVariant(int id, String location) {
      this.id = id;
      this.location = location;
   }

   public static LandfishVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/landfish/landfish_" + this.getLocation() + ".png");
   }

   public int getId() {
      return this.id;
   }

   public String getLocation() {
      return this.location;
   }

   // $FF: synthetic method
   private static LandfishVariant[] $values() {
      return new LandfishVariant[]{GREEN, CYAN, BLUE, COD, SALMON, PUFFER, DOLPHIN, GUARDIAN, ELDER_GUARDIAN};
   }
}
