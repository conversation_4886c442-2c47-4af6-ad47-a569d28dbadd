package com.github.manasmods.tensura.ability.magic.spiritual.water;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class AcidRainMagic extends SpiritualMagic {
   public AcidRainMagic() {
      super(MagicElemental.WATER, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 80;
   }

   public int masteryCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 300.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("BarrierID", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         Level level = entity.m_9236_();
         if (heldTicks >= castTime) {
            if (WaterMagic.isWaterEvaporated(entity, level)) {
               return false;
            } else {
               if (heldTicks == castTime + 1) {
                  this.addMasteryPoint(instance, entity);
               }

               BarrierEntity.spawnLastingBarrier((EntityType)TensuraEntityTypes.ACID_RAIN.get(), 20.0F, instance.isMastered(entity) ? 10.0F : 7.5F, 0.0F, 220, 20.0F, entity.m_20182_(), entity, instance, this.magiculeCost(entity, instance), 100.0D, heldTicks);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
               return heldTicks - castTime <= 400;
            }
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               this.addCastingParticle(instance, player, heldTicks);
            }

            return true;
         }
      }
   }
}
