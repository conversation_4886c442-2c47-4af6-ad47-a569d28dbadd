package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HolyCowEntity;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;

public class Holy<PERSON><PERSON><PERSON><PERSON>er extends <PERSON>b<PERSON><PERSON>er<HolyCowEntity, HolyCowModel<HolyCowEntity>> {
   public HolyCowRenderer(Context context) {
      super(context, new HolyCowModel(context.m_174023_(HolyCowModel.HOLY_COW)), 0.7F);
   }

   public ResourceLocation getTextureLocation(HolyCowEntity pEntity) {
      return new ResourceLocation("tensura", "textures/entity/holy_cow/holy_cow.png");
   }
}
