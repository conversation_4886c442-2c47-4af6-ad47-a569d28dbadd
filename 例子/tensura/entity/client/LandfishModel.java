package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LandfishEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class LandfishModel extends AnimatedGeoModel<LandfishEntity> {
   public ResourceLocation getModelResource(LandfishEntity object) {
      return new ResourceLocation("tensura", "geo/landfish.geo.json");
   }

   public ResourceLocation getTextureResource(LandfishEntity instance) {
      return instance.getVariant().getTextureLocation();
   }

   public ResourceLocation getAnimationResource(LandfishEntity moth) {
      return new ResourceLocation("tensura", "animations/landfish.animation.json");
   }

   public void setCustomAnimations(LandfishEntity fish, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(fish, instanceId, customPredicate);
      this.suitHandling(fish);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationY(extraData.netHeadYaw * 0.017453292F);
      }

   }

   private void suitHandling(LandfishEntity fish) {
      boolean hasSuit = fish.m_7770_() != null && (fish.m_7770_().getString().equalsIgnoreCase("Nieadni") || fish.m_7770_().getString().equalsIgnoreCase("Nie"));
      IBone hat = this.getAnimationProcessor().getBone("Hat");
      if (hasSuit == hat.isHidden()) {
         hat.setHidden(!hasSuit);
      }

      IBone suit = this.getAnimationProcessor().getBone("Suit");
      if (hasSuit == suit.isHidden()) {
         suit.setHidden(!hasSuit);
      }

      IBone suitTail = this.getAnimationProcessor().getBone("SuitTail");
      if (hasSuit == suitTail.isHidden()) {
         suitTail.setHidden(!hasSuit);
      }

      IBone suitRightArm = this.getAnimationProcessor().getBone("SuitRightArm");
      if (hasSuit == suitRightArm.isHidden()) {
         suitRightArm.setHidden(!hasSuit);
      }

      IBone suitLeftArm = this.getAnimationProcessor().getBone("SuitLeftArm");
      if (hasSuit == suitLeftArm.isHidden()) {
         suitLeftArm.setHidden(!hasSuit);
      }

      IBone suitRightLeg = this.getAnimationProcessor().getBone("SuitRightLeg");
      if (hasSuit == suitRightLeg.isHidden()) {
         suitRightLeg.setHidden(!hasSuit);
      }

      IBone suitLeftLeg = this.getAnimationProcessor().getBone("SuitLeftLeg");
      if (hasSuit == suitLeftLeg.isHidden()) {
         suitLeftLeg.setHidden(!hasSuit);
      }

   }
}
