package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;

public class TelepathySkill extends Skill {
   public TelepathySkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 2000.0D;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)CommonSkills.THOUGHT_COMMUNICATION.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Mob mob = (Mob)SkillHelper.getTargetingEntity(Mob.class, entity, 30.0D, 0.2D, false, false);
      if (mob != null && SkillHelper.isSubordinate(entity, mob)) {
         telepathy(instance, entity, mob);
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
      }

   }

   public static void telepathy(ManasSkillInstance instance, LivingEntity entity, Mob mob) {
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128451_("usedTimes") % 10 == 0) {
         instance.addMasteryPoint(entity);
      }

      tag.m_128405_("usedTimes", tag.m_128451_("usedTimes") + 1);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (mob instanceof TensuraTamableEntity) {
            TensuraTamableEntity tamable = (TensuraTamableEntity)mob;
            tamable.commanding(player);
         } else if (mob instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)mob;
            horse.commanding(player);
         }
      }

      entity.m_21011_(InteractionHand.MAIN_HAND, true);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
