package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.ai.control.LookControl;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class BeastGnomeEntity extends TensuraTamableEntity implements IAnimatable, IElementalSpirit, ITensuraMount, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   private static final EntityDataAccessor<Boolean> SHRUNK;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   protected static final UUID SHRINK;
   private final List<Attribute> attributeList;

   public BeastGnomeEntity(EntityType<? extends BeastGnomeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.attributeList = List.of(Attributes.f_22276_, Attributes.f_22288_, Attributes.f_22279_, Attributes.f_22281_, Attributes.f_22278_, Attributes.f_22284_, Attributes.f_22285_);
      this.f_21364_ = 40;
      this.f_19793_ = 1.0F;
      this.f_21342_ = new BeastGnomeEntity.BeastGnomeMoveControl();
      this.f_21365_ = new BeastGnomeEntity.BeastGnomeLookControl();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 28.0D).m_22268_(Attributes.f_22276_, 80.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22284_, 10.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_(Attributes.f_22288_, 2.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new BeastGnomeEntity.BeastGnomeAttackGoal(this));
      this.f_21345_.m_25352_(4, new IElementalSpirit.FollowGreaterSpiritGoal(this, 1.0D, WarGnomeEntity.class));
      this.f_21345_.m_25352_(5, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(SHRUNK, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
      compound.m_128379_("Shrunk", this.isShrunk());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(SHRUNK, compound.m_128471_("Shrunk"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity m_21826_() {
      return this.getSummonerUUID() != null ? null : super.m_21826_();
   }

   public boolean isShrunk() {
      return (Boolean)this.f_19804_.m_135370_(SHRUNK);
   }

   public void setShrunk(boolean shrunk, boolean pResetHealth) {
      this.f_19804_.m_135381_(SHRUNK, shrunk);
      this.m_20090_();
      this.m_6210_();
      if (shrunk) {
         AttributeModifier modifier = new AttributeModifier(SHRINK, "Shrunk", -0.5D, Operation.MULTIPLY_TOTAL);
         Iterator var4 = this.attributeList.iterator();

         while(var4.hasNext()) {
            Attribute attribute = (Attribute)var4.next();
            AttributeInstance attributeInstance = this.m_21051_(attribute);
            if (attributeInstance != null && !attributeInstance.m_22109_(modifier)) {
               attributeInstance.m_22125_(modifier);
            }
         }

         if (pResetHealth) {
            this.m_21153_(this.m_21223_() * 0.5F);
         }
      } else {
         Iterator var7 = this.attributeList.iterator();

         while(var7.hasNext()) {
            Attribute attribute = (Attribute)var7.next();
            AttributeInstance attributeInstance = this.m_21051_(attribute);
            if (attributeInstance != null) {
               attributeInstance.m_22120_(SHRINK);
            }
         }

         if (pResetHealth) {
            this.m_21153_(this.m_21223_() * 2.0F);
         }
      }

   }

   public boolean canSleep() {
      return !this.m_21525_();
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.isShrunk() ? entitydimensions.m_20388_(0.1F) : entitydimensions;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19310_ || super.m_6673_(source);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.m_21824_()) {
            Entity var4 = pSource.m_7639_();
            if (var4 instanceof BeastGnomeEntity) {
               BeastGnomeEntity gnome = (BeastGnomeEntity)var4;
               if (!gnome.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof WarGnomeEntity) {
               WarGnomeEntity gnome = (WarGnomeEntity)var4;
               if (!gnome.m_21824_()) {
                  return false;
               }
            }
         }

         pAmount *= this.getPhysicalAttackInput(pSource);
         return super.m_6469_(pSource, pAmount);
      }
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 16.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 16.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (this.getSummonerUUID() != null) {
         if (entity instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)entity;
            return Objects.equals(spirit.getSummonerUUID(), this.getSummonerUUID());
         } else {
            return Objects.equals(entity.m_20148_(), this.getSummonerUUID());
         }
      } else if (entity instanceof BeastGnomeEntity) {
         BeastGnomeEntity gnome = (BeastGnomeEntity)entity;
         return gnome.m_21824_() == this.m_21824_();
      } else if (entity instanceof WarGnomeEntity) {
         WarGnomeEntity gnome = (WarGnomeEntity)entity;
         return gnome.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this);
      }

      boolean var10000;
      Entity var3;
      label58: {
         var3 = this.m_20202_();
         if (var3 instanceof Player) {
            Player player = (Player)var3;
            if (player.m_36341_()) {
               var10000 = true;
               break label58;
            }
         }

         var10000 = false;
      }

      boolean ownerSneak = var10000;
      if (this.m_20159_()) {
         var3 = this.m_20202_();
         if (var3 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var3;
            if (living.m_20072_() || living.m_20077_() || living.m_21255_() || ownerSneak) {
               this.m_8127_();
               this.m_146884_(Vec3.m_82512_(living.m_20183_()));
            }
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 25) {
            this.areaAttack();
            TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 2.5F);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215778_, SoundSource.NEUTRAL, 5.0F, 1.0F);
            if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
               SkillHelper.launchBlock(this, this.m_20182_(), 3, 1, 0.4F, 0.3F, (blockState) -> {
                  return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
               }, (blockPos) -> {
                  return !blockPos.equals(this.m_20097_().m_7495_());
               });
            }
         } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 30) {
            this.gravityAttack();
            TensuraParticleHelper.spawnGroundSlamParticle(this, 10, 8.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 10, 6.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 10, 4.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 10, 2.0F);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215771_, SoundSource.NEUTRAL, 5.0F, 1.0F);
            if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
               SkillHelper.launchBlock(this, this.m_20182_(), 8, 1, 0.5F, 0.4F, (blockState) -> {
                  return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_DOMINATING);
               }, (blockPos) -> {
                  return !blockPos.equals(this.m_20097_().m_7495_());
               });
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 4) {
         this.setMiscAnimation(4);
         this.m_21573_().m_26573_();
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 30;
         break;
      case 3:
         var10000 = 5;
         break;
      case 4:
         var10000 = 40;
         break;
      default:
         var10000 = 20;
      }

      return var10000;
   }

   public void areaAttack() {
      AABB aabb = this.m_20191_().m_82400_(4.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this) && (!(entity instanceof BeastGnomeEntity) || entity == this.m_5448_());
      });
      if (!livingEntityList.isEmpty()) {
         double damageMultiplier = this.hasEarthManipulation() ? 1.5D : 0.75D;
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 20.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.EARTH_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var6 = livingEntityList.iterator();

         while(var6.hasNext()) {
            LivingEntity target = (LivingEntity)var6.next();
            target.m_6469_(damageSource, (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier));
            target.m_20184_().m_82520_(0.0D, 0.5D * damageMultiplier, 0.0D);
         }

      }
   }

   public void gravityAttack() {
      AABB aabb = this.m_20191_().m_82400_(10.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this) && !(entity instanceof BeastGnomeEntity);
      });
      if (!livingEntityList.isEmpty()) {
         double damageMultiplier = this.hasEarthManipulation() ? 0.5D : 0.25D;
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 100.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.EARTH_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var6 = livingEntityList.iterator();

         while(var6.hasNext()) {
            LivingEntity target = (LivingEntity)var6.next();
            if (target.m_6469_(damageSource, (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier)) && target.m_20096_()) {
               TensuraParticleHelper.addParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.DARK_PURPLE_LIGHTNING_SPARK.get());
               if (target.m_217043_().m_188499_() && this.hasEarthManipulation()) {
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19620_, 200));
               }

               SkillHelper.knockBack(this, target, 2.0F);
            }
         }

      }
   }

   public boolean hasEarthManipulation() {
      return SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.EARTH_MANIPULATION.get()).isPresent();
   }

   public double m_6049_() {
      return 0.5D;
   }

   public MagicElemental getElemental() {
      return MagicElemental.EARTH;
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return SpiritualMagic.SpiritLevel.MEDIUM;
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.SPIRIT_FOOD);
   }

   public boolean isTamingFood(ItemStack pStack) {
      return pStack.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get());
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!player.m_36341_() && this.m_21830_(player)) {
            if (itemstack.m_41619_()) {
               if (this.isShrunk()) {
                  if (player.m_146895_() == null) {
                     this.m_7998_(player, true);
                     this.f_21344_.m_26573_();
                     this.m_6710_((LivingEntity)null);
                     return InteractionResult.SUCCESS;
                  }
               } else if (!this.m_6898_(itemstack)) {
                  if (this.m_21827_()) {
                     this.m_21839_(false);
                  }

                  if (this.isWandering()) {
                     this.setWandering(false);
                  }

                  if (!this.f_19853_.m_5776_()) {
                     player.m_7998_(this, true);
                  }
               }
            } else if (!this.convertElementalCore(this, player, hand, (Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get())) {
               this.setShrunk(!this.isShrunk(), true);
               TensuraParticleHelper.addParticlesAroundSelf(this, ParticleTypes.f_123765_);
               TensuraParticleHelper.addParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.DARK_PURPLE_LIGHTNING_SPARK.get());
               if (this.isShrunk()) {
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
               }
            }

            return InteractionResult.m_19078_(this.f_19853_.m_5776_());
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else {
            if (this.m_21824_()) {
               if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
                  this.commanding(player);
                  return InteractionResult.SUCCESS;
               }
            } else if (this.isTamingFood(itemstack)) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               if (this.f_19796_.m_188503_(10) == 7 && !ForgeEventFactory.onAnimalTame(this, player)) {
                  this.m_21828_(player);
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
                  this.m_21839_(true);
                  this.f_19853_.m_7605_(this, (byte)7);
               } else {
                  if (this.f_19796_.m_188503_(20) == 0) {
                     this.m_6710_(player);
                  }

                  this.f_19853_.m_7605_(this, (byte)6);
               }

               return InteractionResult.SUCCESS;
            }

            return InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_5634_(this.isShrunk() ? 2.5F : 5.0F);
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         this.setMiscAnimation(1);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Entity entity = this.m_146895_();
      return entity instanceof LivingEntity ? (LivingEntity)entity : null;
   }

   public double m_6048_() {
      return (double)(this.m_20206_() * 0.9F);
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
      }

   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               if (this.getMiscAnimation() == 4) {
                  speed = 0.0F;
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
                  super.m_7023_(new Vec3((double)f, (double)livingentity.f_20901_, (double)f1));
               } else {
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_21043_(this, false);
            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected SoundEvent m_7515_() {
      return this.m_6162_() ? SoundEvents.f_12281_ : SoundEvents.f_12280_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      if (this.getMiscAnimation() == 0) {
         this.setMiscAnimation(6);
      }

      return SoundEvents.f_12283_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12282_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.beastGnomeSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (!((double)this.f_19796_.m_188501_() > 0.1D)) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get());
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() == 4) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.yell_slam", EDefaultLoopTypes.PLAY_ONCE));
         return PlayState.CONTINUE;
      } else if (this.getMiscAnimation() == 2) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.slam", EDefaultLoopTypes.PLAY_ONCE));
         return PlayState.CONTINUE;
      } else {
         if (this.m_5803_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.sleep", EDefaultLoopTypes.LOOP));
         } else if (!this.m_21825_() && !(this.m_20202_() instanceof LivingEntity)) {
            if (event.isMoving() && !this.m_21525_()) {
               if ((!this.m_20096_() || !this.m_21660_()) && (this.getControllingPassenger() == null || !this.getControllingPassenger().m_20142_())) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.walk", EDefaultLoopTypes.LOOP));
               } else {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.run", EDefaultLoopTypes.LOOP));
               }
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.idle", EDefaultLoopTypes.LOOP));
            }
         } else if (this.m_21223_() <= this.m_21233_() / 4.0F) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.sit_hurt", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.sit", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.beast_gnome.bite", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(BeastGnomeEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(BeastGnomeEntity.class, EntityDataSerializers.f_135028_);
      SUMMONER_UUID = SynchedEntityData.m_135353_(BeastGnomeEntity.class, EntityDataSerializers.f_135041_);
      SHRUNK = SynchedEntityData.m_135353_(BeastGnomeEntity.class, EntityDataSerializers.f_135035_);
      SHRINK = UUID.fromString("a3463779-d881-4e29-a75a-eb2232a3347b");
   }

   public class BeastGnomeMoveControl extends MoveControl {
      public BeastGnomeMoveControl() {
         super(BeastGnomeEntity.this);
      }

      public void m_8126_() {
         if (!BeastGnomeEntity.this.m_5803_()) {
            if (BeastGnomeEntity.this.getMiscAnimation() != 2) {
               if (BeastGnomeEntity.this.getMiscAnimation() != 4) {
                  super.m_8126_();
               }
            }
         }
      }
   }

   public class BeastGnomeLookControl extends LookControl {
      public BeastGnomeLookControl() {
         super(BeastGnomeEntity.this);
      }

      public void m_8128_() {
         if (!BeastGnomeEntity.this.m_5803_()) {
            if (BeastGnomeEntity.this.getMiscAnimation() != 2) {
               if (BeastGnomeEntity.this.getMiscAnimation() != 4) {
                  super.m_8128_();
               }
            }
         }
      }
   }

   static class BeastGnomeAttackGoal extends MeleeAttackGoal {
      public final BeastGnomeEntity beast;

      public BeastGnomeAttackGoal(BeastGnomeEntity beast) {
         super(beast, 1.5D, true);
         this.beast = beast;
      }

      public void m_8037_() {
         if (this.beast.getMiscAnimation() != 2 && this.beast.getMiscAnimation() != 3) {
            super.m_8037_();
         }

      }

      public boolean m_8036_() {
         if (this.beast.isShrunk()) {
            return false;
         } else {
            return this.beast.m_21827_() ? false : super.m_8036_();
         }
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (this.beast.getMiscAnimation() == 0 || this.beast.getMiscAnimation() == 1) {
            int randomAttack = this.randomAttack();
            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = 25.0D;
               break;
            case 4:
               var10000 = 100.0D;
               break;
            default:
               var10000 = d0;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.beast.setMiscAnimation(randomAttack);
               if (randomAttack == 3) {
                  this.beast.m_7327_(pEnemy);
               } else {
                  this.beast.m_21573_().m_26573_();
               }
            }
         }

      }

      protected int randomAttack() {
         if (this.beast.f_19796_.m_188503_(10) == 5) {
            return 4;
         } else {
            return (double)this.beast.f_19796_.m_188501_() <= 0.4D ? 2 : 3;
         }
      }
   }
}
