package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class HolyCoatEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public HolyCoatEnchantment() {
      super(Rarity.COMMON, EnchantmentCategory.BREAKABLE, EquipmentSlot.MAINHAND);
   }

   public float getDamageBonus(int pLevel, DamageSource source, LivingEntity attacker, LivingEntity target, EquipmentSlot slot, float damage) {
      if (slot == EquipmentSlot.MAINHAND && source.m_7640_() == attacker) {
         return RaceHelper.isAffectedByHolyCoat(target) ? damage * 0.25F * (float)pLevel : 0.0F;
      } else {
         return 0.0F;
      }
   }

   public int m_7205_(int pLevel, DamageSource pSource) {
      Entity entity = pSource.m_7640_();
      if (entity == null) {
         return 0;
      } else {
         return !RaceHelper.isAffectedByHolyCoat(entity) ? 0 : pLevel * 4;
      }
   }

   public static void applyHolyCoat(ItemStack toStack) {
      Item var2 = toStack.m_41720_();
      if (var2 instanceof TieredItem) {
         TieredItem item = (TieredItem)var2;
         if (item.m_43314_().equals(TensuraToolTiers.SILVER) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.HOLY_COAT.get()) < 1) {
            toStack.m_41663_((Enchantment)TensuraEnchantments.HOLY_COAT.get(), 1);
         } else if (item.m_43314_().equals(TensuraToolTiers.MITHRIL) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.HOLY_COAT.get()) < 1) {
            toStack.m_41663_((Enchantment)TensuraEnchantments.HOLY_COAT.get(), 2);
         }

      } else {
         var2 = toStack.m_41720_();
         if (var2 instanceof ArmorItem) {
            ArmorItem item = (ArmorItem)var2;
            if (item.m_40401_().equals(TensuraArmourMaterials.SILVER) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.HOLY_COAT.get()) < 1) {
               toStack.m_41663_((Enchantment)TensuraEnchantments.HOLY_COAT.get(), 1);
            } else if (item.m_40401_().equals(TensuraArmourMaterials.MITHRIL) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.HOLY_COAT.get()) < 1) {
               toStack.m_41663_((Enchantment)TensuraEnchantments.HOLY_COAT.get(), 2);
            }
         }

      }
   }
}
