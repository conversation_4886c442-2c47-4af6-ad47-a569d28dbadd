package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.api.entity.navigator.SwimmingJumpNavigation;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.SmoothSwimmingLookControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.JumpGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.ai.navigation.WaterBoundPathNavigation;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.fluids.FluidType;

public class SwimmingTamableEntity extends TensuraTamableEntity implements IFollower {
   private static final EntityDataAccessor<Integer> MOISTNESS;
   public int jumpCooldown;

   public SwimmingTamableEntity(EntityType<? extends SwimmingTamableEntity> type, Level level) {
      super(type, level);
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.f_21342_ = new SwimmingTamableEntity.JumpLikeDolphinMoveControl(this);
      this.f_21365_ = new SmoothSwimmingLookControl(this, 10);
   }

   protected PathNavigation m_6037_(Level worldIn) {
      return (PathNavigation)(this.isJumper() ? new SwimmingJumpNavigation(this, worldIn) : new WaterBoundPathNavigation(this, worldIn));
   }

   protected boolean isJumper() {
      return false;
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MOISTNESS, 2400);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Moistness", this.getMoistness());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setMoistness(compound.m_128451_("Moistness"));
   }

   public int getMoistness() {
      return (Integer)this.f_19804_.m_135370_(MOISTNESS);
   }

   public void setMoistness(int moistness) {
      this.f_19804_.m_135381_(MOISTNESS, moistness);
   }

   protected float m_6431_(Pose poseIn, EntityDimensions sizeIn) {
      return 1.0F;
   }

   public int m_8132_() {
      return 1;
   }

   public int m_8085_() {
      return 1;
   }

   public boolean isPushedByFluid(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean canDrownInFluidType(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean m_6146_() {
      return true;
   }

   public float m_5610_(BlockPos pos, LevelReader worldIn) {
      if (worldIn.m_8055_(pos).m_60819_().m_205070_(FluidTags.f_13131_)) {
         return 10.0F;
      } else {
         return this.m_20077_() ? Float.NEGATIVE_INFINITY : 0.0F;
      }
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public boolean shouldUseJumpAttack(LivingEntity target) {
      if (!this.isJumper()) {
         return false;
      } else if (this.jumpCooldown != 0) {
         return false;
      } else if (target.m_20069_()) {
         BlockPos up = target.m_20183_().m_7494_();
         return !this.f_19853_.m_6425_(up.m_7494_()).m_76178_() ? false : this.f_19853_.m_6425_(up.m_6630_(2)).m_76178_();
      } else {
         Vec3 eye = target.m_146892_();
         Vec3 below = eye.m_82520_(0.0D, -10.0D, 0.0D);
         return this.f_19853_.m_45547_(new ClipContext(eye, below, Block.COLLIDER, Fluid.NONE, this)).m_6662_().equals(Type.MISS);
      }
   }

   protected boolean canJumpOutOfWater() {
      Vec3 eye = this.m_146892_().m_82520_(0.0D, 3.0D, 0.0D);
      Vec3 above = eye.m_82520_(0.0D, 2.0D, 0.0D);
      return this.f_19853_.m_45547_(new ClipContext(eye, above, Block.COLLIDER, Fluid.ANY, this)).m_6662_().equals(Type.MISS);
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.jumpCooldown > 0) {
         --this.jumpCooldown;
      }

      if (!this.m_21525_()) {
         if (this.m_20071_()) {
            this.setMoistness(2400);
         } else {
            this.setMoistness(this.getMoistness() - 1);
            if (this.getMoistness() <= 0) {
               this.m_6469_(DamageSource.f_19324_, 1.0F);
            }

            if (this.f_19861_) {
               this.m_20256_(this.m_20184_().m_82520_((double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.2F), 0.5D, (double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.2F)));
               this.f_19861_ = false;
               this.f_19812_ = true;
            }
         }

         this.swimmingParticle();
      }
   }

   protected void swimmingParticle() {
      if (this.f_19853_.f_46443_ && this.m_20069_() && this.m_20184_().m_82556_() > 0.03D) {
         Vec3 vector3d = this.m_20252_(0.0F);
         float f = Mth.m_14089_(this.m_146908_() * 0.017453292F) * 0.9F;
         float f1 = Mth.m_14031_(this.m_146908_() * 0.017453292F) * 0.9F;
         float f2 = 1.2F - this.f_19796_.m_188501_() * 0.7F;

         for(int i = 0; i < 2; ++i) {
            this.f_19853_.m_7106_(ParticleTypes.f_123776_, this.m_20185_() - vector3d.f_82479_ * (double)f2 + (double)f, this.m_20186_() - vector3d.f_82480_, this.m_20189_() - vector3d.f_82481_ * (double)f2 + (double)f1, 0.0D, 0.0D, 0.0D);
            this.f_19853_.m_7106_(ParticleTypes.f_123776_, this.m_20185_() - vector3d.f_82479_ * (double)f2 - (double)f, this.m_20186_() - vector3d.f_82480_, this.m_20189_() - vector3d.f_82481_ * (double)f2 - (double)f1, 0.0D, 0.0D, 0.0D);
         }
      }

   }

   public void m_8107_() {
      if (!this.m_20069_() && this.f_19861_ && this.f_19863_) {
         this.m_19877_();
         this.m_20256_(this.m_20184_().m_82520_((double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.05F), 0.4000000059604645D, (double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.05F)));
         this.f_19861_ = false;
         this.f_19812_ = true;
         this.m_5496_(this.getFlopSound(), this.m_6121_(), this.m_6100_());
      }

      super.m_8107_();
   }

   public void m_7023_(Vec3 travelVector) {
      if (this.m_6142_() && this.m_20069_()) {
         this.m_19920_(this.m_6113_(), travelVector);
         this.m_6478_(MoverType.SELF, this.m_20184_());
         this.m_20256_(this.m_20184_().m_82490_(0.9D));
         if (this.m_5448_() == null) {
            this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
         }
      } else {
         super.m_7023_(travelVector);
      }

   }

   public MobType m_6336_() {
      return MobType.f_21644_;
   }

   public boolean m_6914_(LevelReader worldIn) {
      return worldIn.m_45784_(this);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12327_;
   }

   protected SoundEvent m_7975_(DamageSource damageSourceIn) {
      return SoundEvents.f_12330_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12328_;
   }

   protected float m_6121_() {
      return 0.2F;
   }

   protected SoundEvent getFlopSound() {
      return SoundEvents.f_12329_;
   }

   static {
      MOISTNESS = SynchedEntityData.m_135353_(SwimmingTamableEntity.class, EntityDataSerializers.f_135028_);
   }

   static class JumpLikeDolphinMoveControl extends MoveControl {
      private final SwimmingTamableEntity entity;

      public JumpLikeDolphinMoveControl(SwimmingTamableEntity entity) {
         super(entity);
         this.entity = entity;
      }

      public void m_8126_() {
         if (this.entity.m_20069_()) {
            this.entity.m_20256_(this.entity.m_20184_().m_82520_(0.0D, 0.005D, 0.0D));
         }

         if (this.f_24981_ == Operation.MOVE_TO && !this.entity.m_21573_().m_26571_()) {
            double d0 = this.f_24975_ - this.entity.m_20185_();
            double d1 = this.f_24976_ - this.entity.m_20186_();
            double d2 = this.f_24977_ - this.entity.m_20189_();
            double d3 = d0 * d0 + d1 * d1 + d2 * d2;
            if (d3 < 2.500000277905201E-7D) {
               this.f_24974_.m_21564_(0.0F);
            } else {
               float f = (float)(Mth.m_14136_(d2, d0) * 57.2957763671875D) - 90.0F;
               this.entity.m_146922_(this.m_24991_(this.entity.m_146908_(), f, 10.0F));
               this.entity.f_20883_ = this.entity.m_146908_();
               this.entity.f_20885_ = this.entity.m_146908_();
               float speed = (float)(this.f_24978_ * this.entity.m_21133_(Attributes.f_22279_));
               if (this.entity.m_20069_()) {
                  this.entity.m_7910_(speed);
                  float f2 = -((float)(Mth.m_14136_(d1, (double)Mth.m_14116_((float)(d0 * d0 + d2 * d2))) * 57.2957763671875D));
                  f2 = Mth.m_14036_(Mth.m_14177_(f2), -85.0F, 85.0F);
                  this.entity.m_146926_(this.m_24991_(this.entity.m_146909_(), f2, 5.0F));
                  float f3 = Mth.m_14089_(this.entity.m_146909_() * 0.017453292F);
                  float f4 = Mth.m_14031_(this.entity.m_146909_() * 0.017453292F);
                  this.entity.f_20902_ = f3 * speed;
                  this.entity.f_20901_ = -f4 * speed;
               } else {
                  this.entity.m_7910_(speed * 2.0F);
               }

            }
         } else {
            this.entity.m_7910_(0.0F);
            this.entity.m_21570_(0.0F);
            this.entity.m_21567_(0.0F);
            this.entity.m_21564_(0.0F);
         }
      }
   }

   protected static class JumperMeleeAttackGoal extends MeleeAttackGoal {
      private SwimmingTamableEntity entity;

      public JumperMeleeAttackGoal(SwimmingTamableEntity entity, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(entity, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (this.entity.m_5448_() == null) {
            return false;
         } else if (this.entity.m_21827_()) {
            return false;
         } else {
            return this.entity.shouldUseJumpAttack(this.entity.m_5448_()) ? false : super.m_8036_();
         }
      }

      public boolean m_8045_() {
         return this.entity.m_21827_() ? false : super.m_8045_();
      }
   }

   protected static class JumpAttackGoal extends JumpGoal {
      private final SwimmingTamableEntity entity;
      private int attackCooldown = 0;
      private boolean inWater;

      public JumpAttackGoal(SwimmingTamableEntity entity) {
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (this.entity.m_20160_()) {
            return false;
         } else {
            return this.entity.m_5448_() != null && this.entity.shouldUseJumpAttack(this.entity.m_5448_()) && !this.entity.m_20096_() && this.entity.m_20069_() && this.entity.jumpCooldown <= 0;
         }
      }

      public boolean m_8045_() {
         double d0 = this.entity.m_20184_().f_82480_;
         return this.entity.m_5448_() != null && this.entity.jumpCooldown > 0 && (!(d0 * d0 < 0.029999999329447746D) || this.entity.m_146909_() == 0.0F || !(Math.abs(this.entity.m_146909_()) < 10.0F) || !this.entity.m_20069_()) && !this.entity.m_20096_();
      }

      public boolean m_6767_() {
         return false;
      }

      public void m_8056_() {
         LivingEntity target = this.entity.m_5448_();
         if (target != null) {
            double distanceXZ = this.entity.m_20275_(target.m_20185_(), this.entity.m_20186_(), target.m_20189_());
            if (distanceXZ >= 150.0D) {
               this.entity.m_21573_().m_5624_(target, 1.0D);
            } else {
               this.entity.m_21391_(target, 260.0F, 30.0F);
               double smoothX = Mth.m_14008_(Math.abs(target.m_20185_() - this.entity.m_20185_()), 0.0D, 1.0D);
               double smoothZ = Mth.m_14008_(Math.abs(target.m_20189_() - this.entity.m_20189_()), 0.0D, 1.0D);
               double d0 = (target.m_20185_() - this.entity.m_20185_()) * 0.3D * smoothX;
               double d2 = (target.m_20189_() - this.entity.m_20189_()) * 0.3D * smoothZ;
               float up = 1.0F + this.entity.m_217043_().m_188501_() * 0.8F;
               this.entity.m_20256_(this.entity.m_20184_().m_82520_(d0 * 0.3D, (double)up, d2 * 0.3D));
               this.entity.m_21573_().m_26573_();
               this.entity.jumpCooldown = this.entity.m_217043_().m_188503_(32) + 64;
            }
         }
      }

      public void m_8041_() {
         this.entity.m_146926_(0.0F);
         this.attackCooldown = 0;
      }

      public void m_8037_() {
         boolean flag = this.inWater;
         if (!flag) {
            FluidState fluidstate = this.entity.f_19853_.m_6425_(this.entity.m_20183_());
            this.inWater = fluidstate.m_205070_(FluidTags.f_13131_);
         }

         if (this.attackCooldown > 0) {
            --this.attackCooldown;
         }

         if (this.inWater && !flag) {
            this.entity.m_5496_(SoundEvents.f_11805_, 1.0F, 1.0F);
         }

         LivingEntity target = this.entity.m_5448_();
         if (target != null) {
            if (this.entity.m_20270_(target) < 3.0F && this.attackCooldown <= 0) {
               float damage = (float)this.entity.m_21133_(Attributes.f_22281_) * 2.0F;
               if (target.m_6469_(DamageSource.m_19370_(this.entity), damage)) {
                  this.entity.m_19970_(this.entity, target);
               }

               this.attackCooldown = 20;
            } else if (this.entity.m_20270_(target) < 5.0F) {
               this.entity.m_7327_(target);
            }
         }

         Vec3 vector3d = this.entity.m_20184_();
         if (vector3d.f_82480_ * vector3d.f_82480_ < 0.10000000149011612D && this.entity.m_146909_() != 0.0F) {
            this.entity.m_146926_(Mth.m_14189_(this.entity.m_146909_(), 0.0F, 0.2F));
         } else {
            double d0 = Math.sqrt(vector3d.m_165925_());
            double d1 = Math.signum(-vector3d.f_82480_) * Math.acos(d0 / vector3d.m_82553_()) * 57.2957763671875D;
            this.entity.m_146926_((float)d1);
         }

      }
   }

   protected static class JumpLikeDolphinGoal extends JumpGoal {
      private static final int[] JUMP_DISTANCES = new int[]{0, 1, 4, 5, 6, 7, 10};
      private final SwimmingTamableEntity entity;
      private final int interval;
      private boolean inWater;

      public JumpLikeDolphinGoal(SwimmingTamableEntity entity, int interval) {
         this.entity = entity;
         this.interval = interval;
      }

      public boolean m_8036_() {
         if (this.entity.m_20160_()) {
            return false;
         } else if (this.entity.m_217043_().m_188503_(this.interval) == 0 && this.entity.m_5448_() == null && this.entity.jumpCooldown == 0) {
            Direction direction = this.entity.m_6374_();
            int i = direction.m_122429_();
            int j = direction.m_122431_();
            BlockPos blockpos = this.entity.m_20183_();
            int[] var5 = JUMP_DISTANCES;
            int var6 = var5.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               int k = var5[var7];
               if (!this.canJumpTo(blockpos, i, j, k) || !this.isAirAbove(blockpos, i, j, k)) {
                  return false;
               }
            }

            return true;
         } else {
            return false;
         }
      }

      private boolean canJumpTo(BlockPos pos, int dx, int dz, int scale) {
         BlockPos blockpos = pos.m_7918_(dx * scale, 0, dz * scale);
         return this.entity.f_19853_.m_6425_(blockpos).m_205070_(FluidTags.f_13131_) && !this.entity.f_19853_.m_8055_(blockpos).m_60767_().m_76334_();
      }

      private boolean isAirAbove(BlockPos pos, int dx, int dz, int scale) {
         return this.entity.f_19853_.m_8055_(pos.m_7918_(dx * scale, 1, dz * scale)).m_60795_() && this.entity.f_19853_.m_8055_(pos.m_7918_(dx * scale, 2, dz * scale)).m_60795_();
      }

      public boolean m_8045_() {
         double d0 = this.entity.m_20184_().f_82480_;
         return this.entity.jumpCooldown > 0 && (!(d0 * d0 < 0.029999999329447746D) || !this.entity.m_20069_()) && !this.entity.m_20096_();
      }

      public boolean m_6767_() {
         return false;
      }

      public void m_8056_() {
         Direction direction = this.entity.m_6374_();
         float up = 0.7F + this.entity.m_217043_().m_188501_() * 0.8F;
         this.entity.m_20256_(this.entity.m_20184_().m_82520_((double)direction.m_122429_() * 0.6D, (double)up, (double)direction.m_122431_() * 0.6D));
         this.entity.m_21573_().m_26573_();
         this.entity.jumpCooldown = this.entity.m_217043_().m_188503_(256) + 256;
      }

      public void m_8037_() {
         boolean flag = this.inWater;
         if (!flag) {
            FluidState fluidstate = this.entity.f_19853_.m_6425_(this.entity.m_20183_());
            this.inWater = fluidstate.m_205070_(FluidTags.f_13131_);
         }

         if (this.inWater && !flag) {
            this.entity.m_5496_(SoundEvents.f_11805_, 1.0F, 1.0F);
         }

         Vec3 vector3d = this.entity.m_20184_();
         if (vector3d.f_82480_ * vector3d.f_82480_ < 0.10000000149011612D && this.entity.m_146909_() != 0.0F) {
            this.entity.m_146926_(Mth.m_14189_(this.entity.m_146909_(), 0.0F, 0.2F));
         } else {
            double d0 = Math.sqrt(vector3d.m_165925_());
            double d1 = Math.signum(-vector3d.f_82480_) * Math.acos(d0 / vector3d.m_82553_()) * 57.2957763671875D;
            this.entity.m_146926_((float)d1);
         }
      }
   }
}
