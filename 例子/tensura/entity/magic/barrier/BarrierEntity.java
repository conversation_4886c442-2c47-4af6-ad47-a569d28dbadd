package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.entity.PartEntity;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class BarrierEntity extends Projectile {
   private static final EntityDataAccessor<Integer> LIFE;
   private static final EntityDataAccessor<Float> HEALTH;
   private static final EntityDataAccessor<Float> VISUAL_RADIUS;
   private static final EntityDataAccessor<Float> RADIUS;
   private static final EntityDataAccessor<Float> ADDITIONAL_HEIGHT;
   protected double mpCost;
   protected float damage;
   protected ManasSkillInstance skill;
   public final BarrierPart[] parts;

   public BarrierEntity(EntityType<? extends BarrierEntity> entityType, Level level, LivingEntity entity) {
      this(entityType, level);
      this.m_5602_(entity);
   }

   public BarrierEntity(EntityType<? extends BarrierEntity> entityType, Level level) {
      super(entityType, level);
      this.mpCost = 0.0D;
      this.damage = 0.0F;
      this.skill = null;
      this.parts = new BarrierPart[]{new BarrierPart(this, "Up", Direction.UP), new BarrierPart(this, "Down", Direction.DOWN), new BarrierPart(this, "North", Direction.NORTH), new BarrierPart(this, "East", Direction.EAST), new BarrierPart(this, "South", Direction.SOUTH), new BarrierPart(this, "West", Direction.WEST)};
      this.m_20242_(true);
      this.f_19850_ = false;
      this.f_19811_ = true;

      for(int i = 0; i < this.parts.length; ++i) {
         if (this.parts[i] != null) {
            this.parts[i].m_20234_(this.m_19879_() + i + 1);
         }
      }

   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(LIFE, 600);
      this.f_19804_.m_135372_(HEALTH, 20.0F);
      this.f_19804_.m_135372_(RADIUS, 10.0F);
      this.f_19804_.m_135372_(ADDITIONAL_HEIGHT, 0.0F);
      this.f_19804_.m_135372_(VISUAL_RADIUS, 0.0F);
   }

   protected void m_7380_(@NotNull CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128347_("MPCost", this.getMpCost());
      pCompound.m_128350_("Damage", this.getDamage());
      pCompound.m_128405_("Age", this.f_19797_);
      pCompound.m_128405_("Life", this.getLife());
      pCompound.m_128350_("Health", this.getHealth());
      pCompound.m_128350_("Radius", this.getRadius());
      pCompound.m_128350_("AdditionalHeight", this.getHeight());
      pCompound.m_128350_("VisualRadius", this.getVisualRadius());
      if (this.skill != null) {
         pCompound.m_128365_("skill", this.skill.toNBT());
      }

   }

   protected void m_7378_(@NotNull CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setMpCost(pCompound.m_128459_("MPCost"));
      this.setDamage(pCompound.m_128457_("Damage"));
      this.f_19797_ = pCompound.m_128451_("Age");
      this.setLife(pCompound.m_128451_("Life"));
      this.setHealth(pCompound.m_128457_("Health"));
      this.f_19804_.m_135381_(RADIUS, pCompound.m_128457_("Radius"));
      this.setHeight(pCompound.m_128457_("AdditionalHeight"));
      this.setVisualRadius(pCompound.m_128457_("VisualRadius"));
      if (pCompound.m_128441_("skill")) {
         Tag var3 = pCompound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

   }

   public int getLife() {
      return (Integer)this.m_20088_().m_135370_(LIFE);
   }

   public void setLife(int life) {
      this.m_20088_().m_135381_(LIFE, life);
   }

   public void increaseLife(int life) {
      this.m_20088_().m_135381_(LIFE, this.getLife() + life);
   }

   public void setHealth(float pDamageTaken) {
      this.f_19804_.m_135381_(HEALTH, pDamageTaken);
   }

   public float getHealth() {
      return (Float)this.f_19804_.m_135370_(HEALTH);
   }

   public float getRadius() {
      return (Float)this.f_19804_.m_135370_(RADIUS);
   }

   public void setRadius(float radius) {
      this.f_19804_.m_135381_(RADIUS, radius);
      this.m_146884_(this.m_20182_().m_82520_(0.0D, (double)(-radius), 0.0D));
   }

   public float getVisualRadius() {
      return (Float)this.f_19804_.m_135370_(VISUAL_RADIUS);
   }

   public void setVisualRadius(float radius) {
      this.f_19804_.m_135381_(VISUAL_RADIUS, radius);
   }

   public float getHeight() {
      return (Float)this.f_19804_.m_135370_(ADDITIONAL_HEIGHT);
   }

   public void setHeight(float height) {
      this.f_19804_.m_135381_(ADDITIONAL_HEIGHT, height);
   }

   public int getTickEachHit() {
      return 20;
   }

   public boolean canWalkThrough() {
      return false;
   }

   public boolean canWalkThrough(Entity entity) {
      Entity owner = this.m_37282_();
      if (owner != null && owner.m_6144_()) {
         return entity.m_7307_(owner) || entity == owner;
      } else {
         return false;
      }
   }

   public boolean shouldPush() {
      return false;
   }

   public boolean blockBuilding() {
      return true;
   }

   protected boolean m_5603_(Entity pTarget) {
      return !pTarget.m_5833_() && pTarget.m_6084_() && pTarget.m_6087_();
   }

   public boolean m_6060_() {
      return false;
   }

   public boolean isMultipartEntity() {
      return true;
   }

   public PartEntity<?>[] getParts() {
      return this.parts;
   }

   public boolean m_6783_(double pDistance) {
      return super.m_6783_(pDistance) || pDistance < 1024.0D;
   }

   protected Set<Entity> getPartCollision() {
      List<Entity> list = new ArrayList();
      if (this.isMultipartEntity()) {
         BarrierPart[] var2 = this.parts;
         int var3 = var2.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            Entity entity = var2[var4];
            list.addAll(this.m_9236_().m_45933_(entity, entity.m_20191_()));
         }
      }

      return (Set)list.stream().filter((target) -> {
         return target != this.m_37282_() && target instanceof LivingEntity;
      }).collect(Collectors.toSet());
   }

   protected AABB getAffectedArea() {
      return new AABB(this.m_20185_() - (double)this.getRadius(), this.m_20186_(), this.m_20189_() - (double)this.getRadius(), this.m_20185_() + (double)this.getRadius(), this.m_20186_() + (double)(this.getRadius() * 2.0F) + (double)this.getHeight(), this.m_20189_() + (double)this.getRadius());
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.f_19853_.m_5776_() && !this.m_213877_()) {
            this.setHealth(this.getHealth() - pAmount);
            this.m_5834_();
            this.m_146852_(GameEvent.f_223706_, pSource.m_7639_());
            if (this.getHealth() <= 0.0F) {
               this.m_146870_();
            }
         }

         return true;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getVisualRadius() != this.getRadius()) {
         this.setVisualRadius(Math.min(this.getVisualRadius() + 0.5F, this.getRadius()));
      }

      if (this.isMultipartEntity()) {
         BarrierPart[] var1 = this.parts;
         int var2 = var1.length;

         for(int var3 = 0; var3 < var2; ++var3) {
            BarrierPart part = var1[var3];
            part.directionPosition();
            part.directionBoundingBox();
         }
      }

      if (this.f_19853_.m_5776_()) {
         this.spawnParticle();
      } else if (this.f_19797_ % this.getTickEachHit() == 0) {
         Iterator var5 = this.getPartCollision().iterator();

         while(var5.hasNext()) {
            Entity entity = (Entity)var5.next();
            this.m_5790_(new EntityHitResult(entity));
         }

         this.hitTarget();
      }

      if (this.getLife() >= 0 && this.f_19797_ >= this.getLife()) {
         this.m_146870_();
      }

   }

   public List<LivingEntity> getAffectedEntities() {
      return this.m_9236_().m_45976_(LivingEntity.class, this.getAffectedArea());
   }

   protected void hitTarget() {
      Iterator var1 = this.getAffectedEntities().iterator();

      while(var1.hasNext()) {
         LivingEntity target = (LivingEntity)var1.next();
         if (this.m_5603_(target)) {
            this.applyEffect(target);
         }
      }

   }

   public void applyEffect(LivingEntity target) {
   }

   public void spawnParticle() {
   }

   public static void spawnLastingBarrier(EntityType<? extends BarrierEntity> entityType, float damage, float radius, float height, int life, float health, Vec3 pos, LivingEntity owner, @Nullable ManasSkillInstance instance, double cost, double increaseCost, int heldTicks) {
      spawnLastingBarrier(entityType, damage, radius, height, life, health, pos, owner, instance, cost, increaseCost, heldTicks, false);
   }

   public static void spawnLastingBarrier(EntityType<? extends BarrierEntity> entityType, float damage, float radius, float height, int life, float health, Vec3 pos, LivingEntity owner, @Nullable ManasSkillInstance instance, double cost, double increaseCost, int heldTicks, boolean aura) {
      if (instance != null) {
         CompoundTag tag = instance.getOrCreateTag();
         Level level = owner.m_9236_();
         if (tag.m_128451_("BarrierID") == 0 && !checkCost(owner, cost, aura)) {
            BarrierEntity barrier = (BarrierEntity)entityType.m_20615_(level);
            if (barrier == null) {
               return;
            }

            barrier.m_5602_(owner);
            barrier.setDamage(damage);
            barrier.setRadius(radius);
            barrier.setHeight(height);
            barrier.setLife(life);
            barrier.setHealth(health);
            barrier.m_146884_(pos);
            barrier.setSkill(instance);
            if (!aura) {
               barrier.setMpCost(cost);
            }

            owner.m_9236_().m_7967_(barrier);
            owner.m_21011_(InteractionHand.MAIN_HAND, true);
            tag.m_128405_("BarrierID", barrier.m_19879_());
         } else {
            Entity entity = owner.m_9236_().m_6815_(tag.m_128451_("BarrierID"));
            if (entity instanceof BarrierEntity) {
               BarrierEntity barrier = (BarrierEntity)entity;
               if (heldTicks % 20 != 0 || !checkCost(owner, increaseCost, aura)) {
                  barrier.increaseLife(1);
               }
            } else {
               tag.m_128405_("BarrierID", 0);
            }
         }

         instance.markDirty();
      }
   }

   private static boolean checkCost(LivingEntity owner, double cost, boolean aura) {
      return aura ? SkillHelper.outOfAura(owner, cost) : SkillHelper.outOfMagicule(owner, cost);
   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public float getDamage() {
      return this.damage;
   }

   public void setDamage(float damage) {
      this.damage = damage;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   static {
      LIFE = SynchedEntityData.m_135353_(BarrierEntity.class, EntityDataSerializers.f_135028_);
      HEALTH = SynchedEntityData.m_135353_(BarrierEntity.class, EntityDataSerializers.f_135029_);
      VISUAL_RADIUS = SynchedEntityData.m_135353_(BarrierEntity.class, EntityDataSerializers.f_135029_);
      RADIUS = SynchedEntityData.m_135353_(BarrierEntity.class, EntityDataSerializers.f_135029_);
      ADDITIONAL_HEIGHT = SynchedEntityData.m_135353_(BarrierEntity.class, EntityDataSerializers.f_135029_);
   }
}
