package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.controller.JumpingEntityMoveControl;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IJumpingEntity;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.ClimbOnTopOfPowderSnowGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import org.jetbrains.annotations.NotNull;

public class HornedRabbitEntity extends TensuraTamableEntity implements IJumpingEntity {
   private int jumpTicks;
   private int jumpDuration;
   private boolean wasOnGround;
   private int jumpDelayTicks;

   public HornedRabbitEntity(EntityType<? extends HornedRabbitEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 10;
      this.f_21342_ = new JumpingEntityMoveControl(this);
      this.f_21344_ = new StraightFlightNavigator(this, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22276_, 20.0D).m_22268_(Attributes.f_22279_, 0.4000000059604645D).m_22268_(Attributes.f_22277_, 32.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new ClimbOnTopOfPowderSnowGoal(this, this.f_19853_));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 2.0D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(4, new BreedGoal(this, 2.0D));
      this.f_21345_.m_25352_(6, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(11, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(3, new HornedRabbitEntity.RabbitAttackGoal(this, 2.0D, true, 3.0F));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
   }

   protected float m_6118_() {
      if (this.f_19862_) {
         return 0.8F;
      } else if (this.f_21342_.m_24995_() && this.f_21342_.m_25001_() > this.m_20186_() + 0.5D) {
         return 0.8F;
      } else {
         Path path = this.f_21344_.m_26570_();
         if (path != null && !path.m_77392_()) {
            Vec3 vec3 = path.m_77380_(this);
            if (vec3.f_82480_ > this.m_20186_() + 0.5D) {
               return 0.8F;
            }
         }

         return this.f_21342_.m_24999_() <= 0.6D ? 0.4F : 0.5F;
      }
   }

   protected void m_6135_() {
      if (!this.m_21827_()) {
         super.m_6135_();
         double d0 = this.f_21342_.m_24999_();
         if (d0 > 0.0D) {
            double d1 = this.m_20184_().m_165925_();
            if (d1 < 0.01D) {
               this.m_19920_(0.1F, new Vec3(0.0D, 0.0D, 1.0D));
            }
         }

         if (!this.f_19853_.f_46443_) {
            this.f_19853_.m_7605_(this, (byte)1);
         }

      }
   }

   public float getJumpCompletion(float pPartialTick) {
      return this.jumpDuration == 0 ? 0.0F : ((float)this.jumpTicks + pPartialTick) / (float)this.jumpDuration;
   }

   public void m_6862_(boolean pJumping) {
      super.m_6862_(pJumping);
      if (pJumping) {
         this.m_5496_(this.getJumpSound(), this.getJumpSoundVolume(), ((this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F) * 0.8F);
      }
   }

   public void startJumping() {
      this.m_6862_(true);
      this.jumpDuration = 10;
      this.jumpTicks = 0;
   }

   public void m_8024_() {
      if (this.getJumpDelay() > 0) {
         --this.jumpDelayTicks;
      }

      if (this.f_19861_) {
         if (!this.wasOnGround) {
            this.m_6862_(false);
            if (this.f_21342_.m_24999_() < 2.2D) {
               this.jumpDelayTicks = 10;
            } else {
               this.jumpDelayTicks = 1;
            }
         }

         if (this.getJumpDelay() == 0) {
            LivingEntity owner;
            if (this.setWantedTarget(this, 25.0D)) {
               owner = this.m_5448_();

               assert owner != null;

               this.facePoint(owner.m_20185_(), owner.m_20189_());
               this.startJumping();
            } else if (this.setWantedOwner(this, 25.0D)) {
               owner = this.m_21826_();

               assert owner != null;

               this.facePoint(owner.m_20185_(), owner.m_20189_());
               this.startJumping();
            }
         }
      }

      this.wasOnGround = this.f_19861_;
   }

   public boolean m_5843_() {
      return false;
   }

   private void facePoint(double pX, double pZ) {
      this.m_146922_((float)(Mth.m_14136_(pZ - this.m_20189_(), pX - this.m_20185_()) * 57.2957763671875D) - 90.0F);
   }

   public void m_8107_() {
      super.m_8107_();
      if (this.jumpTicks != this.jumpDuration) {
         ++this.jumpTicks;
      } else if (this.jumpDuration != 0) {
         this.jumpTicks = 0;
         this.jumpDuration = 0;
         this.m_6862_(false);
      }

   }

   public int getJumpDelay() {
      return this.jumpDelayTicks;
   }

   public void setJumpAnimation(boolean jump) {
   }

   protected boolean m_8028_() {
      return false;
   }

   public SoundEvent getJumpSound() {
      return SoundEvents.f_12354_;
   }

   public float getJumpSoundVolume() {
      return this.m_6121_();
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12297_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12353_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12352_;
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      HornedRabbitEntity rabbit = (HornedRabbitEntity)((EntityType)TensuraEntityTypes.HORNED_RABBIT.get()).m_20615_(pLevel);
      if (rabbit == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            rabbit.m_21816_(uuid);
            rabbit.m_7105_(true);
         }

         return rabbit;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.RABBIT_FOOD);
   }

   public void m_7822_(byte pId) {
      if (pId == 1) {
         this.m_20076_();
         this.jumpDuration = 10;
         this.jumpTicks = 0;
      } else {
         super.m_7822_(pId);
      }

   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         if (this.m_6898_(itemstack)) {
            if (this.m_21223_() < this.m_21233_()) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               FoodProperties food = itemstack.getFoodProperties((LivingEntity)null);
               if (food != null) {
                  this.m_5634_((float)food.m_38744_());
               } else {
                  this.m_5634_(2.0F);
               }

               this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.SUCCESS;
            }

            if (this.m_6162_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_146740_(m_216967_(-this.m_146764_()), true);
               this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         }

         if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || itemstack.m_204117_(TensuraTags.Items.RABBIT_TAMING_FOOD);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_()) {
            if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player) && !this.m_6162_()) {
               this.commanding(player);
               return InteractionResult.SUCCESS;
            } else {
               return InteractionResult.PASS;
            }
         } else if (itemstack.m_204117_(TensuraTags.Items.RABBIT_TAMING_FOOD)) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            if (this.f_19796_.m_188503_(3) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
               this.m_21828_(player);
               this.f_21344_.m_26573_();
               this.m_6710_((LivingEntity)null);
               this.m_21839_(true);
               this.f_19853_.m_7605_(this, (byte)7);
            } else {
               this.f_19853_.m_7605_(this, (byte)6);
            }

            return InteractionResult.SUCCESS;
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public Vec3 m_7939_() {
      return new Vec3(0.0D, (double)(0.6F * this.m_20192_()), (double)(this.m_20205_() * 0.4F));
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public static boolean checkHornedRabbitSpawnRules(EntityType<HornedRabbitEntity> pRabbit, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184234_) && m_186209_(pLevel, pPos);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.hornedRabbitSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public class RabbitAttackGoal extends MeleeAttackGoal {
      float range = 0.0F;

      public RabbitAttackGoal(PathfinderMob pMob, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen, float additionalRange) {
         super(pMob, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.range = additionalRange;
      }

      protected double m_6639_(@NotNull LivingEntity pAttackTarget) {
         return (double)this.range + super.m_6639_(pAttackTarget);
      }
   }
}
