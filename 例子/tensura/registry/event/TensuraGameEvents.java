package com.github.manasmods.tensura.registry.event;

import net.minecraft.core.Registry;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraGameEvents {
   private static final DeferredRegister<GameEvent> registry;
   public static RegistryObject<GameEvent> AFTER_CHEAT_DEATH;

   private static RegistryObject<GameEvent> registerGameEvent(String name) {
      return registry.register(name, () -> {
         return new GameEvent(name, 16);
      });
   }

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_175423_, "tensura");
      AFTER_CHEAT_DEATH = registerGameEvent("after_cheat_death");
   }
}
