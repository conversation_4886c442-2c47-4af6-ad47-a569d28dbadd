package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.data.pack.Slotting.SlottingElementCombination;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.custom.ElementCoreItem;
import com.github.manasmods.tensura.item.custom.KunaiItem;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import net.minecraft.core.NonNullList;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.SlotAccess;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.inventory.ClickAction;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.inventory.tooltip.BundleTooltip;
import net.minecraft.world.inventory.tooltip.TooltipComponent;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;
import net.minecraftforge.registries.ForgeRegistries;

public class SlottingEnchantment extends EngravingEnchantment {
   public SlottingEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public int m_6586_() {
      return (Integer)TensuraConfig.INSTANCE.enchantmentsConfig.maxSlotting.get();
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity target, int pLevel, float originalDamage) {
      if (target.f_19802_ < 60) {
         int cores = getContentWeight(stack);
         if (cores > 0) {
            float damage = DamageSourceHelper.getMainWeaponDamage(attacker, target);
            float var10000;
            if (attacker instanceof Player) {
               Player player = (Player)attacker;
               var10000 = player.m_36403_(0.5F);
            } else {
               var10000 = 1.0F;
            }

            float f2 = var10000;
            damage *= 0.2F + f2 * f2 * 0.8F;
            int earth = getCores(stack, (Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get());
            int fire = getCores(stack, (Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get());
            int space = getCores(stack, (Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get());
            int water = getCores(stack, (Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get());
            int wind = getCores(stack, (Item)TensuraMaterialItems.ELEMENT_CORE_WIND.get());
            coreExtraDamage(target, attacker, earth, fire, space, water, wind, (double)damage);
            damageEachCore(attacker, stack, cores == 1 ? 5 : 4 - cores);
         }
      }
   }

   private static void ignoreInvulnerableHurt(Entity pTarget, LivingEntity pAttacker, String damageType, float pAmount) {
      if (!(pAmount <= 0.0F)) {
         pTarget.f_19802_ = 0;
         DamageSource magicDamage = (new TensuraEntityDamageSource(damageType, pAttacker)).setSlotting().m_19389_();
         DamageSourceHelper.dealSplitElementalDamage(pTarget, magicDamage, 0.9F, pAmount);
      }
   }

   private static void coreExtraDamage(Entity pTarget, LivingEntity pAttacker, int earth, int fire, int space, int water, int wind, double baseDamage) {
      boolean hasCombination = false;
      double[] coreDamage = new double[]{baseDamage, baseDamage, baseDamage, baseDamage, baseDamage};
      Iterator var11 = TensuraData.getElementCombination().iterator();

      while(var11.hasNext()) {
         SlottingElementCombination combination = (SlottingElementCombination)var11.next();
         if (combination.getEarthCores() == earth && combination.getFireCores() == fire && combination.getSpaceCores() == space && combination.getWaterCores() == water && combination.getWindCores() == wind) {
            coreDamage[0] *= combination.getEarthMultiplier();
            coreDamage[1] *= combination.getFireMultiplier();
            coreDamage[2] *= combination.getSpaceMultiplier();
            coreDamage[3] *= combination.getWaterMultiplier();
            coreDamage[4] *= combination.getWindMultiplier();
            hasCombination = true;
            break;
         }
      }

      if (hasCombination) {
         ignoreInvulnerableHurt(pTarget, pAttacker, "earth_attack", (float)coreDamage[0]);
         ignoreInvulnerableHurt(pTarget, pAttacker, "fire_attack", (float)coreDamage[1]);
         ignoreInvulnerableHurt(pTarget, pAttacker, "space_attack", (float)coreDamage[2]);
         ignoreInvulnerableHurt(pTarget, pAttacker, "water_attack", (float)coreDamage[3]);
         ignoreInvulnerableHurt(pTarget, pAttacker, "wind_attack", (float)coreDamage[4]);
      }
   }

   private static void damageEachCore(LivingEntity pAttacker, ItemStack gear, int amount) {
      if (amount > 0) {
         if (pAttacker instanceof Player) {
            Player player = (Player)pAttacker;
            if (player.m_150110_().f_35937_) {
               return;
            }
         }

         Item var4 = gear.m_41720_();
         if (var4 instanceof TieredItem) {
            TieredItem tieredItem = (TieredItem)var4;
            if (tieredItem.m_43314_().equals(TensuraToolTiers.HIHIIROKANE)) {
               return;
            }
         }

         CompoundTag compoundtag = gear.m_41784_();
         if (compoundtag.m_128441_("Cores")) {
            ListTag newList = new ListTag();
            ListTag list = compoundtag.m_128437_("Cores", 10);

            for(int i = 0; i < list.size(); ++i) {
               CompoundTag itemTag = list.m_128728_(i);
               ItemStack elementCore = ItemStack.m_41712_(itemTag);
               elementCore.m_41622_(amount, pAttacker, (entity) -> {
                  entity.m_21166_(EquipmentSlot.MAINHAND);
               });
               if (!elementCore.m_41619_()) {
                  elementCore.m_41739_(itemTag);
                  newList.add(itemTag);
               }
            }

            compoundtag.m_128365_("Cores", newList);
         }
      }
   }

   public static boolean onUse(LivingEntity entity, InteractionHand pHand) {
      if (entity.m_6144_()) {
         return false;
      } else {
         ItemStack pStack = entity.m_21120_(pHand);
         if (getContentWeight(pStack) <= 0) {
            return false;
         } else {
            boolean hasCombination = false;
            Iterator var4 = TensuraData.getElementCombination().iterator();

            while(var4.hasNext()) {
               SlottingElementCombination elementCombination = (SlottingElementCombination)var4.next();
               if (elementCombination.getEarthCores() == getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get()) && elementCombination.getFireCores() == getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get()) && elementCombination.getSpaceCores() == getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get()) && elementCombination.getWaterCores() == getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get()) && elementCombination.getWindCores() == getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_WIND.get())) {
                  hasCombination = true;
                  break;
               }
            }

            if (hasCombination) {
               entity.m_6672_(pHand);
            }

            return hasCombination;
         }
      }
   }

   public static boolean onRelease(ItemStack pStack, LivingEntity pLiving, int pTimeLeft) {
      if (pLiving.m_6144_()) {
         return false;
      } else {
         int cores = getContentWeight(pStack);
         if (cores <= 0) {
            return false;
         } else {
            int useTicks = pStack.m_41779_() - pTimeLeft;
            if (useTicks < 10) {
               return false;
            } else {
               if (cores >= 3 && pLiving instanceof ServerPlayer) {
                  ServerPlayer serverPlayer = (ServerPlayer)pLiving;
                  TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.INFINITY_CORES);
               }

               int earth = getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get());
               int fire = getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get());
               int space = getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get());
               int water = getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get());
               int wind = getCores(pStack, (Item)TensuraMaterialItems.ELEMENT_CORE_WIND.get());
               coreProjectileShoot(pStack, pLiving, earth, fire, space, water, wind);
               if (pLiving instanceof Player) {
                  Player player = (Player)pLiving;
                  player.m_36246_(Stats.f_12982_.m_12902_(pStack.m_41720_()));
               }

               return true;
            }
         }
      }
   }

   private static void coreProjectileShoot(ItemStack pStack, LivingEntity living, int earth, int fire, int space, int water, int wind) {
      Iterator var7 = TensuraData.getElementCombination().iterator();

      while(var7.hasNext()) {
         SlottingElementCombination combination = (SlottingElementCombination)var7.next();
         if (combination.getEarthCores() == earth && combination.getFireCores() == fire && combination.getSpaceCores() == space && combination.getWaterCores() == water && combination.getWindCores() == wind) {
            if (combination.isDud()) {
               if (living instanceof Player) {
                  Player player = (Player)living;
                  if (!player.m_150110_().f_35937_) {
                     damageEveryCore(living, pStack, 50);
                  }
               }

               living.f_19853_.m_5594_((Player)null, living.m_20183_(), SoundEvents.f_12018_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else {
               Optional<EntityType<?>> entityType = EntityType.m_20632_(combination.getProjectile().getEntity().toString());
               if (entityType.isPresent()) {
                  Entity entity = ((EntityType)entityType.get()).m_20615_(living.m_9236_());
                  if (entity instanceof Projectile) {
                     Projectile projectile = (Projectile)entity;
                     if (living instanceof Player) {
                        Player player = (Player)living;
                        if (!player.m_150110_().f_35937_) {
                           damageEveryCore(living, pStack, 12);
                        }
                     }

                     living.m_6674_(living.m_7655_());
                     if (projectile instanceof TensuraProjectile) {
                        TensuraProjectile magicProjectile = (TensuraProjectile)projectile;
                        magicProjectile.m_5602_(living);
                        magicProjectile.setSpiritAttack(true);
                        float damage = combination.getProjectile().getDamage();
                        damage *= DamageSourceHelper.getWeaponDamage(living, (Entity)null, living.m_7655_() == InteractionHand.OFF_HAND);
                        magicProjectile.setDamage(damage);
                        magicProjectile.setSpeed(combination.getProjectile().getSpeed());
                        magicProjectile.setKnockForce(combination.getProjectile().getKnockForce());
                        magicProjectile.setExplosionRadius(combination.getProjectile().getExplosionRadius());
                        magicProjectile.setBurnTicks(combination.getProjectile().getBurnTicks());
                        magicProjectile.m_20242_(combination.getProjectile().isNoGravity());
                        MobEffect mobEffect = (MobEffect)ForgeRegistries.MOB_EFFECTS.getValue(combination.getProjectile().getMagicEffect().getEffectID());
                        if (mobEffect != null) {
                           magicProjectile.setMobEffect(new MobEffectInstance(mobEffect, combination.getProjectile().getMagicEffect().getTicks(), combination.getProjectile().getMagicEffect().getLevel()));
                           magicProjectile.setEffectRange(combination.getProjectile().getMagicEffect().getRange());
                        }

                        magicProjectile.setPosAndShoot(living);
                     } else {
                        Vector3f vector3f = new Vector3f(living.m_20252_(2.0F));
                        projectile.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.2F, 0.0F);
                     }

                     living.m_9236_().m_7967_(projectile);
                     living.f_19853_.m_5594_((Player)null, living.m_20183_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               } else {
                  living.f_19853_.m_5594_((Player)null, living.m_20183_(), SoundEvents.f_12315_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
            break;
         }
      }

   }

   private static void damageEveryCore(LivingEntity entity, ItemStack gear, int amount) {
      Item var4 = gear.m_41720_();
      if (var4 instanceof TieredItem) {
         TieredItem tieredItem = (TieredItem)var4;
         if (tieredItem.m_43314_().equals(TensuraToolTiers.HIHIIROKANE)) {
            return;
         }
      }

      CompoundTag compoundtag = gear.m_41784_();
      if (compoundtag.m_128441_("Cores")) {
         ListTag newList = new ListTag();
         ListTag listtag = compoundtag.m_128437_("Cores", 10);

         for(int i = 0; i < listtag.size(); ++i) {
            CompoundTag itemTag = listtag.m_128728_(i);
            ItemStack elementCore = ItemStack.m_41712_(itemTag);
            elementCore.m_41622_(amount, entity, (living) -> {
               living.m_21190_(entity.m_7655_());
            });
            if (!elementCore.m_41619_()) {
               elementCore.m_41739_(itemTag);
               newList.add(itemTag);
            }
         }

         compoundtag.m_128365_("Cores", newList);
      }
   }

   public static int getElementalSlots(ItemStack stack) {
      return stack.m_41720_() instanceof KunaiItem ? 0 : stack.getEnchantmentLevel((Enchantment)TensuraEnchantments.SLOTTING.get());
   }

   private static Stream<ItemStack> getContents(ItemStack pStack) {
      CompoundTag compoundtag = pStack.m_41783_();
      if (compoundtag == null) {
         return Stream.empty();
      } else {
         ListTag listtag = compoundtag.m_128437_("Cores", 10);
         Stream var10000 = listtag.stream();
         Objects.requireNonNull(CompoundTag.class);
         return var10000.map(CompoundTag.class::cast).map(ItemStack::m_41712_);
      }
   }

   private static int getCores(ItemStack gear, Item core) {
      int coreNumber = 0;
      Iterator var3 = getContents(gear).toList().iterator();

      while(var3.hasNext()) {
         ItemStack cores = (ItemStack)var3.next();
         if (cores.m_150930_(core)) {
            ++coreNumber;
         }
      }

      return coreNumber;
   }

   public static int getContentWeight(ItemStack pStack) {
      return getContents(pStack).mapToInt(ItemStack::m_41613_).sum();
   }

   public static Optional<TooltipComponent> tooltipCore(ItemStack pStack) {
      if (getContentWeight(pStack) <= 0) {
         return Optional.empty();
      } else {
         NonNullList<ItemStack> list = NonNullList.m_122779_();
         Stream var10000 = getContents(pStack);
         Objects.requireNonNull(list);
         var10000.forEach(list::add);
         return getContentWeight(pStack) >= getElementalSlots(pStack) ? Optional.of(new BundleTooltip(list, 64)) : Optional.of(new BundleTooltip(list, getContentWeight(pStack)));
      }
   }

   public static boolean putCoreOnTool(ItemStack pStack, ItemStack pOther, Slot pSlot, SlotAccess pAccess, ClickAction pAction, Player pPlayer) {
      if (pAction == ClickAction.SECONDARY && pSlot.m_150651_(pPlayer)) {
         if (pOther.m_41619_()) {
            removeCore(pStack).ifPresent((core) -> {
               playRemoveOneSound(pPlayer);
               pAccess.m_142104_(core);
            });
            return true;
         }

         if (pOther.m_41720_() instanceof ElementCoreItem) {
            int i = add(pStack, pOther.m_41777_());
            if (i > 0) {
               playInsertSound(pPlayer);
               pOther.m_41774_(i);
            }

            return true;
         }
      }

      return false;
   }

   public static boolean putToolUponCore(ItemStack pStack, Slot pSlot, ClickAction pAction, Player pPlayer) {
      if (pAction != ClickAction.SECONDARY) {
         return false;
      } else {
         ItemStack itemstack = pSlot.m_7993_();
         if (itemstack.m_41619_()) {
            removeCore(pStack).ifPresent((core) -> {
               pSlot.m_5852_(core);
               pSlot.m_6654_();
               playRemoveOneSound(pPlayer);
            });
         } else if (itemstack.m_41720_().m_142095_() && itemstack.m_41720_() instanceof ElementCoreItem) {
            int i = getElementalSlots(pStack) - getContentWeight(pStack);
            int j = add(pStack, pSlot.m_150647_(itemstack.m_41613_(), i, pPlayer));
            if (j > 0) {
               playInsertSound(pPlayer);
            }
         }

         return true;
      }
   }

   private static int add(ItemStack gear, ItemStack pInsertedStack) {
      if (!pInsertedStack.m_41619_() && pInsertedStack.m_41720_().m_142095_() && pInsertedStack.m_41720_() instanceof ElementCoreItem) {
         CompoundTag compoundtag = gear.m_41784_();
         if (!compoundtag.m_128441_("Cores")) {
            compoundtag.m_128365_("Cores", new ListTag());
         }

         int i = getContentWeight(gear);
         int k = Math.min(pInsertedStack.m_41613_(), getElementalSlots(gear) - i);
         if (k == 0) {
            return 0;
         } else {
            ListTag listtag = compoundtag.m_128437_("Cores", 10);
            ItemStack stack = pInsertedStack.m_41777_();
            stack.m_41764_(k);
            CompoundTag tag = new CompoundTag();
            stack.m_41739_(tag);
            listtag.add(0, tag);
            return k;
         }
      } else {
         return 0;
      }
   }

   private static Optional<ItemStack> removeCore(ItemStack pStack) {
      CompoundTag compoundtag = pStack.m_41784_();
      if (!compoundtag.m_128441_("Cores")) {
         return Optional.empty();
      } else {
         ListTag listtag = compoundtag.m_128437_("Cores", 10);
         if (listtag.isEmpty()) {
            return Optional.empty();
         } else {
            CompoundTag tag = listtag.m_128728_(0);
            ItemStack itemstack = ItemStack.m_41712_(tag).m_41777_();
            listtag.remove(0);
            if (listtag.isEmpty()) {
               pStack.m_41749_("Cores");
            } else {
               compoundtag.m_128365_("Cores", listtag);
            }

            return Optional.of(itemstack);
         }
      }
   }

   private static void playRemoveOneSound(Entity pEntity) {
      pEntity.m_5496_(SoundEvents.f_184216_, 0.8F, 0.8F + pEntity.m_9236_().m_213780_().m_188501_() * 0.4F);
   }

   private static void playInsertSound(Entity pEntity) {
      pEntity.m_5496_(SoundEvents.f_11859_, 0.8F, 0.8F + pEntity.m_9236_().m_213780_().m_188501_() * 0.4F);
   }
}
