package com.github.b4ndithelps.tennogamenolife.client.renderer.layer;

import com.github.b4ndithelps.tennogamenolife.client.model.XiubiHaloModel;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.ExMachinaDecorativeManager;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.IExMachinaDecorativeRace;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.model.PlayerModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;

/**
 * ExMachina种族玩家光环渲染层
 * 为所有ExMachina种族玩家添加光环装饰
 */
public class PlayerExMachinaHaloLayer extends RenderLayer<AbstractClientPlayer, PlayerModel<AbstractClientPlayer>> {
    
    private static final ResourceLocation HALO_TEXTURE =
        new ResourceLocation("tennogamenolife", "textures/entity/xiubi/halo.png");
    
    private final XiubiHaloModel haloModel;
    
    public PlayerExMachinaHaloLayer(RenderLayerParent<AbstractClientPlayer, PlayerModel<AbstractClientPlayer>> renderer, 
                                   XiubiHaloModel haloModel) {
        super(renderer);
        this.haloModel = haloModel;
    }
    
    @Override
    public void render(PoseStack poseStack, MultiBufferSource buffer, int packedLight, 
                      AbstractClientPlayer player, float limbSwing, float limbSwingAmount, 
                      float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
        
        // 检查玩家是否是ExMachina种族
        if (!isExMachinaRace(player)) {
            return;
        }
        
        // 检查是否应该显示装饰
        if (!ExMachinaDecorativeManager.shouldShowDecorative(player)) {
            return;
        }
        
        // 绑定光环到玩家头部并设置动画
        bindHaloToPlayerHead(player, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);

        // 渲染光环
        this.haloModel.renderToBuffer(
            poseStack,
            buffer.getBuffer(RenderType.entityTranslucent(HALO_TEXTURE)),
            packedLight,
            OverlayTexture.NO_OVERLAY,
            1.0F, 1.0F, 1.0F, 0.8F  // 半透明效果
        );
    }
    
    /**
     * 绑定光环到玩家头部，保持自定义位置
     */
    private void bindHaloToPlayerHead(Player player, float limbSwing, float limbSwingAmount,
                                     float ageInTicks, float netHeadYaw, float headPitch) {

        // 获取玩家模型并设置动画
        PlayerModel<AbstractClientPlayer> playerModel = this.getParentModel();
        playerModel.setupAnim((AbstractClientPlayer)player, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);

        // 设置光环的基础动画
        this.haloModel.setupAnim(null, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);

        // 绑定光环到玩家头部（只复制旋转，保持自定义位置）
        bindHaloRotationOnly(playerModel.head);
    }

    /**
     * 只复制头部旋转到光环，保持光环的自定义位置
     */
    private void bindHaloRotationOnly(ModelPart playerHead) {
        // 光环模型会通过setupAnim自动处理头部跟随
        // 这里不需要额外的绑定逻辑
    }

    /**
     * 检查玩家是否是ExMachina种族
     */
    private boolean isExMachinaRace(Player player) {
        Race race = TensuraPlayerCapability.getRace(player);
        return race instanceof IExMachinaDecorativeRace;
    }
}
