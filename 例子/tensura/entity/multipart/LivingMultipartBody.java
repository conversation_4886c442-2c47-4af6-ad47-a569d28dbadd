package com.github.manasmods.tensura.entity.multipart;

import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestPartHurtAnimation;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockBehaviour.BlockStateBase;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.BooleanOp;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class LivingMultipartBody extends TensuraTamableEntity implements ILivingPartEntity {
   private static final EntityDataAccessor<Integer> BODY_INDEX;
   private static final EntityDataAccessor<Float> BODY_X_ROT;
   private static final EntityDataAccessor<Optional<UUID>> HEAD_UUID;
   private static final EntityDataAccessor<Optional<UUID>> PARENT_UUID;
   private static final EntityDataAccessor<Optional<UUID>> CHILD_UUID;
   private static final EntityDataAccessor<Boolean> END_SEGMENT;
   protected float radius;
   protected float angleYaw;
   private double prevHeight = 0.0D;
   public EntityDimensions multipartSize;

   public LivingMultipartBody(EntityType<? extends LivingMultipartBody> type, Level worldIn) {
      super(type, worldIn);
      this.multipartSize = type.m_20680_();
   }

   public LivingMultipartBody(EntityType<? extends LivingMultipartBody> type, LivingEntity parent) {
      super(type, parent.f_19853_);
      this.setParent(parent);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(HEAD_UUID, Optional.empty());
      this.f_19804_.m_135372_(PARENT_UUID, Optional.empty());
      this.f_19804_.m_135372_(CHILD_UUID, Optional.empty());
      this.f_19804_.m_135372_(BODY_INDEX, 0);
      this.f_19804_.m_135372_(BODY_X_ROT, 0.0F);
      this.f_19804_.m_135372_(END_SEGMENT, Boolean.FALSE);
   }

   public void m_7380_(@NotNull CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getHeadId() != null) {
         compound.m_128362_("HeadUUID", this.getHeadId());
      }

      if (this.getParentId() != null) {
         compound.m_128362_("ParentUUID", this.getParentId());
      }

      if (this.getChildId() != null) {
         compound.m_128362_("ChildUUID", this.getChildId());
      }

      compound.m_128379_("EndSegment", this.isEndSegment());
      compound.m_128405_("BodyIndex", this.getBodyIndex());
      compound.m_128350_("PartAngle", this.angleYaw);
      compound.m_128350_("PartRadius", this.radius);
   }

   public void m_7378_(@NotNull CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("HeadUUID")) {
         this.setHeadId(compound.m_128342_("HeadUUID"));
      }

      if (compound.m_128403_("ParentUUID")) {
         this.setParentId(compound.m_128342_("ParentUUID"));
      }

      if (compound.m_128403_("ChildUUID")) {
         this.setChildId(compound.m_128342_("ChildUUID"));
      }

      this.setEndSegment(compound.m_128471_("EndSegment"));
      this.setBodyIndex(compound.m_128451_("BodyIndex"));
      this.angleYaw = compound.m_128457_("PartAngle");
      this.radius = compound.m_128457_("PartRadius");
   }

   public int getBodyIndex() {
      return (Integer)this.f_19804_.m_135370_(BODY_INDEX);
   }

   public void setBodyIndex(int index) {
      this.f_19804_.m_135381_(BODY_INDEX, index);
   }

   @Nullable
   public UUID getHeadId() {
      return (UUID)((Optional)this.f_19804_.m_135370_(HEAD_UUID)).orElse((Object)null);
   }

   public void setHeadId(@Nullable UUID uniqueId) {
      this.f_19804_.m_135381_(HEAD_UUID, Optional.ofNullable(uniqueId));
   }

   public Entity getHead() {
      UUID id = this.getHeadId();
      return id != null && !this.f_19853_.f_46443_ ? ((ServerLevel)this.f_19853_).m_8791_(id) : null;
   }

   public void setHead(Entity entity) {
      this.setHeadId(entity.m_20148_());
   }

   @Nullable
   public UUID getParentId() {
      return (UUID)((Optional)this.f_19804_.m_135370_(PARENT_UUID)).orElse((Object)null);
   }

   public void setParentId(@Nullable UUID uniqueId) {
      this.f_19804_.m_135381_(PARENT_UUID, Optional.ofNullable(uniqueId));
   }

   @Nullable
   public Entity getParent() {
      UUID id = this.getParentId();
      return id != null && !this.f_19853_.f_46443_ ? ((ServerLevel)this.f_19853_).m_8791_(id) : null;
   }

   public void setParent(Entity entity) {
      this.setParentId(entity.m_20148_());
   }

   @Nullable
   public UUID getChildId() {
      return (UUID)((Optional)this.f_19804_.m_135370_(CHILD_UUID)).orElse((Object)null);
   }

   public Entity getChild() {
      UUID id = this.getChildId();
      return id != null && !this.f_19853_.f_46443_ ? ((ServerLevel)this.f_19853_).m_8791_(id) : null;
   }

   public void setChildId(@Nullable UUID uniqueId) {
      this.f_19804_.m_135381_(CHILD_UUID, Optional.ofNullable(uniqueId));
   }

   public boolean isEndSegment() {
      return (Boolean)this.f_19804_.m_135370_(END_SEGMENT);
   }

   public void setEndSegment(boolean end) {
      this.f_19804_.m_135381_(END_SEGMENT, end);
   }

   public float m_146909_() {
      return (Float)this.f_19804_.m_135370_(BODY_X_ROT);
   }

   public boolean canDrownInFluidType(FluidType type) {
      return true;
   }

   public float getBackOffset() {
      return this.m_20205_() / 2.0F;
   }

   public boolean m_7306_(@NotNull Entity entity) {
      return this == entity || this.getHead() == entity || this.getParent() == entity;
   }

   public boolean m_7307_(Entity pEntity) {
      if (this.getHead() != null && pEntity.m_7307_(this.getHead())) {
         return true;
      } else {
         return Objects.equals(this.getHeadId(), pEntity.m_20148_()) ? true : super.m_7307_(pEntity);
      }
   }

   public boolean m_8023_() {
      return super.m_8023_() || this.getParent() != null;
   }

   public boolean m_6673_(@NotNull DamageSource source) {
      return this.getParent() == null ? false : this.getParent().m_6673_(source);
   }

   public boolean m_20068_() {
      return false;
   }

   public boolean m_6087_() {
      return true;
   }

   public boolean m_7848_(@NotNull Animal pOtherAnimal) {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, @NotNull DamageSource pSource) {
      return false;
   }

   public boolean m_20329_(@NotNull Entity entityIn) {
      return false;
   }

   public boolean m_6469_(@NotNull DamageSource source, float damage) {
      Entity parent = this.getParent();
      return parent == null ? false : parent.m_6469_(source, damage);
   }

   public boolean m_147207_(MobEffectInstance pEffectInstance, @Nullable Entity pEntity) {
      Entity var4 = this.getHead();
      if (var4 instanceof LivingEntity) {
         LivingEntity head = (LivingEntity)var4;
         return head.m_147207_(pEffectInstance, pEntity);
      } else {
         return super.m_147207_(pEffectInstance, pEntity);
      }
   }

   public boolean m_21195_(MobEffect pEffect) {
      Entity var3 = this.getHead();
      if (var3 instanceof LivingEntity) {
         LivingEntity head = (LivingEntity)var3;
         return head.m_21195_(pEffect);
      } else {
         return super.m_21195_(pEffect);
      }
   }

   public void m_5634_(float pHealAmount) {
      Entity var3 = this.getHead();
      if (var3 instanceof LivingEntity) {
         LivingEntity head = (LivingEntity)var3;
         head.m_5634_(pHealAmount);
      } else {
         super.m_5634_(pHealAmount);
      }

   }

   protected void m_8034_() {
   }

   public void m_6842_(boolean pInvisible) {
      super.m_6842_(pInvisible);
      if (this.getChild() != null) {
         this.getChild().m_6842_(pInvisible);
      }

   }

   public void m_21837_(boolean pSitting) {
      super.m_21837_(pSitting);
      Entity var3 = this.getChild();
      if (var3 instanceof TamableAnimal) {
         TamableAnimal child = (TamableAnimal)var3;
         child.m_21837_(pSitting);
      }

   }

   protected void miscUpdate() {
      this.f_19817_ = false;
      this.m_20256_(Vec3.f_82478_);
      if (!this.f_19853_.m_5776_() && this.getHead() != null && this.m_142038_() != this.getHead().m_142038_()) {
         this.m_146915_(this.getHead().m_142038_());
      }

   }

   public void m_8119_() {
      super.m_8119_();
      this.miscUpdate();
      if (this.f_19797_ > 1) {
         Entity parent = this.getParent();
         this.m_6210_();
         if (parent != null && !this.f_19853_.f_46443_) {
            if (parent instanceof TensuraTamableEntity) {
               TensuraTamableEntity parentEntity = (TensuraTamableEntity)parent;
               if (parentEntity.f_20916_ > 0 || parentEntity.f_20919_ > 0) {
                  TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                     return this;
                  }), new RequestPartHurtAnimation(this.m_19879_(), parentEntity.m_19879_()));
                  this.f_20916_ = parentEntity.f_20916_;
                  this.f_20919_ = parentEntity.f_20919_;
               }

               if (parentEntity.m_6084_()) {
                  this.m_21153_(parentEntity.m_21223_());
                  this.m_21839_(parentEntity.m_21827_());
               }
            }

            if (parent.m_213877_()) {
               this.m_5907_();
               this.m_142687_(RemovalReason.DISCARDED);
               return;
            }

            boolean var10000;
            LivingMultipartHead head;
            label70: {
               Entity entity = this.getHead();
               if (entity instanceof LivingMultipartHead) {
                  head = (LivingMultipartHead)entity;
                  if (!head.m_213877_() && this.getBodyIndex() + 1 > head.getSegmentCount()) {
                     var10000 = true;
                     break label70;
                  }
               }

               var10000 = false;
            }

            boolean shouldRemove;
            LivingMultipartBody body;
            label98: {
               shouldRemove = var10000;
               if (!shouldRemove) {
                  label95: {
                     if (parent instanceof LivingMultipartBody) {
                        body = (LivingMultipartBody)parent;
                        if (body.getChild() != this) {
                           break label95;
                        }
                     }

                     var10000 = false;
                     break label98;
                  }
               }

               var10000 = true;
            }

            label99: {
               shouldRemove = var10000;
               if (!shouldRemove) {
                  label96: {
                     if (parent instanceof LivingMultipartHead) {
                        head = (LivingMultipartHead)parent;
                        if (head.getChild() != this) {
                           break label96;
                        }
                     }

                     var10000 = false;
                     break label99;
                  }
               }

               var10000 = true;
            }

            shouldRemove = var10000;
            if (shouldRemove) {
               if (parent instanceof LivingMultipartBody) {
                  body = (LivingMultipartBody)parent;
                  body.setEndSegment(true);
               }

               this.m_5907_();
               this.m_142687_(RemovalReason.DISCARDED);
            }
         } else if (!this.f_19853_.f_46443_ && this.f_19797_ > 20) {
            this.m_142687_(RemovalReason.DISCARDED);
            this.m_5907_();
         }

      }
   }

   public void m_6138_() {
      List<Entity> list = this.f_19853_.m_45933_(this, this.m_20191_().m_82363_(0.2D, 0.0D, 0.2D));
      Entity parent = this.getParent();
      if (parent != null) {
         Iterator var3 = list.iterator();

         while(true) {
            Entity entity;
            ILivingPartEntity part;
            do {
               do {
                  do {
                     if (!var3.hasNext()) {
                        return;
                     }

                     entity = (Entity)var3.next();
                  } while(!entity.m_6094_());
               } while(entity == parent);

               if (!(entity instanceof ILivingPartEntity)) {
                  break;
               }

               part = (ILivingPartEntity)entity;
            } while(Objects.equals(part.getHeadId(), this.m_20148_()));

            entity.m_7334_(this);
         }
      }
   }

   public Vec3 repositionParts(float parentOffset, Vec3 parentPosition, float parentXRot, float parentYRot, float ourYRot, boolean doHeight) {
      Vec3 parentButt = parentPosition.m_82549_(this.getOffsetVec(-parentOffset * this.m_6134_(), parentXRot, parentYRot));
      Vec3 ourButt = parentButt.m_82549_(this.getOffsetVec((-this.getBackOffset() - 0.5F * this.m_20205_()) * this.m_6134_(), this.m_146909_(), ourYRot));
      Vec3 avg = new Vec3((parentButt.f_82479_ + ourButt.f_82479_) / 2.0D, (parentButt.f_82480_ + ourButt.f_82480_) / 2.0D, (parentButt.f_82481_ + ourButt.f_82481_) / 2.0D);
      double xDist = parentButt.f_82479_ - ourButt.f_82479_;
      double zDist = parentButt.f_82481_ - ourButt.f_82481_;
      double dist = Math.sqrt(xDist * xDist + zDist * zDist);
      double height = doHeight ? this.getLowPartHeight(parentButt.f_82479_, parentButt.f_82480_, parentButt.f_82481_) + this.getHighPartHeight(ourButt.f_82479_, ourButt.f_82480_, ourButt.f_82481_) : 0.0D;
      if (Math.abs(this.prevHeight - height) > 0.2D) {
         this.prevHeight = height;
      }

      double partYDest = Mth.m_14008_(this.prevHeight, -0.4000000059604645D, 0.4000000059604645D);
      float f = (float)(Mth.m_14136_(zDist, xDist) * 57.2957763671875D) - 90.0F;
      float rawAngle = Mth.m_14177_((float)(-(Mth.m_14136_(partYDest, dist) * 180.0D / 3.1415927410125732D)));
      float f2 = this.getLimitAngle(this.m_146909_(), rawAngle, 10.0F);
      this.m_146926_(f2);
      this.f_19804_.m_135381_(BODY_X_ROT, f2);
      this.m_146922_(f);
      this.f_20885_ = f;
      this.m_7678_(avg.f_82479_, avg.f_82480_, avg.f_82481_, f, f2);
      return avg;
   }

   public double getLowPartHeight(double x, double yIn, double z) {
      if (this.isFluidAt(x, yIn, z)) {
         return 0.0D;
      } else {
         double checkAt;
         for(checkAt = 0.0D; checkAt > -3.0D && !this.isOpaqueBlockAt(x, yIn + checkAt, z); checkAt -= 0.2D) {
         }

         return checkAt;
      }
   }

   public double getHighPartHeight(double x, double yIn, double z) {
      if (this.isFluidAt(x, yIn, z)) {
         return 0.0D;
      } else {
         double checkAt;
         for(checkAt = 0.0D; checkAt <= 3.0D && this.isOpaqueBlockAt(x, yIn + checkAt, z); checkAt += 0.2D) {
         }

         return checkAt;
      }
   }

   public boolean isFluidAt(double x, double y, double z) {
      if (this.f_19794_) {
         return false;
      } else {
         return !this.f_19853_.m_6425_(new BlockPos(x, y, z)).m_76178_();
      }
   }

   public boolean isOpaqueBlockAt(double x, double y, double z) {
      if (this.f_19794_) {
         return false;
      } else {
         float f = 1.0F;
         Vec3 vec3 = new Vec3(x, y, z);
         AABB aabb = AABB.m_165882_(vec3, 1.0D, 1.0E-6D, 1.0D);
         return this.f_19853_.m_45556_(aabb).filter(Predicate.not(BlockStateBase::m_60795_)).anyMatch((shape) -> {
            BlockPos blockpos = new BlockPos(vec3);
            return shape.m_60828_(this.f_19853_, blockpos) && Shapes.m_83157_(shape.m_60812_(this.f_19853_, blockpos).m_83216_(vec3.f_82479_, vec3.f_82480_, vec3.f_82481_), Shapes.m_83064_(aabb), BooleanOp.f_82689_);
         });
      }
   }

   public void onServerHurt(LivingEntity parent) {
      if (parent.f_20919_ > 0) {
         this.f_20919_ = parent.f_20919_;
      }

      if (parent.f_20916_ > 0) {
         this.f_20916_ = parent.f_20916_;
      }

   }

   static {
      BODY_INDEX = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135028_);
      BODY_X_ROT = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135029_);
      HEAD_UUID = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135041_);
      PARENT_UUID = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135041_);
      CHILD_UUID = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135041_);
      END_SEGMENT = SynchedEntityData.m_135353_(LivingMultipartBody.class, EntityDataSerializers.f_135035_);
   }
}
