package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class FarsightSkill extends Skill {
   public FarsightSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 3000.0D;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         CompoundTag tag = instance.getOrCreateTag();
         if (!tag.m_128441_("range")) {
            tag.m_128347_("range", 2.0D);
         }

         instance.markDirty();
         if (instance.isMastered(player)) {
            int distance = Math.min((int)tag.m_128459_("range") * 2, 60);
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.getAnalysisDistance() < distance) {
                  tag.m_128405_("oldRange", cap.getAnalysisDistance());
                  cap.setAnalysisDistance(distance);
                  TensuraSkillCapability.sync(player);
               }

            });
         }
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (instance.isMastered(player) && this.isHeld(entity)) {
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               int oldRange = instance.getOrCreateTag().m_128451_("oldRange");
               if (cap.getAnalysisDistance() != oldRange) {
                  cap.setAnalysisDistance(oldRange);
                  TensuraSkillCapability.sync(player);
               }

            });
         }
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity living, double delta) {
      CompoundTag tag = instance.getOrCreateTag();
      double newRange = tag.m_128459_("range") + delta;
      if (newRange > 50.0D) {
         newRange = 50.0D;
      } else if (newRange < 2.0D) {
         newRange = 2.0D;
      }

      if (tag.m_128459_("range") != newRange) {
         tag.m_128347_("range", newRange);
         instance.markDirty();
         if (instance.isMastered(living) && living instanceof Player) {
            Player player = (Player)living;
            int distance = Math.min((int)newRange * 2, 60);
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.getAnalysisDistance() != distance) {
                  cap.setAnalysisDistance(distance);
                  TensuraSkillCapability.sync(player);
               }

            });
         }
      }

   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 80 == 0 && heldTicks > 0) {
         this.addMasteryPoint(instance, entity);
      }

      return true;
   }
}
