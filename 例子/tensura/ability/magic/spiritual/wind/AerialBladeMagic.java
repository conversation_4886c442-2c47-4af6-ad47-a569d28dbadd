package com.github.manasmods.tensura.ability.magic.spiritual.wind;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class AerialBladeMagic extends SpiritualMagic {
   public AerialBladeMagic() {
      super(MagicElemental.WIND, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 100;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 15000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
            instance.getOrCreateTag().m_128405_("HeldTicks", 0);
            instance.markDirty();
            Level level = entity.m_9236_();
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            int radius = instance.isMastered(entity) ? 4 : 3;
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, (double)radius);
            BlockPos ahead = result.m_82425_();
            Vec3 aheadVec = new Vec3((double)ahead.m_123341_(), (double)ahead.m_123342_(), (double)ahead.m_123343_());
            RandomSource random = entity.m_217043_();
            TensuraParticleHelper.addServerParticlesAroundPos(random, level, aheadVec, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(random, level, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 4.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(random, level, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 5.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(random, level, aheadVec, ParticleTypes.f_123766_, 4.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(random, level, aheadVec, ParticleTypes.f_123766_, 5.0D);
            AABB entityInflation = entity.m_20191_().m_82400_((double)radius);
            List<LivingEntity> aroundList = entity.m_9236_().m_6443_(LivingEntity.class, entityInflation, (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });

            LivingEntity target;
            for(Iterator var12 = aroundList.iterator(); var12.hasNext(); target.f_19864_ = true) {
               target = (LivingEntity)var12.next();
               target.m_20256_(aheadVec.m_82546_(target.m_20182_()).m_82541_().m_82490_(2.0D));
            }

            AABB box = (new AABB(ahead)).m_82400_((double)radius);
            List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, box.m_82367_(entityInflation), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               Iterator var14 = list.iterator();

               while(true) {
                  LivingEntity target;
                  Player player;
                  do {
                     if (!var14.hasNext()) {
                        return;
                     }

                     target = (LivingEntity)var14.next();
                     if (!(target instanceof Player)) {
                        break;
                     }

                     player = (Player)target;
                  } while(player.m_150110_().f_35934_);

                  target.m_6469_(DamageSourceHelper.addSkillAndCost(TensuraDamageSources.elementalAttack("tensura.wind_attack", entity, true), this.magiculeCost(entity, instance), instance), 200.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 3.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
               }
            }
         }
      }
   }
}
