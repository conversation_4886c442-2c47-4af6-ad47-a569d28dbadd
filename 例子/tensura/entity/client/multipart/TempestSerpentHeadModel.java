package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.TempestSerpentEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class TempestSerpentHeadModel extends AnimatedGeoModel<TempestSerpentEntity> {
   public ResourceLocation getModelResource(TempestSerpentEntity object) {
      return new ResourceLocation("tensura", "geo/tempest_serpent_head.geo.json");
   }

   public ResourceLocation getTextureResource(TempestSerpentEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/tempest_serpent/tempest_serpent.png");
   }

   public ResourceLocation getAnimationResource(TempestSerpentEntity entity) {
      return new ResourceLocation("tensura", "animations/multipart/tempest_serpent_head.animation.json");
   }
}
