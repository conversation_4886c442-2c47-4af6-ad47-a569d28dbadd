package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.item.Item;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;

import java.util.ArrayList;
import java.util.List;

/**
 * 狐疑之神帆楼
 * 具备完整的装饰建模功能
 */
public class HorouRace extends Race implements IExMachinaDecorativeRace {

    private double baseHealth = 5200.0;         // 基础生命值
    private double baseAttackDamage = 25.0;     // 基础攻击伤害
    private double baseAttackSpeed = 6.0;       // 基础攻击速度
    private double knockbackResistance = 1.0;   // 击退抗性
    private double jumpHeight = 2.0;            // 跳跃高度
    private double movementSpeed = 0.18;        // 移动速度
    private double sprintSpeed = 0.25;          // 冲刺速度
    private double auraMin = 150000.0;           // 最小灵气值
    private double auraMax = 250000.0;           // 最大灵气值
    private double startingMagiculeMin = 50000.0; // 最小初始魔素值
    private double startingMagiculeMax = 150000.0; // 最大初始魔素值

    private float playerSize = 2.0f;            // 玩家大小

    public HorouRace() {
        super(Difficulty.HARD);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.BODY_ARMOR.get());
        list.add(ExtraSkills.HEAVENLY_EYE.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ExtraSkills.WEATHER_DOMINATION.get());
        
        list.add(ExtraSkills.SPATIAL_MANIPULATION.get());
        list.add(ExtraSkills.SPATIAL_DOMINATION.get());
        list.add(ExtraSkills.ULTRASPEED_REGENERATION.get());
        list.add(ExtraSkills.GRAVITY_MANIPULATION.get());
        
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.COLD_RESISTANCE.get());
        list.add(ResistanceSkills.CORROSION_RESISTANCE.get());
        list.add(ResistanceSkills.DARKNESS_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.EARTH_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.ELECTRICITY_RESISTANCE.get());
        list.add(ResistanceSkills.GRAVITY_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.HEAT_RESISTANCE.get());
        list.add(ResistanceSkills.LIGHT_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.SPATIAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.WATER_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.WIND_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_NULLIFICATION.get());
        list.add(TenreincarnationSkill.REISEN.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return true;
    }

    @Override
    public boolean isDivine() {
        return true;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }
    
    @Override
    public double getEvolutionPercentage(Player player) {
        double totalPercentage = 0.0;
        
        if (TensuraPlayerCapability.isTrueDemonLord(player) || TensuraPlayerCapability.isTrueHero(player)) {
            totalPercentage += 50.0;
        }
        
        double epPercentage = TensuraPlayerCapability.getBaseEP(player) * 100.0 / 2000000.0;
        totalPercentage += Math.min(25.0, epPercentage);
        
        Item dragonEssence = ForgeRegistries.ITEMS.getValue(new ResourceLocation("tensura", "dragon_essence"));
        if (dragonEssence != null) {
            double essenceCount = 0.0;
            if (player instanceof LocalPlayer) {
                LocalPlayer localPlayer = (LocalPlayer)player;
                essenceCount = localPlayer.getStats().getValue(Stats.ITEM_USED.get(dragonEssence));
            } else if (player instanceof ServerPlayer) {
                ServerPlayer serverPlayer = (ServerPlayer)player;
                essenceCount = serverPlayer.getStats().getValue(Stats.ITEM_USED.get(dragonEssence));
            }
            
            double essencePercentage = Math.min(25.0, essenceCount * 2.5);
            totalPercentage += essencePercentage;
        }
        
        double starEssenceCount = 0.0;
        if (player instanceof LocalPlayer) {
            LocalPlayer localPlayer = (LocalPlayer)player;
            starEssenceCount = localPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.STAR_ESSENCE.get()));
        } else if (player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)player;
            starEssenceCount = serverPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.STAR_ESSENCE.get()));
        }
        
        if (starEssenceCount >= 1.0) {
            totalPercentage += 25.0;
        }
        
        return Math.min(100.0, totalPercentage);
    }
    
    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        
        list.add(Component.translatable("tensura.evolution_menu.awaken_requirement", 
                Component.translatable("tensura.attribute.true_demon_lord.name").withStyle(ChatFormatting.DARK_PURPLE), 
                Component.translatable("tensura.attribute.true_hero.name").withStyle(ChatFormatting.GOLD)));
        
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement", 1000000));
        
        list.add(Component.translatable("tensura.evolution_menu.dragon_essence_requirement", 10));
        
        list.add(Component.translatable("tensura.evolution_menu.star_essence_requirement"));
        
        return list;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.EINZEIG.get());
        list.add(Tenreincarnationraces.EMIR_EINS.get());
        list.add(Tenreincarnationraces.HUBIE_DORA.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        return new ArrayList<>();
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return null;
    }
}