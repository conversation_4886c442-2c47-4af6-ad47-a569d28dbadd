package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

public class AbnormalConditionNullification extends ResistSkill {
   public static final ImmutableList<MobEffect> ABNORMAL_NULL;

   public AbnormalConditionNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isAbnormal(damageSource);
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : getAbnormalEffects());
   }

   public static List<MobEffect> getAbnormalEffects() {
      List<MobEffect> list = new ArrayList();
      list.addAll(ABNORMAL_NULL);
      list.addAll(AbnormalConditionResistance.ABNORMAL_RESIST);
      return list;
   }

   static {
      ABNORMAL_NULL = ImmutableList.of((MobEffect)TensuraMobEffects.FATAL_POISON.get(), (MobEffect)TensuraMobEffects.INFECTION.get(), (MobEffect)TensuraMobEffects.PARALYSIS.get(), (MobEffect)TensuraMobEffects.PETRIFICATION.get());
   }
}
