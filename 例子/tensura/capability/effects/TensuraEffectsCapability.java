package com.github.manasmods.tensura.capability.effects;

import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncEffectsCapabilityPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Registry;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraEffectsCapability implements ITensuraEffectsCapability {
   private static final Logger log = LogManager.getLogger(TensuraEffectsCapability.class);
   public static final Capability<ITensuraEffectsCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<ITensuraEffectsCapability>() {
   });
   private static final ResourceLocation IDENTIFIER = new ResourceLocation("tensura", "player_effects");
   private final List<ResourceLocation> syncedEffects = new ArrayList();
   private final HashMap<ResourceLocation, UUID> effectSources = new HashMap();
   private double infectionAge = 0.0D;
   private double severanceAmount = 0.0D;
   private float lockedXRot = 0.0F;
   private float lockedYRot = 0.0F;
   private float height = 1.0F;
   private float heightUpdate = 1.0F;
   private boolean noKnockBack = false;
   private boolean noDyingAnimation = false;
   private boolean colossusStarted = false;
   private boolean colossusPassed = false;
   private boolean colossusWon = false;
   private int insanityFOV = 0;
   private int insanityNightmare = 0;
   private int severanceRemoveSec;
   private int warpPortalTime = 0;
   private int presenceConceal = 0;
   private int presenceSense = 0;
   private BlockPos warpPos = new BlockPos(0, 0, 0);

   @SubscribeEvent
   public static void attach(AttachCapabilitiesEvent<Entity> e) {
      e.addCapability(IDENTIFIER, new TensuraEffectsCapabilityProvider());
   }

   public static LazyOptional<ITensuraEffectsCapability> getFrom(LivingEntity entity) {
      return entity.getCapability(CAPABILITY);
   }

   public static void sync(LivingEntity entity) {
      if (!entity.f_19853_.m_5776_()) {
         getFrom(entity).ifPresent((data) -> {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new SyncEffectsCapabilityPacket(data, entity.m_19879_()));
         });
      }
   }

   public boolean hasSyncedEffect(ResourceLocation effect) {
      return this.syncedEffects.contains(effect);
   }

   public void addSyncedEffect(ResourceLocation effect, LivingEntity entity) {
      if (!this.syncedEffects.contains(effect)) {
         this.syncedEffects.add(effect);
      }

      sync(entity);
   }

   public void removeSyncedEffect(ResourceLocation effect, LivingEntity entity) {
      this.syncedEffects.remove(effect);
      sync(entity);
   }

   public void clearSyncedEffects(LivingEntity entity) {
      this.syncedEffects.clear();
      sync(entity);
   }

   @Nullable
   public UUID getEffectSource(ResourceLocation effect) {
      return this.effectSources.containsKey(effect) ? (UUID)this.effectSources.get(effect) : null;
   }

   public void setEffectSource(@Nullable UUID player, ResourceLocation effect) {
      if (player == null) {
         this.effectSources.remove(effect);
      } else {
         this.effectSources.put(effect, player);
      }

   }

   public void removeEffectSource(ResourceLocation effect) {
      this.setEffectSource((UUID)null, effect);
   }

   public void setWarpPos(double x, double y, double z) {
      this.warpPos = new BlockPos(x, y, z);
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();
      tag.m_128347_("infectionAge", this.infectionAge);
      tag.m_128350_("lockedXRot", this.lockedXRot);
      tag.m_128350_("lockedYRot", this.lockedYRot);
      tag.m_128405_("insanityFOV", this.insanityFOV);
      tag.m_128405_("insanityNightmare", this.insanityNightmare);
      tag.m_128347_("severanceAmount", this.severanceAmount);
      tag.m_128405_("severanceRemoveSec", this.severanceRemoveSec);
      tag.m_128379_("noKnockBack", this.noKnockBack);
      tag.m_128379_("noDyingAnimation", this.noDyingAnimation);
      tag.m_128379_("colossusStarted", this.colossusStarted);
      tag.m_128379_("colossusPassed", this.colossusPassed);
      tag.m_128379_("colossusWon", this.colossusWon);
      tag.m_128347_("warpX", (double)this.warpPos.m_123341_());
      tag.m_128347_("warpY", (double)this.warpPos.m_123342_());
      tag.m_128347_("warpZ", (double)this.warpPos.m_123343_());
      tag.m_128405_("portalTick", this.warpPortalTime);
      tag.m_128405_("PresenceConceal", this.presenceConceal);
      tag.m_128405_("PresenceSense", this.presenceSense);
      tag.m_128350_("height", this.height);
      tag.m_128350_("heightUpdate", this.heightUpdate);
      ListTag syncedEffectList = new ListTag();

      for(int i = 0; i < this.syncedEffects.size(); ++i) {
         CompoundTag effect = new CompoundTag();
         effect.m_128359_("effect" + i, ((ResourceLocation)this.syncedEffects.get(i)).toString());
         syncedEffectList.add(effect);
      }

      tag.m_128365_("syncedEffects", syncedEffectList);
      ListTag effectList = new ListTag();
      this.effectSources.keySet().forEach((instance) -> {
         CompoundTag hashMap = new CompoundTag();
         hashMap.m_128362_(instance.toString(), (UUID)this.effectSources.get(instance));
         effectList.add(hashMap);
      });
      tag.m_128365_("effectSources", effectList);
      return tag;
   }

   public void deserializeNBT(CompoundTag tag) {
      this.infectionAge = tag.m_128459_("infectionAge");
      this.lockedXRot = tag.m_128457_("lockedXRot");
      this.lockedYRot = tag.m_128457_("lockedYRot");
      this.insanityFOV = tag.m_128451_("insanityFOV");
      this.insanityNightmare = tag.m_128451_("insanityNightmare");
      this.severanceAmount = tag.m_128459_("severanceAmount");
      this.severanceRemoveSec = tag.m_128451_("severanceRemoveSec");
      this.noKnockBack = tag.m_128471_("noKnockBack");
      this.noDyingAnimation = tag.m_128471_("noDyingAnimation");
      this.colossusStarted = tag.m_128471_("colossusStarted");
      this.colossusPassed = tag.m_128471_("colossusPassed");
      this.colossusWon = tag.m_128471_("colossusWon");
      this.warpPos = new BlockPos(tag.m_128459_("warpX"), tag.m_128459_("warpY"), tag.m_128459_("warpZ"));
      this.warpPortalTime = tag.m_128451_("portalTick");
      this.presenceConceal = tag.m_128451_("PresenceConceal");
      this.presenceSense = tag.m_128451_("PresenceSense");
      this.height = tag.m_128457_("height");
      this.heightUpdate = tag.m_128457_("heightUpdate");
      ListTag syncedEffectList = (ListTag)tag.m_128423_("syncedEffects");
      if (syncedEffectList != null) {
         this.syncedEffects.clear();

         for(int i = 0; i < syncedEffectList.size(); ++i) {
            CompoundTag spirit = (CompoundTag)syncedEffectList.get(i);
            this.syncedEffects.add(ResourceLocation.m_135820_(spirit.m_128461_("effect" + i)));
         }
      }

      Tag var7 = tag.m_128423_("effectSources");
      if (var7 instanceof CompoundTag) {
         CompoundTag compoundTag = (CompoundTag)var7;
         Iterator var8 = compoundTag.m_128431_().iterator();

         while(var8.hasNext()) {
            String name = (String)var8.next();
            this.effectSources.put(ResourceLocation.m_135820_(name), compoundTag.m_128342_(name));
         }
      }

   }

   public static boolean hasSyncedEffect(LivingEntity pLivingEntity, MobEffect effect) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      if (capability == null) {
         return false;
      } else {
         ResourceLocation resourceLocation = Registry.f_122823_.m_7981_(effect);
         return resourceLocation == null ? false : capability.hasSyncedEffect(resourceLocation);
      }
   }

   @Nullable
   public static Player getEffectSource(LivingEntity pLivingEntity, MobEffect effect) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      if (capability == null) {
         return null;
      } else {
         ResourceLocation resourceLocation = Registry.f_122823_.m_7981_(effect);
         if (resourceLocation == null) {
            return null;
         } else {
            UUID uuid = capability.getEffectSource(resourceLocation);
            return uuid == null ? null : pLivingEntity.m_9236_().m_46003_(uuid);
         }
      }
   }

   public static void setEffectSource(LivingEntity entity, @Nullable LivingEntity source, MobEffect effect) {
      ResourceLocation resourceLocation = Registry.f_122823_.m_7981_(effect);
      if (resourceLocation != null) {
         getFrom(entity).ifPresent((cap) -> {
            if (source == null) {
               cap.removeEffectSource(resourceLocation);
            } else {
               cap.setEffectSource(source.m_20148_(), resourceLocation);
            }

            sync(entity);
         });
      }
   }

   public static boolean noDyingAnimation(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isNoDyingAnimation();
   }

   @Nullable
   public static BlockPos getWarpPos(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? null : capability.getWarpPos();
   }

   public static int getWarpPortalTime(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0 : capability.getWarpPortalTime();
   }

   public static int getPresenceConcealment(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0 : capability.getPresenceConceal();
   }

   public static int getPresenceSense(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      if (capability == null) {
         return 0;
      } else {
         int sense = capability.getPresenceSense();
         if (capability.hasSyncedEffect(TensuraMobEffects.ALL_SEEING.getId())) {
            ++sense;
         }

         return sense;
      }
   }

   public static float getHeight(LivingEntity entity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(entity, CAPABILITY);
      return capability == null ? 0.0F : capability.getHeight();
   }

   public static void setHeight(Player player, float value) {
      getFrom(player).ifPresent((cap) -> {
         if (value != cap.getHeight()) {
            cap.setHeight(value);
            sync(player);
         }

      });
   }

   public static double getSeverance(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0.0D : capability.getSeveranceAmount();
   }

   public static boolean isColossusPassed(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isColossusPassed();
   }

   public static boolean isColossusStarted(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isColossusStarted();
   }

   public static boolean isColossusWon(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability capability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isColossusWon();
   }

   public static void resetEverything(LivingEntity entity, boolean presenceSense, boolean colossus) {
      getFrom(entity).ifPresent((cap) -> {
         if (presenceSense) {
            cap.setPresenceConceal(0);
            cap.setPresenceSense(0);
         }

         if (colossus) {
            cap.setColossusPassed(false);
            cap.setColossusStarted(false);
            cap.setColossusWon(false);
         }

         cap.setSeveranceRemoveSec(0);
         cap.setSeveranceAmount(0.0D);
         cap.setWarpPortalTime(0);
         cap.setNoKnockBack(false);
         cap.setHeight(1.0F);
         cap.setInfectionAge(0.0D);
         cap.setInsanityNightmare(0);
         cap.clearSyncedEffects(entity);
      });
      sync(entity);
   }

   public double getInfectionAge() {
      return this.infectionAge;
   }

   public double getSeveranceAmount() {
      return this.severanceAmount;
   }

   public void setInfectionAge(double infectionAge) {
      this.infectionAge = infectionAge;
   }

   public void setSeveranceAmount(double severanceAmount) {
      this.severanceAmount = severanceAmount;
   }

   public float getLockedXRot() {
      return this.lockedXRot;
   }

   public float getLockedYRot() {
      return this.lockedYRot;
   }

   public float getHeight() {
      return this.height;
   }

   public float getHeightUpdate() {
      return this.heightUpdate;
   }

   public void setLockedXRot(float lockedXRot) {
      this.lockedXRot = lockedXRot;
   }

   public void setLockedYRot(float lockedYRot) {
      this.lockedYRot = lockedYRot;
   }

   public void setHeight(float height) {
      this.height = height;
   }

   public void setHeightUpdate(float heightUpdate) {
      this.heightUpdate = heightUpdate;
   }

   public boolean isNoKnockBack() {
      return this.noKnockBack;
   }

   public boolean isNoDyingAnimation() {
      return this.noDyingAnimation;
   }

   public void setNoKnockBack(boolean noKnockBack) {
      this.noKnockBack = noKnockBack;
   }

   public void setNoDyingAnimation(boolean noDyingAnimation) {
      this.noDyingAnimation = noDyingAnimation;
   }

   public boolean isColossusStarted() {
      return this.colossusStarted;
   }

   public boolean isColossusPassed() {
      return this.colossusPassed;
   }

   public boolean isColossusWon() {
      return this.colossusWon;
   }

   public void setColossusStarted(boolean colossusStarted) {
      this.colossusStarted = colossusStarted;
   }

   public void setColossusPassed(boolean colossusPassed) {
      this.colossusPassed = colossusPassed;
   }

   public void setColossusWon(boolean colossusWon) {
      this.colossusWon = colossusWon;
   }

   public int getInsanityFOV() {
      return this.insanityFOV;
   }

   public int getInsanityNightmare() {
      return this.insanityNightmare;
   }

   public int getSeveranceRemoveSec() {
      return this.severanceRemoveSec;
   }

   public void setInsanityFOV(int insanityFOV) {
      this.insanityFOV = insanityFOV;
   }

   public void setInsanityNightmare(int insanityNightmare) {
      this.insanityNightmare = insanityNightmare;
   }

   public void setSeveranceRemoveSec(int severanceRemoveSec) {
      this.severanceRemoveSec = severanceRemoveSec;
   }

   public int getWarpPortalTime() {
      return this.warpPortalTime;
   }

   public int getPresenceConceal() {
      return this.presenceConceal;
   }

   public int getPresenceSense() {
      return this.presenceSense;
   }

   public void setWarpPortalTime(int warpPortalTime) {
      this.warpPortalTime = warpPortalTime;
   }

   public void setPresenceConceal(int presenceConceal) {
      this.presenceConceal = presenceConceal;
   }

   public void setPresenceSense(int presenceSense) {
      this.presenceSense = presenceSense;
   }

   public BlockPos getWarpPos() {
      return this.warpPos;
   }
}
