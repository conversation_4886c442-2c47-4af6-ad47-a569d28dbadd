package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.IronGolem;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.Tags.Items;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class ArmoursaurusEntity extends TensuraTamableEntity implements IAnimatable, IGiantMob, ITensuraMount, HasCustomInventoryScreen, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private static final EntityDataAccessor<Boolean> DIGGING;
   public int miscAnimationTicks = 0;
   public int diggingTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public ArmoursaurusEntity(EntityType<? extends ArmoursaurusEntity> type, Level level) {
      super(type, level);
      this.f_19793_ = 1.0F;
      this.f_21364_ = 40;
      this.initInventory();
      this.f_21365_ = new ArmoursaurusEntity.ArmoursaurusLookControl();
      this.f_21342_ = new ArmoursaurusEntity.ArmoursaurusMoveControl();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 20.0D).m_22268_(Attributes.f_22276_, 80.0D).m_22268_(Attributes.f_22277_, 24.0D).m_22268_(Attributes.f_22281_, 16.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22278_, 0.699999988079071D).m_22268_(Attributes.f_22288_, 0.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 4.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 1.2D, 30.0F, 5.0F, false) {
         public boolean m_8036_() {
            boolean canUse = super.m_8036_();
            return !canUse || ArmoursaurusEntity.this.getMiscAnimation() != 5 && ArmoursaurusEntity.this.getMiscAnimation() != 6 && ArmoursaurusEntity.this.getMiscAnimation() != 7 ? canUse : false;
         }

         public void m_8056_() {
            super.m_8056_();
            if (ArmoursaurusEntity.this.isDigging()) {
               ArmoursaurusEntity.this.setMiscAnimation(6);
               ArmoursaurusEntity.this.diggingTicks = 0;
            }
         }
      });
      this.f_21345_.m_25352_(4, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(6, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(2, new ArmoursaurusEntity.ArmoursaurusAttackGoal(this, 1.2D, true));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, Player.class, false, 16.0F, (Predicate)null));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, Villager.class, false, 16.0F, (Predicate)null));
      this.f_21346_.m_25352_(5, new UndergroundTargetingEntitiesGoal(this, Animal.class, false, 16.0F, (entity) -> {
         return !(entity instanceof ArmoursaurusEntity);
      }));
      this.f_21346_.m_25352_(5, new UndergroundTargetingEntitiesGoal(this, IronGolem.class, false, 16.0F, (Predicate)null));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
      this.f_19804_.m_135372_(DIGGING, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("MiscAnimation", this.getMiscAnimation());
      pCompound.m_128379_("Saddled", this.isSaddled());
      pCompound.m_128379_("Chested", this.isChested());
      pCompound.m_128379_("Digging", this.isDigging());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         pCompound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.f_19804_.m_135381_(MISC_ANIMATION, pCompound.m_128451_("MiscAnimation"));
      this.setSaddled(pCompound.m_128471_("Saddled"));
      this.setChested(pCompound.m_128471_("Chested"));
      this.setDigging(pCompound.m_128471_("Digging"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = pCompound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = pCompound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      this.f_19804_.m_135381_(MISC_ANIMATION, animation);
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean isDigging() {
      return (Boolean)this.f_19804_.m_135370_(DIGGING);
   }

   public void setDigging(boolean saddled) {
      this.f_19804_.m_135381_(DIGGING, saddled);
   }

   public ArmoursaurusEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      ArmoursaurusEntity entity = (ArmoursaurusEntity)((EntityType)TensuraEntityTypes.ARMOURSAURUS.get()).m_20615_(pLevel);
      if (entity == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            entity.m_21816_(uuid);
            entity.m_7105_(true);
         }

         return entity;
      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   public int m_5792_() {
      return 1;
   }

   public double m_20204_() {
      float threshold = this.m_6162_() ? 0.25F : 0.5F;
      return super.m_20204_() + (double)threshold;
   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return !state.m_204336_(TensuraTags.Blocks.DIGGABLE_BY_MONSTER) && !state.m_204336_(TensuraTags.Blocks.BREAKABLE_BY_MONSTER) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public void m_8107_() {
      super.m_8107_();
      if (this.getMiscAnimation() == 5 || this.getMiscAnimation() == 6 || this.getMiscAnimation() == 7) {
         this.f_20899_ = false;
         this.f_20900_ = 0.0F;
         this.f_20902_ = 0.0F;
      }

   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 7) {
            if (this.miscAnimationTicks == 10) {
               this.areaAttack(1.25D, 0.0F, 2.0F);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }

            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 1.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 2.0F);
         } else if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 20) {
            this.areaAttack(1.5D, 1.0F, 2.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 4.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 3.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 2.0F);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         } else if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 15) {
            this.areaAttack(2.0D, 2.5F, 4.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 3.0D);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.5F, 1.0F);
         } else if (this.getMiscAnimation() == 5 || this.getMiscAnimation() == 6) {
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 1.0F);
            TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 2.0F);
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            if (this.getMiscAnimation() == 5) {
               this.setDigging(true);
               this.m_20219_(this.m_20182_().m_82520_(0.0D, -1.1D * (double)this.m_20206_(), 0.0D));
               double dist = 10.0D;
               if (this.m_5448_() != null) {
                  dist = (double)this.m_20270_(this.m_5448_());
               }

               this.diggingTicks = (int)(5.0D * dist);
               this.m_19877_();
            }

            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      LivingEntity controller;
      if (this.isDigging()) {
         this.m_19877_();
         if (!this.isInsideDiggable(this.m_20183_())) {
            this.setDigging(false);
            this.setMiscAnimation(6);
            this.m_20219_(this.m_20182_().m_82520_(0.0D, 1.1D * (double)this.m_20206_() + 1.0D, 0.0D));
            this.m_21573_().m_26573_();
            this.diggingTicks = 0;
         } else if (--this.diggingTicks <= 0) {
            controller = this.m_5448_();
            if (controller != null) {
               this.setDigging(false);
               Vec3 targetPos;
               if (this.isBelowDiggable(controller)) {
                  targetPos = controller.m_20182_();
                  this.setMiscAnimation(7);
               } else if (!controller.m_20096_() && this.isBelowDiggable(controller, -1.0D)) {
                  targetPos = controller.m_20182_().m_82520_(0.0D, -1.0D, 0.0D);
                  this.setMiscAnimation(7);
               } else {
                  targetPos = this.m_20182_().m_82520_(0.0D, 1.1D * (double)this.m_20206_() + 1.0D, 0.0D);
                  this.setMiscAnimation(8);
               }

               this.m_20219_(targetPos);
               this.m_21573_().m_26573_();
               this.diggingTicks = 0;
            }
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

      if (!this.f_19853_.m_5776_()) {
         controller = this.getControllingPassenger();
         SimpleContainer container = this.isChested() ? this.inventory : null;
         if (!this.m_21824_() || controller != null && this.m_21830_(controller)) {
            this.breakBlocks(this, 1.0F, false, 1, container);
         }

         if (controller != null) {
            if (controller.m_146909_() <= 20.0F) {
               this.digBlocks(this, 1.0F, 1, 2.0F, false, container);
            } else {
               this.digBlocks(this, 1.0F, 0, 2.0F, controller.m_146909_() >= 40.0F, container);
            }

         }
      }
   }

   protected void targetingMovementHelper() {
      LivingEntity livingentity = this.m_5448_();
      LivingEntity owner = this.m_21826_();
      if (livingentity != null && livingentity.m_6084_() && this.m_20280_(livingentity) > 9.0D && !this.isDigging()) {
         this.f_21342_.m_6849_(livingentity.m_20185_(), livingentity.m_20186_(), livingentity.m_20189_(), this.f_21342_.m_24999_());
      } else if (owner != null && owner.m_6084_() && (double)this.m_20270_(owner) > 10.0D && !this.m_21827_() && !this.isWandering()) {
         this.f_21342_.m_6849_(owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), this.f_21342_.m_24999_());
      }

   }

   protected void sleepHandler() {
      if (this.m_21826_() == null) {
         if (this.m_5448_() == null) {
            if (!this.isDigging()) {
               if (!this.isInFluidType()) {
                  if (!this.m_20160_() && !this.m_20159_()) {
                     if (this.isBelowDiggable(this)) {
                        if (this.f_19796_.m_188503_(100) == 0) {
                           if (this.m_217043_().m_188499_()) {
                              return;
                           }

                           this.setMiscAnimation(5);
                           this.m_21573_().m_26573_();
                        }

                     }
                  }
               }
            }
         }
      }
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 3) {
         if (this.getMiscAnimation() != 5) {
            if (this.getMiscAnimation() != 6) {
               if (this.getMiscAnimation() != 7) {
                  this.setMiscAnimation(3);
                  this.m_21573_().m_26573_();
               }
            }
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
      case 6:
         var10000 = 30;
         break;
      case 3:
         var10000 = 22;
         break;
      case 4:
      default:
         var10000 = 20;
         break;
      case 5:
         var10000 = 52;
      }

      return var10000;
   }

   public void areaAttack(double multiplier, float strength, float range) {
      AABB aabb = this.m_20191_().m_82400_((double)range);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this) && (!(entity instanceof ArmoursaurusEntity) || entity == this.m_5448_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var7 = livingEntityList.iterator();

         while(var7.hasNext()) {
            LivingEntity target = (LivingEntity)var7.next();
            target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * multiplier));
            SkillHelper.knockBack(this, target, strength);
         }

      }
   }

   private boolean isBelowDiggable(LivingEntity entity, double yOffset) {
      BlockPos pos = new BlockPos(entity.m_20182_().m_82520_(0.0D, -1.1D * (double)this.m_20206_() + yOffset, 0.0D));
      return this.isInsideDiggable(pos);
   }

   private boolean isBelowDiggable(LivingEntity entity) {
      BlockPos pos = new BlockPos(entity.m_20182_().m_82520_(0.0D, -1.1D * (double)this.m_20206_(), 0.0D));
      return this.isInsideDiggable(pos);
   }

   private boolean isInsideDiggable(BlockPos pos) {
      AABB aabb = this.m_20191_();
      BlockPos min = new BlockPos((double)pos.m_123341_() - aabb.m_82362_() / 2.0D, (double)pos.m_123342_() - aabb.m_82376_() / 2.0D, (double)pos.m_123343_() - aabb.m_82385_() / 2.0D);
      BlockPos max = new BlockPos((double)pos.m_123341_() + aabb.m_82362_() / 2.0D, (double)pos.m_123342_() + aabb.m_82376_() / 2.0D, (double)pos.m_123343_() + aabb.m_82385_() / 2.0D);
      if (!this.f_19853_.m_46832_(min, max)) {
         return false;
      } else {
         boolean cantDig = false;
         MutableBlockPos mutableBlockPos = new MutableBlockPos();

         for(int i = min.m_123341_(); i <= max.m_123341_(); ++i) {
            for(int j = min.m_123342_(); j <= max.m_123342_(); ++j) {
               for(int k = min.m_123343_(); k <= max.m_123343_(); ++k) {
                  mutableBlockPos.m_122178_(i, j, k);
                  BlockState state = this.f_19853_.m_8055_(mutableBlockPos);
                  if (!state.m_204336_(TensuraTags.Blocks.DIGGABLE_BY_MONSTER)) {
                     cantDig = true;
                  }
               }
            }
         }

         return !cantDig;
      }
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(45) {
         public boolean m_6542_(Player player) {
            return ArmoursaurusEntity.this.m_6084_() && !ArmoursaurusEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39961_, menu, inventory, ArmoursaurusEntity.this.inventory, 5);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }

               if (this.isSaddled() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            }

            if (player.m_36341_() || !this.isSaddled() && !this.isChested()) {
               this.commanding(player);
            } else if (player.m_146895_() == null && this.isSaddled()) {
               this.m_21839_(false);
               this.setWandering(false);
               player.m_7998_(this, false);
            } else if (this.isChested()) {
               this.m_213583_(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player pPlayer, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!pPlayer.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_27595_(pPlayer);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_8035_();
            this.m_146740_(m_216967_(-i), true);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(4);
      this.m_5634_(5.0F);
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HealingPotionItem ? false : pStack.m_41614_();
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_(), yOffset, this.m_20189_());
      }
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
      }

   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.m_20096_()) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (!this.m_6109_()) {
               if (controller instanceof Player) {
                  this.m_20256_(Vec3.f_82478_);
               }
            } else {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               if (this.getMiscAnimation() == 3) {
                  speed = 0.0F;
               }

               this.m_7910_(speed);
               if (this.isInFluidType()) {
                  if (this.isInFluidType((fluidType, height) -> {
                     return height > this.m_20204_();
                  }) && controller.f_20899_) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.05D, 0.0D));
                  } else if (this.isInFluidType() && TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                     this.descending(this, controller);
                  }
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 5.0F) {
         return false;
      } else {
         if (pFallDistance > 5.0F) {
            this.m_5496_(SoundEvents.f_12319_, 0.4F, 1.0F);
         }

         int i = this.m_5639_(pFallDistance - 5.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.armoursaurusSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected SoundEvent m_7515_() {
      return this.isDigging() ? null : SoundEvents.f_12356_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_12360_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12359_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.stay", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         if (!this.isInFluidType() && !this.isDigging()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.swim", EDefaultLoopTypes.LOOP));
         }
      } else {
         if (this.isDigging()) {
            if (this.getMiscAnimation() != 5 && this.getMiscAnimation() != 6 && this.getMiscAnimation() != 7) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.swim", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.idle", EDefaultLoopTypes.LOOP));
            }

            return PlayState.CONTINUE;
         }

         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.dig_down", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.dig_up", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 7) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.dig_attack", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.tail_slam", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.tail_swing", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.armorsaurus.eat", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(ArmoursaurusEntity.class, EntityDataSerializers.f_135028_);
      SADDLED = SynchedEntityData.m_135353_(ArmoursaurusEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(ArmoursaurusEntity.class, EntityDataSerializers.f_135035_);
      DIGGING = SynchedEntityData.m_135353_(ArmoursaurusEntity.class, EntityDataSerializers.f_135035_);
   }

   public class ArmoursaurusLookControl extends TensuraTamableEntity.SleepLookControl {
      public ArmoursaurusLookControl() {
         super();
      }

      public void m_8128_() {
         if (ArmoursaurusEntity.this.getMiscAnimation() != 3) {
            if (ArmoursaurusEntity.this.getMiscAnimation() != 5) {
               if (ArmoursaurusEntity.this.getMiscAnimation() != 6) {
                  if (ArmoursaurusEntity.this.getMiscAnimation() != 7) {
                     super.m_8128_();
                  }
               }
            }
         }
      }
   }

   public class ArmoursaurusMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public ArmoursaurusMoveControl() {
         super();
      }

      public void m_8126_() {
         if (ArmoursaurusEntity.this.getMiscAnimation() != 3) {
            if (ArmoursaurusEntity.this.getMiscAnimation() != 5) {
               if (ArmoursaurusEntity.this.getMiscAnimation() != 6) {
                  if (ArmoursaurusEntity.this.getMiscAnimation() != 7) {
                     if (!ArmoursaurusEntity.this.isDigging()) {
                        super.m_8126_();
                     }
                  }
               }
            }
         }
      }
   }

   static class ArmoursaurusAttackGoal extends MeleeAttackGoal {
      private final ArmoursaurusEntity entity;

      public ArmoursaurusAttackGoal(ArmoursaurusEntity spider, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(spider, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.entity = spider;
      }

      public boolean m_8036_() {
         return this.entity.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.entity.m_21827_() ? false : super.m_8045_();
      }

      public void m_8037_() {
         if (this.entity.getMiscAnimation() == 0) {
            super.m_8037_();
         }

      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.entity.getMiscAnimation() == 0) {
            if (this.entity.isDigging()) {
               this.m_25563_();
               this.entity.m_21573_().m_26573_();
               return;
            }

            int randomAttack = this.randomAttack(pDistToEnemySqr);
            if (randomAttack == 5) {
               if (this.entity.m_21827_()) {
                  return;
               }

               if (!this.entity.isBelowDiggable(this.entity)) {
                  return;
               }

               if (!this.entity.isBelowDiggable(pEnemy)) {
                  return;
               }

               this.m_25563_();
               this.entity.setMiscAnimation(5);
               this.entity.m_21573_().m_26573_();
               return;
            }

            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = this.entity.m_6162_() ? 8.0D : 16.0D;
               break;
            case 3:
               this.entity.m_21573_().m_26573_();
               var10000 = this.entity.m_6162_() ? 24.0D : 49.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.entity.setMiscAnimation(randomAttack);
               if (randomAttack == 1) {
                  this.entity.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(double distance) {
         if ((this.entity.f_19796_.m_188503_(40) == 2 || distance > 400.0D) && distance >= 100.0D && this.entity.getControllingPassenger() == null && !this.entity.isInFluidType()) {
            return 5;
         } else if (this.entity.f_19796_.m_188503_(5) == 2) {
            return 3;
         } else {
            return (double)this.entity.f_19796_.m_188501_() <= 0.4D && !this.entity.isInFluidType() ? 2 : 1;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_());
      }
   }
}
