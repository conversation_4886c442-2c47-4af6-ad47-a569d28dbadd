package com.github.manasmods.tensura.ability.magic.spiritual.darkness;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.magic.barrier.DarkCubeEntity;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class DarkCubeMagic extends SpiritualMagic {
   public DarkCubeMagic() {
      super(MagicElemental.DARKNESS, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 800.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      return !entity.m_6144_() ? super.onHeld(instance, entity, heldTicks) : true;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_6144_()) {
         BarrierPart part = (BarrierPart)SkillHelper.getTargetingEntity(BarrierPart.class, entity, 20.0D, 0.1D, false, false);
         if (part != null) {
            BarrierEntity var5 = part.barrier;
            if (var5 instanceof DarkCubeEntity) {
               DarkCubeEntity barrier = (DarkCubeEntity)var5;
               if (barrier.m_37282_() == entity) {
                  barrier.m_146870_();
                  entity.f_19853_.m_6263_((Player)null, part.m_20185_(), part.m_20186_(), part.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }

         }
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (!entity.m_6144_()) {
         if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               this.addMasteryPoint(instance, entity);
               int distance = instance.isMastered(entity) ? 20 : 15;
               Entity target = SkillHelper.getTargetingEntity(entity, (double)distance, false, true);
               Vec3 pos;
               if (target != null) {
                  if (target.m_20096_()) {
                     pos = target.m_20182_().m_82520_(0.0D, 4.5D, 0.0D);
                  } else {
                     pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
                  }
               } else {
                  BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, (double)distance);
                  pos = result.m_82450_().m_82520_(0.0D, 4.5D, 0.0D);
               }

               DarkCubeEntity cube = new DarkCubeEntity(entity.m_9236_(), entity);
               cube.m_146884_(pos);
               cube.setDamage(instance.isMastered(entity) ? 20.0F : 10.0F);
               cube.setMpCost(this.magiculeCost(entity, instance));
               cube.setSkill(instance);
               cube.setLife(600);
               cube.setRadius(5.0F);
               entity.m_9236_().m_7967_(cube);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
               instance.setCoolDown(instance.isMastered(entity) ? 2 : 4);
               instance.getOrCreateTag().m_128405_("HeldTicks", 0);
               instance.markDirty();
            }
         }
      }
   }
}
