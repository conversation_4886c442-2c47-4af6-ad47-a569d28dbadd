package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.client.DisplayConfig;
import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.enchantment.SlottingEnchantment;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.InsanityActionPacket;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.mojang.datafixers.util.Either;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Nullable;
import net.minecraft.client.Camera;
import net.minecraft.client.CameraType;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.InBedChatScreen;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.debug.GameModeSwitcherScreen;
import net.minecraft.client.model.EntityModel;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.tooltip.TooltipComponent;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.ComputeFovModifierEvent;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.client.event.RenderHighlightEvent;
import net.minecraftforge.client.event.RenderNameTagEvent;
import net.minecraftforge.client.event.RenderLivingEvent.Pre;
import net.minecraftforge.client.event.RenderTooltipEvent.GatherComponents;
import net.minecraftforge.client.event.ScreenEvent.Opening;
import net.minecraftforge.client.event.ViewportEvent.ComputeCameraAngles;
import net.minecraftforge.client.event.ViewportEvent.ComputeFov;
import net.minecraftforge.client.gui.overlay.VanillaGuiOverlay;
import net.minecraftforge.event.TickEvent.ClientTickEvent;
import net.minecraftforge.event.TickEvent.Phase;
import net.minecraftforge.event.entity.player.ItemTooltipEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
public class ClientEventsHandler {
   @Nullable
   private static CameraType type = null;

   @SubscribeEvent
   public static void getNameTag(RenderNameTagEvent e) {
      Entity var2 = e.getEntity();
      if (var2 instanceof LivingEntity) {
         LivingEntity entity = (LivingEntity)var2;
         e.setContent(TensuraEPCapability.getDisplayName(entity, e.getContent()));
      }
   }

   @SubscribeEvent
   public static void tooltipEP(ItemTooltipEvent e) {
      ItemStack pStack = e.getItemStack();
      if (pStack.m_41741_() <= 1) {
         CompoundTag tag = pStack.m_41783_();
         if (tag != null) {
            if (tag.m_128459_("EP") > 0.0D) {
               double EP = tag.m_128459_("EP");
               double durabilityEP = tag.m_128459_("DurabilityEP");
               e.getToolTip().add(1, Component.m_237110_("tooltip.tensura.gear_durability_EP", new Object[]{durabilityEP, EP}));
            }

         }
      }
   }

   @SubscribeEvent
   public static void onGatherComponents(GatherComponents e) {
      Optional<TooltipComponent> optional = SlottingEnchantment.tooltipCore(e.getItemStack());
      optional.ifPresent((component) -> {
         e.getTooltipElements().add(Either.right(component));
      });
   }

   @SubscribeEvent
   public static void cancelFovModification(ComputeFovModifierEvent event) {
      if (InsanityEffect.havingNightmare(event.getPlayer())) {
         event.setNewFovModifier(1.0F);
      }

   }

   @SubscribeEvent
   public static void increaseFov(ComputeFov event) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         if (InsanityEffect.havingNightmare(player)) {
            TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.getInsanityFOV() > 0) {
                  if (cap.getInsanityNightmare() == -1) {
                     event.setFOV(event.getFOV() - (double)cap.getInsanityFOV());
                     TensuraNetwork.INSTANCE.sendToServer(new InsanityActionPacket(InsanityActionPacket.Action.RESET_VALUE));
                  } else {
                     event.setFOV(event.getFOV() + (double)cap.getInsanityFOV());
                  }

               }
            });
         }
      }
   }

   @SubscribeEvent
   public static void openScreen(Opening event) {
      Screen screen = event.getScreen();
      if (screen instanceof InBedChatScreen || screen instanceof GameModeSwitcherScreen) {
         Player player = Minecraft.m_91087_().f_91074_;
         if (player != null) {
            if (InsanityEffect.havingNightmare(player)) {
               event.setCanceled(true);
            }
         }
      }
   }

   @SubscribeEvent
   public static void changePerspective(ClientTickEvent event) {
      if (event.phase.equals(Phase.END)) {
         Minecraft minecraft = Minecraft.m_91087_();
         Player player = minecraft.f_91074_;
         if (player != null) {
            if (TensuraEffectsCapability.hasSyncedEffect(player, (MobEffect)TensuraMobEffects.ALL_SEEING.get())) {
               if (type == null) {
                  type = minecraft.f_91066_.m_92176_();
               }

               minecraft.f_91066_.m_92157_(CameraType.THIRD_PERSON_BACK);
            } else if (type != null) {
               minecraft.f_91066_.m_92157_(type);
               type = null;
            }

         }
      }
   }

   @SubscribeEvent
   public static void changeCameraAngles(ComputeCameraAngles event) {
      Camera camera = event.getCamera();
      Entity var3 = camera.m_90592_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         MobEffectInstance instance = living.m_21124_((MobEffect)TensuraMobEffects.ALL_SEEING.get());
         if (instance != null) {
            double zoomLevel = (double)((float)instance.m_19564_() + 2.0F);
            camera.m_90568_(-camera.m_90566_(zoomLevel * 0.5D), 0.0D, 0.0D);
         }
      }
   }

   @SubscribeEvent
   public static void trueInvisible(Pre<? extends LivingEntity, ? extends EntityModel<? extends LivingEntity>> e) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         LivingEntity living = e.getEntity();
         if (MobEffectHelper.hasTrueInvisibility(living)) {
            if (!MobEffectHelper.canSeeInvisibleTarget(player, living)) {
               e.setCanceled(true);
            }
         }
      }
   }

   @SubscribeEvent
   public static void renderOverlayEvent(RenderGuiOverlayEvent event) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null && TensuraPlayerCapability.getRace(player) != null) {
         DisplayConfig cfg = TensuraClientConfig.INSTANCE.displayConfig;
         int hudVariant = 1;
         if (TensuraClientConfig.SPEC.isLoaded() && hudVariant != (Integer)cfg.hudVariant.get()) {
            hudVariant = (Integer)cfg.hudVariant.get();
         }

         boolean shouldCancelVanillaHUD = hudVariant == 1;
         ResourceLocation eventID = event.getOverlay().id();
         Map<ResourceLocation, Boolean> eventsToCancel = Map.of(VanillaGuiOverlay.PLAYER_HEALTH.id(), shouldCancelVanillaHUD, VanillaGuiOverlay.FOOD_LEVEL.id(), shouldCancelVanillaHUD, VanillaGuiOverlay.ARMOR_LEVEL.id(), shouldCancelVanillaHUD, VanillaGuiOverlay.AIR_LEVEL.id(), shouldCancelVanillaHUD, VanillaGuiOverlay.EXPERIENCE_BAR.id(), ClientRaceHandler.jumpChargingTicks != 0L);
         if (eventID != VanillaGuiOverlay.SLEEP_FADE.id() && InsanityEffect.havingNightmare(player)) {
            event.setCanceled(true);
         } else if (eventsToCancel.containsKey(eventID) && (Boolean)eventsToCancel.get(eventID)) {
            event.setCanceled(true);
         }

      }
   }

   @SubscribeEvent
   public static void renderShape(RenderHighlightEvent event) {
      Entity var2 = event.getCamera().m_90592_();
      if (var2 instanceof Player) {
         Player player = (Player)var2;
         if (event.getTarget().m_6662_() == Type.BLOCK) {
            BlockHitResult hitResult = (BlockHitResult)event.getTarget();
            Level level = player.m_9236_();
            BlockState blockState = level.m_8055_(hitResult.m_82425_());
            Set<Block> blockSet = Set.of((Block)TensuraBlocks.HELL_PORTAL.get(), (Block)TensuraBlocks.LABYRINTH_PORTAL.get(), (Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get());
            if (blockSet.contains(blockState.m_60734_())) {
               if (!player.m_7500_()) {
                  event.setCanceled(true);
               }

            }
         }
      }
   }
}
