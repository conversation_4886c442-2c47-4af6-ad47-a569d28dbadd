package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HoverLizardEntity;
import com.github.manasmods.tensura.entity.client.layer.HoverLizardArmorLayer;
import com.github.manasmods.tensura.entity.variant.HoverLizardVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class HoverLizardRenderer extends GeoEntityRenderer<HoverLizardEntity> {
   public HoverLizardRenderer(Context renderManager) {
      super(renderManager, new HoverLizardModel());
      this.f_114477_ = 0.5F;
      this.addLayer(new HoverLizardArmorLayer(this));
   }

   public ResourceLocation getTextureLocation(HoverLizardEntity instance) {
      return (ResourceLocation)HoverLizardVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public RenderType getRenderType(HoverLizardEntity lizard, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (lizard.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(lizard));
   }
}
