package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LizardmanEntity;
import com.github.manasmods.tensura.entity.variant.LizardmanVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class LizardmanModel extends AnimatedGeoModel<LizardmanEntity> {
   public ResourceLocation getModelResource(LizardmanEntity object) {
      return new ResourceLocation("tensura", "geo/lizardman.geo.json");
   }

   public ResourceLocation getTextureResource(LizardmanEntity instance) {
      return (ResourceLocation)LizardmanVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public ResourceLocation getAnimationResource(LizardmanEntity bear) {
      return new ResourceLocation("tensura", "animations/lizardman.animation.json");
   }

   public void setCustomAnimations(LizardmanEntity lizardman, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(lizardman, instanceId, customPredicate);
      IBone wings = this.getAnimationProcessor().getBone("wings");
      if (lizardman.isDragonewt() == wings.isHidden()) {
         wings.setHidden(!lizardman.isDragonewt());
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
         head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
      }

   }
}
