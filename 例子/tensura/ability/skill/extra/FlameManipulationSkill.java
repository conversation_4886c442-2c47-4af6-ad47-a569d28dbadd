package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class FlameManipulationSkill extends Skill {
   public FlameManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return TensuraSkillCapability.getSpiritLevel(entity, MagicElemental.FLAME.getId()) >= 1;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      MolecularManipulationSkill.learnMolecular(entity);
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)ExtraSkills.HEAT_WAVE.get();
      ManasSkillInstance heatWave = new TensuraSkillInstance(skill);
      heatWave.setMastery(-100);
      if (storage.learnSkill(heatWave) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         ManasSkill flame = (ManasSkill)ExtraSkills.FLAME_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(flame);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{flame.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.FLAME_DOMINATION.get())) {
            if (DamageSourceHelper.isFireDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

         }
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(attacker)) {
         if (canUseFire(attacker)) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource()) || e.getSource().m_7639_() instanceof AbstractArrow) {
               LivingEntity target = e.getEntity();
               target.m_20254_(this.isMastered(instance, attacker) ? 10 : 5);
               attacker.m_9236_().m_6263_((Player)null, attacker.m_20185_(), attacker.m_20186_(), attacker.m_20189_(), SoundEvents.f_11909_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123744_, 1.0D);
            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("BreathEntity", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (!canUseFire(entity)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         spawnFlameBreath(entity, instance, this.magiculeCost(entity, instance));
         return true;
      }
   }

   public static void spawnFlameBreath(LivingEntity entity, ManasSkillInstance instance, double cost) {
      EntityType<? extends BreathEntity> entityType = (EntityType)TensuraEntityTypes.FLAME_BREATH.get();
      if (entity.m_21023_((MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
         entityType = (EntityType)TensuraEntityTypes.BLACK_FLAME_BREATH.get();
      }

      float damage = instance.isMastered(entity) ? 20.0F : 8.0F;
      BreathEntity.spawnBreathEntity(entityType, entity, instance, damage, cost);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
      if (entity.m_21023_((MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.BLACK_BURN.get());
      }

      entity.m_20095_();
   }

   public static boolean canUseFire(LivingEntity entity) {
      if (entity.m_6060_()) {
         return true;
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         return TensuraSkillCapability.getSpiritLevel(player, MagicElemental.FLAME.getId()) >= 1;
      } else {
         return false;
      }
   }

   public static void learnFlameManipulation(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      int skills = isFlameSKill(instance) ? 1 : 0;
      Iterator var4 = storage.getLearnedSkills().iterator();

      while(var4.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var4.next();
         if (isFlameSKill(skill)) {
            ++skills;
         }
      }

      if (skills >= 2) {
         ManasSkill skill = (ManasSkill)ExtraSkills.FLAME_MANIPULATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   private static boolean isFlameSKill(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         if (instance.getSkill().equals(ExtraSkills.BLACK_FLAME.get())) {
            return true;
         } else if (instance.getSkill().equals(ResistanceSkills.FLAME_ATTACK_RESISTANCE.get())) {
            return true;
         } else {
            return instance.getSkill().equals(IntrinsicSkills.FLAME_BREATH.get()) ? true : instance.getSkill().equals(IntrinsicSkills.FLAME_TRANSFORM.get());
         }
      } else {
         return false;
      }
   }
}
