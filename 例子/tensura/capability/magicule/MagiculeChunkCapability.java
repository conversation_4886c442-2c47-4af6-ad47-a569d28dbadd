package com.github.manasmods.tensura.capability.magicule;

import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.common.util.INBTSerializable;

public interface MagiculeChunkCapability extends INBTSerializable<CompoundTag> {
   void sync(ServerPlayer var1);

   double getMaxMagicule();

   double getRegenerationRate();

   double getMagicule();

   double getMagicule(BlockPos var1);

   double getMaxMagicule(BlockPos var1);

   boolean consumeMagicule(BlockPos var1, double var2);
}
