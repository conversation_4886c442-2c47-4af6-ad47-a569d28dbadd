package com.github.b4ndithelps.tennogamenolife.ability.skill.unique;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

/**
 * 独特技能，可以将自身魔素提取压缩成一颗混沌魔力
 */
public class MagicBeadsSkill extends Skill {

    private final double skillCastCost = 20000.0;
    private final double learnCost = 3.0;
    private final double epUnlockCost = 400000.0;

    /**
     * @return
     */
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/unique/magic_beads.png");
    }

    public MagicBeadsSkill() {
        super(SkillType.UNIQUE);
    }

    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        return curEP >= epUnlockCost;
    }

    /**
     *
     * @param entity
     * @param curEP
     * @return
     */

    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) { return skillCastCost; }

    public double learningCost() { return learnCost; }

    /**
     * @param instance
     * @param entity
     */
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
        }

        if (entity instanceof Player player) {
            ItemStack itemStack = new ItemStack(Tenreincarnationitems.MAG.get());
            player.addItem(itemStack);
            
            entity.getLevel().playSound(
                null, entity.getX(), entity.getY(), entity.getZ(),
                SoundEvents.EXPERIENCE_ORB_PICKUP, SoundSource.PLAYERS,
                1.0F, 1.0F
            );

            instance.setCoolDown(600);
        }
    }
} 