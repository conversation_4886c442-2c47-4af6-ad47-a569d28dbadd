package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.world.tree.leaves.PalmFoliagePlacer;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacerType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraFoliagePlacers {
   private static final DeferredRegister<FoliagePlacerType<?>> registry;
   public static final RegistryObject<FoliagePlacerType<PalmFoliagePlacer>> PALM_FOLIAGE;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.FOLIAGE_PLACER_TYPES, "tensura");
      PALM_FOLIAGE = registry.register("palm_foliage_placer", () -> {
         return new FoliagePlacerType(PalmFoliagePlacer.CODEC);
      });
   }
}
