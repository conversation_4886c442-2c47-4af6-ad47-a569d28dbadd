package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class ParalysisSkill extends Skill {
   public ParalysisSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         return player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE.get())) >= 500;
      } else {
         return false;
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(attacker) || instance.isToggled()) {
         if (e.getSource().m_7640_() == attacker) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource())) {
               Level level = attacker.m_9236_();
               LivingEntity target = e.getEntity();
               int paralysis = instance.isMastered(attacker) ? 1 : 0;
               target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 200, paralysis), attacker);
               level.m_6263_((Player)null, attacker.m_20185_(), attacker.m_20186_(), attacker.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
               ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.PARALYSING_BUBBLE.get(), target.m_20182_().f_82479_, target.m_20182_().f_82480_ + (double)target.m_20206_() / 2.0D, target.m_20182_().f_82481_, 20, 0.08D, 0.08D, 0.08D, 0.15D);
               CompoundTag tag = instance.getOrCreateTag();
               int time = tag.m_128451_("activatedTimes");
               if (time % 10 == 0) {
                  this.addMasteryPoint(instance, attacker);
               }

               tag.m_128405_("activatedTimes", time + 1);
            }
         }
      }
   }
}
