package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.PacketDistributor;

public class BlizzardEntity extends BarrierEntity {
   public BlizzardEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.BLIZZARD.get(), level);
      this.m_5602_(entity);
      this.f_19811_ = false;
   }

   public BlizzardEntity(EntityType<? extends BlizzardEntity> entityType, Level level) {
      super(entityType, level);
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean isMultipartEntity() {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      Entity owner = this.m_37282_();
      if (owner != null) {
         this.m_6034_(owner.m_20185_(), owner.m_20186_() + (double)(owner.m_20206_() / 2.0F), owner.m_20189_());
         List<BlizzardEntity> blizzards = this.m_9236_().m_6443_(BlizzardEntity.class, owner.m_20191_(), (entityData) -> {
            return entityData.m_37282_() == owner && entityData != this;
         });
         if (!blizzards.isEmpty() || !owner.m_6084_()) {
            this.m_146870_();
         }
      }

      if (!this.f_19853_.m_5776_()) {
         if (this.f_19797_ % 5 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:blizzard"), this.m_19879_(), 0.0D, 0.0D, 0.0D, true));
         }

         if (this.f_19797_ % 10 == 0 && TensuraGameRules.canSkillGrief(this.f_19853_)) {
            if (!this.f_19853_.isAreaLoaded(this.m_20183_(), 5)) {
               return;
            }

            BlockPos.m_121921_((new AABB(this.m_20183_())).m_82400_((double)this.getRadius())).forEach((pos) -> {
               if (!(this.m_20275_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()) > (double)(this.getRadius() * this.getRadius()))) {
                  if (this.f_19853_.m_8055_(pos).m_60713_(Blocks.f_49990_)) {
                     if (this.f_19853_.m_6425_(pos).m_76170_()) {
                        if (!this.f_19853_.m_8055_(pos.m_7494_()).m_60713_(Blocks.f_49990_)) {
                           if (!(this.f_19796_.m_188501_() > 0.2F)) {
                              SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(owner, this.getSkill(), pos);
                              if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                                 this.f_19853_.m_46597_(pos, Blocks.f_50449_.m_49966_());
                                 this.f_19853_.m_186460_(pos, Blocks.f_50449_, Mth.m_216271_(this.f_19796_, 200, 400));
                                 MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(owner, this.getSkill(), pos));
                              }
                           }
                        }
                     }
                  }
               }
            });
         }

      }
   }

   protected void hitTarget() {
      AABB barrierBox = new AABB(this.m_20185_() - (double)this.getRadius(), this.m_20186_() - (double)this.getRadius(), this.m_20189_() - (double)this.getRadius(), this.m_20185_() + (double)this.getRadius(), this.m_20186_() + (double)this.getRadius() + (double)this.getHeight(), this.m_20189_() + (double)this.getRadius());
      List<LivingEntity> targets = this.m_9236_().m_45976_(LivingEntity.class, barrierBox);
      Iterator var3 = targets.iterator();

      while(var3.hasNext()) {
         LivingEntity target = (LivingEntity)var3.next();
         if (this.m_5603_(target)) {
            this.applyEffect(target);
         }
      }

   }

   public void applyEffect(LivingEntity entity) {
      Entity owner = this.m_37282_();
      if (owner == null || !entity.m_7307_(owner) && entity != owner) {
         DamageSource damageSource = TensuraDamageSources.indirectElementalAttack("tensura.water_attack", this, this.m_37282_(), this.getMpCost() / 10.0D, this.getSkill(), true);
         if (entity.m_6469_(damageSource, this.getDamage()) && this.f_19797_ % 400 == 0) {
            int chillLevel = 0;
            MobEffectInstance insanity = entity.m_21124_((MobEffect)TensuraMobEffects.CHILL.get());
            if (insanity != null) {
               chillLevel = insanity.m_19564_() + 1;
            }

            SkillHelper.checkThenAddEffectSource(entity, owner, new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 420, chillLevel, false, false, false));
         }

      }
   }
}
