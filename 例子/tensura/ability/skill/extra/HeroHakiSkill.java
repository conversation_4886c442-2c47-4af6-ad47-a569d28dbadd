package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.PacketDistributor;

public class HeroHakiSkill extends Skill {
   private static final String HAKI = "bc1c5f82-48cf-43f5-89f7-d4c898fcc171";

   public HeroHakiSkill() {
      super(Skill.SkillType.EXTRA);
      this.addHeldAttributeModifier(Attributes.f_22279_, "bc1c5f82-48cf-43f5-89f7-d4c898fcc171", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (TensuraEPCapability.isMajin(entity) && !TensuraEPCapability.isChaos(entity)) {
         return false;
      } else if (!SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.HAKI.get())) {
         return false;
      } else {
         return newEP > 200000.0D;
      }
   }

   public double learningCost() {
      return 500.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 25.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (TensuraPlayerCapability.isTrueHero(player)) {
            ManasSkill skill = (ManasSkill)ExtraSkills.SACRED_HAKI.get();
            ManasSkillInstance sacred = new TensuraSkillInstance(skill);
            sacred.setMastery(-100);
            if (storage.learnSkill(sacred)) {
               player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
            }
         }
      }

   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         activateHeroHaki(instance, entity, heldTicks);
         return true;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (this.hasAttributeApplied(entity, Attributes.f_22279_, "bc1c5f82-48cf-43f5-89f7-d4c898fcc171")) {
         instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
      }
   }

   public static void activateHeroHaki(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12362_, SoundSource.PLAYERS, 1.0F, 1.0F);
      if (heldTicks % 2 == 0) {
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return entity;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:hero_haki"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
      }

      List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (entityData) -> {
         return !entityData.m_7306_(entity) && entityData.m_6084_() && !entityData.m_7307_(entity);
      });
      if (!list.isEmpty()) {
         double scale = instance.getTag() == null ? 0.0D : instance.getTag().m_128459_("scale");
         double multiplier = scale == 0.0D ? 1.0D : Math.min(scale, 1.0D);
         double ownerEP = TensuraEPCapability.getEP(entity) * multiplier;
         Iterator var10 = list.iterator();

         while(true) {
            LivingEntity target;
            Player player;
            do {
               if (!var10.hasNext()) {
                  return;
               }

               target = (LivingEntity)var10.next();
               if (!(target instanceof Player)) {
                  break;
               }

               player = (Player)target;
            } while(player.m_150110_().f_35934_);

            double targetEP = TensuraEPCapability.getEP(target);
            double difference = ownerEP / targetEP;
            if (!(difference <= 2.0D)) {
               int fearLevel = (int)(difference * 0.375D - 0.75D);
               fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
               SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
               HakiSkill.hakiPush(target, entity, fearLevel);
            }
         }
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         HakiSkill.changeEPUsed(instance, entity, delta);
      }
   }
}
