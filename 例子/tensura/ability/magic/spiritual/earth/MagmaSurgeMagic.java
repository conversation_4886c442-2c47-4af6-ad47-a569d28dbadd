package com.github.manasmods.tensura.ability.magic.spiritual.earth;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.MagmaShotProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class MagmaSurgeMagic extends SpiritualMagic {
   public MagmaSurgeMagic() {
      super(MagicElemental.EARTH, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 80;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
            instance.getOrCreateTag().m_128405_("HeldTicks", 0);
            instance.markDirty();

            for(int i = 0; i < 4; ++i) {
               this.shootMagmaBall(instance, entity);
            }

         }
      }
   }

   private void shootMagmaBall(ManasSkillInstance instance, LivingEntity entity) {
      MagmaShotProjectile magmaBall = new MagmaShotProjectile(entity.m_9236_(), entity);
      magmaBall.setBurnTicks(10);
      magmaBall.setSpeed(1.0F);
      magmaBall.setDamage(200.0F);
      if (instance.isMastered(entity)) {
         MobEffectInstance burden = new MobEffectInstance((MobEffect)TensuraMobEffects.BURDEN.get(), 200, 1, false, false, false);
         magmaBall.setMobEffect(burden);
         magmaBall.setDamage(350.0F);
      }

      magmaBall.setMpCost(this.magiculeCost(entity, instance));
      magmaBall.setSkill(instance);
      magmaBall.setPosAndShoot(entity, 5.0F);
      entity.m_9236_().m_7967_(magmaBall);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
