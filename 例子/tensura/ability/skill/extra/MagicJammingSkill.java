package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.effect.skill.debuff.MagicInterferenceEffect;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class MagicJammingSkill extends Skill {
   public MagicJammingSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (this.isMastered(instance, attacker)) {
            if (e.getSource().m_7640_() == attacker) {
               if (DamageSourceHelper.isPhysicalAttack(e.getSource())) {
                  LivingEntity target = e.getEntity();
                  if (!(TensuraEPCapability.getEP(target) > TensuraEPCapability.getEP(attacker) * 3.5D)) {
                     SkillHelper.removePredicateEffect(target, (effect) -> {
                        return effect instanceof Transformation;
                     });
                     attacker.m_9236_().m_6263_((Player)null, attacker.m_20185_(), attacker.m_20186_(), attacker.m_20189_(), SoundEvents.f_12362_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), 600, 1, false, false, false), attacker);
                  }
               }
            }
         }
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      if (instance.isToggled()) {
         if (!e.getSource().m_19387_()) {
            label35: {
               DamageSource var4 = e.getSource();
               if (var4 instanceof TensuraDamageSource) {
                  TensuraDamageSource source = (TensuraDamageSource)var4;
                  if (source.getSkill() != null) {
                     break label35;
                  }
               }

               return;
            }
         }

         Entity var7 = e.getSource().m_7639_();
         if (var7 instanceof LivingEntity) {
            LivingEntity attacker = (LivingEntity)var7;
            if (SkillUtils.reducingResistances(attacker)) {
               return;
            }
         }

         float multiplier = instance.isMastered(e.getEntity()) ? 3.0F : 2.0F;
         e.setAmount(e.getAmount() / multiplier);
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * 5.0D)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         AABB box = entity.m_20191_().m_82400_(entity.m_6095_().equals(TensuraEntityTypes.CHARYBDIS.get()) ? 30.0D : (instance.isMastered(entity) ? 15.0D : 10.0D));
         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, box, (living) -> {
            return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
         });
         if (!list.isEmpty()) {
            CompoundTag tag = instance.getOrCreateTag();
            int time = tag.m_128451_("activatedTimes");
            if (time % 10 == 0) {
               this.addMasteryPoint(instance, entity);
            }

            tag.m_128405_("activatedTimes", time + 1);
            Iterator var7 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var7.hasNext()) {
                     return;
                  }

                  target = (LivingEntity)var7.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_7500_() || player.m_5833_());

               if (!(TensuraEPCapability.getEP(target) > TensuraEPCapability.getEP(entity) * 3.5D)) {
                  target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), 120, 0, false, false, false), entity);
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (!MagicInterferenceEffect.canStillFly(player)) {
                        player.m_20242_(false);
                        if (player.m_150110_().f_35936_) {
                           player.m_150110_().f_35936_ = false;
                           player.m_150110_().f_35935_ = false;
                        }
                     }
                  }
               }
            }
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }
}
