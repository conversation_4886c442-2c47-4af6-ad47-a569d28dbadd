package com.github.manasmods.tensura.capability.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.event.SpiritLevelUpdateEvent;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncSkillCapabilityPacket;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.NonNullList;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.Nullable;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraSkillCapability implements ITensuraSkillCapability {
   public static final Capability<ITensuraSkillCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<ITensuraSkillCapability>() {
   });
   private static final ResourceLocation IDENTIFIER = new ResourceLocation("tensura", "presets");
   private int activePreset = 0;
   private int analysisLevel;
   private int analysisMode;
   private int analysisDistance;
   private int spiritCooldown = 0;
   private double waterPoint = 0.0D;
   private double lavaPoint;
   private final List<String> presetNames = new ArrayList();
   private final NonNullList<NonNullList<ResourceLocation>> presets = NonNullList.m_122780_(9, NonNullList.m_122780_(3, new ResourceLocation("tensura:none")));
   private List<String> warpNames = new ArrayList();
   private NonNullList<NonNullList<Double>> warps = NonNullList.m_122780_(3, NonNullList.m_122780_(3, 0.0D));
   private final List<Integer> spiritLevels = new ArrayList();

   @SubscribeEvent
   public static void attach(AttachCapabilitiesEvent<Entity> e) {
      if (e.getObject() instanceof Player) {
         e.addCapability(IDENTIFIER, new TensuraSkillCapabilityProvider());
      }

   }

   public static LazyOptional<ITensuraSkillCapability> getFrom(Player player) {
      return player.getCapability(CAPABILITY);
   }

   public static void sync(Player player) {
      if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         getFrom(serverPlayer).ifPresent((data) -> {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return serverPlayer;
            }), new SyncSkillCapabilityPacket(data, serverPlayer.m_19879_()));
         });
      }
   }

   public TensuraSkillCapability() {
      int i;
      for(i = 1; i <= 9; ++i) {
         this.presetNames.add("Preset " + i);
      }

      for(i = 1; i <= 3; ++i) {
         this.warpNames.add("Warp " + i);
      }

      for(i = 1; i <= 7; ++i) {
         this.spiritLevels.add(0);
      }

      this.analysisLevel = 0;
      this.analysisMode = 0;
      this.analysisDistance = 5;
   }

   public int getSpiritLevel(int spirit) {
      return (Integer)this.spiritLevels.get(spirit);
   }

   public boolean setSpiritLevel(Player player, int spirit, int level) {
      SpiritLevelUpdateEvent event = new SpiritLevelUpdateEvent(player, spirit, level);
      if (MinecraftForge.EVENT_BUS.post(event)) {
         return false;
      } else {
         this.spiritLevels.set(event.getElemental().getId(), event.getSpiritLevel().getId());
         return true;
      }
   }

   public void clearSpiritLevel() {
      Collections.fill(this.spiritLevels, 0);
   }

   @Nullable
   public ManasSkill getSkillInSlot(int slot) {
      ResourceLocation skillId = (ResourceLocation)((NonNullList)this.presets.get(this.activePreset)).get(slot);
      return (ManasSkill)SkillAPI.getSkillRegistry().getValue(skillId);
   }

   @Nullable
   public ManasSkill getSkillInPresetSlot(int preset, int slot) {
      ResourceLocation skillId = (ResourceLocation)((NonNullList)this.presets.get(preset)).get(slot);
      return (ManasSkill)SkillAPI.getSkillRegistry().getValue(skillId);
   }

   public boolean isSkillInSlots(ManasSkill skill) {
      return ((NonNullList)this.presets.get(this.activePreset)).contains(SkillUtils.getSkillId(skill));
   }

   public boolean isSkillInSpecificSlot(ManasSkill skill, int slot) {
      return ((NonNullList)this.presets.get(this.activePreset)).isEmpty() ? false : ((ResourceLocation)((NonNullList)this.presets.get(this.activePreset)).get(slot)).equals(SkillUtils.getSkillId(skill));
   }

   public void setInstanceInSlot(@Nullable ManasSkillInstance instance, int slot) {
      if (instance == null) {
         ((NonNullList)this.presets.get(this.activePreset)).set(slot, new ResourceLocation("tensura", "none"));
      } else {
         ((NonNullList)this.presets.get(this.activePreset)).set(slot, instance.getSkillId());
      }
   }

   public void setInstanceInPresetSlot(@Nullable ManasSkillInstance instance, int preset, int slot) {
      if (instance == null) {
         ((NonNullList)this.presets.get(preset)).set(slot, new ResourceLocation("tensura", "none"));
      } else {
         ((NonNullList)this.presets.get(preset)).set(slot, instance.getSkillId());
      }
   }

   public boolean removeSkillFromSlots(ManasSkill skill) {
      boolean success = false;
      ResourceLocation id = SkillUtils.getSkillId(skill);

      for(int list = 0; list < 9; ++list) {
         if (((NonNullList)this.presets.get(list)).contains(id)) {
            for(int i = 0; i < 3; ++i) {
               if (((ResourceLocation)((NonNullList)this.presets.get(list)).get(i)).equals(id)) {
                  ((NonNullList)this.presets.get(list)).set(i, new ResourceLocation("tensura:none"));
                  success = true;
               }
            }
         }
      }

      return success;
   }

   public String getPresetName(int number) {
      return (String)this.presetNames.get(number);
   }

   public void setPresetName(int number, String name) {
      this.presetNames.set(number, name);
   }

   public String getWarpName(int warp) {
      return (String)this.warpNames.get(warp);
   }

   public void setWarpName(int warp, String name) {
      this.warpNames.set(warp, name);
   }

   public double getWarpX(int warp) {
      return (Double)((NonNullList)this.warps.get(warp)).get(0);
   }

   public double getWarpY(int warp) {
      return (Double)((NonNullList)this.warps.get(warp)).get(1);
   }

   public double getWarpZ(int warp) {
      return (Double)((NonNullList)this.warps.get(warp)).get(2);
   }

   public void setWarp(int warp, double x, double y, double z) {
      ((NonNullList)this.warps.get(warp)).set(0, x);
      ((NonNullList)this.warps.get(warp)).set(1, y);
      ((NonNullList)this.warps.get(warp)).set(2, z);
   }

   public void clearAllWarp() {
      this.warpNames = new ArrayList();

      for(int i = 1; i <= 3; ++i) {
         this.warpNames.add("Warp " + i);
      }

      this.warps = NonNullList.m_122780_(3, NonNullList.m_122780_(3, 0.0D));
   }

   public void setWaterPoint(double point) {
      this.waterPoint = Mth.m_14008_(point, 0.0D, 10000.0D);
   }

   public void setLavaPoint(double point) {
      this.lavaPoint = Mth.m_14008_(point, 0.0D, 10000.0D);
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();
      tag.m_128405_("spiritCooldown", this.spiritCooldown);
      tag.m_128405_("activePreset", this.activePreset);
      tag.m_128347_("waterPoint", this.waterPoint);
      tag.m_128347_("lavaPoint", this.lavaPoint);
      tag.m_128405_("analysisLevel", this.analysisLevel);
      tag.m_128405_("analysisMode", this.analysisMode);
      tag.m_128405_("analysisDistance", this.analysisDistance);
      ListTag presetList = new ListTag();

      CompoundTag spirit;
      CompoundTag name;
      for(int i = 0; i < this.presets.size(); ++i) {
         List<ResourceLocation> list = (List)this.presets.get(i);
         ListTag preset = new ListTag();
         spirit = new CompoundTag();
         spirit.m_128359_("name", (String)this.presetNames.get(i));
         preset.add(spirit);
         Iterator var7 = list.iterator();

         while(var7.hasNext()) {
            ResourceLocation resourceLocation = (ResourceLocation)var7.next();
            CompoundTag ability = new CompoundTag();
            ability.m_128359_("ability", resourceLocation.toString());
            preset.add(ability);
         }

         name = new CompoundTag();
         name.m_128365_("preset", preset);
         presetList.add(name);
      }

      tag.m_128365_("presetList", presetList);
      ListTag warpList = new ListTag();

      for(int i = 0; i < this.warps.size(); ++i) {
         List<Double> list = (List)this.warps.get(i);
         ListTag warp = new ListTag();
         name = new CompoundTag();
         name.m_128359_("name", (String)this.warpNames.get(i));
         warp.add(name);
         Iterator var18 = list.iterator();

         while(var18.hasNext()) {
            Double coord = (Double)var18.next();
            CompoundTag coordinate = new CompoundTag();
            coordinate.m_128347_("coord", coord);
            warp.add(coordinate);
         }

         CompoundTag coordinateList = new CompoundTag();
         coordinateList.m_128365_("warp", warp);
         warpList.add(coordinateList);
      }

      tag.m_128365_("warpList", warpList);
      ListTag spiritList = new ListTag();

      for(int i = 0; i < this.spiritLevels.size(); ++i) {
         spirit = new CompoundTag();
         spirit.m_128405_("spirit" + i, (Integer)this.spiritLevels.get(i));
         spiritList.add(spirit);
      }

      tag.m_128365_("spiritList", spiritList);
      return tag;
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.spiritCooldown = nbt.m_128451_("spiritCooldown");
      this.activePreset = nbt.m_128451_("activePreset");
      this.waterPoint = nbt.m_128459_("waterPoint");
      this.lavaPoint = nbt.m_128459_("lavaPoint");
      this.analysisLevel = nbt.m_128451_("analysisLevel");
      this.analysisMode = nbt.m_128451_("analysisMode");
      this.analysisDistance = nbt.m_128451_("analysisDistance");
      ListTag presetList = (ListTag)nbt.m_128423_("presetList");
      if (presetList != null) {
         this.presetNames.clear();

         for(int i = 0; i < presetList.size(); ++i) {
            NonNullList<ResourceLocation> listRL = NonNullList.m_122780_(3, new ResourceLocation("tensura:none"));
            Tag tag = presetList.get(i);
            ListTag listTag = (ListTag)((CompoundTag)tag).m_128423_("preset");

            assert listTag != null;

            listRL.set(0, new ResourceLocation(((CompoundTag)listTag.get(1)).m_128461_("ability")));
            listRL.set(1, new ResourceLocation(((CompoundTag)listTag.get(2)).m_128461_("ability")));
            listRL.set(2, new ResourceLocation(((CompoundTag)listTag.get(3)).m_128461_("ability")));
            this.presetNames.add(((CompoundTag)listTag.get(0)).m_128461_("name"));
            this.presets.set(i, listRL);
         }
      }

      ListTag warpList = (ListTag)nbt.m_128423_("warpList");
      if (warpList != null) {
         this.warpNames.clear();

         for(int i = 0; i < warpList.size(); ++i) {
            NonNullList<Double> coordinates = NonNullList.m_122780_(3, 0.0D);
            Tag tag = warpList.get(i);
            ListTag listTag = (ListTag)((CompoundTag)tag).m_128423_("warp");

            assert listTag != null;

            coordinates.set(0, ((CompoundTag)listTag.get(1)).m_128459_("coord"));
            coordinates.set(1, ((CompoundTag)listTag.get(2)).m_128459_("coord"));
            coordinates.set(2, ((CompoundTag)listTag.get(3)).m_128459_("coord"));
            this.warpNames.add(((CompoundTag)listTag.get(0)).m_128461_("name"));
            this.warps.set(i, coordinates);
         }
      }

      ListTag spiritList = (ListTag)nbt.m_128423_("spiritList");
      if (spiritList != null) {
         this.spiritLevels.clear();

         for(int i = 0; i < spiritList.size(); ++i) {
            CompoundTag spirit = (CompoundTag)spiritList.get(i);
            this.spiritLevels.add(spirit.m_128451_("spirit" + i));
         }
      }

   }

   @Nullable
   public static ManasSkill getSkillInSlot(LivingEntity pLivingEntity, int slot) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? null : capability.getSkillInSlot(slot);
   }

   public static boolean isSkillInSlot(LivingEntity pLivingEntity, ManasSkill skill) {
      if (!SkillUtils.hasSkill(pLivingEntity, skill)) {
         return false;
      } else if (!(pLivingEntity instanceof Player)) {
         return true;
      } else {
         ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
         return capability == null ? false : capability.isSkillInSlots(skill);
      }
   }

   public static boolean isSkillInSlot(LivingEntity pLivingEntity, ManasSkill skill, int slot) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isSkillInSpecificSlot(skill, slot);
   }

   public static void removeSkillFromSlots(Player player, ManasSkill skill) {
      getFrom(player).ifPresent((cap) -> {
         if (cap.removeSkillFromSlots(skill)) {
            sync(player);
         }

      });
   }

   @Nullable
   public static String getWarpName(Player player, int warp) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? null : capability.getWarpName(warp);
   }

   public static double getWarpX(Player player, int warp) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getWarpX(warp);
   }

   public static double getWarpY(Player player, int warp) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getWarpY(warp);
   }

   public static double getWarpZ(Player player, int warp) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getWarpZ(warp);
   }

   public static int getSpiritCooldown(Player player) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getSpiritCooldown();
   }

   public static void setSpiritCooldown(Player player, int cooldown) {
      getFrom(player).ifPresent((cap) -> {
         cap.setSpiritCooldown(cooldown);
      });
      sync(player);
   }

   public static int getSpiritLevel(Player player, int spirit) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getSpiritLevel(spirit);
   }

   public static int getAnalysisLevel(Player player) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getAnalysisLevel();
   }

   public static int getAnalysisMode(Player player) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getAnalysisMode();
   }

   public static int getAnalysisDistance(Player player) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getAnalysisDistance();
   }

   public static double getWaterPoint(LivingEntity pLivingEntity) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0.0D : capability.getWaterPoint();
   }

   public static double getLavaPoint(LivingEntity pLivingEntity) {
      ITensuraSkillCapability capability = (ITensuraSkillCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0.0D : capability.getLavaPoint();
   }

   public static void resetEverything(Player player, boolean clearWarp, boolean spirit) {
      getFrom(player).ifPresent((cap) -> {
         if (clearWarp) {
            cap.clearAllWarp();
         }

         if (spirit) {
            cap.clearSpiritLevel();
            cap.setSpiritCooldown(0);
         }

         cap.setAnalysisLevel(0);
         cap.setAnalysisDistance(0);
         cap.setLavaPoint(0.0D);
         cap.setWaterPoint(0.0D);
      });
      sync(player);
   }

   public int getActivePreset() {
      return this.activePreset;
   }

   public int getAnalysisLevel() {
      return this.analysisLevel;
   }

   public int getAnalysisMode() {
      return this.analysisMode;
   }

   public int getAnalysisDistance() {
      return this.analysisDistance;
   }

   public int getSpiritCooldown() {
      return this.spiritCooldown;
   }

   public void setActivePreset(int activePreset) {
      this.activePreset = activePreset;
   }

   public void setAnalysisLevel(int analysisLevel) {
      this.analysisLevel = analysisLevel;
   }

   public void setAnalysisMode(int analysisMode) {
      this.analysisMode = analysisMode;
   }

   public void setAnalysisDistance(int analysisDistance) {
      this.analysisDistance = analysisDistance;
   }

   public void setSpiritCooldown(int spiritCooldown) {
      this.spiritCooldown = spiritCooldown;
   }

   public double getWaterPoint() {
      return this.waterPoint;
   }

   public double getLavaPoint() {
      return this.lavaPoint;
   }

   public List<String> getPresetNames() {
      return this.presetNames;
   }
}
