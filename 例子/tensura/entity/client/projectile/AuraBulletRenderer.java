package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.world.entity.player.Player;

public class AuraBulletRenderer extends MagicSphereRenderer<AuraBulletProjectile> {
   public AuraBulletRenderer(Context context) {
      super(context);
   }

   public void render(AuraBulletProjectile entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      Player player = minecraft.f_91074_;
      if (player == null || !entity.m_20177_(player)) {
         poseStack.m_85836_();
         float scale = entity.getVisualSize();
         poseStack.m_85841_(scale, scale, scale);
         poseStack.m_85845_(Vector3f.f_122225_.m_122270_(((float)entity.f_19797_ + partialTicks) / 5.0F - 45.0F));
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.m_5478_(entity)));
         float alpha = entity.m_37282_() == player && player != null && player.m_20270_(entity) <= 5.0F ? 0.5F : 1.0F;
         this.sphere.m_104306_(poseStack, consumer, 15728880, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, alpha);
         poseStack.m_85849_();
      }
   }
}
