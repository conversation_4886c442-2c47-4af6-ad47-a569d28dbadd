package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.block.HolyFireBlock;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.LiquidBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import org.jetbrains.annotations.NotNull;

public class MagmaShotProjectile extends FireBoltProjectile {
   public MagmaShotProjectile(EntityType<? extends MagmaShotProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public MagmaShotProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.MAGMA_SHOT.get(), levelIn);
      this.m_5602_(shooter);
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public void updateMovement() {
      Vec3 vector3d = this.m_20184_();
      double d0 = this.m_20185_() + vector3d.f_82479_;
      double d1 = this.m_20186_() + vector3d.f_82480_;
      double d2 = this.m_20189_() + vector3d.f_82481_;
      this.m_37283_();
      if (!this.m_20068_()) {
         this.m_20256_(vector3d.m_82490_(0.9900000095367432D));
         this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.019999999552965164D, 0.0D));
      }

      this.m_6034_(d0, d1, d2);
   }

   protected void m_8060_(@NotNull BlockHitResult pResult) {
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(2.0D), (entity) -> {
         return !entity.m_5825_() && !entity.m_21023_(MobEffects.f_19607_);
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var3 = livingEntityList.iterator();

         while(var3.hasNext()) {
            LivingEntity pLivingEntity = (LivingEntity)var3.next();
            pLivingEntity.m_20254_(this.getBurnTicks());
         }
      }

      super.m_8060_(pResult);
      if (!this.f_19853_.f_46443_) {
         this.placeLava(this);
      }

   }

   protected void m_5790_(@NotNull EntityHitResult result) {
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(2.0D), (entity) -> {
         return !entity.m_5825_() && !entity.m_21023_(MobEffects.f_19607_) && !entity.equals(result.m_82443_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var3 = livingEntityList.iterator();

         while(var3.hasNext()) {
            LivingEntity pLivingEntity = (LivingEntity)var3.next();
            pLivingEntity.m_20254_(this.getBurnTicks());
            pLivingEntity.m_6469_(DamageSource.f_19307_, this.getDamage());
            Entity var6 = this.m_37282_();
            if (var6 instanceof Player) {
               Player player = (Player)var6;
               pLivingEntity.m_6598_(player);
            }
         }
      }

      super.m_5790_(result);
      if (!this.f_19853_.f_46443_) {
         this.placeLava(this);
      }

   }

   public void setPosAndShoot(LivingEntity entity) {
      this.setPosAndShoot(entity, 0.0F);
   }

   public void setPosAndShoot(LivingEntity entity, float pInaccuracy) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_() - this.m_20191_().m_82376_() * 0.5D, 0.0D));
      Vector3f vector3f = new Vector3f(entity.m_20252_(2.0F));
      this.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), this.getSpeed(), pInaccuracy);
   }

   protected void placeLava(Entity entity) {
      if (TensuraGameRules.canSkillGrief(this.f_19853_)) {
         int yPos = Mth.m_14107_(entity.m_20186_()) - 1;
         int xPos = Mth.m_14107_(entity.m_20185_());
         int zPos = Mth.m_14107_(entity.m_20189_());
         BlockState lava = (BlockState)Blocks.f_49991_.m_49966_().m_61124_(LiquidBlock.f_54688_, 12);
         boolean placeFire = false;
         boolean removeBlock = false;

         for(int j = -2; j <= 2; ++j) {
            for(int k = -2; k <= 2; ++k) {
               for(int i = -1; i <= 3; ++i) {
                  if (this.f_19853_.f_46441_.m_188500_() > 0.3D) {
                     int newYPos = yPos + i;
                     int newXPos = xPos + j;
                     int newZPos = zPos + k;
                     BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
                     BlockState blockState = this.f_19853_.m_8055_(blockpos);
                     SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), blockpos);
                     if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                        if (blockState.m_60767_().m_76336_() && blockState.m_60819_().m_76178_()) {
                           BlockPos blockPosDown = blockpos.m_7495_();
                           BlockState blockStateDown = this.f_19853_.m_8055_(blockPosDown);
                           if (blockStateDown.m_60783_(this.f_19853_, blockPosDown, Direction.UP)) {
                              removeBlock = this.f_19853_.m_7471_(blockpos, true) || removeBlock;
                           }
                        }

                        if (HolyFireBlock.canBePlacedAt(this.f_19853_, blockpos)) {
                           placeFire = this.f_19853_.m_46597_(blockpos, lava) || placeFire;
                           this.f_19853_.m_186460_(blockpos, blockState.m_60734_(), HolyFireBlock.getFireTickDelay(this.f_19853_.f_46441_));
                        }

                        MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), blockpos));
                     }
                  }
               }
            }
         }

         if (removeBlock && this.m_37282_() != null) {
            this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
         }

         if (placeFire && this.m_37282_() != null) {
            this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123756_, x, y, z, 30, 1.5D, 0.1D, 1.5D, 1.0D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20184_();
      double d0 = this.m_20185_() - vec3.f_82479_;
      double d1 = this.m_20186_() - vec3.f_82480_;
      double d2 = this.m_20189_() - vec3.f_82481_;

      for(int i = 0; i < 4; ++i) {
         Vec3 random = this.vec3Random().m_82490_(0.20000000298023224D);
         this.f_19853_.m_7106_(ParticleTypes.f_123762_, d0 - random.f_82479_, d1 + 0.5D - random.f_82480_, d2 - random.f_82481_, random.f_82479_ * 0.5D, random.f_82480_ * 0.5D, random.f_82481_ * 0.5D);
      }

   }
}
