package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import org.jetbrains.annotations.Nullable;

public class CookSkill extends Skill {
   public static final UUID COOK = UUID.fromString("6cb9493c-7b20-11ee-b962-0242ac120002");

   public CookSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public String modeLearningId(int mode) {
      return mode == 1 ? "ChaoticFate" : "None";
   }

   public Component getModeName(int mode) {
      return Component.m_237115_("tensura.skill.mode.master_chef.chaotic_fate");
   }

   public void onToggleOn(ManasSkillInstance skillInstance, LivingEntity entity) {
      AttributeInstance instance = entity.m_21051_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get());
      AttributeModifier attributemodifier = new AttributeModifier(COOK, "Cook", 100.0D, Operation.ADDITION);
      if (instance != null && !instance.m_22109_(attributemodifier)) {
         instance.m_22125_(attributemodifier);
      }

   }

   public void onToggleOff(ManasSkillInstance skillInstance, LivingEntity entity) {
      AttributeInstance instance = entity.m_21051_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get());
      if (instance != null) {
         instance.m_22127_(COOK);
      }

   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (instance.isToggled()) {
         LivingEntity target = e.getEntity();
         if (!(TensuraEPCapability.getEP(target) > TensuraEPCapability.getEP(entity) * 2.0D)) {
            AttributeInstance attribute = target.m_21051_((Attribute)TensuraAttributeRegistry.BARRIER.get());
            if (attribute != null) {
               attribute.m_22132_();
            }

            entity.m_9236_().m_5594_((Player)null, entity.m_20183_(), SoundEvents.f_11983_, SoundSource.AMBIENT, 1.0F, 1.0F);
         }
      }
   }

   private boolean activatedChaoticFate(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128451_("ChaoticFate") < 100) {
         return false;
      } else {
         return instance.isMastered(entity) && instance.isToggled() ? true : tag.m_128471_("ChaoticFateActivated");
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent event) {
      CompoundTag tag = instance.getOrCreateTag();
      if (this.activatedChaoticFate(instance, entity)) {
         if (!instance.onCoolDown()) {
            LivingEntity target = event.getEntity();
            AttributeInstance health = target.m_21051_(Attributes.f_22276_);
            if (health != null) {
               double amount = (double)event.getAmount();
               AttributeModifier chefModifier = health.m_22111_(COOK);
               if (chefModifier != null) {
                  amount -= chefModifier.m_22218_();
               }

               AttributeModifier attributemodifier = new AttributeModifier(COOK, "Cook", amount * -1.0D, Operation.ADDITION);
               health.m_22130_(attributemodifier);
               health.m_22125_(attributemodifier);
               if (!instance.isMastered(entity) || !instance.isToggled()) {
                  tag.m_128379_("ChaoticFateActivated", false);
               }

               this.addMasteryPoint(instance, entity);
               instance.setCoolDown(1);
               entity.m_9236_().m_5594_((Player)null, entity.m_20183_(), SoundEvents.f_12052_, SoundSource.AMBIENT, 1.0F, 1.0F);
            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int learnPoint = tag.m_128451_("ChaoticFate");
      if (learnPoint < 100) {
         tag.m_128405_("ChaoticFate", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (tag.m_128451_("ChaoticFate") >= 100) {
               player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(0)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            } else {
               instance.setCoolDown(10);
               SkillUtils.learningFailPenalty(entity);
               player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(0)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
            }

            player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      } else {
         if (entity.m_6144_()) {
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 6.0D, false);
            if (target == null || !target.m_6084_()) {
               target = entity;
            }

            removeCookedHP(target, instance);
         } else {
            boolean activated = tag.m_128471_("ChaoticFateActivated");
            tag.m_128379_("ChaoticFateActivated", !activated);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), activated ? SoundEvents.f_11824_ : SoundEvents.f_11767_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }

   public static void removeCookedHP(LivingEntity entity, @Nullable ManasSkillInstance instance) {
      AttributeInstance health = entity.m_21051_(Attributes.f_22276_);
      if (health != null) {
         if (health.m_22111_(COOK) != null) {
            health.m_22127_(COOK);
            if (instance != null) {
               instance.setCoolDown(1);
            }

            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_);
         }

      }
   }
}
