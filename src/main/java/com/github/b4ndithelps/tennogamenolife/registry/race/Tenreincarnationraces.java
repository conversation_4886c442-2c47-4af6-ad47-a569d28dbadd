package com.github.b4ndithelps.tennogamenolife.registry.race;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.BefehlerRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.EinzeigRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.EmirEinsRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.HorouRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.HubieDoraRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.KampfRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.MachineRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.PrueferRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.SeherRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.ZeichenRace;
import com.github.b4ndithelps.tennogamenolife.race.Fantasuma.FantasumaRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.FlugelRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.JibrilRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.RaphaelRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.AzrilRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.ArutoshuRace;
import com.github.b4ndithelps.tennogamenolife.race.Flügel.HigherFlugelRace;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.PuraiyaRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.DragoniaRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.LesserDragoniaRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.MiddleDragoniaRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.HigherDragoniaRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.AranreivuRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.HartyleifRace;
import com.github.b4ndithelps.tennogamenolife.race.Doragonia.ReginreivuRace;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

/**
 * 种族注册类
 * 用于注册模组中的所有种族
 */
public class Tenreincarnationraces {
    private static final ResourceKey<Registry<Race>> RACE_REGISTRY_KEY = ResourceKey.createRegistryKey(new ResourceLocation("tensura", "races"));
    public static final DeferredRegister<Race> RACES = DeferredRegister.create(RACE_REGISTRY_KEY, Tenreincarnation.MODID);

    public static void register(IEventBus modEventBus) {
        RACES.register(modEventBus);
    }

    /**
     * 注册天翼种种族
     */
    public static final RegistryObject<FlugelRace> FLUGEL =
            RACES.register("flugel", FlugelRace::new);

    /**
     * 注册吉普莉尔种族
     */
    public static final RegistryObject<JibrilRace> JIBRIL =
            RACES.register("jibril", JibrilRace::new);

    /**
     * 注册机凯种种族
     */
    public static final RegistryObject<MachineRace> MACHINE =
            RACES.register("machine", MachineRace::new);

    /**
     * 注册指挥体种族
     */
    public static final RegistryObject<BefehlerRace> BEFEHLER =
            RACES.register("befehler", BefehlerRace::new);

    /**
     * 注册战斗体种族
     */
    public static final RegistryObject<KampfRace> KAMPF =
            RACES.register("kampf", KampfRace::new);

    /**
     * 注册设计体种族
     */
    public static final RegistryObject<ZeichenRace> ZEICHEN =
            RACES.register("zeichen", ZeichenRace::new);

    /**
     * 注册解析体种族
     */
    public static final RegistryObject<PrueferRace> PRUEFER =
            RACES.register("pruefer", PrueferRace::new);

    /**
     * 注册观测体种族
     */
    public static final RegistryObject<SeherRace> SEHER =
            RACES.register("seher", SeherRace::new);

    /**
     * 注册休比·多拉种族
     */
    public static final RegistryObject<HubieDoraRace> HUBIE_DORA =
            RACES.register("hubie_dora", HubieDoraRace::new);

    /**
     * 注册爱因兹希种族
     */
    public static final RegistryObject<EinzeigRace> EINZEIG =
            RACES.register("einzeig", EinzeigRace::new);

    /**
     * 注册依蜜尔爱因种族
     */
    public static final RegistryObject<EmirEinsRace> EMIR_EINS =
            RACES.register("emir_eins", EmirEinsRace::new);

    /**
     * 注册狐疑之神帆楼种族
     */
    public static final RegistryObject<HorouRace> HOROU =
            RACES.register("horou", HorouRace::new);

    /**
     * 注册拉斐尔种族
     */
    public static final RegistryObject<RaphaelRace> RAPHAEL = 
            RACES.register("raphael", RaphaelRace::new);

    /**
     * 注册阿兹瑞尔种族
     */
    public static final RegistryObject<AzrilRace> AZRIL = RACES.register("azril", AzrilRace::new);

    /**
     * 注册阿尔特修种族
     */
    public static final RegistryObject<ArutoshuRace> ARUTOSHU = RACES.register("arutoshu", ArutoshuRace::new);

    /**
     * 注册遗志体种族
     */
    public static final RegistryObject<PuraiyaRace> PURAIYA = RACES.register("puraiya", PuraiyaRace::new);

    /**
     * 注册幻想种种族
     */
    public static final RegistryObject<FantasumaRace> FANTASUMA = RACES.register("fantasuma", FantasumaRace::new);

    // 注册地精种族
    public static final RegistryObject<Race> DWARF = RACES.register("dwarf", com.github.b4ndithelps.tennogamenolife.race.Dwarf.DwarfRace::new);

    /**
     * 注册尼依・缇儿威尔古种族
     */
    public static final RegistryObject<Race> NII_TIRUVIRUGU = RACES.register("nii_tiruvirugu", com.github.b4ndithelps.tennogamenolife.race.Dwarf.NiiTiruviruguRace::new);

    /**
     * 注册维格・德劳乌尼尔种族
     */
    public static final RegistryObject<Race> VEIGU_DORAUVUNIRU = RACES.register("veigu_dorauvuniru", com.github.b4ndithelps.tennogamenolife.race.Dwarf.VeiguDorauvuniruRace::new);

    /**
     * 注册罗尼・德劳乌尼尔种族
     */
    public static final RegistryObject<Race> RONI_DORAUVUNIRU = RACES.register("roni_dorauvuniru", com.github.b4ndithelps.tennogamenolife.race.Dwarf.RoniDorauvuniruRace::new);

    /**
     * 注册奥肯因种族
     */
    public static final RegistryObject<Race> OCAIN = RACES.register("ocain", com.github.b4ndithelps.tennogamenolife.race.Dwarf.OcainRace::new);
    
    /**
     * 注册龙精种种族
     */
    public static final RegistryObject<DragoniaRace> DRAGONIA = RACES.register("dragonia", DragoniaRace::new);

    /**
     * 注册下级龙精种种族
     */
    public static final RegistryObject<LesserDragoniaRace> LESSER_DRAGONIA = RACES.register("lesser_dragonia", LesserDragoniaRace::new);
    
    /**
     * 注册中阶龙精种种族
     */
    public static final RegistryObject<MiddleDragoniaRace> MIDDLE_DRAGONIA = RACES.register("middle_dragonia", MiddleDragoniaRace::new);

    /**
     * 注册上阶龙精种种族
     */
    public static final RegistryObject<HigherDragoniaRace> HIGHER_DRAGONIA = RACES.register("higher_dragonia", HigherDragoniaRace::new);
    
    /**
     * 注册焉龙种族
     */
    public static final RegistryObject<AranreivuRace> ARANREIVU = RACES.register("aranreivu", AranreivuRace::new);
    
    /**
     * 注册终龙种族
     */
    public static final RegistryObject<HartyleifRace> HARTYLEIF = RACES.register("hartyleif", HartyleifRace::new);
    
    /**
     * 注册聪龙种族
     */
    public static final RegistryObject<ReginreivuRace> REGINREIVU = RACES.register("reginreivu", ReginreivuRace::new);

    /**
     * 注册上级天翼种族
     */
    public static final RegistryObject<HigherFlugelRace> HIGHER_FLUGEL = RACES.register("higher_flugel", HigherFlugelRace::new);
}
