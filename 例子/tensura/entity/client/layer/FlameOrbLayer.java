package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.Arrays;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoLayerRenderer;
import software.bernie.geckolib3.renderers.geo.IGeoRenderer;

public class FlameOrbLayer extends GeoLayerRenderer<MadOgreOrbsEntity> {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_6.png")};
   private final int orb;

   public FlameOrbLayer(IGeoRenderer<MadOgreOrbsEntity> entityRendererIn, int orb) {
      super(entityRendererIn);
      this.orb = orb;
   }

   public ResourceLocation getTextureResource(MadOgreOrbsEntity orbs) {
      return (ResourceLocation)Arrays.stream(TEXTURES).toList().get(orbs.f_19797_ / 2 % TEXTURES.length);
   }

   public ResourceLocation getModel() {
      return new ResourceLocation("tensura", "geo/projectile/orb_" + this.orb + ".geo.json");
   }

   public void render(PoseStack matrixStackIn, MultiBufferSource bufferIn, int packedLightIn, MadOgreOrbsEntity orbs, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
      if (orbs.getSpheres() >= this.orb) {
         matrixStackIn.m_85836_();
         RenderType cameo = RenderType.m_110473_(this.getTextureResource(orbs));
         this.getRenderer().render(this.getEntityModel().getModel(this.getModel()), orbs, partialTicks, cameo, matrixStackIn, bufferIn, bufferIn.m_6299_(cameo), packedLightIn, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, 1.0F);
         matrixStackIn.m_85849_();
      }
   }
}
