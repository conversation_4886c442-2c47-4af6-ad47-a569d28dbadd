package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.field.HellFlare;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class HellFlareProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/hell_flare/hell_flare_6.png")};
   protected float areaRadius = 0.0F;
   protected int areaLife = 0;

   public HellFlareProjectile(EntityType<? extends HellFlareProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public HellFlareProjectile(Level levelIn, Entity shooter) {
      super((EntityType)TensuraEntityTypes.HELL_FLARE_PROJECTILE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   protected void m_7378_(CompoundTag compound) {
      this.areaLife = compound.m_128451_("AreaLife");
      this.areaRadius = compound.m_128457_("AreaLife");
      super.m_7378_(compound);
   }

   protected void m_7380_(CompoundTag compound) {
      compound.m_128405_("AreaLife", this.areaLife);
      compound.m_128350_("AreaLife", this.areaRadius);
      super.m_7380_(compound);
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void m_8060_(BlockHitResult pResult) {
   }

   protected void m_5790_(@NotNull EntityHitResult result) {
   }

   protected void m_6532_(@NotNull HitResult hitresult) {
      super.m_6532_(hitresult);
      HellFlare flare = new HellFlare(this.m_9236_(), this.m_37282_());
      flare.setDamage(this.getDamage());
      flare.setTickEachHit(10);
      flare.setMpCost(this.getMpCost());
      flare.setSkill(this.getSkill());
      flare.setLife(this.getAreaLife());
      flare.setRadius(this.getAreaRadius());
      flare.m_146884_(this.m_20182_().m_82520_(0.0D, (double)(-this.getAreaRadius()), 0.0D));
      this.m_9236_().m_7967_(flare);
      if (!this.f_19853_.m_5776_()) {
         String location = this.getAreaRadius() > 10.0F ? "tensura:hell_flare" : "tensura:hell_flare_mastery";
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return flare;
         }), new RequestFxSpawningPacket(new ResourceLocation(location), flare.m_19879_(), 0.0D, (double)this.getAreaRadius() + 0.5D, 0.0D, false));
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 3.0F, 0.9F + this.f_19853_.f_46441_.m_188501_() * 0.2F);
         TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, this.m_20185_(), this.m_20186_(), this.m_20189_(), 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
         TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.BLACK_FIRE.get(), this.m_20185_(), this.m_20186_(), this.m_20189_(), 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
         this.m_146870_();
      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.BLACK_FIRE.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }

   public float getAreaRadius() {
      return this.areaRadius;
   }

   public void setAreaRadius(float areaRadius) {
      this.areaRadius = areaRadius;
   }

   public int getAreaLife() {
      return this.areaLife;
   }

   public void setAreaLife(int areaLife) {
      this.areaLife = areaLife;
   }
}
