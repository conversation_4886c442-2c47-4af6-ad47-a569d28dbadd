package com.github.manasmods.tensura.registry.recipe;

import com.github.manasmods.tensura.data.recipe.GreatSageRefiningRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMeltingRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import net.minecraft.world.item.crafting.RecipeSerializer;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraRecipeTypes {
   public static final DeferredRegister<RecipeType<?>> registry;
   public static final RegistryObject<RecipeType<KilnMeltingRecipe>> KILN_MELTING;
   public static final RegistryObject<RecipeType<KilnMixingRecipe>> KILN_MIXING;
   public static final RegistryObject<RecipeType<SmithingBenchRecipe>> SMITHING;
   public static final RegistryObject<RecipeType<GreatSageRefiningRecipe>> REFINING;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
      TensuraRecipeTypes.Serializer.registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.RECIPE_TYPES, "tensura");
      KILN_MELTING = registry.register("kiln_melting", KilnMeltingRecipe.Type::new);
      KILN_MIXING = registry.register("kiln_mixing", KilnMixingRecipe.Type::new);
      SMITHING = registry.register("smithing", SmithingBenchRecipe.Type::new);
      REFINING = registry.register("refining", GreatSageRefiningRecipe.Type::new);
   }

   public static class Serializer {
      private static final DeferredRegister<RecipeSerializer<?>> registry;
      public static final RegistryObject<RecipeSerializer<KilnMeltingRecipe>> KILN_MELTING;
      public static final RegistryObject<RecipeSerializer<KilnMixingRecipe>> KILN_MIXING;
      public static final RegistryObject<RecipeSerializer<SmithingBenchRecipe>> SMITHING;
      public static final RegistryObject<RecipeSerializer<GreatSageRefiningRecipe>> REFINING;

      static {
         registry = DeferredRegister.create(ForgeRegistries.RECIPE_SERIALIZERS, "tensura");
         KILN_MELTING = registry.register("kiln_melting", KilnMeltingRecipe.Serializer::new);
         KILN_MIXING = registry.register("kiln_mixing", KilnMixingRecipe.Serializer::new);
         SMITHING = registry.register("smithing", SmithingBenchRecipe.Serializer::new);
         REFINING = registry.register("refining", GreatSageRefiningRecipe.Serializer::new);
      }
   }
}
