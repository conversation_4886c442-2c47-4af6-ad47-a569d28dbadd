package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SalamanderEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class SalamanderModel extends AnimatedGeoModel<SalamanderEntity> {
   public ResourceLocation getModelResource(SalamanderEntity object) {
      return new ResourceLocation("tensura", "geo/salamander.geo.json");
   }

   public ResourceLocation getTextureResource(SalamanderEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/salamander/salamander.png");
   }

   public ResourceLocation getAnimationResource(SalamanderEntity bear) {
      return new ResourceLocation("tensura", "animations/salamander.animation.json");
   }

   public void setCustomAnimations(SalamanderEntity lizardman, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(lizardman, instanceId, customPredicate);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
      }

   }
}
