package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ArmyWaspEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class ArmyWaspRenderer extends GeoEntityRenderer<ArmyWaspEntity> {
   public ArmyWaspRenderer(Context renderManager) {
      super(renderManager, new ArmyWaspModel());
      this.f_114477_ = 0.4F;
   }

   public ResourceLocation getTextureLocation(ArmyWaspEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/insect/army_wasp.png");
   }

   public RenderType getRenderType(ArmyWaspEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      float scale = 0.8F;
      if (entity.m_6162_()) {
         scale /= 2.0F;
      }

      stack.m_85841_(scale, scale, scale);
      return super.getRenderType(entity, partialTicks, stack, renderTypeBuffer, vertexBuilder, packedLightIn, textureLocation);
   }
}
