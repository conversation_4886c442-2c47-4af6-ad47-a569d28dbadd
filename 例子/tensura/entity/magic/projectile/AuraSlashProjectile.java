package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;

public class AuraSlashProjectile extends TensuraProjectile {
   public AuraSlashProjectile(EntityType<? extends AuraSlashProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public AuraSlashProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.AURA_SLASH.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/aura_slash.png")};
   }

   protected void dealDamage(Entity target) {
      if (this.damage > 0.0F) {
         target.m_6469_(TensuraDamageSources.auraSlash(this, this.m_37282_(), this.getSkill(), this.getApCost()), this.damage);
      }

   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean piercingBlock() {
      return this.getSize() >= 2.0F;
   }

   public boolean piercingEntity() {
      return this.getSize() >= 2.0F;
   }

   protected boolean m_5603_(Entity pTarget) {
      return !super.m_5603_(pTarget) ? false : pTarget instanceof LivingEntity;
   }

   protected void m_8060_(BlockHitResult pResult) {
      super.m_8060_(pResult);
      if (this.getSize() >= 2.0F && this.shouldGrief()) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215778_, SoundSource.PLAYERS, 3.0F, 1.0F);
         Predicate<BlockPos> posPredicate = this.m_37282_() != null ? (pos) -> {
            return !Objects.equals(pos, this.m_37282_().m_20097_());
         } : (pos) -> {
            return true;
         };
         SkillHelper.launchBlock(this, pResult.m_82450_(), (int)(0.5D * (double)this.getSize()), (int)this.getSize(), 0.3F, 0.2F, (blockState) -> {
            return this.f_19796_.m_188503_(2) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
         }, posPredicate, this.getSkill());
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12317_);
   }

   public void flyingParticles() {
   }
}
