package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class DeadEndRainbowEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public DeadEndRainbowEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public ChatFormatting getNameFormatting() {
      return ChatFormatting.LIGHT_PURPLE;
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity entity, int pLevel, float damage) {
      if (entity.f_19802_ < 60) {
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            double SHP = target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
            float max = Math.max(500.0F, damage);
            float shpDamage = (float)Mth.m_14008_(SHP / 7.0D + SHP * 0.10000000149011612D, 10.0D, (double)max);
            float var10000;
            if (attacker instanceof Player) {
               Player player = (Player)attacker;
               var10000 = player.m_36403_(0.5F);
            } else {
               var10000 = 1.0F;
            }

            float f2 = var10000;
            shpDamage *= 0.2F + f2 * f2 * 0.8F;
            DamageSourceHelper.directSpiritualHurt(target, attacker, shpDamage);
         }
      }
   }

   public static void applyDeadEndRainbow(ItemStack toStack) {
      if (toStack.m_150930_((Item)TensuraToolItems.DEAD_END_RAINBOW.get())) {
         if (toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.DEAD_END_RAINBOW.get()) < 1) {
            toStack.m_41663_((Enchantment)TensuraEnchantments.DEAD_END_RAINBOW.get(), 1);
         }
      }
   }
}
