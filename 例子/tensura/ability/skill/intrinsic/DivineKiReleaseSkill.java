package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ToolActions;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class DivineKiReleaseSkill extends Skill {
   public DivineKiReleaseSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      Race race = TensuraPlayerCapability.getRace(entity);
      return race != null && race.isDivine();
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      Race race = TensuraPlayerCapability.getRace(entity);
      return race == null || race.isDivine();
   }

   public static boolean isBattlewillDamage(DamageSource source, LivingEntity attacker) {
      if (source instanceof TensuraDamageSource) {
         TensuraDamageSource damageSource = (TensuraDamageSource)source;
         if (damageSource.getSkill() != null && damageSource.getSkill().getSkill() instanceof Battewill) {
            return true;
         }
      }

      if (source.m_7640_() == attacker && DamageSourceHelper.isPhysicalAttack(source)) {
         return attacker.m_21023_((MobEffect)TensuraMobEffects.AURA_SWORD.get()) || attacker.m_21023_((MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get());
      } else {
         return false;
      }
   }

   private boolean isDamagePhysicalOrBattelwill(DamageSource source, LivingEntity attacker) {
      if (isBattlewillDamage(source, attacker)) {
         return true;
      } else {
         return source.m_7640_() == attacker && DamageSourceHelper.isPhysicalAttack(source);
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (isBattlewillDamage(e.getSource(), entity)) {
            float damage = instance.isMastered(entity) ? 200.0F : 100.0F;
            e.setAmount(e.getAmount() + damage);
         }
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (this.isDamagePhysicalOrBattelwill(e.getSource(), entity)) {
            LivingEntity target = e.getEntity();
            int durabilityBreak = (int)Math.max(1.0F, e.getAmount() / 4.0F) * 2;
            if (instance.isMastered(entity)) {
               durabilityBreak *= 2;
            }

            EquipmentSlot[] var6 = EquipmentSlot.values();
            int var7 = var6.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               EquipmentSlot slot = var6[var8];
               if (!slot.m_20743_().equals(Type.HAND) || target.m_6844_(slot).canPerformAction(ToolActions.SHIELD_BLOCK)) {
                  ItemStack slotStack = target.m_6844_(slot);
                  if (slotStack.m_41783_() == null || !(slotStack.m_41783_().m_128459_("EP") >= 1000000.0D)) {
                     slotStack.m_41622_(durabilityBreak, target, (living) -> {
                        living.m_21166_(slot);
                     });
                  }
               }
            }

         }
      }
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
