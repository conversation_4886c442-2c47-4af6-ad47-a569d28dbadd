package com.github.manasmods.tensura.ability.magic.spiritual.light;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.projectile.LightArrowProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class SolarRainMagic extends SpiritualMagic {
   public SolarRainMagic() {
      super(MagicElemental.LIGHT, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 80;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 8000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            Level level = entity.m_9236_();
            int distance = instance.isMastered(entity) ? 30 : 20;
            Entity target = SkillHelper.getTargetingEntity(entity, (double)distance, false, true);
            Vec3 pos;
            if (target != null) {
               pos = target.m_146892_();
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, (double)distance);
               pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
            }

            if (instance.isMastered(entity)) {
               this.spawnLightArrows(instance, entity, pos, 10, 2.0D);
               this.spawnLightArrows(instance, entity, pos, 10, 4.0D);
            } else {
               this.spawnLightArrows(instance, entity, pos, 10, 3.0D);
            }

            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }

   private void spawnLightArrows(ManasSkillInstance instance, LivingEntity entity, Vec3 pos, int arrowAmount, double distance) {
      int arrowRot = 360 / arrowAmount;

      for(int i = 0; i < arrowAmount; ++i) {
         Vec3 arrowPos = entity.m_146892_().m_82549_((new Vec3(0.0D, distance, 0.0D)).m_82535_(((float)(arrowRot * i) - (float)arrowRot / 2.0F) * 0.017453292F).m_82496_(-entity.m_146909_() * 0.017453292F).m_82524_(-entity.m_146908_() * 0.017453292F));
         LightArrowProjectile arrow = new LightArrowProjectile(entity.m_9236_(), entity);
         arrow.setSpeed(2.0F);
         arrow.m_146884_(arrowPos);
         arrow.shootFromRot(pos.m_82546_(arrowPos).m_82541_());
         arrow.setLife(50);
         arrow.setDamage(30.0F);
         arrow.setMpCost(this.magiculeCost(entity, instance) / (double)arrowAmount);
         arrow.setSpiritAttack(true);
         arrow.setSkill(instance);
         entity.m_9236_().m_7967_(arrow);
      }

   }
}
