package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.entity.GoblinEntity;
import com.github.manasmods.tensura.entity.client.player.PlayerLikeModel;
import com.github.manasmods.tensura.entity.variant.GoblinVariant;
import com.google.common.collect.ImmutableList;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;

public class GoblinLayer {
   public static final LayerDefinition FACE_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.01F), 0.0F), 64, 64);
   public static final LayerDefinition HAIR_HEAD_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.02F), 0.0F), 64, 64);
   public static final LayerDefinition HAIR_BODY_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.45F), 0.0F), 64, 64);
   public static final LayerDefinition CLOTHING_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.27F), 0.0F), 64, 64);
   public static final LayerDefinition BANDAGES_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.3F), 0.0F), 64, 64);
   public static final LayerDefinition HEAD_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.5F), 0.0F), 64, 64);
   public static final LayerDefinition TOP_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.4F), 0.0F), 64, 64);
   public static final LayerDefinition BOTTOM_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.35F), 0.0F), 64, 64);

   private static void renderWithColor(HumanoidModel model, PoseStack pMatrixStack, VertexConsumer vertexconsumer, int pPackedLight, int i) {
      float f = (float)(i >> 16 & 255) / 255.0F;
      float f1 = (float)(i >> 8 & 255) / 255.0F;
      float f2 = (float)(i & 255) / 255.0F;
      model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, f, f1, f2, 1.0F);
   }

   private static RenderType getRenderType(ResourceLocation texture) {
      return RenderType.m_110473_(texture);
   }

   public static class Bottom<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation BOTTOM = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_bottom"), "main");
      private final HumanoidModel<T> model;

      public Bottom(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(BOTTOM)) {
            // $FF: synthetic field
            final GoblinLayer.Bottom this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         if (entity.isHobgoblin()) {
            if (entity.m_6844_(EquipmentSlot.LEGS).m_41619_()) {
               HumanoidModel<T> model = this.model();
               model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
               ((HumanoidModel)this.m_117386_()).m_102872_(model);
               VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getBottomTexture(entity)));
               model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
               PlayerLikeModel.sittingPose(entity, model);
               GoblinLayer.renderWithColor(model, pMatrixStack, vertexconsumer, pPackedLight, entity.getBottomColor());
            }
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getBottomTexture(T entity) {
         return GoblinVariant.Bottom.getTextureLocation(entity);
      }
   }

   public static class Top<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation TOP = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_top"), "main");
      private final HumanoidModel<T> model;

      public Top(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(TOP)) {
            // $FF: synthetic field
            final GoblinLayer.Top this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         if (entity.isHobgoblin()) {
            HumanoidModel<T> model = this.model();
            model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
            ((HumanoidModel)this.m_117386_()).m_102872_(model);
            VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getTopTexture(entity)));
            model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
            PlayerLikeModel.sittingPose(entity, model);
            GoblinLayer.renderWithColor(model, pMatrixStack, vertexconsumer, pPackedLight, entity.getTopColor());
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getTopTexture(T entity) {
         return GoblinVariant.Top.getTextureLocation(entity);
      }
   }

   public static class Head<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation HEAD = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_head"), "main");
      private final HumanoidModel<T> model;

      public Head(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(HEAD)) {
            // $FF: synthetic field
            final GoblinLayer.Head this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5608_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         if (entity.isHobgoblin()) {
            if ((Integer)entity.m_20088_().m_135370_(GoblinEntity.HEAD) != -1) {
               if (entity.m_6844_(EquipmentSlot.HEAD).m_41619_()) {
                  HumanoidModel<T> model = this.model();
                  model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
                  ((HumanoidModel)this.m_117386_()).m_102872_(model);
                  VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getHeadTexture(entity)));
                  model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
                  GoblinLayer.renderWithColor(model, pMatrixStack, vertexconsumer, pPackedLight, entity.getHeadColor());
               }
            }
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getHeadTexture(T entity) {
         return GoblinVariant.Head.getTextureLocation(entity);
      }
   }

   public static class Bandages<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation BANDAGES = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_bandages"), "main");
      private final HumanoidModel<T> model;

      public Bandages(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(BANDAGES)) {
            // $FF: synthetic field
            final GoblinLayer.Bandages this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         if (entity.hasBandages()) {
            HumanoidModel<T> model = this.model();
            model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
            ((HumanoidModel)this.m_117386_()).m_102872_(model);
            VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getBandagesTexture()));
            model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
            PlayerLikeModel.sittingPose(entity, model);
            model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getBandagesTexture() {
         return new ResourceLocation("tensura", "textures/entity/goblin/unisex/bandages.png");
      }
   }

   public static class Clothing<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation CLOTHING = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_clothing"), "main");
      private final HumanoidModel<T> model;

      public Clothing(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(CLOTHING)) {
            // $FF: synthetic field
            final GoblinLayer.Clothing this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         HumanoidModel<T> model = this.model();
         model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
         ((HumanoidModel)this.m_117386_()).m_102872_(model);
         VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getClothingTexture(entity)));
         model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
         PlayerLikeModel.sittingPose(entity, model);
         model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getClothingTexture(T entity) {
         return GoblinVariant.Clothing.getTextureLocation(entity);
      }
   }

   public static class HairBody<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation HAIR_BODY = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_hair_body"), "main");
      private final HumanoidModel<T> model;

      public HairBody(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(HAIR_BODY)) {
            // $FF: synthetic field
            final GoblinLayer.HairBody this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         HumanoidModel<T> model = this.model();
         model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
         ((HumanoidModel)this.m_117386_()).m_102872_(model);
         VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getHairTexture(entity)));
         model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
         model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getHairTexture(T entity) {
         return GoblinVariant.Hair.getTextureLocation(entity);
      }
   }

   public static class Hair<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation HAIR = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_hair"), "main");
      private final HumanoidModel<T> model;

      public Hair(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(HAIR)) {
            // $FF: synthetic field
            final GoblinLayer.Hair this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5608_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         if (entity.m_6844_(EquipmentSlot.HEAD).m_41619_()) {
            HumanoidModel<T> model = this.model();
            model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
            ((HumanoidModel)this.m_117386_()).m_102872_(model);
            VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(this.getHairTexture(entity)));
            model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
            model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      private ResourceLocation getHairTexture(T entity) {
         return GoblinVariant.Hair.getTextureLocation(entity);
      }
   }

   public static class Face<T extends GoblinEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation FACE = new ModelLayerLocation(new ResourceLocation("tensura", "goblin_face"), "main");
      private final HumanoidModel<T> model;

      public Face(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(FACE)) {
            // $FF: synthetic field
            final GoblinLayer.Face this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5608_() {
               return ImmutableList.of();
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         HumanoidModel<T> model = this.model();
         model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
         ((HumanoidModel)this.m_117386_()).m_102872_(model);
         VertexConsumer vertexconsumer = pBuffer.m_6299_(GoblinLayer.getRenderType(entity.getFace().getTextureLocation()));
         model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
         model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }
   }
}
