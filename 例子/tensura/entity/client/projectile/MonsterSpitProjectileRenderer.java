package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.GiantAntEntity;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.LlamaSpitModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

public class MonsterSpitProjectileRenderer extends EntityRenderer<MonsterSpitProjectile> {
   private static final ResourceLocation LLAMA_SPIT_LOCATION = new ResourceLocation("textures/entity/llama/spit.png");
   private final LlamaSpitModel<MonsterSpitProjectile> model;

   public MonsterSpitProjectileRenderer(Context renderManager) {
      super(renderManager);
      this.model = new LlamaSpitModel(renderManager.m_174023_(ModelLayers.f_171196_));
   }

   public void render(MonsterSpitProjectile pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      pMatrixStack.m_85836_();
      pMatrixStack.m_85837_(0.0D, 0.15000000596046448D, 0.0D);
      pMatrixStack.m_85845_(Vector3f.f_122225_.m_122240_(Mth.m_14179_(pPartialTicks, pEntity.f_19859_, pEntity.m_146908_()) - 90.0F));
      pMatrixStack.m_85845_(Vector3f.f_122227_.m_122240_(Mth.m_14179_(pPartialTicks, pEntity.f_19860_, pEntity.m_146909_())));
      this.model.m_6973_(pEntity, pPartialTicks, 0.0F, -0.1F, 0.0F, 0.0F);
      VertexConsumer vertexconsumer = pBuffer.m_6299_(this.model.m_103119_(this.getTextureLocation(pEntity)));
      this.model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, 1.0F);
      pMatrixStack.m_85849_();
      super.m_7392_(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
   }

   public ResourceLocation getTextureLocation(MonsterSpitProjectile pEntity) {
      return pEntity.m_37282_() instanceof GiantAntEntity ? new ResourceLocation("tensura", "textures/blank_texture.png") : LLAMA_SPIT_LOCATION;
   }
}
