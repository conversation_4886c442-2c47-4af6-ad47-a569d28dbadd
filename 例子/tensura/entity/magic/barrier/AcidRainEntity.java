package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.network.PacketDistributor;

public class AcidRainEntity extends BarrierEntity {
   @Nullable
   private Entity target;

   public AcidRainEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.ACID_RAIN.get(), level);
      this.m_5602_(entity);
   }

   public AcidRainEntity(EntityType<? extends AcidRainEntity> entityType, Level level) {
      super(entityType, level);
      this.f_19811_ = false;
   }

   @Nullable
   public Entity getTarget() {
      return this.target;
   }

   public void setTarget(@Nullable Entity pTarget) {
      this.target = pTarget;
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean isMultipartEntity() {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getTarget() != null) {
         this.m_6034_(this.getTarget().m_20185_(), this.getTarget().m_20186_(), this.getTarget().m_20189_());
      } else {
         Entity owner = this.m_37282_();
         if (owner != null) {
            this.m_6034_(owner.m_20185_(), owner.m_20186_(), owner.m_20189_());
            List<AcidRainEntity> rains = this.m_9236_().m_6443_(AcidRainEntity.class, owner.m_20191_(), (entityData) -> {
               return entityData.m_37282_() == owner && entityData != this;
            });
            if (!rains.isEmpty() || !owner.m_6084_()) {
               this.m_146870_();
            }
         }
      }

      if (!this.f_19853_.m_5776_()) {
         if (this.f_19797_ % 5 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:acid_rain"), this.m_19879_(), 0.0D, -2.0D, 0.0D, true));
         }

         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12541_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   protected void hitTarget() {
      double height = (double)(this.getRadius() * 2.0F + this.getHeight());
      AABB barrierBox = new AABB(this.m_20185_() - (double)this.getRadius(), this.m_20186_() - height, this.m_20189_() - (double)this.getRadius(), this.m_20185_() + (double)this.getRadius(), this.m_20186_() + height, this.m_20189_() + (double)this.getRadius());
      List<LivingEntity> targets = this.m_9236_().m_45976_(LivingEntity.class, barrierBox);
      Iterator var5 = targets.iterator();

      while(var5.hasNext()) {
         LivingEntity target = (LivingEntity)var5.next();
         if (this.m_5603_(target)) {
            this.applyEffect(target);
         }
      }

   }

   public void applyEffect(LivingEntity entity) {
      Entity owner = this.m_37282_();
      if (owner == null || !entity.m_7307_(owner) && entity != owner) {
         DamageSource damageSource = TensuraDamageSources.indirectElementalAttack("tensura.water_attack", this, this.m_37282_(), this.getMpCost() / 10.0D, this.getSkill(), true);
         if (entity.m_6469_(damageSource, this.getDamage())) {
            SkillHelper.checkThenAddEffectSource(entity, owner, new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 40, 0, false, false, false));
         }

      }
   }
}
