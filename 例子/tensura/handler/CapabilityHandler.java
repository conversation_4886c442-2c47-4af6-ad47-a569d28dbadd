package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.capability.effects.ITensuraEffectsCapability;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.ITensuraEPCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.ITensuraSkillCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.capability.smithing.ISmithingCapability;
import com.github.manasmods.tensura.capability.smithing.SmithingCapability;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import java.util.Iterator;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.RegisterCapabilitiesEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.Clone;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerChangedDimensionEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerLoggedInEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.StartTracking;
import net.minecraftforge.event.level.ChunkWatchEvent.Watch;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class CapabilityHandler {
   public static void registerCapabilities(RegisterCapabilitiesEvent e) {
      e.register(ITensuraPlayerCapability.class);
      e.register(ISmithingCapability.class);
      e.register(ITensuraEffectsCapability.class);
      e.register(ITensuraEPCapability.class);
      e.register(ITensuraSkillCapability.class);
      e.register(MagiculeChunkCapability.class);
   }

   @SubscribeEvent
   static void onPlayerLogin(PlayerLoggedInEvent e) {
      Player player = e.getEntity();
      if (player.m_20147_() && !player.m_150110_().f_35934_) {
         player.m_20331_(false);
      }

      TensuraPlayerCapability.sync(player);
      TensuraPlayerCapability.checkForFirstLogin(player);
      TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
         if (cap.getHeight() == 0.0F) {
            cap.setHeight(1.0F);
         }

      });
      SmithingCapability.sync(player);
      TensuraEffectsCapability.sync(player);
      TensuraEPCapability.sync(player);
      TensuraSkillCapability.sync(player);
   }

   @SubscribeEvent
   static void onPlayerTrack(StartTracking e) {
      Entity var2 = e.getTarget();
      if (var2 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var2;
         TensuraEffectsCapability.sync(living);
         TensuraEffectsCapability.sync(e.getEntity());
         TensuraEPCapability.sync(living);
         TensuraEPCapability.sync(e.getEntity());
      }

      var2 = e.getTarget();
      if (var2 instanceof Player) {
         Player player = (Player)var2;
         TensuraPlayerCapability.sync(player);
         TensuraPlayerCapability.sync(e.getEntity());
         SmithingCapability.sync(player);
         TensuraSkillCapability.sync(player);
         TensuraSkillCapability.sync(e.getEntity());
      }

   }

   @SubscribeEvent
   static void onPlayerClone(Clone e) {
      e.getOriginal().reviveCaps();
      TensuraPlayerCapability.getFrom(e.getOriginal()).ifPresent((oldData) -> {
         TensuraPlayerCapability.getFrom(e.getEntity()).ifPresent((data) -> {
            data.deserializeNBT((CompoundTag)oldData.serializeNBT());
            data.applyBaseAttributeModifiers(e.getEntity());
            data.setSprintSpeed(data.getSprintSpeed(), e.getEntity());
         });
      });
      if (!e.isWasDeath()) {
         CloneEntity.copyAttributeModifiers(e.getOriginal(), e.getEntity());
         Iterator var1 = e.getOriginal().m_21220_().iterator();

         while(var1.hasNext()) {
            MobEffectInstance instance = (MobEffectInstance)var1.next();
            e.getEntity().m_7292_(new MobEffectInstance(instance));
         }

         e.getEntity().m_6210_();
      }

      SmithingCapability.getFrom(e.getOriginal()).ifPresent((oldData) -> {
         SmithingCapability.getFrom(e.getEntity()).ifPresent((data) -> {
            data.deserializeNBT((CompoundTag)oldData.serializeNBT());
         });
      });
      TensuraEffectsCapability.getFrom(e.getOriginal()).ifPresent((oldData) -> {
         TensuraEffectsCapability.getFrom(e.getEntity()).ifPresent((data) -> {
            data.deserializeNBT((CompoundTag)oldData.serializeNBT());
         });
      });
      TensuraEPCapability.getFrom(e.getOriginal()).ifPresent((oldData) -> {
         TensuraEPCapability.getFrom(e.getEntity()).ifPresent((data) -> {
            data.deserializeNBT((CompoundTag)oldData.serializeNBT());
         });
      });
      TensuraSkillCapability.getFrom(e.getOriginal()).ifPresent((oldData) -> {
         TensuraSkillCapability.getFrom(e.getEntity()).ifPresent((data) -> {
            data.deserializeNBT((CompoundTag)oldData.serializeNBT());
         });
      });
      e.getOriginal().invalidateCaps();
   }

   @SubscribeEvent
   static void onPlayerRespawn(PlayerRespawnEvent e) {
      TensuraPlayerCapability.checkForFirstLogin(e.getEntity());
      TensuraPlayerCapability.sync(e.getEntity());
      if (!e.isEndConquered()) {
         TensuraPlayerCapability.resetMagiculeAura(e.getEntity());
      }

      SmithingCapability.sync(e.getEntity());
      TensuraEffectsCapability.sync(e.getEntity());
      TensuraEPCapability.sync(e.getEntity());
      TensuraSkillCapability.sync(e.getEntity());
   }

   @SubscribeEvent
   static void onPlayerChangeDimension(PlayerChangedDimensionEvent e) {
      TensuraPlayerCapability.sync(e.getEntity());
      SmithingCapability.sync(e.getEntity());
      TensuraEffectsCapability.sync(e.getEntity());
      TensuraEPCapability.sync(e.getEntity());
      TensuraSkillCapability.sync(e.getEntity());
   }

   @Nullable
   public static <T> T getCapability(Entity entity, Capability<T> capability) {
      return entity.getCapability(capability).isPresent() ? entity.getCapability(capability).orElseThrow(() -> {
         return new IllegalArgumentException("Lazy optional must not be empty");
      }) : null;
   }

   @SubscribeEvent
   static void onWatchChunk(Watch e) {
      MagiculeChunkCapabilityImpl.get(e.getChunk()).sync(e.getPlayer());
   }
}
