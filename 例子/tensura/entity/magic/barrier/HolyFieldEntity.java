package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class HolyFieldEntity extends BarrierEntity {
   public HolyFieldEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.HOLY_FIELD.get(), level);
      this.m_5602_(entity);
   }

   public HolyFieldEntity(EntityType<? extends HolyFieldEntity> entityType, Level level) {
      super(entityType, level);
   }

   public boolean m_6783_(double pDistance) {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean canWalkThrough(Entity entity) {
      Entity owner = this.m_37282_();
      if (entity == owner) {
         return true;
      } else {
         boolean var10000;
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            if (!target.m_21023_((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get()) || !target.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get()) || !target.m_21023_((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get())) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      Entity owner = this.m_37282_();
      if (owner != null) {
         this.m_6034_(owner.m_20185_(), owner.m_20186_() - (double)(this.getRadius() / 2.0F), owner.m_20189_());
      } else {
         this.m_146870_();
      }

   }

   public void applyEffect(LivingEntity entity) {
      Entity owner = this.m_37282_();
      if (owner == null || !entity.m_7307_(owner) && entity != owner) {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get(), 40, 4, false, false, false));
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), 40, 0, false, false, false));
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get(), 40, 0, false, false, false));
      }
   }
}
