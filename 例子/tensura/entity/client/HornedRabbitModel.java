package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HornedRabbitEntity;
import com.google.common.collect.ImmutableList;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class HornedRabbitModel<T extends HornedRabbitEntity> extends EntityModel<T> {
   public static final ModelLayerLocation HORNED_RABBIT = new ModelLayerLocation(new ResourceLocation("tensura", "horned_rabbit"), "horned_rabbit");
   private final ModelPart leftRearFoot;
   private final ModelPart rightRearFoot;
   private final ModelPart leftHaunch;
   private final ModelPart rightHaunch;
   private final ModelPart body;
   private final ModelPart leftFrontLeg;
   private final ModelPart rightFrontLeg;
   private final ModelPart head;
   private final ModelPart rightEar;
   private final ModelPart leftEar;
   private final ModelPart tail;
   private final ModelPart nose;
   private float jumpRotation;

   public HornedRabbitModel(ModelPart pRoot) {
      this.leftRearFoot = pRoot.m_171324_("left_hind_foot");
      this.rightRearFoot = pRoot.m_171324_("right_hind_foot");
      this.leftHaunch = pRoot.m_171324_("left_haunch");
      this.rightHaunch = pRoot.m_171324_("right_haunch");
      this.body = pRoot.m_171324_("body");
      this.leftFrontLeg = pRoot.m_171324_("left_front_leg");
      this.rightFrontLeg = pRoot.m_171324_("right_front_leg");
      this.head = pRoot.m_171324_("head");
      this.rightEar = pRoot.m_171324_("right_ear");
      this.leftEar = pRoot.m_171324_("left_ear");
      this.tail = pRoot.m_171324_("tail");
      this.nose = pRoot.m_171324_("nose");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      PartDefinition head = partdefinition.m_171599_("head", CubeListBuilder.m_171558_().m_171514_(32, 0).m_171481_(-2.5F, -4.0F, -5.0F, 5.0F, 4.0F, 5.0F), PartPose.m_171419_(0.0F, 16.0F, -1.0F));
      head.m_171599_("horn", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171481_(-0.5F, -2.0F, -1.5F, 1.0F, 5.0F, 1.0F), PartPose.m_171423_(0.0F, -6.0F, -2.5F, 0.4363F, 0.0F, 0.0F));
      partdefinition.m_171599_("right_ear", CubeListBuilder.m_171558_().m_171514_(52, 0).m_171481_(-2.5F, -9.0F, -1.0F, 2.0F, 5.0F, 1.0F), PartPose.m_171423_(0.0F, 16.0F, -1.0F, 0.0F, -0.2617994F, 0.0F));
      partdefinition.m_171599_("left_ear", CubeListBuilder.m_171558_().m_171514_(58, 0).m_171481_(0.5F, -9.0F, -1.0F, 2.0F, 5.0F, 1.0F), PartPose.m_171423_(0.0F, 16.0F, -1.0F, 0.0F, 0.2617994F, 0.0F));
      partdefinition.m_171599_("tail", CubeListBuilder.m_171558_().m_171514_(52, 6).m_171481_(-1.5F, -1.5F, 0.0F, 3.0F, 3.0F, 2.0F), PartPose.m_171423_(0.0F, 20.0F, 7.0F, -0.3490659F, 0.0F, 0.0F));
      partdefinition.m_171599_("nose", CubeListBuilder.m_171558_().m_171514_(32, 9).m_171481_(-0.5F, -2.5F, -5.5F, 1.0F, 1.0F, 1.0F), PartPose.m_171419_(0.0F, 16.0F, -1.0F));
      partdefinition.m_171599_("left_hind_foot", CubeListBuilder.m_171558_().m_171514_(26, 24).m_171481_(-1.0F, 5.5F, -3.7F, 2.0F, 1.0F, 7.0F), PartPose.m_171419_(3.0F, 17.5F, 3.7F));
      partdefinition.m_171599_("right_hind_foot", CubeListBuilder.m_171558_().m_171514_(8, 24).m_171481_(-1.0F, 5.5F, -3.7F, 2.0F, 1.0F, 7.0F), PartPose.m_171419_(-3.0F, 17.5F, 3.7F));
      partdefinition.m_171599_("left_haunch", CubeListBuilder.m_171558_().m_171514_(30, 15).m_171481_(-1.0F, 0.0F, 0.0F, 2.0F, 4.0F, 5.0F), PartPose.m_171423_(3.0F, 17.5F, 3.7F, -0.34906584F, 0.0F, 0.0F));
      partdefinition.m_171599_("right_haunch", CubeListBuilder.m_171558_().m_171514_(16, 15).m_171481_(-1.0F, 0.0F, 0.0F, 2.0F, 4.0F, 5.0F), PartPose.m_171423_(-3.0F, 17.5F, 3.7F, -0.34906584F, 0.0F, 0.0F));
      partdefinition.m_171599_("body", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171481_(-3.0F, -2.0F, -10.0F, 6.0F, 5.0F, 10.0F), PartPose.m_171423_(0.0F, 19.0F, 8.0F, -0.34906584F, 0.0F, 0.0F));
      partdefinition.m_171599_("left_front_leg", CubeListBuilder.m_171558_().m_171514_(8, 15).m_171481_(-1.0F, 0.0F, -1.0F, 2.0F, 7.0F, 2.0F), PartPose.m_171423_(3.0F, 17.0F, -1.0F, -0.17453292F, 0.0F, 0.0F));
      partdefinition.m_171599_("right_front_leg", CubeListBuilder.m_171558_().m_171514_(0, 15).m_171481_(-1.0F, 0.0F, -1.0F, 2.0F, 7.0F, 2.0F), PartPose.m_171423_(-3.0F, 17.0F, -1.0F, -0.17453292F, 0.0F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 64, 32);
   }

   public void m_7695_(PoseStack pPoseStack, VertexConsumer pBuffer, int pPackedLight, int pPackedOverlay, float pRed, float pGreen, float pBlue, float pAlpha) {
      if (this.f_102610_) {
         pPoseStack.m_85836_();
         pPoseStack.m_85841_(0.56666666F, 0.56666666F, 0.56666666F);
         pPoseStack.m_85837_(0.0D, 1.375D, 0.125D);
         ImmutableList.of(this.head, this.leftEar, this.rightEar, this.nose).forEach((p_103597_) -> {
            p_103597_.m_104306_(pPoseStack, pBuffer, pPackedLight, pPackedOverlay, pRed, pGreen, pBlue, pAlpha);
         });
         pPoseStack.m_85849_();
         pPoseStack.m_85836_();
         pPoseStack.m_85841_(0.4F, 0.4F, 0.4F);
         pPoseStack.m_85837_(0.0D, 2.25D, 0.0D);
         ImmutableList.of(this.leftRearFoot, this.rightRearFoot, this.leftHaunch, this.rightHaunch, this.body, this.leftFrontLeg, this.rightFrontLeg, this.tail).forEach((p_103587_) -> {
            p_103587_.m_104306_(pPoseStack, pBuffer, pPackedLight, pPackedOverlay, pRed, pGreen, pBlue, pAlpha);
         });
         pPoseStack.m_85849_();
      } else {
         pPoseStack.m_85836_();
         pPoseStack.m_85841_(0.6F, 0.6F, 0.6F);
         pPoseStack.m_85837_(0.0D, 1.0D, 0.0D);
         ImmutableList.of(this.leftRearFoot, this.rightRearFoot, this.leftHaunch, this.rightHaunch, this.body, this.leftFrontLeg, this.rightFrontLeg, this.head, this.rightEar, this.leftEar, this.tail, this.nose, new ModelPart[0]).forEach((p_103572_) -> {
            p_103572_.m_104306_(pPoseStack, pBuffer, pPackedLight, pPackedOverlay, pRed, pGreen, pBlue, pAlpha);
         });
         pPoseStack.m_85849_();
      }

   }

   public void setupAnim(T pEntity, float pLimbSwing, float pLimbSwingAmount, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
      float f = pAgeInTicks - (float)pEntity.f_19797_;
      this.nose.f_104203_ = pHeadPitch * 0.017453292F;
      this.head.f_104203_ = pHeadPitch * 0.017453292F;
      this.rightEar.f_104203_ = pHeadPitch * 0.017453292F;
      this.leftEar.f_104203_ = pHeadPitch * 0.017453292F;
      this.nose.f_104204_ = pNetHeadYaw * 0.017453292F;
      this.head.f_104204_ = pNetHeadYaw * 0.017453292F;
      this.rightEar.f_104204_ = this.nose.f_104204_ - 0.2617994F;
      this.leftEar.f_104204_ = this.nose.f_104204_ + 0.2617994F;
      this.jumpRotation = Mth.m_14031_(pEntity.getJumpCompletion(f) * 3.1415927F);
      this.leftHaunch.f_104203_ = (this.jumpRotation * 50.0F - 21.0F) * 0.017453292F;
      this.rightHaunch.f_104203_ = (this.jumpRotation * 50.0F - 21.0F) * 0.017453292F;
      this.leftRearFoot.f_104203_ = this.jumpRotation * 50.0F * 0.017453292F;
      this.rightRearFoot.f_104203_ = this.jumpRotation * 50.0F * 0.017453292F;
      this.leftFrontLeg.f_104203_ = (this.jumpRotation * -40.0F - 11.0F) * 0.017453292F;
      this.rightFrontLeg.f_104203_ = (this.jumpRotation * -40.0F - 11.0F) * 0.017453292F;
   }

   public void prepareMobModel(T pEntity, float pLimbSwing, float pLimbSwingAmount, float pPartialTick) {
      super.m_6839_(pEntity, pLimbSwing, pLimbSwingAmount, pPartialTick);
      this.jumpRotation = Mth.m_14031_(pEntity.getJumpCompletion(pPartialTick) * 3.1415927F);
   }
}
