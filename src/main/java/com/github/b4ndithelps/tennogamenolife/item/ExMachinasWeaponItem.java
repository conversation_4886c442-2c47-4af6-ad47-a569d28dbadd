package com.github.b4ndithelps.tennogamenolife.item;

import com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

/**
 * 机铠种之械 (Ex-Machina's Weapon)
 * 一把来自机铠种的神秘武器
 * 右键发射青色激光子弹，支持连射
 */
public class ExMachinasWeaponItem extends Item {

    // 移除冷却时间，实现无冷却发射
    public ExMachinasWeaponItem() {
        super(new Properties()
            .stacksTo(1)                    // 最大堆叠数量为1
            .rarity(Rarity.EPIC)           // 史诗级稀有度（紫色）
            .fireResistant()               // 防火
        );
    }

    @Override
    public InteractionResultHolder<ItemStack> use(Level level, Player player, InteractionHand hand) {
        ItemStack itemStack = player.getItemInHand(hand);

        // 发射子弹（无冷却，无声音）
        if (!level.isClientSide) {
            fireBullet(level, player);
        }

        // 返回fail避免任何动画，但子弹已经发射
        return InteractionResultHolder.fail(itemStack);
    }

    /**
     * 重写使用持续时间，返回0避免使用动画
     */
    @Override
    public int getUseDuration(ItemStack stack) {
        return 0; // 不需要使用持续时间，避免动画
    }

    /**
     * 重写使用动作，返回NONE避免动画
     */
    @Override
    public net.minecraft.world.item.UseAnim getUseAnimation(ItemStack stack) {
        return net.minecraft.world.item.UseAnim.NONE; // 不使用任何动画
    }

    /**
     * 发射子弹
     */
    private void fireBullet(Level level, Player player) {
        // 创建子弹实体
        ExMachinaBulletEntity bullet = new ExMachinaBulletEntity(level, player);

        // 设置子弹的发射方向和速度
        Vec3 lookDirection = player.getLookAngle();
        double speed = 3.0D; // 玩家武器保持原速度

        bullet.setDeltaMovement(
            lookDirection.x * speed,
            lookDirection.y * speed,
            lookDirection.z * speed
        );

        // 将子弹添加到世界中
        level.addFreshEntity(bullet);
    }
}
