package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.water.WaterMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.projectile.WaterBallProjectile;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class WaterDominationSkill extends Skill {
   public WaterDominationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 100.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (DamageSourceHelper.isWaterDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 4.0F);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_6144_()) {
         instance.getOrCreateTag().m_128405_("BreathEntity", 0);
         instance.markDirty();
      } else {
         if (WaterManipulationSkill.hasWater(entity)) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            this.shootBall(instance, entity);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (!(cap.getWaterPoint() <= 0.0D) || cap.getSpiritLevel(MagicElemental.WATER.getId()) >= 1) {
                  if (!SkillHelper.outOfMagicule(entity, instance)) {
                     cap.setWaterPoint(cap.getWaterPoint() - 1.0D);
                     this.shootBall(instance, entity);
                     TensuraSkillCapability.sync(player);
                  }
               }
            });
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity.m_6144_()) {
         return false;
      } else {
         if (WaterManipulationSkill.hasWater(entity)) {
            if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
               return false;
            }

            if (heldTicks % 60 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            WaterManipulationSkill.waterBreath(instance, entity, this.magiculeCost(entity, instance));
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (!(cap.getWaterPoint() <= 0.0D)) {
                  if (heldTicks % 20 != 0 || !SkillHelper.outOfMagicule(entity, instance)) {
                     if (heldTicks % 100 == 0) {
                        if (heldTicks % 100 == 0 && heldTicks > 0) {
                           this.addMasteryPoint(instance, entity);
                        }

                        cap.setWaterPoint(cap.getWaterPoint() - 1.0D);
                        TensuraSkillCapability.sync(player);
                     }

                     WaterManipulationSkill.waterBreath(instance, entity, this.magiculeCost(entity, instance));
                  }
               }
            });
         }

         return true;
      }
   }

   private void shootBall(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21011_(InteractionHand.MAIN_HAND, true);
      if (!WaterMagic.isWaterEvaporated(entity, entity.f_19853_)) {
         this.addMasteryPoint(instance, entity);
         WaterBallProjectile waterBall = new WaterBallProjectile(entity.m_9236_(), entity);
         waterBall.setSpeed(2.0F);
         waterBall.setDamage(12.0F);
         waterBall.setKnockForce(1.5F);
         waterBall.setBurnTicks(-1);
         waterBall.setMpCost(this.magiculeCost(entity, instance));
         waterBall.setSkill(instance);
         waterBall.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(waterBall);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12324_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }
}
