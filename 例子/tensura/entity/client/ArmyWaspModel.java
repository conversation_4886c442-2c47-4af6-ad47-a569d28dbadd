package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ArmyWaspEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class ArmyWaspModel extends AnimatedGeoModel<ArmyWaspEntity> {
   public ResourceLocation getModelResource(ArmyWaspEntity object) {
      return new ResourceLocation("tensura", "geo/army_wasp.geo.json");
   }

   public ResourceLocation getTextureResource(ArmyWaspEntity object) {
      return new ResourceLocation("tensura", "textures/entity/insect/army_wasp.png");
   }

   public ResourceLocation getAnimationResource(ArmyWaspEntity entity) {
      return new ResourceLocation("tensura", "animations/army_wasp.animation.json");
   }

   public void setCustomAnimations(ArmyWaspEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      if (entity.getMiscAnimation() == 0 && !entity.hovering) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
