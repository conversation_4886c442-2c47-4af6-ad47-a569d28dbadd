package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.IronGolem;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.WebBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.predicate.BlockStatePredicate;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class GiantAntEntity extends TensuraTamableEntity implements IAnimatable, IGiantMob, ITensuraMount, SpittingRangedMonster {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> RANGED_ATTACK;
   private static final EntityDataAccessor<Boolean> SADDLED;
   protected int rangedCoolDown = 0;
   public int miscAnimationTicks = 0;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public GiantAntEntity(EntityType<? extends GiantAntEntity> type, Level level) {
      super(type, level);
      this.f_19793_ = 3.0F;
      this.f_21364_ = 20;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 4.0D).m_22268_(Attributes.f_22276_, 45.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22281_, 8.0D).m_22268_(Attributes.f_22279_, 0.3199999928474426D).m_22268_(Attributes.f_22278_, 0.800000011920929D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 1.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(4, new GiantAntEntity.AntEatOrCleanGoal(this));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(6, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(2, new GiantAntEntity.GiantAntAttackGoal(this, 1.2D, 40, 20.0F, true));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Villager.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return !(entity instanceof GiantAntEntity);
      }));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, IronGolem.class, false, (Predicate)null));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(RANGED_ATTACK, false);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Ranged", this.getRanged());
      compound.m_128379_("Saddled", this.isSaddled());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setRanged(compound.m_128471_("Ranged"));
      this.setSaddled(compound.m_128471_("Saddled"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean getRanged() {
      return (Boolean)this.f_19804_.m_135370_(RANGED_ATTACK);
   }

   public void setRanged(boolean ranged) {
      this.f_19804_.m_135381_(RANGED_ATTACK, ranged);
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public void m_7334_(Entity pEntity) {
      if (!(pEntity instanceof GiantAntEntity)) {
         super.m_7334_(pEntity);
      }
   }

   public boolean m_5957_() {
      return false;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public GiantAntEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      GiantAntEntity ant = (GiantAntEntity)((EntityType)TensuraEntityTypes.GIANT_ANT.get()).m_20615_(pLevel);
      if (ant == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            ant.m_21816_(uuid);
            ant.m_7105_(true);
         }

         return ant;
      }
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 8.0F) {
         return false;
      } else {
         if (pFallDistance > 8.0F) {
            this.m_5496_(SoundEvents.f_12319_, 0.4F, 1.0F);
         }

         int i = this.m_5639_(pFallDistance - 8.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.giantAntSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public int m_5792_() {
      return 3;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            Item item = itemstack.m_41720_();
            if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
               if (!player.m_150110_().f_35937_) {
                  itemstack.m_41774_(1);
               }

               this.setSaddled(true);
               this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               return InteractionResult.SUCCESS;
            } else if (this.isSaddled() && item.equals(Items.f_42574_)) {
               this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
               this.setSaddled(false);
               return InteractionResult.SUCCESS;
            } else {
               if (!player.m_36341_() && this.isSaddled()) {
                  if (player.m_146895_() == null) {
                     this.m_21839_(false);
                     this.setWandering(false);
                     player.m_7998_(this, true);
                  }
               } else {
                  this.commanding(player);
               }

               return InteractionResult.SUCCESS;
            }
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(2);
      this.m_5634_(5.0F);
   }

   public boolean m_6898_(ItemStack pStack) {
      if (pStack.m_41720_() instanceof HealingPotionItem) {
         return false;
      } else {
         return pStack.m_204117_(TensuraTags.Items.ADDITIONAL_ANT_FOOD) || pStack.m_41614_();
      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(3);
      }

      return flag;
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 1.0F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null && this.isSaddled()) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.75D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            this.m_146872_();
         } else {
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!(pState.m_60734_() instanceof WebBlock)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected SoundEvent m_7515_() {
      return (SoundEvent)TensuraSoundEvents.CATERPILLAR_AMBIENT.get();
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return (SoundEvent)TensuraSoundEvents.MOTH_HURT.get();
   }

   protected SoundEvent m_5592_() {
      return (SoundEvent)TensuraSoundEvents.MOTH_DEATH.get();
   }

   public SoundSource m_5720_() {
      return SoundSource.NEUTRAL;
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (!this.getRanged() && ++this.rangedCoolDown == 100) {
         this.rangedCoolDown = 0;
         this.setRanged(Boolean.TRUE);
      }

      LivingEntity controller;
      if (!this.f_19853_.m_5776_()) {
         controller = this.getControllingPassenger();
         if (!this.m_21824_() || controller != null && this.m_21830_(controller)) {
            this.breakBlocks(this, 1.0F, false);
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 10) {
            controller = this.m_5448_();
            if (controller != null) {
               this.performRangedAttack(controller, this.m_6162_() ? 2.0D : 4.0D, this.m_6162_() ? 1.0D : 2.0D);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         } else if (this.getMiscAnimation() == -1 && this.miscAnimationTicks == 15) {
            controller = this.getControllingPassenger();
            if (controller != null) {
               BlockHitResult hitResult = SkillHelper.getPlayerPOVHitResult(this.f_19853_, controller, Fluid.NONE, 30.0D);
               this.performRangedAttack(hitResult.m_82425_(), -1.5D, 5.0D);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 4) {
         if (!this.m_6162_()) {
            this.setMiscAnimation(-1);
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case -1:
      case 4:
         var10000 = 15;
         break;
      case 0:
      case 1:
      default:
         var10000 = 40;
         break;
      case 2:
         var10000 = 20;
         break;
      case 3:
         var10000 = 10;
      }

      return var10000;
   }

   public void spitParticle(MonsterSpitProjectile projectile) {
      this.particleSpawning(projectile, new DustParticleOptions(new Vector3f(new Vec3(0.0D, 255.0D, 0.0D)), 1.0F), 30);
   }

   public void spitHit(LivingEntity pTarget) {
      if (!(pTarget instanceof GiantAntEntity)) {
         pTarget.m_6469_(DamageSource.m_19370_(this), 12.0F);
         this.setRanged(Boolean.FALSE);
      }
   }

   public void impactEffect(MonsterSpitProjectile spit, double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.ACID_EFFECT.get(), x, y, z, 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.ACID_BUBBLE.get(), x, y, z, 25, 0.08D, 0.08D, 0.08D, 0.15D, false);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, spit.m_20191_().m_82400_(3.0D), (entityData) -> {
         return !entityData.m_7307_(this) && !entityData.m_7306_(this) && entityData != this.m_21826_();
      });
      if (!list.isEmpty()) {
         Iterator var9 = list.iterator();

         while(var9.hasNext()) {
            LivingEntity pTarget = (LivingEntity)var9.next();
            pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 300, 0, false, false, true), this);
            if (this.f_19796_.m_188503_(10) >= 8) {
               pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 300, 0, false, false, true), this);
            } else {
               pTarget.m_147207_(new MobEffectInstance(MobEffects.f_19597_, 300, 1, false, false, true), this);
            }
         }

      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      String name;
      if (this.m_21825_()) {
         name = "animation.giant_ant.stay";
      } else if (event.isMoving()) {
         if (this.m_21660_()) {
            name = "animation.giant_ant.search";
         } else {
            name = "animation.giant_ant.walk";
         }
      } else {
         name = "animation.giant_ant.idle";
      }

      event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.LOOP));
      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         String name = null;
         if (this.getMiscAnimation() == 1) {
            name = "animation.giant_ant.clean";
         } else if (this.getMiscAnimation() == 2) {
            name = "animation.giant_ant.eat";
         } else if (this.getMiscAnimation() == 3) {
            name = "animation.giant_ant.bite";
         } else if (this.getMiscAnimation() == 4 || this.getMiscAnimation() == -1) {
            name = "animation.giant_ant.spit";
         }

         if (name != null) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(GiantAntEntity.class, EntityDataSerializers.f_135028_);
      RANGED_ATTACK = SynchedEntityData.m_135353_(GiantAntEntity.class, EntityDataSerializers.f_135035_);
      SADDLED = SynchedEntityData.m_135353_(GiantAntEntity.class, EntityDataSerializers.f_135035_);
   }

   static class AntEatOrCleanGoal extends EatBlockGoal {
      protected final GiantAntEntity ant;

      public AntEatOrCleanGoal(GiantAntEntity giantAnt) {
         super(giantAnt);
         this.ant = giantAnt;
      }

      public boolean m_8036_() {
         if (this.ant.m_217043_().m_188503_(400) != 0) {
            return false;
         } else {
            BlockPos blockpos = this.ant.m_20183_();
            boolean shouldEat = false;
            if (BlockStatePredicate.m_61287_(Blocks.f_50034_).test(this.ant.f_19853_.m_8055_(blockpos))) {
               shouldEat = true;
            } else if (this.ant.f_19853_.m_8055_(blockpos.m_7495_()).m_60713_(Blocks.f_50440_)) {
               shouldEat = true;
            }

            if (shouldEat && this.ant.m_217043_().m_188503_(7) > 5) {
               return true;
            } else {
               this.ant.setMiscAnimation(1);
               return false;
            }
         }
      }
   }

   static class GiantAntAttackGoal extends MeleeAttackGoal {
      private final int attackInterval;
      private final float attackRadius;
      private int attackTime = -1;
      private final GiantAntEntity giantAnt;

      public GiantAntAttackGoal(GiantAntEntity ant, double pSpeedModifier, int pAttackInterval, float pAttackRadius, boolean pFollowingTargetEvenIfNotSeen) {
         super(ant, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.attackInterval = pAttackInterval;
         this.attackRadius = pAttackRadius;
         this.giantAnt = ant;
      }

      public boolean m_8036_() {
         if (this.giantAnt.m_21827_()) {
            return false;
         } else if (!this.giantAnt.getRanged()) {
            return super.m_8036_();
         } else {
            LivingEntity livingentity = this.f_25540_.m_5448_();
            return livingentity != null && livingentity.m_6084_();
         }
      }

      public boolean m_8045_() {
         if (this.giantAnt.m_21827_()) {
            return false;
         } else if (this.giantAnt.getRanged()) {
            LivingEntity target = this.f_25540_.m_5448_();
            return target != null && target.m_6084_() && !this.f_25540_.m_21573_().m_26571_() ? true : this.m_8036_();
         } else {
            return super.m_8045_();
         }
      }

      public void m_8041_() {
         this.attackTime = -1;
         super.m_8041_();
      }

      public void m_8037_() {
         if (this.giantAnt.getRanged()) {
            LivingEntity target = this.giantAnt.m_5448_();
            if (target != null) {
               this.f_25540_.m_21563_().m_24960_(target, 30.0F, 30.0F);
               double d0 = this.f_25540_.m_20275_(target.m_20185_(), target.m_20186_(), target.m_20189_());
               boolean flag = this.f_25540_.m_21574_().m_148306_(target);
               if (--this.attackTime == 0) {
                  if (!flag) {
                     return;
                  }

                  this.giantAnt.f_21344_.m_26573_();
                  this.giantAnt.setMiscAnimation(4);
                  this.attackTime = Mth.m_14143_((float)this.attackInterval);
               } else if (this.attackTime < 0) {
                  this.attackTime = Mth.m_14107_(Mth.m_14139_(Math.sqrt(d0) / (double)this.attackRadius, (double)this.attackInterval, (double)this.attackInterval));
               }
            }
         }

         super.m_8037_();
      }
   }
}
