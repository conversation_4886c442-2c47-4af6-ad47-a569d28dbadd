package com.github.b4ndithelps.tennogamenolife.client.renderer.entity;

import com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.resources.ResourceLocation;

/**
 * 机铠种之械子弹的空渲染器
 * 不渲染任何模型，只依赖粒子效果
 */
public class ExMachinaBulletRenderer extends EntityRenderer<ExMachinaBulletEntity> {
    
    private static final ResourceLocation TEXTURE = new ResourceLocation("minecraft", "textures/misc/white.png");

    public ExMachinaBulletRenderer(EntityRendererProvider.Context context) {
        super(context);
    }

    @Override
    public void render(ExMachinaBulletEntity bullet, float entityYaw, float partialTicks, 
                      PoseStack poseStack, MultiBufferSource buffer, int packedLight) {
        // 不渲染任何东西，只依赖粒子效果
        super.render(bullet, entityYaw, partialTicks, poseStack, buffer, packedLight);
    }

    @Override
    public ResourceLocation getTextureLocation(ExMachinaBulletEntity bullet) {
        return TEXTURE;
    }
}
