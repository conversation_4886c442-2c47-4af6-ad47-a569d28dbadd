package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.entity.magic.barrier.FlareCircleEntity;
import com.github.manasmods.tensura.entity.magic.field.Hellfire;
import com.github.manasmods.tensura.entity.magic.projectile.FireBoltProjectile;
import com.github.manasmods.tensura.entity.magic.skill.FlameOrbProjectile;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.SpawnPlacements;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.SpawnPlacements.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.NaturalSpawner;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.network.PacketDistributor;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class IfritEntity extends GreaterSpiritEntity {
   protected static final EntityDataAccessor<BlockPos> FLARE_POS;

   public IfritEntity(EntityType<? extends IfritEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.RED, BossBarOverlay.NOTCHED_20)).m_7005_(true);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 40.0D).m_22268_(Attributes.f_22276_, 400.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : IfritEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new IfritEntity.IfritAttackGoal(this));
      this.f_21345_.m_25352_(3, new GreaterSpiritEntity.FollowHinataGoal(this, 1.0D, HinataSakaguchiEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new GreaterSpiritEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 12));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{IfritEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(FLARE_POS, BlockPos.f_121853_);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128347_("FlareX", (double)this.getFlarePos().m_123341_());
      compound.m_128347_("FlareY", (double)this.getFlarePos().m_123342_());
      compound.m_128347_("FlareZ", (double)this.getFlarePos().m_123343_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setFlarePos(compound.m_128459_("FlareX"), compound.m_128459_("FlareY"), compound.m_128459_("FlareZ"));
   }

   public BlockPos getFlarePos() {
      return (BlockPos)this.f_19804_.m_135370_(FLARE_POS);
   }

   public void setFlarePos(double x, double y, double z) {
      this.f_19804_.m_135381_(FLARE_POS, new BlockPos(x, y, z));
   }

   public boolean m_6060_() {
      return TensuraEffectsCapability.hasSyncedEffect(this, (MobEffect)TensuraMobEffects.BLACK_BURN.get());
   }

   public static boolean shouldEvaporateProjectile(@Nullable Entity entity) {
      return entity == null ? false : entity.m_6095_().m_204039_(TensuraTags.EntityTypes.CAN_EVAPORATE);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (!(pSource.m_7639_() instanceof SalamanderEntity) && !(pSource.m_7639_() instanceof IfritCloneEntity)) {
         if (DamageSourceHelper.isHeat(pSource)) {
            pAmount *= 0.05F;
         } else if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         } else {
            pAmount *= this.getPhysicalAttackInput(pSource);
         }

         if (shouldEvaporateProjectile(pSource.m_7640_())) {
            this.m_5496_(SoundEvents.f_12031_, 10.0F, 0.8F);
            pSource.m_7640_().m_142687_(RemovalReason.KILLED);
            return false;
         } else {
            boolean hurt = super.m_6469_(pSource, pAmount);
            if (hurt) {
               Entity var5 = pSource.m_7639_();
               if (var5 instanceof LivingEntity) {
                  LivingEntity damageSource = (LivingEntity)var5;
                  if (!damageSource.m_6084_()) {
                     return true;
                  }

                  if (damageSource instanceof Player) {
                     Player player = (Player)damageSource;
                     if (player.m_7500_() || player.m_5833_()) {
                        return true;
                     }
                  }

                  List<SalamanderEntity> list = this.f_19853_.m_6443_(SalamanderEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
                     return !entity.m_21824_();
                  });
                  if (!list.isEmpty()) {
                     list.forEach((salamander) -> {
                        salamander.m_6710_(damageSource);
                     });
                  }
               }
            }

            return hurt;
         }
      } else {
         return false;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof SalamanderEntity) {
         SalamanderEntity salamander = (SalamanderEntity)entity;
         return salamander.m_21824_() == this.m_21824_();
      } else if (entity instanceof IfritEntity) {
         IfritEntity ifrit = (IfritEntity)entity;
         return ifrit.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   protected void m_8024_() {
      super.m_8024_();
      if (this.m_20072_() && this.f_19797_ % 10 == 0 && this.removeFluid(this, FluidTags.f_13131_, 3.0F, true, 0)) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12031_, SoundSource.NEUTRAL, 10.0F, 1.0F);
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (this.f_19853_.m_5776_() && this.m_6084_()) {
         if (this.f_19797_ % 10 == 0) {
            TensuraParticleHelper.addParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
         }

         Vec3 vec3 = this.m_20252_(1.0F).m_82541_();
         float radius = -0.15F;
         double yPos = this.m_20188_() + 0.20000000298023224D;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
         double extraZ = (double)Mth.m_14089_(angle);
         if (this.m_5803_()) {
            yPos -= 0.20000000298023224D;
            radius = -0.5F;
         } else if (this.getMiscAnimation() == -1) {
            --yPos;
            radius = 0.6F;
         } else if (this.getMiscAnimation() == 7) {
            radius = -0.3F;
            yPos -= 0.10000000149011612D;
         } else if (this.m_20184_().m_82556_() > 0.03D && this.getMiscAnimation() < 5 && this.getMiscAnimation() != 3) {
            if (!this.m_20096_()) {
               yPos -= 0.20000000298023224D;
               radius = 0.3F;
            } else if (this.m_21660_()) {
               radius = 0.0F;
            }
         }

         for(int i = 0; i < 15; ++i) {
            double ox = Math.random() * 0.3D - 0.15D;
            double oy = Math.random() * 0.3D - 0.15D;
            double oz = Math.random() * 0.3D - 0.15D;
            Vec3 randomVec = (new Vec3(Math.random() - 0.5D, Math.random() - 0.5D, Math.random() - 0.5D)).m_82541_();
            Vec3 result = vec3.m_82490_(-1.0D).m_82549_(randomVec).m_82541_().m_82490_(0.1D);
            this.m_9236_().m_7106_((ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), this.m_20185_() - vec3.f_82479_ * 0.1D + ox + extraX * (double)radius, yPos + oy, this.m_20189_() - vec3.f_82481_ * 0.1D + oz + extraZ * (double)radius, result.f_82479_, result.f_82480_, result.f_82481_);
            this.m_9236_().m_7106_(ParticleTypes.f_123744_, this.m_20185_() - vec3.f_82479_ * 0.1D + ox + extraX * (double)radius, yPos + oy, this.m_20189_() - vec3.f_82481_ * 0.1D + oz + extraZ * (double)radius, result.f_82479_, result.f_82480_, result.f_82481_);
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if ((this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2) && this.miscAnimationTicks == 10) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               shootFireBolt(this);
               this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() != 3) {
               if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 25) {
                  hellFire(this);
                  this.m_5496_(SoundEvents.f_11862_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               } else if (this.getMiscAnimation() == 5) {
                  this.m_21573_().m_26573_();
                  if (this.miscAnimationTicks == 1) {
                     LivingEntity target = this.m_5448_();
                     Vec3 pos;
                     if (target != null) {
                        pos = target.m_20182_();
                     } else {
                        BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, 20.0D);
                        pos = result.m_82450_();
                     }

                     this.setFlarePos(pos.m_7096_(), pos.m_7098_(), pos.m_7094_());
                     this.setMagicID(0);
                  } else {
                     this.flareCircle(this.miscAnimationTicks);
                  }
               } else if (this.getMiscAnimation() == 6) {
                  this.m_21573_().m_26573_();
                  if (this.miscAnimationTicks == 15) {
                     combust(this, this::shouldAttack);
                     this.m_5496_(SoundEvents.f_11913_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 7) {
                  this.m_21573_().m_26573_();
                  switch(this.miscAnimationTicks) {
                  case 10:
                     this.summonSalamanders(3, 7);
                     break;
                  case 20:
                     this.summonSalamanders(5, 10);
                     break;
                  case 30:
                     this.summonSalamanders(7, 15);
                  }

                  this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               } else if (this.getMiscAnimation() == 8) {
                  this.m_21573_().m_26573_();
                  switch(this.miscAnimationTicks) {
                  case 10:
                     this.summonClones(3, 7);
                     break;
                  case 20:
                     this.summonClones(5, 10);
                     break;
                  case 30:
                     this.summonClones(7, 15);
                  }

                  this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 25) {
                  this.flameOrb();
               } else {
                  this.setMagicID(0);
               }

               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 1:
      case 2:
      default:
         var10000 = 15;
         break;
      case 3:
      case 7:
      case 8:
         var10000 = 40;
         break;
      case 4:
      case 6:
         var10000 = 30;
         break;
      case 5:
         var10000 = 65;
      }

      return var10000;
   }

   public static void shootFireBolt(LivingEntity ifrit) {
      FireBoltProjectile bolt = new FireBoltProjectile(ifrit.f_19853_, ifrit);
      bolt.setSkill(SkillUtils.getSkillOrNull(ifrit, (ManasSkill)SpiritualMagics.FIRE_BOLT.get()));
      float angle = 0.017453292F * ifrit.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      bolt.m_7678_(ifrit.m_20185_() + xOffset, ifrit.m_20188_() - 0.2D, ifrit.m_20189_() + zOffset, ifrit.m_146908_(), ifrit.m_146909_());
      bolt.setSize(1.5F);
      bolt.setDamage((float)ifrit.m_21133_(Attributes.f_22281_));
      bolt.setExplosionRadius(1.0F);
      bolt.setBurnTicks(100);
      bolt.setSpiritAttack(true);
      bolt.setSpeed(1.5F);
      bolt.m_20242_(true);
      bolt.shootFromRot(ifrit.m_20154_());
      ifrit.f_19853_.m_7967_(bolt);
   }

   private void flameOrb() {
      int orbID = this.getMagicID();
      if (orbID == 0) {
         FlameOrbProjectile orb = new FlameOrbProjectile(this.f_19853_, this);
         orb.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
         orb.setBurnTicks(100);
         orb.setSpiritAttack(true);
         orb.setMpCost(500.0D);
         orb.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()).orElse((Object)null));
         orb.setExplosionRadius(4.0F);
         orb.m_146884_(this.m_146892_().m_82520_(0.0D, 4.0D, 0.0D));
         orb.setOwnerOffset(new Vec3(0.0D, 4.0D, 0.0D));
         orb.setLookDistance(30.0F);
         orb.setDelayTick(15);
         orb.m_20242_(true);
         this.m_9236_().m_7967_(orb);
         this.setMagicID(orb.m_19879_());
      } else {
         Entity entity = this.m_9236_().m_6815_(orbID);
         if (entity instanceof FlameOrbProjectile) {
            FlameOrbProjectile orb = (FlameOrbProjectile)entity;
            if (orb.getDelayTick() > 0) {
               orb.setSize(orb.getSize() + 0.1F);
            }
         } else {
            this.setMagicID(0);
            this.flameOrb();
         }
      }

      this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
   }

   public static void hellFire(Mob ifrit) {
      Entity target = ifrit.m_5448_();
      Vec3 pos;
      if (target != null) {
         pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
      } else {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(ifrit.m_9236_(), ifrit, Fluid.NONE, 15.0D);
         pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
      }

      Hellfire sphere = new Hellfire(ifrit.m_9236_(), ifrit);
      sphere.setDamage((float)(ifrit.m_21133_(Attributes.f_22281_) * 8.0D));
      sphere.setMpCost(10000.0D);
      sphere.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(ifrit).getSkill((ManasSkill)SpiritualMagics.HELLFIRE.get()).orElse((Object)null));
      sphere.setLife(60);
      sphere.setRadius(2.5F);
      sphere.m_6034_(pos.f_82479_, pos.f_82480_ - (double)sphere.getRadius(), pos.f_82481_);
      ifrit.m_9236_().m_7967_(sphere);
      ifrit.m_5496_(SoundEvents.f_11913_, 10.0F, 0.95F + ifrit.m_217043_().m_188501_() * 0.1F);
      ((ServerLevel)ifrit.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.RED_FIRE.get(), ifrit.m_20185_(), ifrit.m_20186_() + (double)ifrit.m_20206_() / 2.0D, ifrit.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
      TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
         return sphere;
      }), new RequestFxSpawningPacket(new ResourceLocation("tensura:fire_sphere_5x5"), sphere.m_19879_(), 0.0D, (double)sphere.getRadius(), 0.0D, false));
   }

   private void flareCircle(int heldTicks) {
      BlockPos pos = this.getFlarePos();
      if (heldTicks >= 20) {
         int flareID = this.getMagicID();
         if (flareID == 0) {
            FlareCircleEntity barrier = new FlareCircleEntity((EntityType)TensuraEntityTypes.FLARE_CIRCLE.get(), this.f_19853_);
            barrier.m_5602_(this);
            barrier.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 2.0D));
            barrier.setRadius(5.0F);
            barrier.setHeight(7.0F);
            barrier.setLife(30);
            barrier.setHealth(200.0F);
            barrier.m_146884_(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()));
            barrier.setMpCost(2000.0D);
            barrier.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.FLARE_CIRCLE.get()).orElse((Object)null));
            this.m_9236_().m_7967_(barrier);
            this.setMagicID(barrier.m_19879_());
         } else {
            Entity entity = this.m_9236_().m_6815_(flareID);
            if (entity instanceof BarrierEntity) {
               BarrierEntity barrier = (BarrierEntity)entity;
               barrier.increaseLife(2);
            } else {
               this.setMagicID(0);
            }
         }

         this.m_5496_(SoundEvents.f_11705_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
      } else {
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return this;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:flare_circle_circle"), pos, 0.0D, 0.0D, 0.0D, 0, true));
      }

   }

   public static void combust(LivingEntity ifrit, Predicate<LivingEntity> predicate) {
      TensuraParticleHelper.spawnServerParticles(ifrit.f_19853_, (ParticleOptions)TensuraParticles.RED_FIRE.get(), ifrit.m_20185_(), ifrit.m_20188_(), ifrit.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.spawnServerParticles(ifrit.f_19853_, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), ifrit.m_20185_(), ifrit.m_20188_(), ifrit.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.addServerParticlesAroundSelf(ifrit, ParticleTypes.f_123756_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(ifrit, ParticleTypes.f_123756_, 3.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(ifrit, ParticleTypes.f_123756_, 4.0D);
      AABB aabb = ifrit.m_20191_().m_82400_(ifrit.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 10.0D);
      List<LivingEntity> list = ifrit.f_19853_.m_6443_(LivingEntity.class, aabb, predicate);
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_6469_(TensuraDamageSources.heatWave(ifrit), (float)ifrit.m_21133_(Attributes.f_22281_) * 3.0F);
            target.m_7311_(Math.max(200, target.m_20094_()));
            target.m_20334_(0.0D, 0.1D, 0.0D);
            SkillHelper.knockBack(ifrit, target, 2.0F);
         }

      }
   }

   private void summonSalamanders(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         SalamanderEntity salamander = new SalamanderEntity((EntityType)TensuraEntityTypes.SALAMANDER.get(), level);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            BlockPos blockpos = new BlockPos(i1, j1, k1);
            EntityType<?> entitytype = (EntityType)TensuraEntityTypes.SALAMANDER.get();
            Type type = SpawnPlacements.m_21752_(entitytype);
            if (NaturalSpawner.m_47051_(type, level, blockpos, entitytype)) {
               salamander.m_6034_((double)i1, (double)j1, (double)k1);
               if (level.m_45784_(salamander) && level.m_45786_(salamander) && !level.m_46855_(salamander.m_20191_())) {
                  salamander.m_6518_(level, level.m_6436_(salamander.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                  salamander.m_6710_(this.m_5448_());
                  level.m_47205_(salamander);
                  salamander.setSummonerUUID(this.getSummonerUUID());
                  TensuraParticleHelper.addServerParticlesAroundSelf(salamander, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(salamander, ParticleTypes.f_123756_, 2.0D);
                  break;
               }
            }
         }

      }
   }

   private void summonClones(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         double EP = TensuraEPCapability.getEP(this);
         IfritCloneEntity clone = new IfritCloneEntity((EntityType)TensuraEntityTypes.IFRIT_CLONE.get(), level);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            BlockPos blockpos = new BlockPos(i1, j1, k1);
            EntityType<?> entitytype = (EntityType)TensuraEntityTypes.IFRIT_CLONE.get();
            Type type = SpawnPlacements.m_21752_(entitytype);
            if (NaturalSpawner.m_47051_(type, level, blockpos, entitytype)) {
               clone.m_6034_((double)i1, (double)j1, (double)k1);
               if (level.m_45784_(clone) && level.m_45786_(clone) && !level.m_46855_(clone.m_20191_())) {
                  clone.m_6518_(level, level.m_6436_(clone.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                  clone.m_6710_(this.m_5448_());
                  clone.copySkills(this);
                  TensuraEPCapability.setLivingEP(clone, EP * 0.1D);
                  level.m_47205_(clone);
                  TensuraParticleHelper.addServerParticlesAroundSelf(clone, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 2.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(clone, ParticleTypes.f_123756_, 2.0D);
                  break;
               }
            }
         }

      }
   }

   public MagicElemental getElemental() {
      return MagicElemental.FLAME;
   }

   public Item getElementalCore() {
      return (Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get();
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 35) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         this.spawnDeathParticles();
      }

   }

   protected void spawnDeathParticles() {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.RED_FIRE.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 2.0D);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11701_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11704_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11703_;
   }

   public boolean shouldStand() {
      if (this.m_21525_()) {
         return true;
      } else if (this.getMiscAnimation() == 3) {
         return true;
      } else {
         return this.getMiscAnimation() >= 5 ? true : this.m_20096_();
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.sleep", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_() && this.getMiscAnimation() == -1) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle_train", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving() && this.getMiscAnimation() < 5 && this.getMiscAnimation() != 3 && !this.m_21525_()) {
         if (this.m_20096_()) {
            if (this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.run", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.walk", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fly", EDefaultLoopTypes.LOOP));
         }
      } else if (this.shouldStand()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.idle_fly", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_right", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_left", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_ball_massive", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.fire_wall", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.flare_circle", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.burst", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() != 7 && this.getMiscAnimation() != 8) {
            if (this.getMiscAnimation() == 9) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.death", EDefaultLoopTypes.PLAY_ONCE));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.ifrit.summon", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static {
      FLARE_POS = SynchedEntityData.m_135353_(IfritEntity.class, EntityDataSerializers.f_135038_);
   }

   static class IfritAttackGoal extends Goal {
      private final IfritEntity ifrit;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private int attackCooldown;

      public IfritAttackGoal(IfritEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.ifrit = entity;
      }

      public boolean m_8036_() {
         if (this.ifrit.m_21827_()) {
            return false;
         } else if (this.ifrit.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.ifrit.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      public void m_8037_() {
         LivingEntity target = this.ifrit.m_5448_();
         if (target != null && target.m_6084_()) {
            this.ifrit.setFlying(true);
            if (this.orbitTime < this.maxOrbitTime) {
               if (this.ifrit.getMiscAnimation() == 0) {
                  ++this.orbitTime;
                  --this.attackCooldown;
               }

               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(15.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.ifrit.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
               if (this.isTimeToAttack()) {
                  this.resetAttackCooldown();
                  this.ifrit.setMiscAnimation(this.randomAttack((double)this.ifrit.m_20270_(target)));
                  this.ifrit.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else {
               this.orbitTime = 0;
               if (this.canSummonSalamanders()) {
                  this.ifrit.setMiscAnimation(7);
               } else if (this.canSummonClone() && this.ifrit.m_217043_().m_188503_(4) == 1) {
                  this.ifrit.setMiscAnimation(8);
               }
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 80 + this.ifrit.m_217043_().m_188503_(80);
         this.ifrit.m_21561_(true);
         this.attackCooldown = 0;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private boolean canSummonSalamanders() {
         if (this.ifrit.m_21824_()) {
            return false;
         } else {
            List<SalamanderEntity> list = this.ifrit.f_19853_.m_45971_(SalamanderEntity.class, TargetingConditions.m_148353_().m_26883_(20.0D).m_148355_().m_26893_(), this.ifrit, this.ifrit.m_20191_().m_82400_(20.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  SalamanderEntity entity = (SalamanderEntity)var2.next();
                  entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false));
               }
            }

            return this.ifrit.f_19796_.m_188503_(3) + 1 > list.size();
         }
      }

      private boolean canSummonClone() {
         if (!this.ifrit.m_21824_() && this.ifrit.getSummonerUUID() == null) {
            List<IfritCloneEntity> list = this.ifrit.f_19853_.m_45971_(IfritCloneEntity.class, TargetingConditions.m_148353_().m_26883_(20.0D).m_148355_().m_26893_(), this.ifrit, this.ifrit.m_20191_().m_82400_(20.0D));
            return this.ifrit.f_19796_.m_188503_(3) + 1 > list.size();
         } else {
            return false;
         }
      }

      private void resetAttackCooldown() {
         this.attackCooldown = this.m_183277_(30);
      }

      private boolean isTimeToAttack() {
         return this.attackCooldown <= 0;
      }

      private int randomAttack(double distance) {
         if (distance < 10.0D && (double)this.ifrit.f_19796_.m_188501_() <= 0.2D) {
            return 6;
         } else {
            if (distance < 15.0D) {
               if ((double)this.ifrit.f_19796_.m_188501_() <= 0.05D) {
                  return 4;
               }

               if ((double)this.ifrit.f_19796_.m_188501_() <= 0.1D) {
                  return 5;
               }
            }

            if ((double)this.ifrit.f_19796_.m_188501_() <= 0.4D) {
               return 3;
            } else {
               return this.ifrit.m_217043_().m_188499_() ? 1 : 2;
            }
         }
      }
   }
}
