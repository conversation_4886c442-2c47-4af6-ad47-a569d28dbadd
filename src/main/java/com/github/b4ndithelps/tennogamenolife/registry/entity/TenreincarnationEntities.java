package com.github.b4ndithelps.tennogamenolife.registry.entity;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;


public class TenreincarnationEntities {

    public static final DeferredRegister<EntityType<?>> ENTITY_TYPES = 
        DeferredRegister.create(ForgeRegistries.ENTITY_TYPES, Tenreincarnation.MODID);
    public static final RegistryObject<EntityType<HurtDummyEntity>> HURT_DUMMY =
        ENTITY_TYPES.register("hurt_dummy",
            () -> EntityType.Builder.<HurtDummyEntity>of(HurtDummyEntity::new, MobCategory.MISC)
                .sized(0.7F, 1.9F)
                .clientTrackingRange(8)
                .build(new ResourceLocation(Tenreincarnation.MODID, "hurt_dummy").toString())
        );

    // 注册休比异世界人实体
    public static final RegistryObject<EntityType<XiubiEntity>> XIUBI =
        ENTITY_TYPES.register("xiubi",
            () -> EntityType.Builder.<XiubiEntity>of(XiubiEntity::new, MobCategory.CREATURE)
                .sized(0.6F, 1.95F)
                .clientTrackingRange(10)
                .build(new ResourceLocation(Tenreincarnation.MODID, "xiubi").toString())
        );

    // 注册机铠种之械子弹实体
    public static final RegistryObject<EntityType<ExMachinaBulletEntity>> EX_MACHINA_BULLET =
        ENTITY_TYPES.register("ex_machina_bullet",
            () -> EntityType.Builder.<ExMachinaBulletEntity>of(ExMachinaBulletEntity::new, MobCategory.MISC)
                .sized(0.25F, 0.25F)
                .clientTrackingRange(64)
                .updateInterval(1)
                .build(new ResourceLocation(Tenreincarnation.MODID, "ex_machina_bullet").toString())
        );
    public static void register(IEventBus eventBus) {
        ENTITY_TYPES.register(eventBus);
    }
} 