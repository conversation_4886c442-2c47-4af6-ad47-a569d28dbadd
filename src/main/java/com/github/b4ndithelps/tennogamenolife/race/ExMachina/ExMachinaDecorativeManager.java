package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.nbt.CompoundTag;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * ExMachina种族装饰管理器
 * 统一管理所有ExMachina种族的装饰建模功能
 * 
 * 功能包括：
 * - 机铠装饰显示
 * - 武器动画状态管理
 * - 装饰数据持久化
 */
public class ExMachinaDecorativeManager {
    
    // 玩家装饰数据缓存
    private static final Map<UUID, ExMachinaDecorativeData> playerDecorativeData = new HashMap<>();
    
    /**
     * 机铠动画状态枚举
     */
    public enum ArmorAnimationState {
        IDLE,           // 待机动画
        WEAPON,         // 拿武器动画
        CANCEL_WEAPON   // 取消武器动画
    }
    
    /**
     * 获取玩家的装饰数据
     */
    public static ExMachinaDecorativeData getDecorativeData(Player player) {
        UUID playerId = player.getUUID();
        return playerDecorativeData.computeIfAbsent(playerId, k -> new ExMachinaDecorativeData());
    }
    
    /**
     * 获取机铠动画状态
     */
    public static ArmorAnimationState getArmorAnimationState(Player player) {
        ExMachinaDecorativeData data = getDecorativeData(player);
        if (data.isCancelAnimationPlaying()) {
            return ArmorAnimationState.CANCEL_WEAPON;
        } else if (data.hasWeapon()) {
            return ArmorAnimationState.WEAPON;
        } else {
            return ArmorAnimationState.IDLE;
        }
    }
    
    /**
     * 获取动画计时器
     */
    public static int getAnimationTimer(Player player) {
        ExMachinaDecorativeData data = getDecorativeData(player);
        if (data.isCancelAnimationPlaying()) {
            return data.getCancelAnimationTimer();
        } else if (data.hasWeapon()) {
            return data.getWeaponAnimationTimer();
        } else {
            return player.tickCount; // 待机动画使用玩家tick计数
        }
    }
    
    /**
     * 更新装饰建模状态
     * 应该在玩家tick时调用
     */
    public static void updateDecorativeState(Player player) {
        ExMachinaDecorativeData data = getDecorativeData(player);
        
        // 更新武器状态
        boolean previousHasWeapon = data.hasWeapon();
        boolean currentHasWeapon = isHoldingExMachinasWeapon(player);
        data.setHasWeapon(currentHasWeapon);

        // 处理动画状态转换
        if (currentHasWeapon && !previousHasWeapon) {
            // 开始拿武器 - 启动武器动画
            data.setWeaponAnimationTimer(0);
            data.setCancelAnimationPlaying(false);
        } else if (!currentHasWeapon && previousHasWeapon) {
            // 放下武器 - 启动取消动画
            data.setCancelAnimationTimer(0);
            data.setCancelAnimationPlaying(true);
        }

        // 更新动画计时器
        if (data.hasWeapon()) {
            data.incrementWeaponAnimationTimer();
        }

        if (data.isCancelAnimationPlaying()) {
            data.incrementCancelAnimationTimer();
            // 取消动画播放完毕 (0.5833秒 = 约12 ticks)
            if (data.getCancelAnimationTimer() >= 12) {
                data.setCancelAnimationPlaying(false);
                data.setCancelAnimationTimer(0);
            }
        }
    }
    
    /**
     * 检查是否持有机铠种之械
     */
    public static boolean isHoldingExMachinasWeapon(Player player) {
        ItemStack mainHand = player.getMainHandItem();
        ItemStack offHand = player.getOffhandItem();
        return mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get()) ||
               offHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get());
    }
    
    /**
     * 检查玩家是否应该显示装饰
     * 只有ExMachina种族才显示装饰
     */
    public static boolean shouldShowDecorative(Player player) {
        // 这里可以添加种族检查逻辑
        // 暂时返回true，让所有玩家都能看到装饰效果
        return true;
    }
    
    /**
     * 保存装饰数据到NBT
     */
    public static void saveDecorativeData(Player player, CompoundTag nbt) {
        ExMachinaDecorativeData data = getDecorativeData(player);
        CompoundTag decorativeTag = new CompoundTag();
        
        decorativeTag.putBoolean("HasWeapon", data.hasWeapon());
        decorativeTag.putBoolean("IsCancelAnimationPlaying", data.isCancelAnimationPlaying());
        decorativeTag.putInt("WeaponAnimationTimer", data.getWeaponAnimationTimer());
        decorativeTag.putInt("CancelAnimationTimer", data.getCancelAnimationTimer());
        
        nbt.put("ExMachinaDecorative", decorativeTag);
    }
    
    /**
     * 从NBT读取装饰数据
     */
    public static void loadDecorativeData(Player player, CompoundTag nbt) {
        if (nbt.contains("ExMachinaDecorative")) {
            CompoundTag decorativeTag = nbt.getCompound("ExMachinaDecorative");
            ExMachinaDecorativeData data = getDecorativeData(player);
            
            data.setHasWeapon(decorativeTag.getBoolean("HasWeapon"));
            data.setCancelAnimationPlaying(decorativeTag.getBoolean("IsCancelAnimationPlaying"));
            data.setWeaponAnimationTimer(decorativeTag.getInt("WeaponAnimationTimer"));
            data.setCancelAnimationTimer(decorativeTag.getInt("CancelAnimationTimer"));
        }
    }
    
    /**
     * 清理玩家数据（玩家离开时调用）
     */
    public static void cleanupPlayerData(UUID playerId) {
        playerDecorativeData.remove(playerId);
    }
    
    /**
     * 获取所有装饰种族列表（用于管理）
     */
    public static String[] getDecorativeRaceList() {
        return new String[]{
            "MachineRace - 基础机铠种",
            "BefehlerRace - 指挥机铠种", 
            "KampfRace - 战斗机铠种",
            "ZeichenRace - 标记机铠种",
            "PrueferRace - 检验机铠种",
            "SeherRace - 观察机铠种",
            "EinzeigRace - 单体机铠种",
            "EmirEinsRace - 统领机铠种",
            "HorouRace - 破坏机铠种",
            "HubieDoraRace - 休比朵拉机铠种",
            "PuraiyaRace - 普莱雅机铠种"
        };
    }
    
    /**
     * 获取装饰功能描述
     */
    public static String getDecorativeDescription() {
        return """
            ExMachina种族装饰系统功能：
            
            1. 机铠装饰显示
               - 所有ExMachina种族玩家都会显示机铠装饰
               - 包括翅膀、光环、机械部件等
            
            2. 武器动画系统
               - 拿出机铠种之械时播放武器动画
               - 收起武器时播放取消动画
               - 待机时播放待机动画
            
            3. 数据持久化
               - 装饰状态会保存到玩家数据
               - 重新登录后状态保持
            
            4. 性能优化
               - 使用缓存系统减少计算
               - 自动清理离线玩家数据
            """;
    }
}
