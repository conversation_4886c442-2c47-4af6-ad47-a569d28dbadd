package com.github.manasmods.tensura.ability.magic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.text.DecimalFormat;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.Util;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.ForgeMod;

public class Magic extends TensuraSkill {
   public static final String CASTING = "d1d356ef-eceb-41db-b85b-3174f8f149eb";
   private final Magic.MagicType type;
   protected final DecimalFormat roundDouble = new DecimalFormat("#.#");

   public Magic(Magic.MagicType type) {
      this.type = type;
      this.addHeldAttributeModifier(Attributes.f_22279_, "d1d356ef-eceb-41db-b85b-3174f8f149eb", -0.75D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier((Attribute)ForgeMod.SWIM_SPEED.get(), "d1d356ef-eceb-41db-b85b-3174f8f149eb", -0.75D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier((Attribute)ForgeMod.ATTACK_RANGE.get(), "d1d356ef-eceb-41db-b85b-3174f8f149eb", -7.0D, Operation.ADDITION);
      this.addHeldAttributeModifier((Attribute)ForgeMod.REACH_DISTANCE.get(), "d1d356ef-eceb-41db-b85b-3174f8f149eb", -7.0D, Operation.ADDITION);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return false;
   }

   public int defaultCast() {
      return 0;
   }

   public int masteryCast() {
      return this.defaultCast();
   }

   public int castingTime(ManasSkillInstance instance, LivingEntity entity) {
      if (this.isInstant(instance, entity)) {
         return 0;
      } else if (MagicUltils.hasChantAnnulment(entity) && instance.isMastered(entity)) {
         return 1;
      } else {
         int castTime = instance.isMastered(entity) ? this.masteryCast() : this.defaultCast();
         return Math.max((int)((float)castTime * MagicUltils.castingSpeedMultipiler(entity)), 1);
      }
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return instance.isMastered(entity) ? modifier.m_22218_() * 0.5D : modifier.m_22218_();
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.alreadyCasting(entity)) {
         super.addHeldAttributeModifiers(instance, entity);
      }

   }

   public void removeHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.alreadyCasting(entity)) {
         super.removeHeldAttributeModifiers(instance, entity);
      }

      instance.getOrCreateTag().m_128405_("HeldTicks", 0);
      instance.markDirty();
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      if (this.isHeld(entity)) {
         return false;
      } else {
         AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22279_);
         if (attributeInstance == null) {
            return false;
         } else {
            AttributeModifier modifier = attributeInstance.m_22111_(UUID.fromString("d1d356ef-eceb-41db-b85b-3174f8f149eb"));
            return modifier == null ? false : modifier.m_22214_().equals(Util.m_137492_("skill", this.getRegistryName()));
         }
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      this.removeHeldAttributeModifiers(instance, entity);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.onCoolDown()) {
         return false;
      } else {
         CompoundTag tag = instance.getOrCreateTag();
         tag.m_128405_("HeldTicks", heldTicks);
         instance.markDirty();
         if (heldTicks == 0 && this.alreadyCasting(entity)) {
            tag.m_128405_("HeldTicks", 0);
            return false;
         } else if (this.isInstant(instance, entity)) {
            tag.m_128405_("HeldTicks", 0);
            return false;
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               this.addCastingParticle(instance, player, heldTicks);
            }

            return true;
         }
      }
   }

   protected boolean alreadyCasting(LivingEntity entity) {
      AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22279_);
      if (attributeInstance == null) {
         return false;
      } else {
         AttributeModifier modifier = attributeInstance.m_22111_(UUID.fromString("d1d356ef-eceb-41db-b85b-3174f8f149eb"));
         if (modifier == null) {
            return false;
         } else {
            return !modifier.m_22214_().equals(Util.m_137492_("skill", this.getRegistryName()));
         }
      }
   }

   protected void addCastingParticle(ManasSkillInstance instance, Player player, int heldTicks) {
      int cast = this.castingTime(instance, player);
      double sec = heldTicks >= cast ? (double)cast / 20.0D : (double)heldTicks / 20.0D;
      player.m_5661_(Component.m_237110_("tensura.magic.cast_time.max", new Object[]{this.roundDouble.format(sec), (double)cast / 20.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
      if (MagicUltils.hasChantAnnulment(player)) {
         if (heldTicks % 10 == 0) {
            TensuraParticleHelper.addServerParticlesAroundSelf(player, ParticleTypes.f_123809_, 2.0D);
         }
      } else {
         TensuraParticleHelper.addServerParticlesAroundSelf(player, ParticleTypes.f_123809_, 2.0D);
      }

   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (!super.canInteractSkill(instance, entity)) {
         return false;
      } else {
         return entity.m_20146_() >= entity.m_6062_() && !entity.m_21023_((MobEffect)TensuraMobEffects.SILENCE.get()) ? true : MagicUltils.hasChantAnnulment(entity);
      }
   }

   public int getHeldTicks(ManasSkillInstance instance) {
      return instance.getOrCreateTag().m_128451_("HeldTicks");
   }

   @Nullable
   public ResourceLocation getSkillIcon() {
      ResourceLocation id = this.getRegistryName();
      if (id == null) {
         return new ResourceLocation("tensura", "textures/temp_textures/item/confused_rimuru.png");
      } else {
         String var10003 = this.getType().getNamespace();
         return new ResourceLocation("tensura", "textures/magic/" + var10003 + "/" + id.m_135815_().replace('/', '.') + ".png");
      }
   }

   public Magic.MagicType getType() {
      return this.type;
   }

   public static enum MagicType {
      SPIRITUAL("spiritual", ChatFormatting.DARK_PURPLE),
      SUMMONING("summoning", ChatFormatting.BLUE);

      private final String namespace;
      private final ChatFormatting chatFormatting;

      public String getNamespace() {
         return this.namespace;
      }

      public MutableComponent getName() {
         return Component.m_237115_("tensura.magic.type." + this.namespace).m_130940_(this.chatFormatting);
      }

      public ChatFormatting getChatFormatting() {
         return this.chatFormatting;
      }

      private MagicType(String namespace, ChatFormatting chatFormatting) {
         this.namespace = namespace;
         this.chatFormatting = chatFormatting;
      }

      // $FF: synthetic method
      private static Magic.MagicType[] $values() {
         return new Magic.MagicType[]{SPIRITUAL, SUMMONING};
      }
   }
}
