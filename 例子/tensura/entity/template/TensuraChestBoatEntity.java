package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.vehicle.ChestBoat;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;

public class TensuraChestBoatEntity extends ChestBoat {
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE;

   public TensuraChestBoatEntity(EntityType<? extends TensuraChestBoatEntity> entityType, Level level) {
      super(entityType, level);
      this.f_19850_ = true;
   }

   public TensuraChestBoatEntity(Level worldIn, double x, double y, double z) {
      this((EntityType)TensuraEntityTypes.CHEST_BOAT_ENTITY.get(), worldIn);
      this.m_6034_(x, y, z);
      this.f_19854_ = x;
      this.f_19855_ = y;
      this.f_19856_ = z;
   }

   protected void m_7380_(CompoundTag compound) {
      compound.m_128359_("Type", this.getTensuraChestBoatType().getName());
   }

   protected void m_7378_(CompoundTag compound) {
      if (compound.m_128425_("Type", 8)) {
         this.setBoatType(TensuraChestBoatEntity.Type.byName(compound.m_128461_("Type")));
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE, 0);
   }

   public Item m_38369_() {
      Item var10000;
      switch(this.getTensuraChestBoatType()) {
      case PALM:
         var10000 = (Item)TensuraMaterialItems.PALM_CHEST_BOAT.get();
         break;
      case SAKURA:
         var10000 = (Item)TensuraMaterialItems.SAKURA_CHEST_BOAT.get();
         break;
      default:
         throw new IncompatibleClassChangeError();
      }

      return var10000;
   }

   public void setBoatType(TensuraChestBoatEntity.Type boatType) {
      this.f_19804_.m_135381_(DATA_ID_TYPE, boatType.ordinal());
   }

   public TensuraChestBoatEntity.Type getTensuraChestBoatType() {
      return TensuraChestBoatEntity.Type.byId((Integer)this.f_19804_.m_135370_(DATA_ID_TYPE));
   }

   static {
      DATA_ID_TYPE = SynchedEntityData.m_135353_(TensuraChestBoatEntity.class, EntityDataSerializers.f_135028_);
   }

   public static enum Type {
      SAKURA("sakura"),
      PALM("palm");

      private final String name;

      private Type(String name) {
         this.name = name;
      }

      public String getName() {
         return this.name;
      }

      public static TensuraChestBoatEntity.Type byId(int idType) {
         TensuraChestBoatEntity.Type[] types = values();
         if (idType < 0 || idType >= types.length) {
            idType = 0;
         }

         return types[idType];
      }

      public static TensuraChestBoatEntity.Type byName(String nameIn) {
         TensuraChestBoatEntity.Type[] types = values();
         TensuraChestBoatEntity.Type[] var2 = types;
         int var3 = types.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            TensuraChestBoatEntity.Type type = var2[var4];
            if (type.getName().equals(nameIn)) {
               return type;
            }
         }

         return types[0];
      }

      // $FF: synthetic method
      private static TensuraChestBoatEntity.Type[] $values() {
         return new TensuraChestBoatEntity.Type[]{SAKURA, PALM};
      }
   }
}
