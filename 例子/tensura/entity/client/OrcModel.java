package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OrcEntity;
import com.github.manasmods.tensura.entity.variant.OrcVariant;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class OrcModel extends AnimatedGeoModel<OrcEntity> {
   public ResourceLocation getModelResource(OrcEntity object) {
      return new ResourceLocation("tensura", "geo/orc.geo.json");
   }

   public ResourceLocation getTextureResource(OrcEntity instance) {
      return (ResourceLocation)OrcVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public ResourceLocation getAnimationResource(OrcEntity bear) {
      return new ResourceLocation("tensura", "animations/orc.animation.json");
   }

   public void setCustomAnimations(OrcEntity bear, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bear, instanceId, customPredicate);
      boolean hasChestplate = !bear.m_6844_(EquipmentSlot.CHEST).m_41619_();
      IBone belly = this.getAnimationProcessor().getBone("Belly");
      if (hasChestplate != belly.isHidden()) {
         belly.setHidden(hasChestplate);
      }

      IBone armorBelly = this.getAnimationProcessor().getBone("ArmorBelly");
      if (hasChestplate == armorBelly.isHidden()) {
         armorBelly.setHidden(!hasChestplate);
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
         head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
      }

   }
}
