package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.projectile.WebBulletProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Matrix4f;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.LlamaSpitModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.LightLayer;
import net.minecraft.world.phys.Vec3;

public class WebBulletProjectileRenderer extends EntityRenderer<WebBulletProjectile> {
   private static final ResourceLocation LLAMA_SPIT_LOCATION = new ResourceLocation("textures/entity/llama/spit.png");
   private final LlamaSpitModel<WebBulletProjectile> model;

   public WebBulletProjectileRenderer(Context renderManager) {
      super(renderManager);
      this.model = new LlamaSpitModel(renderManager.m_174023_(ModelLayers.f_171196_));
   }

   public void render(WebBulletProjectile pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      if (!pEntity.isSlinger()) {
         pMatrixStack.m_85836_();
         pMatrixStack.m_85837_(0.0D, 0.15000000596046448D, 0.0D);
         pMatrixStack.m_85845_(Vector3f.f_122225_.m_122240_(Mth.m_14179_(pPartialTicks, pEntity.f_19859_, pEntity.m_146908_()) - 90.0F));
         pMatrixStack.m_85845_(Vector3f.f_122227_.m_122240_(Mth.m_14179_(pPartialTicks, pEntity.f_19860_, pEntity.m_146909_())));
         this.model.m_6973_(pEntity, pPartialTicks, 0.0F, -0.1F, 0.0F, 0.0F);
         VertexConsumer vertexconsumer = pBuffer.m_6299_(this.model.m_103119_(LLAMA_SPIT_LOCATION));
         this.model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, 1.0F);
         pMatrixStack.m_85849_();
      }

      if (pEntity.isSlinger() && pEntity.m_37282_() != null) {
         this.renderLeash(pEntity, pPartialTicks, pMatrixStack, pBuffer, pEntity.m_37282_());
      }

      super.m_7392_(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
   }

   public ResourceLocation getTextureLocation(WebBulletProjectile pEntity) {
      return LLAMA_SPIT_LOCATION;
   }

   private <E extends Entity> void renderLeash(WebBulletProjectile bullet, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, E pLeashHolder) {
      pMatrixStack.m_85836_();
      Vec3 vec3 = pLeashHolder.m_7398_(pPartialTicks);
      double d0 = (double)(Mth.m_14179_(pPartialTicks, bullet.f_19859_, bullet.m_146908_()) * 0.017453292F) + 1.5707963267948966D;
      Vec3 bulletOffset = bullet.m_7939_();
      double d1 = Math.cos(d0) * bulletOffset.f_82481_ + Math.sin(d0) * bulletOffset.f_82479_;
      double d2 = Math.sin(d0) * bulletOffset.f_82481_ - Math.cos(d0) * bulletOffset.f_82479_;
      double d3 = Mth.m_14139_((double)pPartialTicks, bullet.f_19854_, bullet.m_20185_()) + d1;
      double d4 = Mth.m_14139_((double)pPartialTicks, bullet.f_19855_, bullet.m_20186_()) + bulletOffset.f_82480_;
      double d5 = Mth.m_14139_((double)pPartialTicks, bullet.f_19856_, bullet.m_20189_()) + d2;
      pMatrixStack.m_85837_(d1, bulletOffset.f_82480_, d2);
      float x = (float)(vec3.f_82479_ - d3);
      float y = (float)(vec3.f_82480_ - d4);
      float z = (float)(vec3.f_82481_ - d5);
      BlockPos bulletPos = new BlockPos(bullet.m_20299_(pPartialTicks));
      int i = this.m_6086_(bullet, bulletPos);
      int k = bullet.f_19853_.m_45517_(LightLayer.SKY, bulletPos);
      BlockPos ownerPos = new BlockPos(pLeashHolder.m_20299_(pPartialTicks));
      int j = this.getBlockLightLevels(pLeashHolder, ownerPos);
      int l = bullet.f_19853_.m_45517_(LightLayer.SKY, ownerPos);
      float offset = Mth.m_14195_(x * x + z * z) * 0.025F / 2.0F;
      float zOff = z * offset;
      float xOff = x * offset;
      Matrix4f matrix4f = pMatrixStack.m_85850_().m_85861_();
      VertexConsumer vertexconsumer = pBuffer.m_6299_(RenderType.m_110475_());

      int segment;
      for(segment = 0; segment <= 60; ++segment) {
         addVertexPair(vertexconsumer, matrix4f, x, y, z, i, j, k, l, 0.025F, zOff, xOff, segment, false);
      }

      for(segment = 60; segment >= 0; --segment) {
         addVertexPair(vertexconsumer, matrix4f, x, y, z, i, j, k, l, 0.0F, zOff, xOff, segment, true);
      }

      pMatrixStack.m_85849_();
   }

   protected int getBlockLightLevels(Entity pEntity, BlockPos pPos) {
      return pEntity.m_6060_() ? 15 : pEntity.f_19853_.m_45517_(LightLayer.BLOCK, pPos);
   }

   private static void addVertexPair(VertexConsumer consumer, Matrix4f matrix4f, float x, float y, float z, int bulletLight, int holderLight, int bulletBright, int holderBright, float yOff, float xOff, float zOff, int segment, boolean darken) {
      float f = (float)segment / 60.0F;
      int i = (int)Mth.m_14179_(f, (float)bulletLight, (float)holderLight);
      int j = (int)Mth.m_14179_(f, (float)bulletBright, (float)holderBright);
      int k = LightTexture.m_109885_(i, j);
      float f1 = segment % 2 == (darken ? 1 : 0) ? 0.7F : 1.0F;
      float f5 = x * f;
      float f6 = y > 0.0F ? y * f * f : y - y * (1.0F - f) * (1.0F - f);
      float f7 = z * f;
      consumer.m_85982_(matrix4f, f5 - xOff, f6 + yOff, f7 + zOff).m_85950_(f1, f1, f1, 1.0F).m_85969_(k).m_5752_();
      consumer.m_85982_(matrix4f, f5 + xOff, f6 + 0.025F - yOff, f7 - zOff).m_85950_(f1, f1, f1, 1.0F).m_85969_(k).m_5752_();
   }
}
