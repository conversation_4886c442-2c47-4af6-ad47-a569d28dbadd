package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class FrostBallProjectile extends WaterBallProjectile {
   public FrostBallProjectile(EntityType<? extends FrostBallProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public FrostBallProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.FROST_BALL.get(), levelIn);
      this.m_5602_(shooter);
   }

   public boolean shouldDiscardInWater() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/frost_ball.png")};
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12474_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SNOWFLAKE.get(), x, y, z, 15, 0.08D, 0.08D, 0.08D, 0.05D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123754_, x, y, z, 15, 0.1D, 0.1D, 0.1D, 0.1D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_175821_, x, y, z, 35, 0.08D, 0.08D, 0.08D, 0.08D, true);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double speed = 0.05D;
         double dx = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         double dy = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         double dz = this.f_19853_.f_46441_.m_188500_() * 2.0D * speed - speed;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.SNOWFLAKE.get(), this.m_20185_() + dx, this.m_20186_() + dy, this.m_20189_() + dz, dx, dy, dz);
      }

      Vec3 random = this.vec3Random().m_82490_(0.009999999776482582D);

      for(int i = 0; i < 3; ++i) {
         this.f_19853_.m_7106_(ParticleTypes.f_175821_, vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, random.f_82479_, random.f_82480_, random.f_82481_);
      }

   }
}
