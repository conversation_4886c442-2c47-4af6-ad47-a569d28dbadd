package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.block.CharybdisCoreBlock;
import com.github.manasmods.tensura.block.entity.CharybdisCoreBlockEntity;
import com.github.manasmods.tensura.block.entity.PrayingPathBlockEntity;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.menu.DegenerateCraftingMenu;
import com.github.manasmods.tensura.menu.DegenerateEnchantmentMenu;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.google.common.collect.BiMap;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.item.HoneycombItem;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.WeatheringCopper;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.SculkSensorPhase;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.Tags.Blocks;
import net.minecraftforge.network.NetworkHooks;

public class DegenerateSkill extends Skill {
   public DegenerateSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 35000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.degenerate.crafting");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.degenerate.synthesise");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.degenerate.separate");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Player player;
      label317: {
         Level level = entity.m_9236_();
         Player player;
         switch(instance.getMode()) {
         case 1:
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               if (serverPlayer.m_6047_()) {
                  NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer1) -> {
                     return new DegenerateEnchantmentMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(level, serverPlayer.m_20183_()));
                  }, Component.m_237119_()));
               } else {
                  NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer1) -> {
                     return new DegenerateCraftingMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(level, serverPlayer.m_20183_()));
                  }, Component.m_237119_()));
               }

               serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
            break;
         case 2:
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 5.0D);
            BlockPos pos = result.m_82425_();
            BlockEntity var21 = level.m_7702_(pos);
            if (var21 instanceof CharybdisCoreBlockEntity) {
               CharybdisCoreBlockEntity core = (CharybdisCoreBlockEntity)var21;
               switch((SculkSensorPhase)level.m_8055_(pos).m_61143_(CharybdisCoreBlock.MODE)) {
               case INACTIVE:
                  this.fusingCore(entity, (List)TensuraConfig.INSTANCE.blocksConfig.fusingInactiveCoreSkills.get(), core.getEP());
                  break;
               case ACTIVE:
                  this.fusingCore(entity, (List)TensuraConfig.INSTANCE.blocksConfig.fusingActiveCoreSkills.get(), core.getEP());
                  break;
               case COOLDOWN:
                  this.fusingCore(entity, (List)TensuraConfig.INSTANCE.blocksConfig.fusingInertCoreSkills.get(), (Double)TensuraConfig.INSTANCE.blocksConfig.fusingCoreEP.get());
               }

               level.m_46961_(pos, false);
               TensuraParticleHelper.addServerParticlesAroundPos(level.f_46441_, level, Vec3.m_82512_(pos), ParticleTypes.f_235898_, 1.0D);
               TensuraParticleHelper.addServerParticlesAroundPos(level.f_46441_, level, Vec3.m_82512_(pos), (ParticleOptions)TensuraParticles.SOUL.get(), 1.0D);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
               return;
            }

            LivingEntity target = SkillHelper.getTargetingEntity(entity, 4.0D, false);
            Player player;
            if (target == null || !target.m_6084_()) {
               if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }

               return;
            }

            if (target instanceof Player) {
               player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            if (!RaceHelper.isSpiritual(target) || (double)target.m_21223_() > (double)target.m_21233_() * 0.25D) {
               break label317;
            }

            IElementalSpirit spirit;
            if (target instanceof IElementalSpirit) {
               spirit = (IElementalSpirit)target;
               if (spirit.getSummoningTick() > 0) {
                  break label317;
               }
            }

            if (target.m_6469_(TensuraDamageSources.synthesise(entity), target.m_21233_() * 10.0F)) {
               if (target instanceof IElementalSpirit) {
                  spirit = (IElementalSpirit)target;
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                        if (cap.setSpiritLevel(player, spirit.getElemental().getId(), spirit.getSpiritLevel().getId())) {
                           TensuraSkillCapability.sync(player);
                           PrayingPathBlockEntity.grantSpiritMagic(player, spirit.getElemental(), spirit.getSpiritLevel());
                           PrayingPathBlockEntity.grantManipulation(player, spirit.getElemental());
                        }

                     });
                  }
               }

               if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                  List<ManasSkill> learntSkill = new ArrayList();
                  Iterator var30 = SkillAPI.getSkillsFrom(target).getLearnedSkills().iterator();

                  while(var30.hasNext()) {
                     ManasSkillInstance targetSkill = (ManasSkillInstance)var30.next();
                     if (!targetSkill.isTemporarySkill() && targetSkill.getMastery() >= 0 && targetSkill.getSkill() != this) {
                        ManasSkill var37 = targetSkill.getSkill();
                        if (var37 instanceof Skill) {
                           Skill skill = (Skill)var37;
                           SkillPlunderEvent event = new SkillPlunderEvent(target, entity, TensuraGameRules.canStealSkill(level), skill);
                           if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime())) {
                              learntSkill.add(event.getSkill());
                           }
                        }
                     }
                  }

                  if (TensuraGameRules.canStealSkill(level)) {
                     learntSkill.forEach((skillx) -> {
                        SkillAPI.getSkillsFrom(target).forgetSkill(skillx);
                     });
                     SkillAPI.getSkillsFrom(target).syncChanges();
                  }
               }

               CompoundTag tag = instance.getOrCreateTag();
               CompoundTag synthesisedList;
               if (tag.m_128441_("synthesisedList")) {
                  synthesisedList = (CompoundTag)tag.m_128423_("synthesisedList");
                  if (synthesisedList == null) {
                     return;
                  }

                  String targetID = EntityType.m_20613_(target.m_6095_()).toString();
                  if (synthesisedList.m_128441_(targetID)) {
                     return;
                  }

                  synthesisedList.m_128379_(targetID, true);
               } else {
                  synthesisedList = new CompoundTag();
                  synthesisedList.m_128379_(EntityType.m_20613_(target.m_6095_()).toString(), true);
                  tag.m_128365_("synthesisedList", synthesisedList);
               }

               double difference = Math.min(SkillUtils.getEPGain(target, entity), (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get());
               if (target instanceof Player) {
                  Player playerTarget = (Player)target;
                  if (TensuraGameRules.canEpSteal(level)) {
                     DamageSourceHelper.markHurt(target, entity);
                     SkillHelper.gainMaxMP(entity, difference / 2.0D);
                     SkillHelper.gainMaxAP(entity, difference / 2.0D);
                     TensuraEPCapability.setSkippingEPDrop(target, true);
                     TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                        double reducedAura = cap.getBaseAura() - difference / 2.0D;
                        double reducedMana = cap.getBaseMagicule() - difference / 2.0D;
                        if (reducedAura < 0.0D) {
                           reducedMana -= reducedAura * -1.0D;
                           reducedAura = 100.0D;
                        } else if (reducedMana < 0.0D) {
                           reducedAura -= reducedMana * -1.0D;
                           reducedMana = 100.0D;
                        }

                        double minusMP = cap.getBaseMagicule() - reducedMana;
                        cap.setMagicule(cap.getMagicule() - minusMP);
                        double minusAP = cap.getBaseAura() - reducedAura;
                        cap.setAura(cap.getAura() - minusAP);
                        cap.setBaseMagicule(reducedMana, playerTarget);
                        cap.setBaseAura(reducedAura, playerTarget);
                     });
                     TensuraPlayerCapability.sync(playerTarget);
                     this.addMasteryPoint(instance, entity);
                     instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 1.0D);
                  } else if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }
               } else {
                  if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER)) {
                     return;
                  }

                  DamageSourceHelper.markHurt(target, entity);
                  SkillHelper.gainMaxMP(entity, difference / 2.0D);
                  SkillHelper.gainMaxAP(entity, difference / 2.0D);
                  TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                     cap.setEP(target, cap.getEP() - difference);
                  });
                  this.addMasteryPoint(instance, entity);
                  instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 1.0D);
               }
            }
            break;
         case 3:
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 4.0D, false);
            if (target != null) {
               if (!target.m_6084_()) {
                  return;
               }

               Player player;
               if (target instanceof Player) {
                  player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               if (SkillHelper.isSubordinate(entity, target) && entity.m_6144_()) {
                  Predicate<MobEffect> predicate = (effect) -> {
                     return !(effect instanceof TensuraMobEffect) || !effect.m_19486_();
                  };
                  if (SkillHelper.removePredicateEffect(target, predicate, this.magiculeCost(entity, instance))) {
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 1.0D);
                  }
               } else {
                  if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER) && TensuraEPCapability.getEP(target) < TensuraEPCapability.getEP(entity) * 0.75D) {
                     Iterator var6 = SkillAPI.getSkillsFrom(target).getLearnedSkills().iterator();

                     label266:
                     while(true) {
                        ManasSkillInstance targetSkill;
                        Skill skill;
                        do {
                           ManasSkill var9;
                           do {
                              do {
                                 do {
                                    do {
                                       if (!var6.hasNext()) {
                                          break label266;
                                       }

                                       targetSkill = (ManasSkillInstance)var6.next();
                                    } while(targetSkill.isTemporarySkill());
                                 } while(targetSkill.getMastery() < 0);
                              } while(targetSkill.getSkill() == this);

                              var9 = targetSkill.getSkill();
                           } while(!(var9 instanceof Skill));

                           skill = (Skill)var9;
                        } while(!skill.getType().equals(Skill.SkillType.COMMON) && !skill.getType().equals(Skill.SkillType.EXTRA));

                        boolean steal = TensuraGameRules.canStealSkill(level);
                        SkillPlunderEvent event = new SkillPlunderEvent(target, entity, steal, targetSkill.getSkill());
                        if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, targetSkill.getSkill(), instance.getRemoveTime())) {
                           if (steal) {
                              SkillAPI.getSkillsFrom(target).forgetSkill(targetSkill);
                           }

                           TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 1.0D);
                           if (entity instanceof Player) {
                              player = (Player)entity;
                              player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                           }

                           this.addMasteryPoint(instance, entity);
                        }
                     }
                  } else if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

                  DamageSourceHelper.markHurt(target, entity);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 5.0D);
               BlockPos pos = result.m_82425_();
               BlockState state = level.m_8055_(pos);
               if (state.m_60795_()) {
                  Predicate<MobEffect> predicate = (effect) -> {
                     return !(effect instanceof TensuraMobEffect) || !effect.m_19486_();
                  };
                  if (SkillHelper.removePredicateEffect(entity, predicate, this.magiculeCost(entity, instance))) {
                     instance.setCoolDown(instance.isMastered(entity) ? 5 : 3);
                     TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 1.0D);
                  }
               } else {
                  SkillGriefEvent.Pre preGrief;
                  if (state.m_204336_(Blocks.ORES)) {
                     if (!TensuraGameRules.canSkillGrief(level)) {
                        return;
                     }

                     this.addMasteryPoint(instance, entity);
                     preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                     if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                        return;
                     }

                     if (state.m_204336_(Blocks.ORES_IN_GROUND_STONE)) {
                        level.m_46953_(pos, true, entity);
                        level.m_46597_(pos, net.minecraft.world.level.block.Blocks.f_50069_.m_49966_());
                     } else if (state.m_204336_(Blocks.ORES_IN_GROUND_DEEPSLATE)) {
                        level.m_46953_(pos, true, entity);
                        level.m_46597_(pos, net.minecraft.world.level.block.Blocks.f_152550_.m_49966_());
                     } else if (state.m_204336_(Blocks.ORES_IN_GROUND_NETHERRACK)) {
                        level.m_46953_(pos, true, entity);
                        level.m_46597_(pos, net.minecraft.world.level.block.Blocks.f_50134_.m_49966_());
                     }

                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                  } else {
                     if (!TensuraGameRules.canSkillGrief(level)) {
                        return;
                     }

                     preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                     if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                        return;
                     }

                     if (state.m_60734_() instanceof WeatheringCopper) {
                        this.addMasteryPoint(instance, entity);
                        WeatheringCopper.m_154899_(state).ifPresent((blockState) -> {
                           level.m_46597_(pos, blockState);
                        });
                     } else {
                        Optional.ofNullable((Block)((BiMap)HoneycombItem.f_150864_.get()).get(state.m_60734_())).map((block) -> {
                           return block.m_152465_(state);
                        }).ifPresent((waxOff) -> {
                           level.m_46597_(pos, waxOff);
                        });
                     }

                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                  }
               }
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         return;
      }

      if (entity instanceof Player) {
         player = (Player)entity;
         player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
      }

   }

   private void fusingCore(LivingEntity entity, List<? extends String> strings, double EP) {
      List<ManasSkill> list = strings.stream().map((skillx) -> {
         return (ManasSkill)SkillAPI.getSkillRegistry().getValue(new ResourceLocation(skillx));
      }).filter(Objects::nonNull).toList();
      if (!list.isEmpty()) {
         Iterator var6 = list.iterator();

         while(var6.hasNext()) {
            ManasSkill skill = (ManasSkill)var6.next();
            if (SkillUtils.learnSkill(entity, skill) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }
         }
      }

      SkillHelper.gainMaxMP(entity, EP);
      SkillHelper.gainMP(entity, EP, false);
   }
}
