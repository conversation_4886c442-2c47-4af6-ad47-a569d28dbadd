package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class DiamondPathArt extends Battewill {
   public double learningCost() {
      return 150.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 150.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfAura(entity, instance)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_aura.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.DIAMOND_PATH.get(), 240, 0, false, false, false));
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_21023_((MobEffect)TensuraMobEffects.DIAMOND_PATH.get())) {
         if (!SkillHelper.outOfAura(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            instance.addMasteryPoint(entity);
            this.addMasteryPoint(instance, entity);
            int duration = this.isMastered(instance, entity) ? 3600 : 1200;
            int level = this.isMastered(instance, entity) ? 1 : 0;
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.DIAMOND_PATH.get(), duration, level, false, false, false));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 1.0F);
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 0.5F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_((MobEffect)TensuraMobEffects.DIAMOND_PATH.get());
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 0.5F);
   }
}
