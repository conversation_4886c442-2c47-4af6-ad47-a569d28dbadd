package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SissieEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class SissieRenderer extends GeoEntityRenderer<SissieEntity> {
   public SissieRenderer(Context renderManager) {
      super(renderManager, new SissieModel());
      this.f_114477_ = 2.0F;
   }

   public ResourceLocation getTextureLocation(SissieEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/sissie/sissie.png");
   }

   public RenderType getRenderType(SissieEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (entity.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
