package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.unique.AntiSkill;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.gameevent.GameEvent;

public class RangedBarrierEntity extends BarrierEntity {
   public RangedBarrierEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.BARRIER.get(), level);
      this.m_5602_(entity);
   }

   public RangedBarrierEntity(EntityType<? extends RangedBarrierEntity> entityType, Level level) {
      super(entityType, level);
   }

   private float getPhysicalResistances(DamageSource source) {
      Entity var3 = this.m_37282_();
      if (var3 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var3;
         if (!DamageSourceHelper.isPhysicalAttack(source)) {
            return 1.0F;
         } else if (SkillUtils.isSkillToggled(owner, (ManasSkill)ResistanceSkills.PHYSICAL_ATTACK_NULLIFICATION.get())) {
            return SkillUtils.reducingResistances(owner) ? 0.5F : 0.0F;
         } else if (SkillUtils.isSkillToggled(owner, (ManasSkill)ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get())) {
            return SkillUtils.reducingResistances(owner) ? 1.0F : 0.5F;
         } else {
            return 1.0F;
         }
      } else {
         return 1.0F;
      }
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.f_19853_.m_5776_() && !this.m_213877_()) {
            label21: {
               Entity var4 = pSource.m_7640_();
               if (var4 instanceof LivingEntity) {
                  LivingEntity attacker = (LivingEntity)var4;
                  if (shouldInstaBreak(attacker, this.m_37282_())) {
                     pAmount = this.getHealth();
                     break label21;
                  }
               }

               pAmount *= this.getPhysicalResistances(pSource);
            }

            this.setHealth(this.getHealth() - pAmount);
            this.m_5834_();
            this.m_146852_(GameEvent.f_223706_, pSource.m_7639_());
            if (this.getHealth() <= 0.0F) {
               this.m_146870_();
               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_144242_, this.m_5720_(), 2.0F, 1.0F);
            } else {
               this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12346_, this.m_5720_(), 2.0F, 1.0F);
            }
         }

         return true;
      }
   }

   public static boolean shouldInstaBreak(LivingEntity attacker, @Nullable Entity target) {
      if (!((AntiSkill)UniqueSkills.ANTI_SKILL.get()).isInSlot(attacker) && !SkillUtils.isSkillToggled(attacker, (ManasSkill)UniqueSkills.ANTI_SKILL.get())) {
         if (SkillUtils.isSkillToggled(attacker, (ManasSkill)UniqueSkills.COOK.get())) {
            return true;
         } else if (attacker.m_21205_().getEnchantmentLevel((Enchantment)TensuraEnchantments.BARRIER_PIERCING.get()) > 0) {
            return true;
         } else if (attacker.m_21205_().getEnchantmentLevel((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get()) > 0 && target instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)target;
            return SkillHelper.getMP(entity, false) < SkillHelper.getMP(attacker, false) * 1.5D;
         } else {
            return false;
         }
      } else {
         return true;
      }
   }

   protected void m_5834_() {
      this.f_19864_ = true;
      BarrierPart[] var1 = this.parts;
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         BarrierPart part = var1[var3];
         part.addServerParticlesAroundSelf(ParticleTypes.f_123790_, 0.5D);
         part.addServerParticlesAroundSelf(ParticleTypes.f_123790_, 0.5D);
         part.addServerParticlesAroundSelf(ParticleTypes.f_123790_, 0.5D);
      }

   }
}
