package com.github.manasmods.tensura.ability.magic.spiritual.fire;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.field.Hellfire;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

public class HellfireMagic extends SpiritualMagic {
   public HellfireMagic() {
      super(MagicElemental.FLAME, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 100;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
            int distance = instance.isMastered(entity) ? 20 : 15;
            Entity target = SkillHelper.getTargetingEntity(entity, (double)distance, false, true);
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, (double)distance);
               pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
            }

            Hellfire sphere = new Hellfire(entity.m_9236_(), entity);
            sphere.setDamage(instance.isMastered(entity) ? 500.0F : 250.0F);
            sphere.setMpCost(this.magiculeCost(entity, instance));
            sphere.setSkill(instance);
            sphere.setLife(60);
            sphere.setRadius(instance.isMastered(entity) ? 5.0F : 2.5F);
            sphere.m_6034_(pos.f_82479_, pos.f_82480_ - (double)sphere.getRadius(), pos.f_82481_);
            entity.m_9236_().m_7967_(sphere);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
            ((ServerLevel)entity.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.RED_FIRE.get(), entity.m_20185_(), entity.m_20186_() + (double)entity.m_20206_() / 2.0D, entity.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
            String location = instance.isMastered(entity) ? "tensura:fire_sphere_10x10" : "tensura:fire_sphere_5x5";
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return sphere;
            }), new RequestFxSpawningPacket(new ResourceLocation(location), sphere.m_19879_(), 0.0D, (double)sphere.getRadius(), 0.0D, false));
         }
      }
   }
}
