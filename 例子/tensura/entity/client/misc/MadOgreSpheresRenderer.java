package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.client.layer.FlameOrbLayer;
import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class MadOgreSpheresRenderer extends GeoEntityRenderer<MadOgreOrbsEntity> {
   public MadOgreSpheresRenderer(Context renderManager) {
      super(renderManager, new MadOgreSpheresModel());
      this.addLayer(new FlameOrbLayer(this, 1));
      this.addLayer(new FlameOrbLayer(this, 2));
      this.addLayer(new FlameOrbLayer(this, 3));
      this.addLayer(new FlameOrbLayer(this, 4));
      this.addLayer(new FlameOrbLayer(this, 5));
      this.addLayer(new FlameOrbLayer(this, 6));
   }

   public ResourceLocation getTextureLocation(MadOgreOrbsEntity instance) {
      return new ResourceLocation("tensura", "textures/blank_texture.png");
   }

   public RenderType getRenderType(MadOgreOrbsEntity spheres, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(spheres.getInitSize(), spheres.getInitSize(), spheres.getInitSize());
      return RenderType.m_110473_(this.getTextureLocation(spheres));
   }
}
