package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class ReaperSkill extends Skill {
   public static final String RECON = "17d4dca8-fc25-465a-9c51-ac7095f4d457";
   public static final UUID REAPER = UUID.fromString("17d4dca8-fc25-465a-9c51-ac7095f4d457");

   public ReaperSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance recon = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
      if (recon == null) {
         return false;
      } else {
         return recon.m_22111_(REAPER) == null;
      }
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.reaper.attack");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.reaper.eater");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      return mode == 2 ? "InfiniteEater" : "None";
   }

   private boolean dodgeChance(ManasSkillInstance instance, LivingEntity entity) {
      float chance = entity.m_217043_().m_188501_();
      if (instance.isMastered(entity)) {
         return (double)chance < 0.75D;
      } else {
         return (double)chance < 0.5D;
      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled()) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!damageSource.m_19387_()) {
                     LivingEntity entity = event.getEntity();
                     if (!this.dodgeChance(instance, entity)) {
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                        event.setCanceled(true);
                        if (SkillUtils.canNegateDodge(entity, damageSource)) {
                           event.setCanceled(false);
                        }

                     }
                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (instance.isToggled()) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (!this.dodgeChance(instance, entity)) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      int level = instance.isMastered(entity) ? 1 : 0;
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.REAPER_RECON.get(), 240, level, false, false, false));
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance recon = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
      if (recon != null && recon.m_22111_(REAPER) != null) {
         recon.m_22127_(REAPER);
         AttributeInstance aura = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
         if (aura != null) {
            aura.m_22120_(REAPER);
         }

         AttributeInstance magicule = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
         if (magicule != null) {
            magicule.m_22120_(REAPER);
         }
      }

      entity.m_21195_((MobEffect)TensuraMobEffects.REAPER_RECON.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
      TensuraEPCapability.updateEP(entity);
   }

   private boolean cannotUseAttack(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.isToggled()) {
         return true;
      } else {
         AttributeInstance recon = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
         return recon != null && recon.m_22111_(REAPER) != null;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 2) {
         Level level = entity.m_9236_();
         if (this.cannotUseAttack(instance, entity)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               String message = instance.isToggled() ? "tensura.skill.mode.need_toggle_off" : "tensura.ability.activation_failed";
               player.m_5661_(Component.m_237110_(message, new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

         } else {
            CompoundTag tag = instance.getOrCreateTag();
            int learnPoint = tag.m_128451_("InfiniteEater");
            if (learnPoint < 100) {
               tag.m_128405_("InfiniteEater", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  if (tag.m_128451_("InfiniteEater") >= 100) {
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(2)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  } else {
                     instance.setCoolDown(10);
                     SkillUtils.learningFailPenalty(entity);
                     player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(2)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
                  }

                  player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               instance.markDirty();
            } else {
               LivingEntity target = SkillHelper.getTargetingEntity(entity, 6.0D, false);
               Player player;
               if (target != null && target.m_6084_()) {
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        return;
                     }
                  }

                  double targetEP = TensuraEPCapability.getEP(target);
                  if (targetEP > TensuraEPCapability.getEP(entity)) {
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     }

                  } else if (!SkillHelper.outOfMagicule(entity, targetEP)) {
                     if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.infiniteEater(entity), entity, instance), target.m_21233_() * 10.0F)) {
                        entity.m_21011_(InteractionHand.MAIN_HAND, true);
                        this.addMasteryPoint(instance, entity);
                        instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
                        level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                        TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_, 1.0D);
                        TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_, 2.0D);
                        if (instance.isTemporarySkill()) {
                           return;
                        }

                        CompoundTag eatenList;
                        if (tag.m_128441_("eatenList")) {
                           eatenList = (CompoundTag)tag.m_128423_("eatenList");
                           if (eatenList == null) {
                              return;
                           }

                           String targetID = EntityType.m_20613_(target.m_6095_()).toString();
                           if (eatenList.m_128441_(targetID)) {
                              return;
                           }

                           eatenList.m_128379_(targetID, true);
                           instance.markDirty();
                        } else {
                           eatenList = new CompoundTag();
                           eatenList.m_128379_(EntityType.m_20613_(target.m_6095_()).toString(), true);
                           tag.m_128365_("eatenList", eatenList);
                           instance.markDirty();
                        }

                        if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER)) {
                           return;
                        }

                        double amountToMax = 0.5D;
                        double EP = Math.min(SkillUtils.getEPGain(target, entity), (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get() / amountToMax);
                        if (target instanceof Player) {
                           Player playerTarget = (Player)target;
                           if (TensuraGameRules.canEpSteal(target.m_9236_())) {
                              int minEP = TensuraGameRules.getMinEp(target.m_9236_());
                              if (minEP > 0) {
                                 EP -= (double)minEP;
                              }

                              if (EP <= 0.0D) {
                                 return;
                              }

                              SkillHelper.gainMaxMP(entity, EP * amountToMax);
                              TensuraEPCapability.setSkippingEPDrop(target, true);
                              TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                                 cap.setBaseMagicule(cap.getBaseMagicule() - EP / 2.0D, playerTarget);
                                 cap.setBaseAura(cap.getBaseAura() - EP / 2.0D, playerTarget);
                              });
                              TensuraPlayerCapability.sync(playerTarget);
                           }
                        } else {
                           SkillHelper.gainMaxMP(entity, EP * amountToMax);
                           SkillHelper.reduceEP(target, entity, 1.0D, true, true);
                           TensuraEPCapability.setSkippingEPDrop(target, true);
                        }
                     }

                  }
               } else {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
               }
            }
         }
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 1 && this.isHeld(entity)) {
         if (instance.isToggled()) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.mode.need_toggle_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

         } else {
            Level level = entity.m_9236_();
            AttributeInstance recon = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
            AttributeInstance aura = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
            AttributeInstance magicule = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
            if (recon != null && recon.m_22111_(REAPER) != null) {
               recon.m_22127_(REAPER);
               instance.setCoolDown(10);
               if (aura != null) {
                  aura.m_22120_(REAPER);
               }

               if (magicule != null) {
                  magicule.m_22120_(REAPER);
               }

               this.updateCurrentEP(entity, 2.0F);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else if (recon != null) {
               this.addMasteryPoint(instance, entity);
               CompoundTag tag = instance.getTag();
               if (tag == null) {
                  this.summonClones(entity, level, 5);
               } else {
                  this.summonClones(entity, level, tag.m_128451_("clones"));
               }

               float size = 0.5F;
               float sizeMultiplier = RaceHelper.getSizeMultiplier(entity);
               size = (float)(Math.max((double)(size * sizeMultiplier), (Double)TensuraConfig.INSTANCE.attributeConfig.minimumSize.get()) / (double)sizeMultiplier);
               AttributeModifier reaper = new AttributeModifier(REAPER, "ReaperRecon", (double)(size - 1.0F), Operation.MULTIPLY_TOTAL);
               recon.m_22125_(reaper);
               if (aura != null) {
                  aura.m_22125_(reaper);
               }

               if (magicule != null) {
                  magicule.m_22125_(reaper);
               }

               this.updateCurrentEP(entity, 0.5F);
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (this.cannotUseAttack(instance, entity)) {
         return true;
      } else {
         CompoundTag tag = instance.getTag();
         int clones = tag != null ? tag.m_128451_("clones") : 5;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.output_number", new Object[]{clones}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
         }

         return true;
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         CompoundTag tag = instance.getOrCreateTag();
         int newScale = tag.m_128451_("clones") + (int)delta;
         if (newScale > 5) {
            newScale = 1;
         } else if (newScale < 1) {
            newScale = 5;
         }

         if (tag.m_128451_("clones") != newScale) {
            tag.m_128405_("clones", newScale);
            instance.markDirty();
         }

      }
   }

   public void onDeath(ManasSkillInstance instance, LivingDeathEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         AttributeInstance recon = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
         AttributeInstance aura = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
         AttributeInstance magicule = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
         if (recon != null && recon.m_22111_(REAPER) != null) {
            recon.m_22127_(REAPER);
            if (aura != null) {
               aura.m_22120_(REAPER);
            }

            if (magicule != null) {
               magicule.m_22120_(REAPER);
            }

            this.updateCurrentEP(entity, 2.0F);
         }

      }
   }

   private void summonClones(LivingEntity entity, Level level, int number) {
      level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 2.0D);
      double EP = TensuraEPCapability.getEP(entity) * 0.5D / (double)number;
      float var10000;
      switch(number) {
      case 1:
         var10000 = 0.5F;
         break;
      case 2:
         var10000 = 0.42F;
         break;
      case 3:
         var10000 = 0.37F;
         break;
      case 4:
         var10000 = 0.33F;
         break;
      default:
         var10000 = 0.3F;
      }

      float size = var10000;
      float sizeMultiplier = RaceHelper.getSizeMultiplier(entity);
      size = (float)(Math.max((double)(size * sizeMultiplier), (Double)TensuraConfig.INSTANCE.attributeConfig.minimumSize.get()) / (double)sizeMultiplier);
      EntityType<CloneEntity> type = entity.m_6144_() ? (EntityType)TensuraEntityTypes.CLONE_SLIM.get() : (EntityType)TensuraEntityTypes.CLONE_DEFAULT.get();

      for(int i = 0; i < number; ++i) {
         CloneEntity clone = new CloneEntity(type, level);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            clone.m_21828_(player);
         }

         clone.setSkill(this);
         clone.copyStatsAndSkills(entity, true);
         clone.setHeight(clone.getHeight() * size);
         clone.setWidth(clone.getWidth() * size);
         TensuraEPCapability.setLivingEP(clone, (double)Math.round(EP));
         clone.m_146884_(entity.m_20182_());
         level.m_7967_(clone);
      }

   }

   private void updateCurrentEP(LivingEntity entity, float multiplier) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setMagicule(cap.getMagicule() * (double)multiplier);
            cap.setAura(cap.getAura() * (double)multiplier);
            TensuraPlayerCapability.sync(player);
         });
      }

      TensuraEPCapability.updateEP(entity);
   }
}
