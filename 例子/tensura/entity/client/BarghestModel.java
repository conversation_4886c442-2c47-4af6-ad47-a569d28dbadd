package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BarghestEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class BarghestModel extends AnimatedGeoModel<BarghestEntity> {
   public ResourceLocation getModelResource(BarghestEntity object) {
      return new ResourceLocation("tensura", "geo/barghest.geo.json");
   }

   public ResourceLocation getTextureResource(BarghestEntity instance) {
      return instance.m_6162_() ? new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby.png") : new ResourceLocation("tensura", "textures/entity/barghest/barghest.png");
   }

   public ResourceLocation getAnimationResource(BarghestEntity moth) {
      return new ResourceLocation("tensura", "animations/barghest.animation.json");
   }

   public void setCustomAnimations(BarghestEntity dog, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(dog, instanceId, customPredicate);
      if (dog.getMiscAnimation() == 0) {
         if (!dog.m_5803_()) {
            EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
            IBone head = this.getAnimationProcessor().getBone("head");
            if (head != null) {
               float pitch = dog.m_21825_() ? extraData.headPitch - 15.0F : extraData.headPitch;
               head.setRotationX(pitch * 0.017453292F);
               head.setRotationY(extraData.netHeadYaw * 0.017453292F);
            }

         }
      }
   }
}
