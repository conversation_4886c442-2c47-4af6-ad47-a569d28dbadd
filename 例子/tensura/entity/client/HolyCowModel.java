package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HolyCowEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

public class HolyCowModel<T extends HolyCowEntity> extends EntityModel<T> {
   public static final ModelLayerLocation HOLY_COW = new ModelLayerLocation(new ResourceLocation("tensura", "holy_cow"), "main");
   private final ModelPart body;
   private final ModelPart head;
   private final ModelPart leg0;
   private final ModelPart leg1;
   private final ModelPart leg2;
   private final ModelPart leg3;

   public HolyCowModel(ModelPart root) {
      this.body = root.m_171324_("body");
      this.head = root.m_171324_("head");
      this.leg0 = root.m_171324_("leg0");
      this.leg1 = root.m_171324_("leg1");
      this.leg2 = root.m_171324_("leg2");
      this.leg3 = root.m_171324_("leg3");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      PartDefinition body = partdefinition.m_171599_("body", CubeListBuilder.m_171558_().m_171514_(18, 4).m_171488_(-6.0F, -10.0F, -7.0F, 12.0F, 18.0F, 10.0F, new CubeDeformation(0.0F)).m_171514_(52, 0).m_171488_(-2.0F, 2.0F, -8.0F, 4.0F, 6.0F, 1.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 5.0F, 2.0F, 1.5708F, 0.0F, 0.0F));
      body.m_171599_("alb", CubeListBuilder.m_171558_().m_171514_(0, 32).m_171488_(-6.5F, -6.0F, -15.75F, 13.0F, 13.0F, 19.0F, new CubeDeformation(0.0F)).m_171514_(64, 41).m_171488_(-6.5F, -10.0F, -15.75F, 13.0F, 4.0F, 19.0F, new CubeDeformation(0.0F)).m_171514_(64, 20).m_171488_(-6.5F, -8.5F, -15.75F, 13.0F, 1.0F, 19.0F, new CubeDeformation(0.1F)).m_171514_(70, 23).m_171488_(-1.5F, -10.1F, -8.0F, 3.0F, 4.0F, 4.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 0.0F, 0.0F));
      PartDefinition head = partdefinition.m_171599_("head", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-4.0F, -4.0F, -6.0F, 8.0F, 8.0F, 6.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 4.0F, -8.0F));
      PartDefinition mitre = head.m_171599_("mitre", CubeListBuilder.m_171558_().m_171514_(65, 0).m_171488_(-3.0F, -29.0F, -14.0F, 6.0F, 5.0F, 6.0F, new CubeDeformation(0.0F)).m_171514_(71, 20).m_171488_(-1.5F, -29.5F, -14.1F, 3.0F, 4.0F, 3.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 20.0F, 8.0F));
      mitre.m_171599_("head_r1", CubeListBuilder.m_171558_().m_171514_(66, 0).m_171488_(-0.9F, -0.9F, -3.0F, 4.0F, 4.0F, 6.0F, new CubeDeformation(-0.001F)), PartPose.m_171423_(0.0F, -30.5F, -11.0F, 0.0F, 0.0F, 0.7854F));
      partdefinition.m_171599_("leg0", CubeListBuilder.m_171558_().m_171514_(0, 16).m_171488_(-2.0F, 0.0F, -2.0F, 4.0F, 12.0F, 4.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(-4.0F, 12.0F, 7.0F));
      partdefinition.m_171599_("leg1", CubeListBuilder.m_171558_().m_171514_(0, 16).m_171480_().m_171488_(-2.0F, 0.0F, -2.0F, 4.0F, 12.0F, 4.0F, new CubeDeformation(0.0F)).m_171555_(false), PartPose.m_171419_(4.0F, 12.0F, 7.0F));
      partdefinition.m_171599_("leg2", CubeListBuilder.m_171558_().m_171514_(0, 16).m_171488_(-2.0F, 0.0F, -1.0F, 4.0F, 12.0F, 4.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(-4.0F, 12.0F, -6.0F));
      partdefinition.m_171599_("leg3", CubeListBuilder.m_171558_().m_171514_(0, 16).m_171480_().m_171488_(-2.0F, 0.0F, -1.0F, 4.0F, 12.0F, 4.0F, new CubeDeformation(0.0F)).m_171555_(false), PartPose.m_171419_(4.0F, 12.0F, -6.0F));
      return LayerDefinition.m_171565_(meshdefinition, 128, 64);
   }

   public void m_7695_(PoseStack poseStack, VertexConsumer vertexConsumer, int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
      this.body.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
      this.head.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
      this.leg0.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
      this.leg1.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
      this.leg2.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
      this.leg3.m_104306_(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
   }

   public void setupAnim(T pEntity, float pLimbSwing, float pLimbSwingAmount, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
      this.head.f_104203_ = pHeadPitch * 0.017453292F;
      this.head.f_104204_ = pNetHeadYaw * 0.017453292F;
      this.leg0.f_104203_ = Mth.m_14089_(pLimbSwing * 0.6662F) * 1.4F * pLimbSwingAmount;
      this.leg1.f_104203_ = Mth.m_14089_(pLimbSwing * 0.6662F + 3.1415927F) * 1.4F * pLimbSwingAmount;
      this.leg2.f_104203_ = Mth.m_14089_(pLimbSwing * 0.6662F + 3.1415927F) * 1.4F * pLimbSwingAmount;
      this.leg3.f_104203_ = Mth.m_14089_(pLimbSwing * 0.6662F) * 1.4F * pLimbSwingAmount;
   }
}
