package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.extra.FlameManipulationSkill;
import com.github.manasmods.tensura.ability.skill.resist.PhysicalAttackNullification;
import com.github.manasmods.tensura.ability.skill.unique.CookSkill;
import com.github.manasmods.tensura.ability.skill.unique.GourmandSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.entity.OrcLordEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.entity.magic.field.AreaField;
import com.github.manasmods.tensura.item.armor.HolyArmamentsArmorItem;
import com.github.manasmods.tensura.race.daemon.LesserDaemonRace;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.registries.ForgeRegistries;

public class SkillUtils {
   private static final List<MobEffect> misfireEffect;

   public static ResourceLocation getSkillId(@Nullable ManasSkill manasSkill) {
      return manasSkill == null ? new ResourceLocation("tensura:none") : SkillAPI.getSkillRegistry().getDelegateOrThrow(manasSkill).m_205785_().m_135782_();
   }

   @Nullable
   public static ManasSkillInstance getSkillOrNull(@Nullable Entity entity, ManasSkill manasSkill) {
      if (entity == null) {
         return null;
      } else {
         Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(manasSkill);
         return (ManasSkillInstance)instance.orElse((Object)null);
      }
   }

   public static boolean hasSkill(Entity entity, ManasSkill manasSkill) {
      ManasSkillInstance instance = getSkillOrNull(entity, manasSkill);
      if (instance == null) {
         return false;
      } else {
         return instance.getMastery() >= 0;
      }
   }

   public static boolean fullyHasSkill(Entity entity, ManasSkill manasSkill) {
      ManasSkillInstance instance = getSkillOrNull(entity, manasSkill);
      if (instance == null) {
         return false;
      } else if (instance.getMastery() < 0) {
         return false;
      } else {
         return !instance.isTemporarySkill();
      }
   }

   public static boolean isSkillMastered(LivingEntity entity, ManasSkill manasSkill) {
      ManasSkillInstance instance = getSkillOrNull(entity, manasSkill);
      return instance == null ? false : instance.isMastered(entity);
   }

   public static boolean isSkillToggled(ManasSkillInstance instance) {
      return instance.isToggled() && instance.getMastery() >= 0;
   }

   public static boolean isSkillToggled(Entity entity, ManasSkill skill) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      return (Boolean)optional.map(SkillUtils::isSkillToggled).orElse(false);
   }

   public static boolean learnSkill(LivingEntity entity, ManasSkill skill) {
      return learnSkill(entity, skill, -1);
   }

   public static boolean learnSkill(LivingEntity entity, ManasSkill skill, int removeTime) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> optional = storage.getSkill(skill);
      if (optional.isEmpty()) {
         TensuraSkillInstance instance = new TensuraSkillInstance(skill);
         instance.setRemoveTime(removeTime);
         return storage.learnSkill(instance);
      } else {
         ManasSkillInstance instance = (ManasSkillInstance)optional.get();
         if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
            return false;
         } else {
            ManasSkillInstance clone = instance.clone();
            if (removeTime > 0) {
               clone.getOrCreateTag().m_128405_("OldRemoval", clone.getRemoveTime());
               clone.getOrCreateTag().m_128405_("OldMastery", clone.getMastery());
            }

            clone.setRemoveTime(removeTime);
            clone.setMastery(Math.max(0, clone.getMastery()));
            UnlockSkillEvent event = new UnlockSkillEvent(clone, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               return false;
            } else {
               instance.deserialize(event.getSkillInstance().toNBT());
               instance.markDirty();
               storage.syncChanges();
               return true;
            }
         }
      }
   }

   public static boolean learnSkill(LivingEntity entity, ManasSkillInstance skill) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> optional = storage.getSkill(skill.getSkill());
      if (optional.isEmpty()) {
         return storage.learnSkill(skill);
      } else {
         ManasSkillInstance instance = (ManasSkillInstance)optional.get();
         if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
            return false;
         } else {
            ManasSkillInstance clone = (ManasSkillInstance)optional.get();
            if (skill.getRemoveTime() != -1) {
               clone.getOrCreateTag().m_128405_("OldRemoval", clone.getRemoveTime());
               clone.getOrCreateTag().m_128405_("OldMastery", clone.getMastery());
            }

            clone.setRemoveTime(skill.getRemoveTime());
            if (clone.getMastery() < skill.getMastery()) {
               clone.setMastery(skill.getMastery());
            }

            UnlockSkillEvent event = new UnlockSkillEvent(clone, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               return false;
            } else {
               instance.deserialize(event.getSkillInstance().toNBT());
               instance.markDirty();
               storage.syncChanges();
               return true;
            }
         }
      }
   }

   public static boolean learningFailPenalty(LivingEntity entity) {
      if ((double)entity.m_217043_().m_188501_() > 0.1D) {
         return false;
      } else {
         MobEffect effect = (MobEffect)misfireEffect.get(entity.m_217043_().m_188503_(misfireEffect.size()));
         int duration = effect.m_8093_() ? 1 : 200;
         entity.m_7292_(new MobEffectInstance(effect, duration, 0, false, false, true));
         return true;
      }
   }

   public static double getEPGain(LivingEntity pTarget) {
      return getEPGain(pTarget, (LivingEntity)null, false);
   }

   public static double getEPGain(LivingEntity pTarget, @Nullable LivingEntity attacker) {
      return getEPGain(pTarget, attacker, false);
   }

   public static double getEPGain(LivingEntity pTarget, @Nullable LivingEntity attacker, boolean applyReduction) {
      if (pTarget.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER)) {
         return 0.0D;
      } else {
         Level level = pTarget.m_9236_();
         if (pTarget instanceof Player) {
            Player player = (Player)pTarget;
            return TensuraPlayerCapability.getBaseEP(player) * (double)level.m_46469_().m_46215_(TensuraGameRules.PLAYER_EP) / 100.0D;
         } else {
            double EP = TensuraEPCapability.getEP(pTarget);
            int spawnerEP;
            if (pTarget instanceof Mob) {
               Mob mob = (Mob)pTarget;
               if (mob.getSpawnType() == MobSpawnType.MOB_SUMMONED) {
                  return 0.0D;
               }

               if (mob.getSpawnType() == MobSpawnType.TRIGGERED) {
                  return 0.0D;
               }

               if (mob.getSpawnType() == MobSpawnType.SPAWNER) {
                  spawnerEP = level.m_46469_().m_46215_(TensuraGameRules.SPAWNER_EP);
                  if (spawnerEP != 100) {
                     EP *= (double)((float)spawnerEP / 100.0F);
                  }
               }
            }

            int vanillaEP = level.m_46469_().m_46215_(TensuraGameRules.VANILLA_EP);
            if (vanillaEP != 100) {
               ResourceLocation id = ForgeRegistries.ENTITY_TYPES.getKey(pTarget.m_6095_());
               if (id != null && id.m_135827_().equals("minecraft")) {
                  EP *= (double)((float)vanillaEP / 100.0F);
               }
            }

            if (applyReduction && attacker != null) {
               spawnerEP = (int)(TensuraEPCapability.getEP(attacker) / EP);
               double percentage = Mth.m_14008_(0.01D * (double)spawnerEP, 0.0D, (Double)TensuraConfig.INSTANCE.miscConfig.maxEPReductionPercentage.get());
               return EP * (1.0D - percentage);
            } else {
               return EP;
            }
         }
      }
   }

   public static float getMagiculeGain(Player player, boolean majin) {
      float percentage = TensuraGameRules.getEPGain(player.f_19853_) / 3.0F;
      float bonus = majin ? percentage * 2.0F : percentage;
      if (hasSkill(player, (ManasSkill)UniqueSkills.VILLAIN.get())) {
         bonus *= 2.0F;
      }

      bonus += GourmandSkill.getGourmandBoost(player, true, majin);
      if (TensuraSkillCapability.isSkillInSlot(player, (ManasSkill)UniqueSkills.BERSERKER.get())) {
         bonus += 0.02F;
      }

      return bonus;
   }

   public static float getAuraGain(Player player, boolean majin) {
      float percentage = TensuraGameRules.getEPGain(player.f_19853_) / 3.0F;
      float bonus = majin ? percentage : percentage * 2.0F;
      if (hasSkill(player, (ManasSkill)UniqueSkills.VILLAIN.get())) {
         bonus *= 2.0F;
      }

      bonus += GourmandSkill.getGourmandBoost(player, false, majin);
      if (TensuraSkillCapability.isSkillInSlot(player, (ManasSkill)UniqueSkills.BERSERKER.get())) {
         bonus += 0.02F;
      }

      return bonus;
   }

   public static int getEarningLearnPoint(ManasSkillInstance instance, LivingEntity entity, boolean isMode) {
      int point = (Integer)TensuraConfig.INSTANCE.skillsConfig.bonusLearningGain.get() + entity.m_217043_().m_216339_(1, isMode ? 3 : 5);
      if (hasSkill(entity, (ManasSkill)ExtraSkills.SAGE.get())) {
         point += 2;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.MATHEMATICIAN.get())) {
         point += 2;
      }

      if (((CookSkill)UniqueSkills.COOK.get()).isInSlot(entity)) {
         point += 4;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.GREAT_SAGE.get())) {
         point += 9;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.MARTIAL_MASTER.get()) && instance.getSkill() instanceof Battewill) {
         point += 4;
      }

      if (!isSkillToggled(entity, (ManasSkill)UniqueSkills.FIGHTER.get()) || !(instance.getSkill() instanceof Magic) && !(instance.getSkill() instanceof Battewill) && !isMode) {
         if (entity instanceof OrcLordEntity) {
            point += 4;
         }

         return point;
      } else {
         return 100;
      }
   }

   public static int getBonusMasteryPoint(ManasSkillInstance instance, LivingEntity entity, int originalPoint) {
      int point = (Integer)TensuraConfig.INSTANCE.skillsConfig.bonusMasteryGain.get();
      if (hasSkill(entity, (ManasSkill)UniqueSkills.FIGHTER.get())) {
         point += 2 * originalPoint;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.MATHEMATICIAN.get())) {
         point += 2;
      }

      if (hasSkill(entity, (ManasSkill)ExtraSkills.SAGE.get())) {
         point += 2;
      }

      if (((CookSkill)UniqueSkills.COOK.get()).isInSlot(entity)) {
         point += 4;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.GREAT_SAGE.get())) {
         point += 9;
      }

      if (hasSkill(entity, (ManasSkill)UniqueSkills.MARTIAL_MASTER.get()) && instance.getSkill() instanceof Battewill) {
         point += 4;
      }

      return point;
   }

   public static boolean hasPainNull(LivingEntity living) {
      return isSkillToggled(living, (ManasSkill)UniqueSkills.SURVIVOR.get()) ? true : isSkillToggled(living, (ManasSkill)ResistanceSkills.PAIN_NULLIFICATION.get());
   }

   public static boolean canAutoSmelt(Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (noInteractiveMode(living)) {
            return false;
         }

         if (entity.m_6144_()) {
            return false;
         }

         if (isSkillToggled(living, (ManasSkill)ExtraSkills.BLACK_FLAME.get())) {
            return true;
         }

         if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()) || TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)ExtraSkills.FLAME_DOMINATION.get())) {
            return FlameManipulationSkill.canUseFire(living);
         }
      }

      return false;
   }

   public static boolean canBlockSoundDetect(Entity entity) {
      if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SOUND)) {
         return true;
      } else if (entity instanceof Player) {
         Player target = (Player)entity;
         if (noInteractiveMode(target)) {
            return true;
         } else if (isSkillToggled(target, (ManasSkill)UniqueSkills.MURDERER.get())) {
            return true;
         } else {
            return isSkillToggled(target, (ManasSkill)ExtraSkills.SOUND_DOMINATION.get()) ? true : isSkillToggled(target, (ManasSkill)ExtraSkills.SOUND_MANIPULATION.get());
         }
      } else {
         return false;
      }
   }

   public static boolean canInstantWarp(LivingEntity entity) {
      if (fullyHasSkill(entity, (ManasSkill)UniqueSkills.TRAVELER.get())) {
         return true;
      } else {
         return isSkillMastered(entity, (ManasSkill)UniqueSkills.SUPPRESSOR.get()) ? true : isSkillMastered(entity, (ManasSkill)ExtraSkills.SPATIAL_MOTION.get());
      }
   }

   public static boolean hasWarpShot(LivingEntity entity) {
      if (MobEffectHelper.noTeleportation(entity)) {
         return false;
      } else {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         Optional instance;
         if (TensuraSkillCapability.isSkillInSlot(entity, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())) {
            instance = storage.getSkill((ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get());
            if (instance.isPresent() && ((ManasSkillInstance)instance.get()).getOrCreateTag().m_128451_("WarpShot") >= 100) {
               return true;
            }
         }

         if (TensuraSkillCapability.isSkillInSlot(entity, (ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get())) {
            instance = storage.getSkill((ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get());
            if (instance.isPresent() && ((ManasSkillInstance)instance.get()).getMode() == 1) {
               return true;
            }
         }

         if (TensuraSkillCapability.isSkillInSlot(entity, (ManasSkill)UniqueSkills.SNIPER.get())) {
            instance = storage.getSkill((ManasSkill)UniqueSkills.SNIPER.get());
            return instance.isPresent() && ((ManasSkillInstance)instance.get()).getMode() == 2;
         } else if (!TensuraSkillCapability.isSkillInSlot(entity, (ManasSkill)UniqueSkills.TRAVELER.get())) {
            return false;
         } else {
            instance = storage.getSkill((ManasSkill)UniqueSkills.TRAVELER.get());
            return instance.isPresent() && ((ManasSkillInstance)instance.get()).getMode() == 3;
         }
      }
   }

   public static boolean canFlyLegit(Player player) {
      if (!player.m_7500_() && !player.m_5833_()) {
         if (HolyArmamentsArmorItem.isFullSet(player)) {
            return true;
         } else {
            return player.m_21023_((MobEffect)TensuraMobEffects.BATS_MODE.get()) ? true : canFlyWithSkills(player);
         }
      } else {
         return true;
      }
   }

   public static boolean canFlyWithSkills(Player player) {
      if (TensuraPlayerCapability.isSpiritualForm(player)) {
         return true;
      } else if (hasSkill(player, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
         return true;
      } else if (hasSkill(player, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
         return true;
      } else if (TensuraPlayerCapability.getRace(player) instanceof LesserDaemonRace) {
         return true;
      } else {
         MobEffectInstance beast = player.m_21124_((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get());
         return beast != null && beast.m_19564_() >= 1;
      }
   }

   public static boolean noInteractiveMode(LivingEntity entity) {
      return noInteractiveMode(entity, false);
   }

   public static boolean noInteractiveMode(LivingEntity entity, boolean takeDamage) {
      if (InsanityEffect.havingNightmare(entity)) {
         return !takeDamage;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.BATS_MODE.get())) {
         return !takeDamage;
      } else {
         return entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get());
      }
   }

   public static boolean isProjectileAlwaysHit(Projectile projectile) {
      if (projectile instanceof BreathEntity) {
         return false;
      } else {
         return projectile instanceof BarrierEntity ? false : projectile instanceof AreaField;
      }
   }

   public static boolean canNegateCritChance(Entity entity) {
      return isSkillToggled(entity, (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get());
   }

   public static boolean canNegateDodge(LivingEntity entity, DamageSource source) {
      Entity var3 = source.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (living == SkillHelper.getSubordinateOwner(entity)) {
            return true;
         } else if (isSkillToggled(living, (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get()) && !isSkillToggled(entity, (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get())) {
            return true;
         } else if (isSkillToggled(entity, (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get()) && !isSkillToggled(living, (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get())) {
            return false;
         } else if (entity.m_21023_((MobEffect)TensuraMobEffects.FUTURE_VISION.get())) {
            return false;
         } else if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.COOK.get())) {
            return true;
         } else if (!TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.COMMANDER.get()) && !TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.MATHEMATICIAN.get()) && !TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.SNIPER.get())) {
            return isSkillToggled(living, (ManasSkill)ExtraSkills.HEAVENLY_EYE.get()) ? living.m_217043_().m_188499_() : false;
         } else {
            return living.m_217043_().m_188499_();
         }
      } else {
         return false;
      }
   }

   public static boolean reducingResistances(LivingEntity living) {
      if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.SNIPER.get())) {
         return true;
      } else if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.MATHEMATICIAN.get())) {
         return true;
      } else {
         return TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.COOK.get()) ? true : TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.COMMANDER.get());
      }
   }

   public static boolean haveSeveranceAttack(DamageSource damageSource, LivingEntity target) {
      if (PhysicalAttackNullification.ogreBerserkerResist(damageSource, target)) {
         return false;
      } else if (isSkillMastered(target, (ManasSkill)UniqueSkills.SUPPRESSOR.get())) {
         return false;
      } else {
         Entity var3 = damageSource.m_7640_();
         if (var3 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var3;
            if (living.m_21205_().getEnchantmentLevel((Enchantment)TensuraEnchantments.SEVERANCE.get()) > 0) {
               return true;
            }

            if ((living.m_21023_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get()) || living.m_21023_((MobEffect)TensuraMobEffects.MAGIC_SPACE.get())) && DamageSourceHelper.isPhysicalAttack(damageSource) && damageSource.m_7640_() == living) {
               return true;
            }
         }

         boolean var10000;
         if (damageSource instanceof TensuraDamageSource) {
            TensuraDamageSource source = (TensuraDamageSource)damageSource;
            if (source.isSpatial()) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }

   static {
      misfireEffect = List.of(MobEffects.f_19597_, MobEffects.f_19604_, MobEffects.f_19613_, MobEffects.f_19610_, (MobEffect)TensuraMobEffects.FRAGILITY.get(), (MobEffect)TensuraMobEffects.INSANITY.get(), (MobEffect)TensuraMobEffects.PARALYSIS.get());
   }
}
