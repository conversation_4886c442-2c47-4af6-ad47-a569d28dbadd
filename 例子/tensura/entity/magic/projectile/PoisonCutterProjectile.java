package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class PoisonCutterProjectile extends WaterBallProjectile {
   public PoisonCutterProjectile(EntityType<? extends PoisonCutterProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(1.5F);
   }

   public PoisonCutterProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.POISON_CUTTER.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(1.5F);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_6.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_7.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_8.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_9.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_10.png"), new ResourceLocation("tensura", "textures/entity/projectiles/poison_blade/poison_blade_11.png")};
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.POISON_EFFECT.get(), x, y, z, 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
   }

   public void flyingParticles() {
   }
}
