package com.github.manasmods.tensura.capability.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.util.INBTSerializable;
import org.jetbrains.annotations.Nullable;

public interface ITensuraSkillCapability extends INBTSerializable<CompoundTag> {
   int getSpiritCooldown();

   void setSpiritCooldown(int var1);

   int getSpiritLevel(int var1);

   boolean setSpiritLevel(Player var1, int var2, int var3);

   void clearSpiritLevel();

   int getActivePreset();

   void setActivePreset(int var1);

   List<String> getPresetNames();

   String getPresetName(int var1);

   void setPresetName(int var1, String var2);

   int getAnalysisLevel();

   void setAnalysisLevel(int var1);

   int getAnalysisMode();

   void setAnalysisMode(int var1);

   int getAnalysisDistance();

   void setAnalysisDistance(int var1);

   @Nullable
   ManasSkill getSkillInSlot(int var1);

   @Nullable
   ManasSkill getSkillInPresetSlot(int var1, int var2);

   boolean isSkillInSlots(ManasSkill var1);

   boolean isSkillInSpecificSlot(ManasSkill var1, int var2);

   void setInstanceInSlot(ManasSkillInstance var1, int var2);

   void setInstanceInPresetSlot(@Nullable ManasSkillInstance var1, int var2, int var3);

   boolean removeSkillFromSlots(ManasSkill var1);

   String getWarpName(int var1);

   void setWarpName(int var1, String var2);

   double getWarpX(int var1);

   double getWarpY(int var1);

   double getWarpZ(int var1);

   void setWarp(int var1, double var2, double var4, double var6);

   void clearAllWarp();

   double getWaterPoint();

   void setWaterPoint(double var1);

   double getLavaPoint();

   void setLavaPoint(double var1);
}
