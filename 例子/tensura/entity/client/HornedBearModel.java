package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HornedBearEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class HornedBearModel extends AnimatedGeoModel<HornedBearEntity> {
   public ResourceLocation getModelResource(HornedBearEntity object) {
      return new ResourceLocation("tensura", "geo/horned_bear.geo.json");
   }

   public ResourceLocation getTextureResource(HornedBearEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/bear/horned_bear.png");
   }

   public ResourceLocation getAnimationResource(HornedBearEntity bear) {
      return new ResourceLocation("tensura", "animations/horned_bear.animation.json");
   }

   public void setCustomAnimations(HornedBearEntity bear, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bear, instanceId, customPredicate);
      IBone horn = this.getAnimationProcessor().getBone("horn");
      if (bear.m_6162_() != horn.isHidden()) {
         horn.setHidden(bear.m_6162_());
      }

      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (bear.isChested() == chest.isHidden()) {
         chest.setHidden(!bear.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (bear.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!bear.isSaddled());
      }

      IBone collar = this.getAnimationProcessor().getBone("Collar");
      if (!collar.isHidden() || !bear.isSaddled() && !bear.isChested()) {
         if (!collar.isHidden() && !bear.isSaddled() && !bear.isChested()) {
            collar.setHidden(true);
         }
      } else {
         collar.setHidden(false);
      }

      if (bear.getMiscAnimation() == 0) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("head");
         if (head != null) {
            float pitch = bear.m_21825_() ? extraData.headPitch - 35.0F : extraData.headPitch;
            head.setRotationX(pitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
