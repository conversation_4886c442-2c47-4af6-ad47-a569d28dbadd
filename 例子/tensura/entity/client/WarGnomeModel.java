package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.WarGnomeEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class WarGnomeModel extends AnimatedGeoModel<WarGnomeEntity> {
   public ResourceLocation getModelResource(WarGnomeEntity object) {
      return new ResourceLocation("tensura", "geo/war_gnome.geo.json");
   }

   public ResourceLocation getTextureResource(WarGnomeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/war_gnome/war_gnome.png");
   }

   public ResourceLocation getAnimationResource(WarGnomeEntity warGnome) {
      return new ResourceLocation("tensura", "animations/war_gnome.animation.json");
   }

   public void setCustomAnimations(WarGnomeEntity gnome, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(gnome, instanceId, customPredicate);
      IBone sword = this.getAnimationProcessor().getBone("Sword");
      if (sword.isHidden() == gnome.m_21205_().m_41619_()) {
         sword.setHidden(!gnome.m_21205_().m_41619_());
      }

      IBone shield = this.getAnimationProcessor().getBone("Shield");
      if (shield.isHidden() == gnome.m_21206_().m_41619_()) {
         shield.setHidden(!gnome.m_21206_().m_41619_());
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
         head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
      }

   }
}
