package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.entity.human.PlayerLikeEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.GoblinVariant;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraftforge.common.ForgeMod;

public class GoblinEntity extends PlayerLikeEntity implements IRanking {
   private static final EntityDataAccessor<Integer> EVOLUTION_STATE;
   private static final EntityDataAccessor<Integer> GENDER;
   private static final EntityDataAccessor<Integer> SKIN;
   private static final EntityDataAccessor<Integer> FACE;
   private static final EntityDataAccessor<Integer> HAIR;
   private static final EntityDataAccessor<Integer> CLOTHING;
   private static final EntityDataAccessor<Boolean> BANDAGES;
   public static final EntityDataAccessor<Integer> HEAD;
   public static final EntityDataAccessor<Integer> HEAD_COLOR;
   private static final EntityDataAccessor<Integer> TOP;
   private static final EntityDataAccessor<Integer> TOP_COLOR;
   private static final EntityDataAccessor<Integer> BOTTOM;
   private static final EntityDataAccessor<Integer> BOTTOM_COLOR;

   public GoblinEntity(EntityType<? extends GoblinEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 5;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 1.0D).m_22268_(Attributes.f_22276_, 12.0D).m_22268_(Attributes.f_22279_, 0.15000000596046448D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 1.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.2D, GoblinEntity.class));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{GoblinEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(EVOLUTION_STATE, 0);
      this.f_19804_.m_135372_(GENDER, 0);
      this.f_19804_.m_135372_(SKIN, 0);
      this.f_19804_.m_135372_(FACE, 0);
      this.f_19804_.m_135372_(HAIR, 0);
      this.f_19804_.m_135372_(CLOTHING, 0);
      this.f_19804_.m_135372_(BANDAGES, false);
      this.f_19804_.m_135372_(HEAD, 0);
      this.f_19804_.m_135372_(HEAD_COLOR, 0);
      this.f_19804_.m_135372_(TOP, 0);
      this.f_19804_.m_135372_(TOP_COLOR, 0);
      this.f_19804_.m_135372_(BOTTOM, 0);
      this.f_19804_.m_135372_(BOTTOM_COLOR, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("EvoState", this.getCurrentEvolutionState());
      compound.m_128405_("Gender", (Integer)this.f_19804_.m_135370_(GENDER));
      compound.m_128405_("Skin", (Integer)this.f_19804_.m_135370_(SKIN));
      compound.m_128405_("Face", (Integer)this.f_19804_.m_135370_(FACE));
      compound.m_128405_("Hair", (Integer)this.f_19804_.m_135370_(HAIR));
      compound.m_128405_("Clothing", (Integer)this.f_19804_.m_135370_(CLOTHING));
      compound.m_128379_("Bandages", (Boolean)this.f_19804_.m_135370_(BANDAGES));
      compound.m_128405_("Head", (Integer)this.f_19804_.m_135370_(HEAD));
      compound.m_128405_("HeadColor", (Integer)this.f_19804_.m_135370_(HEAD_COLOR));
      compound.m_128405_("Top", (Integer)this.f_19804_.m_135370_(TOP));
      compound.m_128405_("TopColor", (Integer)this.f_19804_.m_135370_(TOP_COLOR));
      compound.m_128405_("Bottom", (Integer)this.f_19804_.m_135370_(BOTTOM));
      compound.m_128405_("BottomColor", (Integer)this.f_19804_.m_135370_(BOTTOM_COLOR));
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setCurrentEvolutionState(compound.m_128451_("EvoState"));
      this.f_19804_.m_135381_(GENDER, compound.m_128451_("Gender"));
      this.f_19804_.m_135381_(SKIN, compound.m_128451_("Skin"));
      this.f_19804_.m_135381_(FACE, compound.m_128451_("Face"));
      this.f_19804_.m_135381_(HAIR, compound.m_128451_("Hair"));
      this.f_19804_.m_135381_(CLOTHING, compound.m_128451_("Clothing"));
      this.f_19804_.m_135381_(BANDAGES, compound.m_128471_("Bandages"));
      this.f_19804_.m_135381_(HEAD, compound.m_128451_("Head"));
      this.f_19804_.m_135381_(HEAD_COLOR, compound.m_128451_("HeadColor"));
      this.f_19804_.m_135381_(TOP, compound.m_128451_("TopColor"));
      this.f_19804_.m_135381_(TOP_COLOR, compound.m_128451_("Top"));
      this.f_19804_.m_135381_(BOTTOM, compound.m_128451_("Bottom"));
      this.f_19804_.m_135381_(BOTTOM_COLOR, compound.m_128451_("BottomColor"));
   }

   public GoblinVariant.Gender getGender() {
      return GoblinVariant.Gender.byId((Integer)this.f_19804_.m_135370_(GENDER));
   }

   public void setGender(int gender) {
      this.f_19804_.m_135381_(GENDER, gender);
   }

   public GoblinVariant.Skin getSkin() {
      return GoblinVariant.Skin.byId((Integer)this.f_19804_.m_135370_(SKIN));
   }

   public void setSkin(int skin) {
      this.f_19804_.m_135381_(SKIN, skin);
   }

   public GoblinVariant.Face getFace() {
      return GoblinVariant.Face.byId((Integer)this.f_19804_.m_135370_(FACE));
   }

   public void setFace(int face) {
      this.f_19804_.m_135381_(FACE, face);
   }

   public GoblinVariant.Hair getHair() {
      return GoblinVariant.Hair.byId((Integer)this.f_19804_.m_135370_(HAIR));
   }

   public void setHair(int hair) {
      this.f_19804_.m_135381_(HAIR, hair);
   }

   public GoblinVariant.Clothing getClothing() {
      return GoblinVariant.Clothing.byId((Integer)this.f_19804_.m_135370_(CLOTHING));
   }

   public void setClothing(int clothing) {
      this.f_19804_.m_135381_(CLOTHING, clothing);
   }

   public GoblinVariant.Head getHead() {
      return GoblinVariant.Head.byId((Integer)this.f_19804_.m_135370_(HEAD));
   }

   public void setHead(int head) {
      this.f_19804_.m_135381_(HEAD, head);
   }

   public int getHeadColor() {
      return (Integer)this.f_19804_.m_135370_(HEAD_COLOR);
   }

   public void setHeadColor(int i) {
      this.f_19804_.m_135381_(HEAD_COLOR, i);
   }

   public GoblinVariant.Top getTop() {
      return GoblinVariant.Top.byId((Integer)this.f_19804_.m_135370_(TOP));
   }

   public void setTop(int top) {
      this.f_19804_.m_135381_(TOP, top);
   }

   public int getTopColor() {
      return (Integer)this.f_19804_.m_135370_(TOP_COLOR);
   }

   public void setTopColor(int i) {
      this.f_19804_.m_135381_(TOP_COLOR, i);
   }

   public GoblinVariant.Bottom getBottom() {
      return GoblinVariant.Bottom.byId((Integer)this.f_19804_.m_135370_(BOTTOM));
   }

   public void setBottom(int bottom) {
      this.f_19804_.m_135381_(BOTTOM, bottom);
   }

   public int getBottomColor() {
      return (Integer)this.f_19804_.m_135370_(BOTTOM_COLOR);
   }

   public void setBottomColor(int i) {
      this.f_19804_.m_135381_(BOTTOM_COLOR, i);
   }

   public boolean hasBandages() {
      return (Boolean)this.f_19804_.m_135370_(BANDAGES);
   }

   public void setBandages(boolean bandages) {
      this.f_19804_.m_135381_(BANDAGES, bandages);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (EVOLUTION_STATE.equals(pKey)) {
         this.m_20090_();
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      if (pOtherAnimal == this) {
         return false;
      } else if (pOtherAnimal.getClass() != this.getClass()) {
         return false;
      } else {
         return this.m_27593_() && pOtherAnimal.m_27593_() && ((GoblinEntity)pOtherAnimal).getGender() != this.getGender();
      }
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      GoblinEntity baby = (GoblinEntity)((EntityType)TensuraEntityTypes.GOBLIN.get()).m_20615_(pLevel);
      if (baby == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            baby.m_21816_(uuid);
            baby.m_7105_(true);
         }

         baby.randomTexture();
         if (this.isHobgoblin() && ((GoblinEntity)pOtherParent).isHobgoblin()) {
            baby.evolve();
         }

         return baby;
      }
   }

   public float m_6134_() {
      float multiplier = this.isHobgoblin() ? 1.3333334F : 1.0F;
      return multiplier * (this.m_6162_() ? 0.5F : 1.0F);
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   public boolean isHobgoblin() {
      return this.getCurrentEvolutionState() >= 1;
   }

   public int getCurrentEvolutionState() {
      return (Integer)this.f_19804_.m_135370_(EVOLUTION_STATE);
   }

   public void setCurrentEvolutionState(int state) {
      this.f_19804_.m_135381_(EVOLUTION_STATE, state);
   }

   public void evolve() {
      int current = this.getCurrentEvolutionState();
      if (current < this.getMaxEvolutionState()) {
         this.setCurrentEvolutionState(current + 1);
         this.gainMovementSpeed(this, 0.05D);
         this.gainSwimSpeed(this, 1.0D);
         this.gainMaxHealth(this, 10.0D);
      }

   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack stack) {
      if (this.m_6898_(stack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               stack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, stack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (!this.m_6162_() && this.m_5957_()) {
            this.m_142075_(player, hand, stack);
            this.m_27595_(player);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
         this.randomTexture();
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   private void randomTexture() {
      this.setGender(this.f_19796_.m_188499_() ? 0 : 1);
      this.setSkin(GoblinVariant.Skin.getRandom(this));
      this.setFace(GoblinVariant.Face.getRandom(this.getGender(), this));
      this.setHair(GoblinVariant.Hair.getRandom(this));
      this.setClothing(GoblinVariant.Clothing.getRandom(this));
      this.setBandages(this.f_19796_.m_188499_());
      List<? extends Integer> colors = (List)TensuraConfig.INSTANCE.entitiesConfig.goblinClothingColors.get();
      this.setHead(this.f_19796_.m_188499_() ? -1 : GoblinVariant.Head.getRandom(this));
      this.setHeadColor((Integer)colors.get(this.f_19796_.m_188503_(colors.size())));
      this.setTop(GoblinVariant.Top.getRandom(this));
      this.setTopColor((Integer)colors.get(this.f_19796_.m_188503_(colors.size())));
      this.setBottom(GoblinVariant.Bottom.getRandom(this));
      this.setBottomColor((Integer)colors.get(this.f_19796_.m_188503_(colors.size())));
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.goblinSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      if (!(pRandom.m_188501_() >= 0.5F)) {
         int i = pRandom.m_188503_(3);
         ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.GOBLIN_CLUB.get());
         if (i == 0) {
            stack = new ItemStack((ItemLike)TensuraToolItems.STONE_SHORT_SWORD.get());
         }

         this.m_8061_(EquipmentSlot.MAINHAND, stack);
         this.inventory.m_6836_(4, stack);
         this.inventory.m_6596_();
      }
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      Item var10000;
      switch(pSlot) {
      case HEAD:
         var10000 = pChance == 2 ? Items.f_42407_ : null;
         break;
      case CHEST:
         var10000 = pChance == 2 ? Items.f_42408_ : (pChance == 4 ? Items.f_42465_ : null);
         break;
      case LEGS:
         var10000 = pChance == 2 ? Items.f_42462_ : (pChance == 4 ? Items.f_42466_ : null);
         break;
      case FEET:
         var10000 = pChance == 2 ? Items.f_42463_ : (pChance == 4 ? Items.f_42467_ : null);
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   static {
      EVOLUTION_STATE = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      GENDER = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      SKIN = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      FACE = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      HAIR = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      CLOTHING = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      BANDAGES = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135035_);
      HEAD = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      HEAD_COLOR = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      TOP = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      TOP_COLOR = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      BOTTOM = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
      BOTTOM_COLOR = SynchedEntityData.m_135353_(GoblinEntity.class, EntityDataSerializers.f_135028_);
   }
}
