package com.github.manasmods.tensura.ability.magic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import net.minecraft.world.entity.LivingEntity;

public class MagicUltils {
   public static boolean hasChantAnnulment(LivingEntity entity) {
      return SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.CHANT_ANNULMENT.get()) ? true : SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.GREAT_SAGE.get());
   }

   public static float castingSpeedMultipiler(LivingEntity entity) {
      float speed = 1.0F;
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.THOUGHT_ACCELERATION.get())) {
         ++speed;
      }

      if (hasUniqueThoughtAcceleration(entity)) {
         speed += 3.0F;
      }

      return 1.0F / speed;
   }

   public static boolean hasUniqueThoughtAcceleration(LivingEntity entity) {
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.GREAT_SAGE.get())) {
         return true;
      } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.COMMANDER.get())) {
         return true;
      } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.MARTIAL_MASTER.get())) {
         return true;
      } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.MATHEMATICIAN.get())) {
         return true;
      } else {
         return SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.SHADOW_STRIKER.get()) ? true : SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.SUPPRESSOR.get());
      }
   }
}
