package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.magic.spike.SpikeEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;

public class SpikeRenderer extends EntityRenderer<SpikeEntity> {
   public static final ModelLayerLocation SPIKE = new ModelLayerLocation(new ResourceLocation("tensura", "spike"), "main");
   private final ModelPart spike;

   public SpikeRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(SPIKE);
      this.spike = modelpart.m_171324_("spike");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      PartDefinition spike = partdefinition.m_171599_("spike", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-8.0F, -64.0F, 0.0F, 16.0F, 64.0F, 0.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 0.0F, 0.0F, 3.1416F, 0.0F, 0.0F));
      spike.m_171599_("side_spike", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-8.0F, -64.0F, 0.0F, 16.0F, 64.0F, 0.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 0.0F, 0.0F, 0.0F, -1.5708F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 32, 64);
   }

   public void render(SpikeEntity spike, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !spike.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         float radius = (float)Math.min(spike.getTickCount(), spike.getExtendingTick()) / (float)spike.getExtendingTick();
         poseStack.m_85841_(1.0F, radius * spike.getHeight() / 4.0F, 1.0F);
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(spike)));
         this.spike.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         super.m_7392_(spike, yaw, partialTicks, poseStack, bufferSource, light);
      }
   }

   public ResourceLocation getTextureLocation(SpikeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/spike/earth_spike.png");
   }
}
