package com.github.manasmods.tensura.ability.magic.spiritual.water;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class WaterMagic extends SpiritualMagic {
   public WaterMagic() {
      super(MagicElemental.WATER, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 20;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         Level level = entity.m_9236_();
         if (!isWaterEvaporated(entity, level)) {
            if (TensuraGameRules.canSkillGrief(level)) {
               this.addMasteryPoint(instance, entity);
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 6.0D);
               if (result.m_6662_() != Type.ENTITY) {
                  if (SkillHelper.outOfMagicule(entity, instance)) {
                     return;
                  }

                  BlockPos pos = result.m_82425_();
                  BlockPos relative = pos.m_121945_(result.m_82434_());
                  BlockState relativeState = level.m_8055_(relative);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, relative);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  if (result.m_6662_() == Type.MISS && relativeState.m_60819_().m_192917_(Fluids.f_76193_)) {
                     level.m_7731_(relative, Blocks.f_50126_.m_49966_(), 11);
                     level.m_142346_(entity, GameEvent.f_157797_, relative);
                     this.addMasteryPoint(instance, entity);
                  } else if (result.m_6662_() == Type.BLOCK) {
                     if (level.m_8055_(pos).m_60713_(Blocks.f_50126_)) {
                        level.m_7731_(pos, Blocks.f_49990_.m_49966_(), 11);
                        level.m_142346_(entity, GameEvent.f_157797_, relative);
                        this.addMasteryPoint(instance, entity);
                     } else if (relativeState.m_60819_().m_192917_(Fluids.f_76193_)) {
                        level.m_7731_(relative, Blocks.f_50126_.m_49966_(), 11);
                        level.m_142346_(entity, GameEvent.f_157797_, relative);
                        this.addMasteryPoint(instance, entity);
                     } else {
                        level.m_7731_(relative, Blocks.f_49990_.m_49966_(), 11);
                        level.m_142346_(entity, GameEvent.f_157797_, relative);
                        this.addMasteryPoint(instance, entity);
                     }
                  }

                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, relative));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_144205_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               }

            }
         }
      }
   }

   public static boolean isWaterEvaporated(LivingEntity entity, Level level) {
      if (!level.m_6042_().f_63857_()) {
         return false;
      } else {
         level.m_5594_((Player)null, entity.m_20097_().m_7494_(), SoundEvents.f_11937_, SoundSource.BLOCKS, 0.5F, 2.6F + (entity.m_217043_().m_188501_() - entity.m_217043_().m_188501_()) * 0.8F);
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123759_);
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123759_, 2.0D);
         return true;
      }
   }
}
