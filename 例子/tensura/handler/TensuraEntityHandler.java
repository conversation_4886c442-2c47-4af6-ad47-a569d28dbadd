package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.entity.AkashEntity;
import com.github.manasmods.tensura.entity.AquaFrogEntity;
import com.github.manasmods.tensura.entity.ArmoursaurusEntity;
import com.github.manasmods.tensura.entity.ArmyWaspEntity;
import com.github.manasmods.tensura.entity.BarghestEntity;
import com.github.manasmods.tensura.entity.BeastGnomeEntity;
import com.github.manasmods.tensura.entity.BlackSpiderEntity;
import com.github.manasmods.tensura.entity.BladeTigerEntity;
import com.github.manasmods.tensura.entity.BulldeerEntity;
import com.github.manasmods.tensura.entity.CharybdisEntity;
import com.github.manasmods.tensura.entity.DirewolfEntity;
import com.github.manasmods.tensura.entity.DragonPeacockEntity;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.entity.FeatheredSerpentEntity;
import com.github.manasmods.tensura.entity.GiantAntEntity;
import com.github.manasmods.tensura.entity.GiantBatEntity;
import com.github.manasmods.tensura.entity.GiantBearEntity;
import com.github.manasmods.tensura.entity.GiantCodEntity;
import com.github.manasmods.tensura.entity.GiantSalmonEntity;
import com.github.manasmods.tensura.entity.GoblinEntity;
import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.entity.HolyCowEntity;
import com.github.manasmods.tensura.entity.HornedBearEntity;
import com.github.manasmods.tensura.entity.HornedRabbitEntity;
import com.github.manasmods.tensura.entity.HoundDogEntity;
import com.github.manasmods.tensura.entity.HoverLizardEntity;
import com.github.manasmods.tensura.entity.IfritCloneEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.KnightSpiderEntity;
import com.github.manasmods.tensura.entity.LandfishEntity;
import com.github.manasmods.tensura.entity.LeechLizardEntity;
import com.github.manasmods.tensura.entity.LizardmanEntity;
import com.github.manasmods.tensura.entity.MegalodonEntity;
import com.github.manasmods.tensura.entity.MetalSlimeEntity;
import com.github.manasmods.tensura.entity.OneEyedOwlEntity;
import com.github.manasmods.tensura.entity.OrcDisasterEntity;
import com.github.manasmods.tensura.entity.OrcEntity;
import com.github.manasmods.tensura.entity.OrcLordEntity;
import com.github.manasmods.tensura.entity.SalamanderEntity;
import com.github.manasmods.tensura.entity.SissieEntity;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.entity.SpearToroEntity;
import com.github.manasmods.tensura.entity.SupermassiveSlimeEntity;
import com.github.manasmods.tensura.entity.SylphideEntity;
import com.github.manasmods.tensura.entity.UndineEntity;
import com.github.manasmods.tensura.entity.UnicornEntity;
import com.github.manasmods.tensura.entity.WarGnomeEntity;
import com.github.manasmods.tensura.entity.WingedCatEntity;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.entity.human.FalmuthKnightEntity;
import com.github.manasmods.tensura.entity.human.FolgenEntity;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.human.KiraraMizutaniEntity;
import com.github.manasmods.tensura.entity.human.KyoyaTachibanaEntity;
import com.github.manasmods.tensura.entity.human.MaiFurukiEntity;
import com.github.manasmods.tensura.entity.human.MarkLaurenEntity;
import com.github.manasmods.tensura.entity.human.ShinRyuseiEntity;
import com.github.manasmods.tensura.entity.human.ShinjiTanimuraEntity;
import com.github.manasmods.tensura.entity.human.ShizuEntity;
import com.github.manasmods.tensura.entity.human.ShogoTaguchiEntity;
import com.github.manasmods.tensura.entity.magic.barrier.MagicEngineBarrierEntity;
import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import com.github.manasmods.tensura.entity.multipart.EvilCentipedeEntity;
import com.github.manasmods.tensura.entity.multipart.TempestSerpentEntity;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.util.List;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.SpawnPlacements.Type;
import net.minecraft.world.entity.animal.WaterAnimal;
import net.minecraft.world.level.levelgen.Heightmap.Types;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.event.entity.EntityAttributeCreationEvent;
import net.minecraftforge.event.entity.SpawnPlacementRegisterEvent;
import net.minecraftforge.event.entity.SpawnPlacementRegisterEvent.Operation;
import net.minecraftforge.event.entity.living.LivingSpawnEvent.CheckSpawn;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.eventbus.api.Event.Result;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraEntityHandler {
   public static void entityAttributeEvent(EntityAttributeCreationEvent event) {
      event.put((EntityType)TensuraEntityTypes.MAD_OGRE_ORBS.get(), MadOgreOrbsEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.CLONE_DEFAULT.get(), CloneEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.CLONE_SLIM.get(), CloneEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.FALMUTH_KNIGHT.get(), FalmuthKnightEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.FOLGEN.get(), FolgenEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HINATA_SAKAGUCHI.get(), HinataSakaguchiEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.KIRARA_MIZUTANI.get(), KiraraMizutaniEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.KYOYA_TACHIBANA.get(), KyoyaTachibanaEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.MAI_FURUKI.get(), MaiFurukiEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.MARK_LAUREN.get(), MarkLaurenEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SHINJI_TANIMURA.get(), ShinjiTanimuraEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SHIN_RYUSEI.get(), ShinRyuseiEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SHIZU.get(), ShizuEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SHOGO_TAGUCHI.get(), ShogoTaguchiEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.AKASH.get(), AkashEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.AQUA_FROG.get(), AquaFrogEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ARMOURSAURUS.get(), ArmoursaurusEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ARMY_WASP.get(), ArmyWaspEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.BARGHEST.get(), BarghestEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.BEAST_GNOME.get(), BeastGnomeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.BLACK_SPIDER.get(), BlackSpiderEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.BLADE_TIGER.get(), BladeTigerEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.BULLDEER.get(), BulldeerEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.CHARYBDIS.get(), CharybdisEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.DIREWOLF.get(), DirewolfEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.DRAGON_PEACOCK.get(), DragonPeacockEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get(), ElementalColossusEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE.get(), EvilCentipedeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE_BODY.get(), EvilCentipedeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get(), FeatheredSerpentEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GIANT_ANT.get(), GiantAntEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GIANT_BAT.get(), GiantBatEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GIANT_BEAR.get(), GiantBearEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GIANT_COD.get(), GiantCodEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GIANT_SALMON.get(), GiantSalmonEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.GOBLIN.get(), GoblinEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HELL_CATERPILLAR.get(), HellCaterpillarEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HELL_MOTH.get(), HellMothEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HOLY_COW.get(), HolyCowEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HORNED_RABBIT.get(), HornedRabbitEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HOUND_DOG.get(), HoundDogEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HOVER_LIZARD.get(), HoverLizardEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.HORNED_BEAR.get(), HornedBearEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.IFRIT.get(), IfritEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.IFRIT_CLONE.get(), IfritCloneEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.KNIGHT_SPIDER.get(), KnightSpiderEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.LANDFISH.get(), LandfishEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.LEECH_LIZARD.get(), LeechLizardEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.LIZARDMAN.get(), LizardmanEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.MEGALODON.get(), MegalodonEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ONE_EYED_OWL.get(), OneEyedOwlEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ORC.get(), OrcEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ORC_DISASTER.get(), OrcDisasterEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.ORC_LORD.get(), OrcLordEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SISSIE.get(), SissieEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SALAMANDER.get(), SalamanderEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SLIME.get(), SlimeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SUPERMASSIVE_SLIME.get(), SupermassiveSlimeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.METAL_SLIME.get(), MetalSlimeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SPEAR_TORO.get(), SpearToroEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.SYLPHIDE.get(), SylphideEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.TEMPEST_SERPENT.get(), TempestSerpentEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.TEMPEST_SERPENT_BODY.get(), TempestSerpentEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.UNDINE.get(), UndineEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.UNICORN.get(), UnicornEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.WAR_GNOME.get(), WarGnomeEntity.setAttributes());
      event.put((EntityType)TensuraEntityTypes.WINGED_CAT.get(), WingedCatEntity.setAttributes());
   }

   public static void registerEntityPlacements(SpawnPlacementRegisterEvent e) {
      e.register((EntityType)TensuraEntityTypes.FALMUTH_KNIGHT.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.KYOYA_TACHIBANA.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.AQUA_FROG.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.ARMOURSAURUS.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.ARMY_WASP.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING_NO_LEAVES, FLyingTamableEntity::checkFlyingSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.BARGHEST.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.BEAST_GNOME.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.BLACK_SPIDER.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, BlackSpiderEntity::checkSpiderSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.BLADE_TIGER.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileGrassMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.BULLDEER.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkGrassMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.DIREWOLF.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileSandMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.DRAGON_PEACOCK.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING, DragonPeacockEntity::checkPeacockSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING_NO_LEAVES, FLyingTamableEntity::checkFlyingSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GIANT_ANT.get(), Type.ON_GROUND, Types.MOTION_BLOCKING, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GIANT_BAT.get(), Type.ON_GROUND, Types.MOTION_BLOCKING, GiantBatEntity::checkBatSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GIANT_BEAR.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkGrassMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GIANT_COD.get(), Type.IN_WATER, Types.MOTION_BLOCKING_NO_LEAVES, WaterAnimal::m_218282_, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GIANT_SALMON.get(), Type.IN_WATER, Types.MOTION_BLOCKING_NO_LEAVES, WaterAnimal::m_218282_, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.GOBLIN.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HELL_CATERPILLAR.get(), Type.ON_GROUND, Types.MOTION_BLOCKING, HellCaterpillarEntity::checkCaterpillarSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HELL_MOTH.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING, HellMothEntity::checkMothSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HORNED_BEAR.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileGrassMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HORNED_RABBIT.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkSandMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HOUND_DOG.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.HOVER_LIZARD.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkGrassMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.KNIGHT_SPIDER.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileSandMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.LANDFISH.get(), Type.IN_WATER, Types.MOTION_BLOCKING_NO_LEAVES, LandfishEntity::checkLandfishSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.LEECH_LIZARD.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileSandMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.LIZARDMAN.get(), Type.ON_GROUND, Types.MOTION_BLOCKING, LizardmanEntity::checkLizardSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.MEGALODON.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING_NO_LEAVES, MegalodonEntity::checkMegalodonSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.ONE_EYED_OWL.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING, OneEyedOwlEntity::checkOwlSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.ORC.get(), Type.ON_GROUND, Types.MOTION_BLOCKING, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.SISSIE.get(), Type.IN_WATER, Types.MOTION_BLOCKING_NO_LEAVES, SissieEntity::checkSissieSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.SALAMANDER.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.SLIME.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkTensuraMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.SPEAR_TORO.get(), Type.IN_WATER, Types.MOTION_BLOCKING_NO_LEAVES, SpearToroEntity::checkSpearToroSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.TEMPEST_SERPENT.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, TensuraTamableEntity::checkHostileMobSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.UNICORN.get(), Type.ON_GROUND, Types.MOTION_BLOCKING_NO_LEAVES, UnicornEntity::checkUnicornSpawnRules, Operation.AND);
      e.register((EntityType)TensuraEntityTypes.WINGED_CAT.get(), Type.NO_RESTRICTIONS, Types.MOTION_BLOCKING_NO_LEAVES, WingedCatEntity::checkWingedCatSpawnRules, Operation.AND);
   }

   @SubscribeEvent
   public static void onCheckSpawn(CheckSpawn e) {
      if (e.getResult() == Result.DEFAULT) {
         LivingEntity entity = e.getEntity();
         List<MagicEngineBarrierEntity> list = e.getLevel().m_45976_(MagicEngineBarrierEntity.class, (new AABB(entity.m_20183_())).m_82400_(25.0D));
         if (!list.isEmpty()) {
            e.setResult(Result.DENY);
         }

      }
   }
}
