package com.github.b4ndithelps.tennogamenolife.entity;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;
import net.minecraft.server.level.ServerLevel;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class HurtDummyEntity extends Mob {
    private int animationTicks = 0;
    private boolean isPlayingAnimation = false;
    private static final int ANIMATION_DURATION = 4;
    
    public HurtDummyEntity(EntityType<? extends Mob> entityType, Level level) {
        super(entityType, level);
        this.setNoAi(true);
        this.setInvulnerable(true);
        this.setHealth(this.getMaxHealth());
        
        this.hurtTime = 0;
        this.hurtDuration = 0;
    }
    
    @Override
    public void baseTick() {
        int prevHurtTime = this.hurtTime;
        super.baseTick();
        
        this.hurtTime = 0;
        this.hurtDuration = 0;
    }
    
    public static AttributeSupplier.Builder createAttributes() {
        return Mob.createMobAttributes()
                .add(Attributes.MAX_HEALTH, 20.0D)
                .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D)
                .add(Attributes.MOVEMENT_SPEED, 0.0D);
    }
    
    public boolean isPlayingAnimation() {
        return isPlayingAnimation;
    }
    
    public float getAnimationProgress() {
        if (!isPlayingAnimation) return 0.0F;
        return (float) animationTicks / ANIMATION_DURATION;
    }
    
    public void triggerAnimation() {
        isPlayingAnimation = true;
        animationTicks = 0;
    }

    @Override
    public MobType getMobType() {
        return MobType.UNDEFINED;
    }

    @Override
    public boolean isPushable() {
        return false;
    }

    @Override
    protected void pushEntities() {
    }

    @Override
    public boolean canBeCollidedWith() {
        return true;
    }

    @Override
    public void tick() {
        this.hurtTime = 0;
        this.hurtDuration = 0;
        
        super.tick();
        
        if (isPlayingAnimation) {
            animationTicks++;
            if (animationTicks >= ANIMATION_DURATION) {
                isPlayingAnimation = false;
                animationTicks = 0;
            }
        }
        
        if (!this.getLevel().isClientSide()) {
            BlockPos belowPos = new BlockPos(this.getX(), this.getY() - 0.01D, this.getZ());
            BlockState belowState = this.getLevel().getBlockState(belowPos);
            
            if (belowState.isAir() || !belowState.getMaterial().isSolid()) {
                this.getLevel().playSound(null, this.blockPosition(), SoundEvents.ARMOR_STAND_BREAK, SoundSource.BLOCKS, 0.75F, 0.8F);
                
                this.spawnAtLocation(Tenreincarnationitems.HURT_DUMMY_ITEM.get());
                
                this.discard();
            }
        }
    }

    @Override
    public InteractionResult interactAt(Player player, Vec3 hitVec, InteractionHand hand) {
        if (player.isShiftKeyDown() && hand == InteractionHand.MAIN_HAND) {
            if (!this.getLevel().isClientSide()) {
                this.getLevel().playSound(null, this.blockPosition(), SoundEvents.ARMOR_STAND_BREAK, SoundSource.BLOCKS, 0.75F, 0.8F);
                if (!player.getAbilities().instabuild) {
                    this.spawnAtLocation(Tenreincarnationitems.HURT_DUMMY_ITEM.get());
                }

                this.discard();
                
                return InteractionResult.SUCCESS;
            }
            return InteractionResult.SUCCESS;
        }
        
        return super.interactAt(player, hitVec, hand);
    }

    private void spawnEnchantedHitParticles() {
        if (this.getLevel() instanceof ServerLevel serverLevel) {
            double x = this.getX();
            double y = this.getY() + this.getBbHeight() * 0.5;
            double z = this.getZ();
            
            for (int i = 0; i < 12; i++) {
                double offsetX = (this.random.nextDouble() - 0.5) * 0.5;
                double offsetY = (this.random.nextDouble() - 0.5) * 0.5;
                double offsetZ = (this.random.nextDouble() - 0.5) * 0.5;
                
                serverLevel.sendParticles(
                    ParticleTypes.ENCHANTED_HIT,
                    x + offsetX,
                    y + offsetY,
                    z + offsetZ,
                    1,
                    0.0, 0.0, 0.0,
                    0.1
                );
            }
        }
    }

    @Override
    public boolean hurt(DamageSource source, float amount) {
        if (source.getEntity() instanceof Player player) {
            this.hurtTime = 0;
            this.hurtDuration = 0;
            
            triggerAnimation();
            
            if (!this.level.isClientSide()) {
                double roundedDamage = new BigDecimal(amount).setScale(1, RoundingMode.HALF_UP).doubleValue();
                player.sendSystemMessage(Component.translatable("message." + Tenreincarnation.MODID + ".damage_dealt", 
                        String.format("%.1f", roundedDamage)));
            }
            
            this.getLevel().playSound(null, this.blockPosition(), SoundEvents.ARMOR_STAND_HIT, SoundSource.NEUTRAL, 0.5F, 1.0F);
            
            spawnEnchantedHitParticles();
        }

        this.hurtTime = 0;
        this.hurtDuration = 0;
        
        return false; 
    }
    
    @Override
    public boolean isCurrentlyGlowing() {
        return false;
    }
    
    @Override
    public boolean skipAttackInteraction(net.minecraft.world.entity.Entity attacker) {
        return false;
    }
    
    @Override
    protected float tickHeadTurn(float yRot, float animSpeed) {
        this.yBodyRot = this.getYRot();
        
        this.hurtTime = 0;
        this.hurtDuration = 0;
        
        return animSpeed;
    }

    @Override
    public boolean isInvulnerable() {
        return true;
    }

    @Override
    public boolean isInvulnerableTo(DamageSource source) {
        return true;
    }

    @Override
    public boolean isDeadOrDying() {
        return false;
    }

    @Override
    protected float getStandingEyeHeight(Pose pose, EntityDimensions dimensions) {
        return 1.62F * 0.8F;
    }

    @Override
    public boolean canBeSeenByAnyone() {
        return true;
    }
    
    @Override
    public boolean canBeLeashed(Player player) {
        return false;
    }
    
    @Override
    protected boolean shouldDespawnInPeaceful() {
        return false;
    }
    
    @Override
    public boolean removeWhenFarAway(double distanceToClosestPlayer) {
        return false;
    }
    
    @Override
    public Iterable<ItemStack> getArmorSlots() {
        return java.util.Collections.emptyList();
    }
    
    @Override
    public ItemStack getItemBySlot(EquipmentSlot slot) {
        return ItemStack.EMPTY;
    }
    
    @Override
    public void setItemSlot(EquipmentSlot slot, ItemStack stack) {
    }
} 