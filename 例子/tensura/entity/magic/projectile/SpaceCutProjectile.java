package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class SpaceCutProjectile extends TensuraProjectile {
   public SpaceCutProjectile(EntityType<? extends SpaceCutProjectile> type, Level level) {
      super(type, level);
      this.setSize(1.5F);
   }

   public SpaceCutProjectile(Level worldIn, LivingEntity shooter) {
      this((EntityType)TensuraEntityTypes.SPACE_CUT.get(), worldIn);
      this.m_5602_(shooter);
      this.invis = true;
      this.setSize(1.5F);
   }

   public String getMagic() {
      return "tensura.space_attack";
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/space_cut.png")};
   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F)) {
         DamageSource damagesource = DamageSourceHelper.turnTensura(TensuraDamageSources.indirectElementalAttack(this.getMagic(), this, this.m_37282_(), this.getMpCost(), this.getSkill(), this.isSpiritAttack())).setSpatial();
         if (this.isSpiritAttack()) {
            target.m_6469_(damagesource, this.getDamage());
         } else {
            DamageSourceHelper.dealSplitElementalDamage(target, damagesource, 0.9F, this.getDamage());
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.empty();
   }

   public void flyingParticles() {
   }

   public void hitParticles(double x, double y, double z) {
   }
}
