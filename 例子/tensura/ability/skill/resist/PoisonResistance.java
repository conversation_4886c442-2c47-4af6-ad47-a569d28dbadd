package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

public class PoisonResistance extends ResistSkill {
   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isPoison(damageSource);
   }

   public int pointRequirement() {
      return 350;
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : List.of(MobEffects.f_19614_, MobEffects.f_19604_));
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.POISON_NULLIFICATION.get();
   }
}
