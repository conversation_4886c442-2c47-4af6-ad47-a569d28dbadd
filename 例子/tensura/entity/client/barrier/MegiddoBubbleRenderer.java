package com.github.manasmods.tensura.entity.client.barrier;

import com.github.manasmods.tensura.entity.magic.barrier.MegiddoBubbleEntity;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.blaze3d.vertex.VertexFormat.Mode;
import com.mojang.math.Matrix4f;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderStateShard;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.RenderType.CompositeState;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.level.LightLayer;
import net.minecraft.world.phys.Vec3;

public class MegiddoBubbleRenderer extends EntityRenderer<MegiddoBubbleEntity> {
   public static final ModelLayerLocation BUBBLE = new ModelLayerLocation(new ResourceLocation("tensura", "megiddo_bubble"), "main");
   private static final RenderType TRANSPARENT_LEASH;
   private final ModelPart bubble;

   public MegiddoBubbleRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(BUBBLE);
      this.bubble = modelpart.m_171324_("bubble");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      partdefinition.m_171599_("bubble", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-8.0F, -9.0F, -8.0F, 16.0F, 9.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 24.0F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 64, 25);
   }

   public void render(MegiddoBubbleEntity entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      if (!entity.getTargetPos().equals(Vec3.f_82478_)) {
         this.renderBeam(entity, partialTicks, poseStack, bufferSource, 0.025F, 1.0F);
         this.renderBeam(entity, partialTicks, poseStack, bufferSource, 0.1F, 0.15F);
      }

      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !entity.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         float radius = entity.getVisualRadius() * 2.0F;
         poseStack.m_85841_(radius, radius, radius);
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(entity)));
         this.bubble.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         super.m_7392_(entity, yaw, partialTicks, poseStack, bufferSource, light);
      }
   }

   public ResourceLocation getTextureLocation(MegiddoBubbleEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/barrier/megiddo_bubble.png");
   }

   private void renderBeam(MegiddoBubbleEntity bubble, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, float size, float alpha) {
      pMatrixStack.m_85836_();
      Vec3 targetPos = bubble.getTargetPos();
      double d0 = (double)(Mth.m_14179_(pPartialTicks, bubble.f_19859_, bubble.m_146908_()) * 0.017453292F) + 1.5707963267948966D;
      Vec3 startBeam = bubble.getStartBeamOffset().m_82546_(bubble.m_20182_());
      double d1 = Math.cos(d0) * startBeam.f_82481_ + Math.sin(d0) * startBeam.f_82479_;
      double d2 = Math.sin(d0) * startBeam.f_82481_ - Math.cos(d0) * startBeam.f_82479_;
      double d3 = Mth.m_14139_((double)pPartialTicks, bubble.f_19854_, bubble.m_20185_()) + d1;
      double d4 = Mth.m_14139_((double)pPartialTicks, bubble.f_19855_, bubble.m_20186_()) + startBeam.f_82480_;
      double d5 = Mth.m_14139_((double)pPartialTicks, bubble.f_19856_, bubble.m_20189_()) + d2;
      pMatrixStack.m_85837_(d1, startBeam.f_82480_, d2);
      float x = (float)(targetPos.f_82479_ - d3);
      float y = (float)(targetPos.f_82480_ - d4);
      float z = (float)(targetPos.f_82481_ - d5);
      int k = bubble.f_19853_.m_45517_(LightLayer.SKY, new BlockPos(startBeam));
      int l = bubble.f_19853_.m_45517_(LightLayer.SKY, new BlockPos(targetPos));
      float offset = Mth.m_14195_(x * x + z * z) * size;
      float zOff = z * offset;
      float xOff = x * offset;
      Matrix4f matrix4f = pMatrixStack.m_85850_().m_85861_();
      VertexConsumer vertexconsumer = pBuffer.m_6299_(TRANSPARENT_LEASH);

      int segment;
      for(segment = 0; segment <= 10; ++segment) {
         addVertexPair(vertexconsumer, matrix4f, x, y, z, 15, 15, k, l, 0.025F, zOff, xOff, segment, alpha);
      }

      for(segment = 10; segment >= 0; --segment) {
         addVertexPair(vertexconsumer, matrix4f, x, y, z, 5, 15, k, l, 0.0F, zOff, xOff, segment, alpha);
      }

      pMatrixStack.m_85849_();
   }

   private static void addVertexPair(VertexConsumer consumer, Matrix4f matrix4f, float x, float y, float z, int bulletLight, int holderLight, int bulletBright, int holderBright, float yOff, float xOff, float zOff, int segment, float alpha) {
      float f = (float)segment / 10.0F;
      int i = (int)Mth.m_14179_(f, (float)bulletLight, (float)holderLight);
      int j = (int)Mth.m_14179_(f, (float)bulletBright, (float)holderBright);
      int k = LightTexture.m_109885_(i, j);
      float f1 = 1.0F;
      float f5 = x * f;
      float f6 = y * f;
      float f7 = z * f;
      consumer.m_85982_(matrix4f, f5 - xOff, f6 + yOff, f7 + zOff).m_85950_(f1, f1, f1, alpha).m_85969_(k).m_5752_();
      consumer.m_85982_(matrix4f, f5 + xOff, f6 + 0.025F - yOff, f7 - zOff).m_85950_(f1, f1, f1, alpha).m_85969_(k).m_5752_();
   }

   static {
      TRANSPARENT_LEASH = RenderType.m_173215_("transparent_leash", DefaultVertexFormat.f_85816_, Mode.TRIANGLE_STRIP, 256, false, false, CompositeState.m_110628_().m_173292_(RenderStateShard.f_173075_).m_173290_(RenderStateShard.f_110147_).m_110661_(RenderStateShard.f_110110_).m_110671_(RenderStateShard.f_110152_).m_110685_(RenderStateShard.f_110139_).m_110691_(false));
   }
}
