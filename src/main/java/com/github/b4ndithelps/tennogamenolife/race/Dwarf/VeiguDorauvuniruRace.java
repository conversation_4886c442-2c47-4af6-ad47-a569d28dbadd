package com.github.b4ndithelps.tennogamenolife.race.Dwarf;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.network.chat.Component;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

/**
 * 地精 - 维格・德劳乌尼尔
 */
public class VeiguDorauvuniruRace extends Race {

    // 种族基础属性
    private double baseHealth = 1000.0;          // 基础生命值
    private double baseAttackDamage = 10.0;     // 基础攻击伤害
    private double baseAttackSpeed = 5.0;       // 基础攻击速度
    private double knockbackResistance = 0.5;   // 击退抗性
    private double jumpHeight = 1.5;            // 跳跃高度
    private double movementSpeed = 0.2;         // 移动速度
    private double sprintSpeed = 0.25;          // 冲刺速度
    private double auraMin = 3000.0;            // 最小灵气值
    private double auraMax = 5000.0;            // 最大灵气值
    private double startingMagiculeMin = 1000.0; // 最小初始魔素值
    private double startingMagiculeMax = 3000.0; // 最大初始魔素值

    private float playerSize = 2.0f;            // 玩家大小

    public VeiguDorauvuniruRace() {
        super(Difficulty.INTERMEDIATE);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.BODY_ARMOR.get());
        list.add(IntrinsicSkills.OGRE_BERSERKER.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ExtraSkills.ULTRASPEED_REGENERATION.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_NULLIFICATION.get());
        list.add(TenreincarnationSkill.MAGIC_BEADS.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.NII_TIRUVIRUGU.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.RONI_DORAUVUNIRU.get());
        return list;
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.RONI_DORAUVUNIRU.get();
    }

    @Override
    public Race getAwakeningEvolution(Player player) {
        return null;
    }

    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        return TensuraPlayerCapability.getBaseEP(player) * 100.0D / 400000.0D;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        return list;
    }
} 