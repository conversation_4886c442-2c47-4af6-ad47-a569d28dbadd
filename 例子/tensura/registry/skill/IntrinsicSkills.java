package com.github.manasmods.tensura.registry.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.intrinsic.AbsorbDissolveSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.BeastTransformationSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.BloodMistSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.BodyArmorSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DarknessTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DivineKiReleaseSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DragonEarSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DragonEyeSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DragonModeSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DragonSkinSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DrainSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.EarthTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.EyeOfTruthSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.FlameBreathSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.FlameTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.LightTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.OgreBerserkerSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.ParalysingBreathSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.PoisonousBreathSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.PossessionSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.ScaleArmorSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.SpaceTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.ThunderBreathSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.UltrasonicWavesSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.UnpredictabilitySkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.WaterBreathingSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.WaterTransformSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.WindTransformSkill;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class IntrinsicSkills {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AbsorbDissolveSkill> ABSORB_DISSOLVE;
   public static final RegistryObject<BeastTransformationSkill> BEAST_TRANSFORMATION;
   public static final RegistryObject<BodyArmorSkill> BODY_ARMOR;
   public static final RegistryObject<CharmSkill> CHARM;
   public static final RegistryObject<DarknessTransformSkill> DARKNESS_TRANSFORM;
   public static final RegistryObject<DivineKiReleaseSkill> DIVINE_KI_RELEASE;
   public static final RegistryObject<BloodMistSkill> BLOOD_MIST;
   public static final RegistryObject<DragonEarSkill> DRAGON_EAR;
   public static final RegistryObject<DragonEyeSkill> DRAGON_EYE;
   public static final RegistryObject<DragonModeSkill> DRAGON_MODE;
   public static final RegistryObject<DragonSkinSkill> DRAGON_SKIN;
   public static final RegistryObject<DrainSkill> DRAIN;
   public static final RegistryObject<EarthTransformSkill> EARTH_TRANSFORM;
   public static final RegistryObject<EyeOfTruthSkill> EYE_OF_TRUTH;
   public static final RegistryObject<FlameBreathSkill> FLAME_BREATH;
   public static final RegistryObject<FlameTransformSkill> FLAME_TRANSFORM;
   public static final RegistryObject<LightTransformSkill> LIGHT_TRANSFORM;
   public static final RegistryObject<OgreBerserkerSkill> OGRE_BERSERKER;
   public static final RegistryObject<ParalysingBreathSkill> PARALYSING_BREATH;
   public static final RegistryObject<PoisonousBreathSkill> POISONOUS_BREATH;
   public static final RegistryObject<PossessionSkill> POSSESSION;
   public static final RegistryObject<ScaleArmorSkill> SCALE_ARMOR;
   public static final RegistryObject<SpaceTransformSkill> SPACE_TRANSFORM;
   public static final RegistryObject<ThunderBreathSkill> THUNDER_BREATH;
   public static final RegistryObject<UltrasonicWavesSkill> ULTRASONIC_WAVES;
   public static final RegistryObject<UnpredictabilitySkill> UNPREDICTABILITY;
   public static final RegistryObject<WaterBreathingSkill> WATER_BREATHING;
   public static final RegistryObject<WaterTransformSkill> WATER_TRANSFORM;
   public static final RegistryObject<WindTransformSkill> WIND_TRANSFORM;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      ABSORB_DISSOLVE = registry.register("absorb_and_dissolve", AbsorbDissolveSkill::new);
      BEAST_TRANSFORMATION = registry.register("beast_transformation", BeastTransformationSkill::new);
      BODY_ARMOR = registry.register("body_armor", BodyArmorSkill::new);
      CHARM = registry.register("charm", CharmSkill::new);
      DARKNESS_TRANSFORM = registry.register("darkness_transform", DarknessTransformSkill::new);
      DIVINE_KI_RELEASE = registry.register("divine_ki_release", DivineKiReleaseSkill::new);
      BLOOD_MIST = registry.register("blood_mist", BloodMistSkill::new);
      DRAGON_EAR = registry.register("dragon_ear", DragonEarSkill::new);
      DRAGON_EYE = registry.register("dragon_eye", DragonEyeSkill::new);
      DRAGON_MODE = registry.register("dragon_mode", DragonModeSkill::new);
      DRAGON_SKIN = registry.register("dragon_skin", DragonSkinSkill::new);
      DRAIN = registry.register("drain", DrainSkill::new);
      EARTH_TRANSFORM = registry.register("earth_transform", EarthTransformSkill::new);
      EYE_OF_TRUTH = registry.register("eye_of_truth", EyeOfTruthSkill::new);
      FLAME_BREATH = registry.register("flame_breath", FlameBreathSkill::new);
      FLAME_TRANSFORM = registry.register("flame_transform", FlameTransformSkill::new);
      LIGHT_TRANSFORM = registry.register("light_transform", LightTransformSkill::new);
      OGRE_BERSERKER = registry.register("ogre_berserker", OgreBerserkerSkill::new);
      PARALYSING_BREATH = registry.register("paralysing_breath", ParalysingBreathSkill::new);
      POISONOUS_BREATH = registry.register("poisonous_breath", PoisonousBreathSkill::new);
      POSSESSION = registry.register("possession", PossessionSkill::new);
      SCALE_ARMOR = registry.register("scale_armor", ScaleArmorSkill::new);
      SPACE_TRANSFORM = registry.register("space_transform", SpaceTransformSkill::new);
      THUNDER_BREATH = registry.register("thunder_breath", ThunderBreathSkill::new);
      ULTRASONIC_WAVES = registry.register("ultrasonic_waves", UltrasonicWavesSkill::new);
      UNPREDICTABILITY = registry.register("unpredictability", UnpredictabilitySkill::new);
      WATER_BREATHING = registry.register("water_breathing", WaterBreathingSkill::new);
      WATER_TRANSFORM = registry.register("water_transform", WaterTransformSkill::new);
      WIND_TRANSFORM = registry.register("wind_transform", WindTransformSkill::new);
   }
}
