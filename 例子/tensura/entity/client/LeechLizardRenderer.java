package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LeechLizardEntity;
import com.github.manasmods.tensura.entity.variant.LeechLizardVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class LeechLizardRenderer extends GeoEntityRenderer<LeechLizardEntity> {
   public LeechLizardRenderer(Context renderManager) {
      super(renderManager, new LeechLizardModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(LeechLizardEntity instance) {
      return (ResourceLocation)LeechLizardVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public RenderType getRenderType(LeechLizardEntity lizard, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (lizard.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(lizard));
   }
}
