package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ArmoursaurusEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class ArmoursaurusRenderer extends GeoEntityRenderer<ArmoursaurusEntity> {
   public ArmoursaurusRenderer(Context renderManager) {
      super(renderManager, new ArmoursaurusModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(ArmoursaurusEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/armoursaurus/armoursaurus.png");
   }

   public RenderType getRenderType(ArmoursaurusEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (entity.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(entity));
   }

   public void render(ArmoursaurusEntity animatable, float entityYaw, float partialTick, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight) {
      if (!animatable.isDigging()) {
         super.render(animatable, entityYaw, partialTick, poseStack, bufferSource, packedLight);
      }
   }
}
