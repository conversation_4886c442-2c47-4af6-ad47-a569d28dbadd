package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class SniperBulletProjectile extends TensuraProjectile {
   @Nullable
   protected DamageSource damageSource;
   @Nullable
   protected Entity target;

   public SniperBulletProjectile(EntityType<? extends SniperBulletProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(0.25F);
   }

   public SniperBulletProjectile(Level worldIn, LivingEntity shooter, boolean right) {
      super((EntityType)TensuraEntityTypes.SNIPER_BULLET.get(), worldIn);
      this.m_5602_(shooter);
      this.setSize(0.25F);
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("minecraft:textures/block/stone.png")};
   }

   protected void m_8060_(BlockHitResult pResult) {
      if (this.target != null) {
         this.hitEntity(this.target);
         this.f_19853_.m_214171_(GameEvent.f_157777_, this.target.m_20182_(), Context.m_223719_(this, (BlockState)null));
         this.m_146870_();
      } else {
         super.m_8060_(pResult);
      }

   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F) && target != this.m_37282_()) {
         DamageSource source = TensuraDamageSources.shot(this, this.m_37282_());
         if (this.damageSource != null) {
            source = this.damageSource;
         }

         if (!target.m_6469_(DamageSourceHelper.addSkillAndCost(source, this.getMpCost(), this.getSkill()), this.getDamage())) {
            this.m_146870_();
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12018_);
   }

   public void hitParticles(double x, double y, double z) {
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.f_19853_.m_7106_(ParticleTypes.f_123762_, vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);
   }

   @Nullable
   public DamageSource getDamageSource() {
      return this.damageSource;
   }

   public void setDamageSource(@Nullable DamageSource damageSource) {
      this.damageSource = damageSource;
   }

   @Nullable
   public Entity getTarget() {
      return this.target;
   }

   public void setTarget(@Nullable Entity target) {
      this.target = target;
   }
}
