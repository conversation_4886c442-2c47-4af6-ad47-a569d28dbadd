package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.util.Objects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.Cow;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ItemUtils;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;

public class HolyCowEntity extends Cow {
   public HolyCowEntity(EntityType<? extends Cow> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 20.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22265_();
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.f_46443_) {
         if (this.m_21023_(MobEffects.f_19620_)) {
            if (!this.m_21023_(MobEffects.f_19619_)) {
               int duration = ((MobEffectInstance)Objects.requireNonNull(this.m_21124_(MobEffects.f_19620_))).m_19557_();
               this.m_7292_(new MobEffectInstance(MobEffects.f_19619_, duration + 20));
            }
         }
      }
   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      ItemStack milk;
      if (itemstack.m_150930_(Items.f_42590_)) {
         pPlayer.m_5496_(SoundEvents.f_11833_, 1.0F, 1.0F);
         milk = ItemUtils.m_41813_(itemstack, pPlayer, ((Item)TensuraConsumableItems.HOLY_MILK.get()).m_7968_());
         pPlayer.m_21008_(pHand, milk);
         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else if (itemstack.m_150930_(Items.f_42446_)) {
         pPlayer.m_5496_(SoundEvents.f_11833_, 1.0F, 1.0F);
         milk = ItemUtils.m_41813_(itemstack, pPlayer, ((Item)TensuraConsumableItems.HOLY_MILK_BUCKET.get()).m_7968_());
         pPlayer.m_21008_(pHand, milk);
         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else {
         return super.m_6071_(pPlayer, pHand);
      }
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }
}
