package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITeleportation;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.beam.SpatialRayProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.DimensionCutProjectile;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class AkashEntity extends GreaterSpiritEntity implements ITeleportation {
   public AkashEntity(EntityType<? extends AkashEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.PURPLE, BossBarOverlay.NOTCHED_20)).m_7005_(true);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22276_, 240.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : AkashEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new AkashEntity.AkashAttackGoal(this));
      this.f_21345_.m_25352_(3, new GreaterSpiritEntity.FollowHinataGoal(this, 1.0D, HinataSakaguchiEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new GreaterSpiritEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 12));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{AkashEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (pSource.m_7639_() instanceof WingedCatEntity) {
         return false;
      } else if (this.shouldDodge(pSource, pAmount)) {
         return false;
      } else {
         if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         } else {
            pAmount *= this.getPhysicalAttackInput(pSource);
         }

         boolean hurt = super.m_6469_(pSource, pAmount);
         if (hurt) {
            Entity var5 = pSource.m_7639_();
            if (var5 instanceof LivingEntity) {
               LivingEntity damageSource = (LivingEntity)var5;
               if (!damageSource.m_6084_()) {
                  return true;
               }

               if (damageSource instanceof Player) {
                  Player player = (Player)damageSource;
                  if (player.m_7500_() || player.m_5833_()) {
                     return true;
                  }
               }

               List<WingedCatEntity> list = this.f_19853_.m_6443_(WingedCatEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
                  return !entity.m_21824_();
               });
               if (!list.isEmpty()) {
                  list.forEach((cat) -> {
                     cat.m_6710_(damageSource);
                  });
               }
            }
         }

         return hurt;
      }
   }

   private boolean shouldDodge(DamageSource source, float damage) {
      if (this.m_9236_().m_5776_()) {
         return false;
      } else {
         Entity var4 = source.m_7639_();
         if (var4 instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)var4;
            if (damage >= 10.0F) {
               if (source.m_19378_()) {
                  return false;
               }

               if ((double)entity.m_217043_().m_188501_() >= 0.1D) {
                  return false;
               }

               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11852_, SoundSource.PLAYERS, 2.0F, 1.0F);
               if (!this.m_9236_().m_5776_() && this.m_5448_() != null) {
                  this.teleportTowards(this, this.m_5448_(), 12.0D);
               }

               return true;
            }
         }

         return false;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof WingedCatEntity) {
         WingedCatEntity cat = (WingedCatEntity)entity;
         return cat.m_21824_() == this.m_21824_();
      } else if (entity instanceof AkashEntity) {
         AkashEntity akash = (AkashEntity)entity;
         return akash.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean shouldCountMotionBlock() {
      return false;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.m_6084_() && this.f_19797_ % 10 == 0) {
         TensuraParticleHelper.addParticlesAroundSelf(this, ParticleTypes.f_123789_, 2.0D);
      }

      LivingEntity target = this.m_5448_();
      if (target != null) {
         if (this.m_6084_()) {
            if (target.m_6084_() && this.m_20280_(target) > 3.0D) {
               this.setFlying(true);
            }

            if (this.getSummonerUUID() == null && this.m_21826_() == null) {
               if (this.f_19797_ % 20 != 0) {
                  return;
               }

               if (target.m_20270_(this) >= 20.0F) {
                  this.teleportTowards(this, target, 10.0D);
               }

               if (this.f_19797_ % 200 != 0) {
                  return;
               }

               if (this.m_217043_().m_188503_(10) == 1) {
                  this.teleportTowards(this, target, 10.0D);
               }
            }

         }
      }
   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if (this.getMiscAnimation() == 1 && this.miscAnimationTicks == 10) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               shootSpaceCut(this);
               this.m_5496_(SoundEvents.f_12520_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() == 2) {
               this.m_21573_().m_26573_();
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               if (this.miscAnimationTicks == 10) {
                  this.spatialRay();
               }

               if (this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 35) {
                  this.m_5496_(SoundEvents.f_12049_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else if (this.getMiscAnimation() == 3) {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks == 20) {
                  this.combust();
                  this.m_5496_(SoundEvents.f_11913_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else if (this.getMiscAnimation() == 4) {
               this.m_21573_().m_26573_();
               switch(this.miscAnimationTicks) {
               case 10:
                  this.summonWingedCat(3, 7);
                  break;
               case 20:
                  this.summonWingedCat(5, 10);
                  break;
               case 30:
                  this.summonWingedCat(7, 15);
               }

               this.m_5496_(SoundEvents.f_12418_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 1:
      case 5:
      case 6:
      case 7:
      default:
         var10000 = 15;
         break;
      case 2:
      case 8:
         var10000 = 40;
         break;
      case 3:
         var10000 = 25;
         break;
      case 4:
         var10000 = 32;
      }

      return var10000;
   }

   public static void shootSpaceCut(LivingEntity undine) {
      DimensionCutProjectile cut = new DimensionCutProjectile(undine.f_19853_, undine);
      cut.setSkill(SkillUtils.getSkillOrNull(undine, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get()));
      float angle = 0.017453292F * undine.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      cut.m_7678_(undine.m_20185_() + xOffset, undine.m_20188_() - 0.2D, undine.m_20189_() + zOffset, undine.m_146908_(), undine.m_146909_());
      cut.setDamage((float)undine.m_21133_(Attributes.f_22281_));
      cut.setSpiritAttack(true);
      cut.setSpeed(2.0F);
      cut.m_20242_(true);
      cut.shootFromRot(undine.m_20154_());
      undine.f_19853_.m_7967_(cut);
   }

   private void spatialRay() {
      SpatialRayProjectile ray = new SpatialRayProjectile(this.f_19853_, this);
      ray.setFollowingOwner(true);
      ray.setSize(0.5F);
      ray.setLife(20);
      float damage = (float)(this.m_21133_(Attributes.f_22281_) * 2.0D);
      ray.setDamage(damage);
      ray.setRange(30.0F);
      ray.m_146884_(this.m_146892_());
      ray.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get()));
      ray.setMpCost(1000.0D);
      this.f_19853_.m_7967_(ray);
   }

   public void combust() {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123810_, this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123789_, this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123789_, 3.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123789_, 4.0D);
      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 10.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, this::shouldAttack);
      if (!list.isEmpty()) {
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 100.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_6469_(damageSource, (float)this.m_21133_(Attributes.f_22281_) * 3.0F);
            target.m_20334_(0.0D, 0.1D, 0.0D);
            SkillHelper.knockBack(this, target, 2.0F);
         }

      }
   }

   private void summonWingedCat(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         WingedCatEntity cat = new WingedCatEntity((EntityType)TensuraEntityTypes.WINGED_CAT.get(), serverLevel);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            cat.m_6034_((double)i1, (double)j1, (double)k1);
            if (serverLevel.m_45784_(cat) && serverLevel.m_45786_(cat)) {
               cat.m_6518_(serverLevel, serverLevel.m_6436_(cat.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
               cat.m_6710_(this.m_5448_());
               serverLevel.m_47205_(cat);
               cat.setSummonerUUID(this.getSummonerUUID());
               TensuraParticleHelper.addServerParticlesAroundSelf(cat, ParticleTypes.f_123789_, 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(cat, ParticleTypes.f_123810_, 2.0D);
               break;
            }
         }

      }
   }

   public MagicElemental getElemental() {
      return MagicElemental.SPACE;
   }

   public Item getElementalCore() {
      return (Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get();
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 39) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         this.spawnDeathParticles();
      }

   }

   protected void spawnDeathParticles() {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123789_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123810_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123789_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123810_, 2.0D);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12499_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12502_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12501_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.relax", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_() && this.getMiscAnimation() == -1) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.idle_train", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving() && !this.m_21525_()) {
         if (this.m_21660_() && this.getMiscAnimation() == 0) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.fly_fast", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.fly", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.space_cut", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.spatial_ray", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.burst", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.summon", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.akash.death", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static class AkashAttackGoal extends Goal {
      private final AkashEntity akash;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private int attackCooldown;

      public AkashAttackGoal(AkashEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.akash = entity;
      }

      public boolean m_8036_() {
         if (this.akash.m_21827_()) {
            return false;
         } else if (this.akash.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.akash.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      public void m_8037_() {
         LivingEntity target = this.akash.m_5448_();
         if (target != null && target.m_6084_()) {
            this.akash.setFlying(true);
            if (this.orbitTime < this.maxOrbitTime) {
               if (this.akash.getMiscAnimation() == 0) {
                  ++this.orbitTime;
                  --this.attackCooldown;
               }

               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(15.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.akash.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
               if (this.isTimeToAttack()) {
                  this.resetAttackCooldown();
                  this.akash.setMiscAnimation(this.randomAttack((double)this.akash.m_20270_(target)));
                  this.akash.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else {
               this.orbitTime = 0;
               if (this.canSummonCats()) {
                  this.akash.setMiscAnimation(4);
               }
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 80 + this.akash.m_217043_().m_188503_(80);
         this.akash.m_21561_(true);
         this.attackCooldown = 0;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private boolean canSummonCats() {
         if (this.akash.m_21824_()) {
            return false;
         } else {
            List<WingedCatEntity> list = this.akash.f_19853_.m_45971_(WingedCatEntity.class, TargetingConditions.m_148353_().m_26883_(20.0D).m_148355_().m_26893_(), this.akash, this.akash.m_20191_().m_82400_(20.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  WingedCatEntity entity = (WingedCatEntity)var2.next();
                  entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false));
               }
            }

            return this.akash.f_19796_.m_188503_(3) + 1 > list.size();
         }
      }

      private void resetAttackCooldown() {
         this.attackCooldown = this.m_183277_(30);
      }

      private boolean isTimeToAttack() {
         return this.attackCooldown <= 0;
      }

      private int randomAttack(double distance) {
         if (distance < 10.0D && (double)this.akash.f_19796_.m_188501_() <= 0.1D) {
            return 3;
         } else {
            return distance < 30.0D && (double)this.akash.f_19796_.m_188501_() <= 0.2D ? 2 : 1;
         }
      }
   }
}
