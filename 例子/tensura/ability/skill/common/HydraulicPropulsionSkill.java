package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class HydraulicPropulsionSkill extends Skill {
   public HydraulicPropulsionSkill() {
      super(Skill.SkillType.COMMON);
   }

   public double learningCost() {
      return 2.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 2.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 10000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)CommonSkills.WATER_BLADE.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_20072_()) {
         if (!entity.m_21209_()) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               this.addMasteryPoint(instance, entity);
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_204079_(10);
               }

               SkillHelper.riptidePush(entity, 2.0F);
               entity.f_19864_ = true;
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12519_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
               TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 25, 0.08D, 0.08D, 0.08D, 0.15D, false);
            }
         }
      }
   }

   public static void riptideImpact(LivingEntity entity, LivingEntity target) {
      Level level = entity.m_9236_();
      level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12278_, SoundSource.PLAYERS, 1.0F, 1.0F);
      target.f_19802_ = 0;
      target.m_6469_(DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 2.0D, SkillUtils.getSkillOrNull(entity, (ManasSkill)CommonSkills.HYDRAULIC_PROPULSION.get())), 4.0F);
      if (target.m_6060_()) {
         target.m_20095_();
      }

      double d0 = Math.max(0.0D, 1.0D - target.m_21133_(Attributes.f_22278_));
      Vec3 vec3 = entity.m_20252_(1.0F).m_82541_().m_82490_(2.0D * d0 * 0.10000000149011612D);
      if (vec3.m_82556_() > 0.0D) {
         target.m_5997_(vec3.f_82479_, vec3.f_82480_, vec3.f_82481_);
      }

      if (!entity.m_9236_().m_5776_()) {
         ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.WATER_BUBBLE.get(), target.m_20182_().f_82479_, target.m_20182_().f_82480_ + (double)target.m_20206_() / 2.0D, target.m_20182_().f_82481_, 20, 0.08D, 0.08D, 0.08D, 0.15D);
      }
   }
}
