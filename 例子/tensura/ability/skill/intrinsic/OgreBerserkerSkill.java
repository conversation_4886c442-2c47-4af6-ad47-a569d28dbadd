package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class OgreBerserkerSkill extends Skill implements Transformation {
   public OgreBerserkerSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 5000.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance ogre = entity.m_21124_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
      return ogre != null && ogre.m_19564_() == 0;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.failedToActivate(entity, (MobEffect)TensuraMobEffects.OGRE_BERSERKER.get())) {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get())) {
            entity.m_21195_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
            instance.setCoolDown(600);
         } else {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            instance.setCoolDown(this.isMastered(instance, entity) ? 960 : 780);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get(), this.isMastered(instance, entity) ? 7200 : 3600, 0, false, false, false));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12363_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 2.0D);
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.PURPLE_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
         }

      }
   }
}
