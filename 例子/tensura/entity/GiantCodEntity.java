package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.DynamicMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.MoveTowardsTargetGoal;
import com.github.manasmods.tensura.api.entity.ai.RandomFluidSwimmingGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.SmoothSwimmingLookControl;
import net.minecraft.world.entity.ai.control.SmoothSwimmingMoveControl;
import net.minecraft.world.entity.ai.goal.AvoidEntityGoal;
import net.minecraft.world.entity.ai.goal.FollowFlockLeaderGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.TryFindWaterGoal;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.ai.navigation.WaterBoundPathNavigation;
import net.minecraft.world.entity.animal.AbstractSchoolingFish;
import net.minecraft.world.entity.animal.Cod;
import net.minecraft.world.entity.monster.Drowned;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class GiantCodEntity extends AbstractSchoolingFish implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public GiantCodEntity(EntityType<? extends AbstractSchoolingFish> type, Level level) {
      super(type, level);
      this.f_21364_ = 1;
      this.f_21342_ = new SmoothSwimmingMoveControl(this, 85, 10, 0.02F, 0.1F, true);
      this.f_21365_ = new SmoothSwimmingLookControl(this, 10);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 15.0D).m_22268_(Attributes.f_22281_, 4.0D).m_22268_(Attributes.f_22282_, 1.0D).m_22268_(Attributes.f_22284_, 1.0D).m_22268_(Attributes.f_22278_, 0.5D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22279_, 1.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new TryFindWaterGoal(this));
      this.f_21345_.m_25352_(1, new DynamicMeleeAttackGoal(this, List.of((self, target, goal) -> {
         float speed = 1.2F;
         goal.shouldMoveToTarget = target.m_20072_();
         if (this.m_20270_(target) < 10.0F) {
            if ((double)this.m_20270_(target) < 2.0D) {
               this.m_7327_(target);
               speed = 1.8F;
            } else if (goal.shouldMoveToTarget) {
               speed = 1.5F;
               this.m_21391_(target, 70.0F, 70.0F);
            }
         }

         return speed;
      }, (self, target, goal) -> {
         return !(target instanceof Drowned) && !(target instanceof Player) ? 1.2F : 2.0F;
      })));
      this.f_21345_.m_25352_(2, new MoveTowardsTargetGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(2, new RandomFluidSwimmingGoal(this, 1.0D, 8, (fluidState) -> {
         return fluidState.m_205070_(FluidTags.f_13131_);
      }));
      this.f_21345_.m_25352_(3, new AvoidEntityGoal(this, SissieEntity.class, 15.0F, 1.0D, 3.0D));
      this.f_21345_.m_25352_(3, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(4, new LookAtPlayerGoal(this, LivingEntity.class, 15.0F));
      this.f_21345_.m_25352_(4, new TryFindWaterGoal(this));
      this.f_21345_.m_25352_(5, new FollowFlockLeaderGoal(this));
      this.f_21346_.m_25352_(1, (new HurtByTargetGoal(this, new Class[0])).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(2, new NearestAttackableTargetGoal(this, Drowned.class, true));
   }

   protected PathNavigation m_6037_(Level worldIn) {
      return new WaterBoundPathNavigation(this, worldIn);
   }

   public void m_8107_() {
      if (!this.m_20069_() && this.f_19861_ && this.f_19863_) {
         this.m_20256_(this.m_20184_().m_82520_((double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.05F), 0.4000000059604645D, (double)((this.f_19796_.m_188501_() * 2.0F - 1.0F) * 0.05F)));
         this.f_19861_ = false;
         this.f_19812_ = true;
         this.m_5496_(this.m_5699_(), this.m_6121_(), this.m_6100_());
      }

      super.m_8107_();
   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      if (!this.m_20160_() && !pPlayer.m_36341_() && SkillHelper.isSubordinate(pPlayer, this)) {
         if (!this.f_19853_.f_46443_) {
            pPlayer.m_20329_(this);
         }

         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else {
         return InteractionResult.PASS;
      }
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public Entity m_6688_() {
      Entity entity = this.m_146895_();
      return entity != null && this.canBeControlledBy(entity) ? entity : null;
   }

   private boolean canBeControlledBy(Entity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         return player.m_21205_().m_150930_(Items.f_42523_);
      } else {
         return false;
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         Entity entity = this.m_6688_();
         AttributeInstance instance;
         if (this.m_20160_() && entity instanceof Player) {
            this.m_146922_(entity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(entity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.m_146908_();
            this.f_20887_ = this.m_6113_() * 0.1F;
            if (!this.m_6109_()) {
               this.m_20256_(Vec3.f_82478_);
            } else {
               if (this.m_20072_()) {
                  if (this.isInFluidType((fluidType, height) -> {
                     return height > this.m_20204_();
                  }) && entity.m_146909_() <= 0.0F) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.05D, 0.0D));
                  } else if (entity.m_146909_() >= 30.0F) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.05D, 0.0D));
                  }

                  this.m_7910_((float)this.m_21133_(Attributes.f_22279_));
               } else {
                  this.m_7910_((float)this.m_21133_(Attributes.f_22279_) * 0.01F);
               }

               instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
               if (instance != null) {
                  instance.m_22100_(entity.m_20142_() ? 6.0D : 4.0D);
               }

               super.m_7023_(new Vec3(0.0D, 0.0D, 3.0D));
            }

            this.m_146872_();
         } else {
            instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
            if (instance != null && instance.m_22115_() != 1.0D) {
               instance.m_22100_(1.0D);
            }

            if (this.m_6142_() && this.m_20069_()) {
               this.m_19920_(this.m_6113_(), pTravelVector);
               this.m_6478_(MoverType.SELF, this.m_20184_());
               this.m_20256_(this.m_20184_().m_82490_(0.9D));
               if (this.m_5448_() == null) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
               }
            } else {
               super.m_7023_(pTravelVector);
            }
         }

      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11758_;
   }

   protected SoundEvent m_7975_(DamageSource damageSourceIn) {
      return SoundEvents.f_11761_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11759_;
   }

   protected float m_6121_() {
      return 0.2F;
   }

   protected SoundEvent m_5699_() {
      return SoundEvents.f_11760_;
   }

   public ItemStack m_28282_() {
      return ItemStack.f_41583_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (!this.m_20069_() && this.f_19861_ && this.f_19863_) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_salmon.idle", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (!event.isMoving()) {
         return PlayState.CONTINUE;
      } else {
         String animation = this.m_5912_() ? "animation.giant_salmon.strike" : "animation.giant_salmon.swimming";
         event.getController().setAnimation((new AnimationBuilder()).addAnimation(animation, EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      }
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   protected boolean m_8028_() {
      return true;
   }

   public int m_6031_() {
      return 20;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.giantCodSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public int m_5792_() {
      return 3;
   }

   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (pReason == MobSpawnType.NATURAL || pReason == MobSpawnType.CHUNK_GENERATION) {
         for(int i = 0; i <= 3; ++i) {
            Cod cod = new Cod(EntityType.f_20556_, this.m_9236_());
            cod.m_6034_(this.m_20185_(), this.m_20186_() - 1.0D, this.m_20189_());
            cod.m_6518_(pLevel, this.f_19853_.m_6436_(cod.m_20183_()), MobSpawnType.NATURAL, (SpawnGroupData)null, (CompoundTag)null);
            this.m_9236_().m_7967_(cod);
         }
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }
}
