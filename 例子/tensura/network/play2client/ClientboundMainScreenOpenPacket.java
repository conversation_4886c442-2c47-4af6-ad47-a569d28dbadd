package com.github.manasmods.tensura.network.play2client;

import com.github.manasmods.tensura.ability.skill.Skill;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class ClientboundMainScreenOpenPacket {
   protected final int entityId;
   protected final int screenID;
   private final Skill.SkillType type;
   public static final int MAIN = 0;
   public static final int SKILL = 4;
   public static final int MAGIC = 5;
   public static final int ART = 6;
   public static final int EVOLUTIONS = 7;
   public static final int SETTINGS = 8;
   public static final int FALSIFIER = 9;

   public ClientboundMainScreenOpenPacket(int screenID, int entityId) {
      this.screenID = screenID;
      this.entityId = entityId;
      this.type = Skill.SkillType.ULTIMATE;
   }

   public ClientboundMainScreenOpenPacket(int screenID, int entityId, Skill.SkillType type) {
      this.screenID = screenID;
      this.entityId = entityId;
      this.type = type;
   }

   public ClientboundMainScreenOpenPacket(FriendlyByteBuf buf) {
      this.screenID = buf.readByte();
      this.entityId = buf.readInt();
      this.type = (Skill.SkillType)buf.m_130066_(Skill.SkillType.class);
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeByte(this.screenID);
      buf.writeInt(this.entityId);
      buf.m_130068_(this.type);
   }

   public static void handle(ClientboundMainScreenOpenPacket message, Supplier<Context> context) {
      ((Context)context.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.handleClientboundMainScreenOpenPacket(message, context);
            };
         });
      });
      ((Context)context.get()).setPacketHandled(true);
   }
}
