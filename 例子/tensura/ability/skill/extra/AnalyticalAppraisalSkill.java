package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;

public class AnalyticalAppraisalSkill extends Skill {
   public AnalyticalAppraisalSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 20000.0D;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public double learningCost() {
      return 200.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            int level;
            if (player.m_6047_()) {
               level = cap.getAnalysisMode();
               switch(level) {
               case 1:
                  cap.setAnalysisMode(2);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.block").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               case 2:
                  cap.setAnalysisMode(0);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.both").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               default:
                  cap.setAnalysisMode(1);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.entity").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               }

               player.m_6330_(SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraSkillCapability.sync(player);
            } else {
               level = instance.isMastered(entity) ? 2 : 1;
               if (cap.getAnalysisLevel() != level) {
                  cap.setAnalysisLevel(level);
                  cap.setAnalysisDistance(instance.isMastered(entity) ? 10 : 5);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else {
                  cap.setAnalysisLevel(0);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               TensuraSkillCapability.sync(player);
            }
         });
      }
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      if (!(entity instanceof Player)) {
         return false;
      } else {
         Player player = (Player)entity;
         return TensuraSkillCapability.getAnalysisLevel(player) >= 1 && TensuraSkillCapability.getAnalysisDistance(player) >= 5;
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         double distance = (double)TensuraSkillCapability.getAnalysisDistance(player);
         boolean success = SkillHelper.getTargetingEntity(entity, distance, false, true) != null;
         success = success || !entity.m_9236_().m_8055_(SkillHelper.getPlayerPOVHitResult(player.f_19853_, player, Fluid.NONE, Block.OUTLINE, distance).m_82425_()).m_60795_();
         if (success) {
            CompoundTag tag = instance.getOrCreateTag();
            int time = tag.m_128451_("activatedTimes");
            if (time % 6 == 0) {
               this.addMasteryPoint(instance, entity);
            }

            tag.m_128405_("activatedTimes", time + 1);
         }
      }
   }
}
