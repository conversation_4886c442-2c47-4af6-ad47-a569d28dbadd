package com.github.b4ndithelps.tennogamenolife.item;

import com.github.manasmods.tensura.item.templates.custom.TwoHandedLongSword;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.UseAnim;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.Tiers;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import java.util.List;

public class SickleItem extends TwoHandedLongSword {
    public SickleItem() {
        super(Tiers.NETHERITE, 70, -2.4F, 2.0D, 20.0D, 0.0D, 6, -3.2F, 100.0D, 50.0D, 
            new Properties()
                .rarity(Rarity.EPIC)
                .fireResistant());
    }

    @Override
    public boolean canApplyAtEnchantingTable(ItemStack stack, Enchantment enchantment) {
        return enchantment.equals(Enchantments.SWEEPING_EDGE) ? true : enchantment.category.canEnchant(stack.getItem());
    }

    @Override
    public UseAnim getUseAnimation(ItemStack pStack) {
        return UseAnim.SPEAR;
    }

    @Override
    public int getUseDuration(ItemStack pStack) {
        return 72000;
    }

    @Override
    public void appendHoverText(@NotNull ItemStack pStack, @Nullable Level pLevel, List<net.minecraft.network.chat.Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
        pTooltipComponents.add(net.minecraft.network.chat.Component.translatable("tooltip.tensura.long_sword.tooltip").withStyle(net.minecraft.ChatFormatting.DARK_PURPLE));
    }
} 