package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.EvilCentipedeEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class EvilCentipedeHeadRenderer extends GeoEntityRenderer<EvilCentipedeEntity> {
   public EvilCentipedeHeadRenderer(Context renderManager) {
      super(renderManager, new EvilCentipedeHeadModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(EvilCentipedeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/evil_centipede/evil_centipede.png");
   }

   protected float getDeathMaxRotation(EvilCentipedeEntity animatable) {
      return 180.0F;
   }

   public RenderType getRenderType(EvilCentipedeEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(1.5F, 1.5F, 1.5F);
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
