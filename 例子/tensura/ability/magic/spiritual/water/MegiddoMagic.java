package com.github.manasmods.tensura.ability.magic.spiritual.water;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.barrier.MegiddoBubbleEntity;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;

public class MegiddoMagic extends SpiritualMagic {
   public MegiddoMagic() {
      super(MagicElemental.WATER, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 140;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.megiddo.single");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.megiddo.autonomous");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 2 ? 30000.0D : 3000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      MegiddoBubbleEntity bubble = this.getMegiddoBubble(entity);
      if (bubble != null) {
         if (entity.m_6144_()) {
            bubble.m_146870_();
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 60.0D, false, true);
            if (target != null) {
               if (target.m_6084_()) {
                  if (!SkillHelper.outOfMagicule(entity, 100.0D)) {
                     instance.setCoolDown(1);
                     bubble.startNewBeam(target);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 0.5F, 1.0F);
                     instance.getOrCreateTag().m_128405_("HeldTicks", 0);
                     instance.markDirty();
                  }
               }
            }
         }
      }
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.hasMegiddo(entity)) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      return this.hasMegiddo(entity) ? false : super.onHeld(instance, entity, heldTicks);
   }

   private boolean spawnMegiddoBelow(LivingEntity entity) {
      if (entity.m_6144_()) {
         return false;
      } else if (entity.m_20096_()) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!player.m_150110_().f_35935_) {
               return false;
            }
         }

         return entity.m_9236_().m_45547_(new ClipContext(entity.m_20182_(), entity.m_20182_().m_82520_(0.0D, -10.0D, 0.0D), Block.OUTLINE, Fluid.NONE, entity)).m_6662_() == Type.MISS;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (!this.hasMegiddo(entity)) {
         if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               if (!WaterMagic.isWaterEvaporated(entity, entity.f_19853_)) {
                  this.addMasteryPoint(instance, entity);
                  if (instance.getMode() == 2) {
                     instance.setCoolDown(instance.isMastered(entity) ? 10 : 15);
                     double yOffset = this.spawnMegiddoBelow(entity) ? -10.0D : 20.0D;

                     for(int i = 0; i < 9; ++i) {
                        int radius = i == 0 ? 3 : 2;
                        MegiddoBubbleEntity bubble = this.summonMegiddo(entity, instance, (float)radius, 600);
                        bubble.setCharge(bubble.getCharge() / 2);
                        bubble.setChainCharge(3);
                        Vec3 pos = entity.m_20182_().m_82520_(0.0D, yOffset, 0.0D);
                        if (radius == 2) {
                           Vec3 offset = (new Vec3(0.0D, 0.0D, 15.0D)).m_82524_(0.0F).m_82496_((float)(i * 45) * 0.017453292F).m_82535_(-1.5707964F).m_82542_(1.0D, 0.8500000238418579D, 1.0D);
                           pos = pos.m_82549_(offset).m_82520_(0.0D, ((double)entity.m_217043_().m_188501_() - 0.5D) * 4.0D, 0.0D);
                        }

                        bubble.m_146884_(pos);
                        entity.m_9236_().m_7967_(bubble);
                        List<LivingEntity> list = bubble.getTargetList(bubble.m_20182_().m_82520_(0.0D, -40.0D, 0.0D), 40.0F);
                        if (!list.isEmpty()) {
                           bubble.startNewBeam((LivingEntity)list.get(entity.m_217043_().m_188503_(list.size())));
                        }
                     }
                  } else {
                     MegiddoBubbleEntity bubble = this.summonMegiddo(entity, instance, 5.0F, 6000);
                     bubble.m_6034_(entity.m_20185_(), entity.m_20186_() + 20.0D, entity.m_20189_());
                     entity.m_9236_().m_7967_(bubble);
                  }

               }
            }
         }
      }
   }

   private MegiddoBubbleEntity summonMegiddo(LivingEntity entity, ManasSkillInstance instance, float radius, int life) {
      MegiddoBubbleEntity bubble = new MegiddoBubbleEntity(entity.m_9236_(), entity);
      if (instance.isMastered(entity)) {
         bubble.setCharge(20);
      }

      bubble.setRadius(radius);
      bubble.setLife(life);
      bubble.setMpCost(this.magiculeCost(entity, instance));
      bubble.setSkill(instance);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
      return bubble;
   }

   private boolean hasMegiddo(LivingEntity entity) {
      return this.getMegiddoBubble(entity) != null;
   }

   private MegiddoBubbleEntity getMegiddoBubble(LivingEntity owner) {
      AABB bubbleBox = new AABB(new BlockPos(owner.m_20185_(), owner.m_20186_() + 20.0D, owner.m_20189_()));
      Iterator var3 = owner.m_9236_().m_45976_(MegiddoBubbleEntity.class, bubbleBox).iterator();

      MegiddoBubbleEntity bubble;
      do {
         if (!var3.hasNext()) {
            return null;
         }

         bubble = (MegiddoBubbleEntity)var3.next();
      } while(bubble.m_37282_() != owner);

      return bubble;
   }
}
