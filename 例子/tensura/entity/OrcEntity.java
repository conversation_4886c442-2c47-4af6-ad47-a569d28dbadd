package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.OrcVariant;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class OrcEntity extends HumanoidNPCEntity implements IAnimatable, IRanking {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> EVOLVING;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public boolean prevSwim = false;

   public OrcEntity(EntityType<? extends OrcEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 5;
      this.f_19793_ = 1.0F;
      this.f_21365_ = new OrcEntity.EvolvingLookControl();
      this.f_21342_ = new OrcEntity.EvolvingMoveControl();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 1.5D).m_22268_(Attributes.f_22276_, 28.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 1.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F) {
         public boolean m_8036_() {
            OrcEntity orc = OrcEntity.this;
            LivingEntity target = orc.m_5448_();
            if (target == null) {
               return false;
            } else {
               return !orc.m_21824_() && target.m_20186_() - orc.m_20186_() < 5.0D ? false : super.m_8036_();
            }
         }
      });
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(4, new OrcEntity.FollowLordGoal(this, 1.0D));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.2D, OrcEntity.class));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{OrcEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(EVOLVING, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("Evolving", this.getEvolving());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.setEvolving(compound.m_128451_("Evolving"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int tick) {
      this.f_19804_.m_135381_(MISC_ANIMATION, tick);
   }

   public OrcVariant getVariant() {
      return OrcVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(OrcVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public int getEvolving() {
      return (Integer)this.f_19804_.m_135370_(EVOLVING);
   }

   public void setEvolving(int tick) {
      this.f_19804_.m_135381_(EVOLVING, tick);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public void evolve() {
      this.gainSwimSpeed(this, 1.0D);
   }

   public void m_6136_(boolean pIsCharging) {
      super.m_6136_(pIsCharging);
      if (pIsCharging) {
         this.setMiscAnimation(3);
      }

   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      OrcEntity baby = (OrcEntity)((EntityType)TensuraEntityTypes.ORC.get()).m_20615_(pLevel);
      if (baby == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            baby.m_21816_(uuid);
            baby.m_7105_(true);
         }

         float chance = 0.0F;
         if (this.getVariant().equals(OrcVariant.ROYAL) || this.getVariant().equals(OrcVariant.ROYAL_LORD)) {
            chance += 0.5F;
         }

         if (pOtherParent instanceof OrcEntity) {
            OrcEntity orc = (OrcEntity)pOtherParent;
            if (orc.getVariant().equals(OrcVariant.ROYAL) || orc.getVariant().equals(OrcVariant.ROYAL_LORD)) {
               chance += 0.5F;
            }
         }

         if (this.f_19796_.m_188501_() <= chance) {
            baby.setVariant(OrcVariant.ROYAL);
         }

         return baby;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_150930_((Item)TensuraMobDropItems.ROYAL_BLOOD.get()) ? false : pStack.m_41720_().m_41472_();
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || super.m_6673_(source);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      if (this.m_5803_()) {
         return entitydimensions.m_20390_(1.0F, 0.5F);
      } else if (this.shouldSwim()) {
         return entitydimensions.m_20390_(1.0F, 0.25F);
      } else if (this.getClass() == OrcEntity.class && (this.m_21827_() || this.m_21825_())) {
         return entitydimensions.m_20390_(1.0F, 0.75F);
      } else if (this.getClass() == OrcDisasterEntity.class) {
         return entitydimensions;
      } else {
         int tick = 40 - this.getEvolving();
         if (this.getEvolving() > 0 && tick > 0) {
            float scale = 1.0F + 0.5F * ((float)tick / 40.0F);
            return entitydimensions.m_20388_(scale);
         } else {
            return entitydimensions;
         }
      }
   }

   public boolean canSleep() {
      return true;
   }

   public void m_7334_(Entity pEntity) {
      if (!(pEntity instanceof OrcLordEntity)) {
         super.m_7334_(pEntity);
      }
   }

   public void m_8119_() {
      super.m_8119_();
      this.evolvingTick();
      if (this.prevSwim != this.isInFluidType() && !this.m_20096_()) {
         this.m_6210_();
         this.prevSwim = this.isInFluidType() && !this.m_20096_();
      }

      this.miscAnimationHandler();
   }

   protected void evolvingTick() {
      if (this.getEvolving() > 0) {
         this.setEvolving(this.getEvolving() - 1);
         this.m_6210_();
         this.m_5496_(SoundEvents.f_12241_, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
         if (this.getEvolving() == 0) {
            this.royalLordEvolves();
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      return miscAnimation == 3 ? 25 : 7;
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() != 2) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   protected void hurtShield(ItemStack stack, float pAmount) {
      super.hurtShield(stack, pAmount);
      this.setMiscAnimation(2);
   }

   protected boolean spearThrowAttack(LivingEntity pTarget, ItemStack weapon) {
      boolean success = super.spearThrowAttack(pTarget, weapon);
      if (success) {
         this.setMiscAnimation(4);
      }

      return success;
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      if (this.m_21824_()) {
         return 0.0F;
      } else if (pSlot.m_20743_().equals(Type.ARMOR)) {
         return super.m_21519_(pSlot) >= 2.0F ? 2.0F : 0.0F;
      } else {
         return super.m_21519_(pSlot);
      }
   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      this.dropBlood();
   }

   protected void dropBlood() {
      if (!this.getVariant().equals(OrcVariant.NORMAL)) {
         if (!((double)this.f_19796_.m_188501_() > 0.2D) && this.getSpawnType() != MobSpawnType.BREEDING) {
            this.m_19998_((ItemLike)TensuraMobDropItems.ROYAL_BLOOD.get());
         }
      }
   }

   private boolean canEvolveToLord() {
      if (this.m_21824_()) {
         return false;
      } else if (this.m_6162_()) {
         return false;
      } else {
         return this.getEvolving() > 0 ? false : this.getVariant().equals(OrcVariant.ROYAL_LORD);
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack stack) {
      if (this.canEvolveToLord() && stack.m_150930_((Item)TensuraMobDropItems.ROYAL_BLOOD.get()) && this.getEvolving() > -10 && this.getEvolving() <= 0) {
         if (!player.m_7500_()) {
            stack.m_41774_(1);
         }

         this.setEvolving(this.getEvolving() - 1);
         if (this.getEvolving() <= -10 && TensuraEPCapability.getEP(this) >= 80000.0D) {
            this.setEvolving(40);
            TensuraEPCapability.getFrom(this).ifPresent((cap) -> {
               cap.addNeutralTarget(player.m_20148_());
            });
         }

         this.m_5634_(this.m_21233_());
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         if (this.m_6898_(stack)) {
            if (this.m_21223_() < this.m_21233_()) {
               if (!player.m_7500_()) {
                  stack.m_41774_(1);
               }

               this.m_8035_();
               this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.SUCCESS;
            }

            if (this.m_6162_()) {
               this.m_142075_(player, hand, stack);
               this.m_146740_(m_216967_(-this.m_146764_()), true);
               this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }

            if (!this.m_6162_() && this.m_5957_()) {
               this.m_142075_(player, hand, stack);
               this.m_27595_(player);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         }

         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   private void royalLordEvolves() {
      Level level = this.m_9236_();
      CompoundTag tag = this.serializeNBT();
      this.m_146870_();
      OrcLordEntity orc = new OrcLordEntity((EntityType)TensuraEntityTypes.ORC_LORD.get(), level);
      orc.m_20258_(tag);
      if (level instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)level;
         orc.m_6518_(serverLevel, level.m_6436_(orc.m_20183_()), MobSpawnType.CONVERSION, (SpawnGroupData)null, (CompoundTag)null);
      }

      orc.setVariant(OrcVariant.NORMAL);
      RaceHelper.applyBaseAttribute(OrcLordEntity.setAttributes(), orc, true);
      RaceHelper.updateSpiritualHP(orc);
      RaceHelper.updateEntityEPCount(orc);
      orc.m_21153_(orc.m_21233_());
      level.m_7967_(orc);
      level.m_5594_((Player)null, orc.m_20183_(), SoundEvents.f_12306_, SoundSource.PLAYERS, 1.0F, 1.0F);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123747_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123812_);
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      if (this.m_21824_()) {
         return false;
      } else {
         List<? extends OrcLordEntity> list = this.f_19853_.m_45976_(OrcLordEntity.class, this.m_20191_().m_82377_(30.0D, 8.0D, 30.0D));
         return list.isEmpty();
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (this.getClass() != OrcEntity.class) {
         return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
      } else {
         if (this.canSpawnSpecialVariant(pReason) && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.orcLordChance.get(), pLevel.m_213780_())) {
            OrcLordEntity orcLord = new OrcLordEntity((EntityType)TensuraEntityTypes.ORC_LORD.get(), this.m_9236_());
            orcLord.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
            orcLord.m_213945_(this.f_19796_, pDifficulty);
            orcLord.m_6518_(pLevel, this.f_19853_.m_6436_(this.m_20183_()), MobSpawnType.NATURAL, (SpawnGroupData)null, (CompoundTag)null);
            this.m_9236_().m_7967_(orcLord);
            this.m_142467_(RemovalReason.DISCARDED);
         } else if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.orcRoyalChance.get(), pLevel.m_213780_())) {
            if (this.canSpawnSpecialVariant(pReason) && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.orcRoyalLordChance.get(), pLevel.m_213780_())) {
               this.setVariant(OrcVariant.ROYAL_LORD);
               ManasSkillInstance instance = new TensuraSkillInstance((ManasSkill)CommonSkills.SELF_REGENERATION.get());
               instance.setToggled(true);
               SkillAPI.getSkillsFrom(this).learnSkill(instance);
            } else {
               this.setVariant(OrcVariant.ROYAL);
            }
         }

         if (!pReason.equals(MobSpawnType.BUCKET)) {
            this.m_213945_(this.f_19796_, pDifficulty);
         }

         return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
      }
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.orcSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      if (!(pRandom.m_188501_() >= 0.2F)) {
         int i = pRandom.m_188503_(3);
         ItemStack stack = new ItemStack(Items.f_42386_);
         if (i == 0) {
            stack = new ItemStack((ItemLike)TensuraToolItems.IRON_SPEAR.get());
         }

         this.m_8061_(EquipmentSlot.MAINHAND, stack);
         this.inventory.m_6836_(4, stack);
         this.inventory.m_6596_();
      }
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      switch(pSlot) {
      case HEAD:
         if (pChance == 0) {
            return Items.f_42407_;
         } else {
            if (pChance == 1) {
               return Items.f_42468_;
            }

            return null;
         }
      case CHEST:
         if (pChance == 0) {
            return Items.f_42408_;
         } else if (pChance == 1) {
            return Items.f_42465_;
         } else {
            if (pChance == 2) {
               return Items.f_42469_;
            }

            return null;
         }
      case LEGS:
         if (pChance == 0) {
            return Items.f_42462_;
         } else if (pChance == 1) {
            return Items.f_42466_;
         } else {
            if (pChance == 2) {
               return Items.f_42470_;
            }

            return null;
         }
      case FEET:
         if (pChance == 0) {
            return Items.f_42463_;
         } else if (pChance == 1) {
            return Items.f_42467_;
         } else {
            if (pChance == 2) {
               return Items.f_42471_;
            }

            return null;
         }
      default:
         return null;
      }
   }

   protected SoundEvent m_7515_() {
      if (this.m_21660_()) {
         return SoundEvents.f_12302_;
      } else {
         return this.m_6162_() ? SoundEvents.f_12239_ : SoundEvents.f_12301_;
      }
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12304_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12303_;
   }

   public SoundSource m_5720_() {
      return SoundSource.NEUTRAL;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.sleep", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.shouldSwim()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.swim", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.sitting", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         if (event.isMoving()) {
            if (this.m_20096_() && this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.run", EDefaultLoopTypes.LOOP));
            } else if (this.m_20096_() || !this.isInFluidType()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.walk", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.attack", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.shield", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.crossbow", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4 || this.m_6117_() && this.isSpearType(this.m_21211_())) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc.spear", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(OrcEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(OrcEntity.class, EntityDataSerializers.f_135028_);
      EVOLVING = SynchedEntityData.m_135353_(OrcEntity.class, EntityDataSerializers.f_135028_);
   }

   public class EvolvingLookControl extends TensuraTamableEntity.SleepLookControl {
      public EvolvingLookControl() {
         super();
      }

      public void m_8128_() {
         if (OrcEntity.this.getEvolving() <= 0) {
            super.m_8128_();
         }

      }
   }

   public class EvolvingMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public EvolvingMoveControl() {
         super();
      }

      public void m_8126_() {
         if (OrcEntity.this.getEvolving() <= 0) {
            super.m_8126_();
         }

      }
   }

   static class FollowLordGoal extends Goal {
      private final OrcEntity orc;
      @Nullable
      private OrcLordEntity lord;
      private final double speedModifier;
      private int timeToRecalcPath;

      public FollowLordGoal(OrcEntity orc, double pSpeedModifier) {
         this.orc = orc;
         this.speedModifier = pSpeedModifier;
      }

      public boolean m_8036_() {
         if (this.orc.m_21824_()) {
            return false;
         } else if (!this.orc.m_21523_() && !this.orc.m_20159_()) {
            List<? extends OrcLordEntity> list = this.orc.f_19853_.m_45976_(OrcLordEntity.class, this.orc.m_20191_().m_82377_(30.0D, 8.0D, 30.0D));
            OrcLordEntity lord = null;
            double d0 = Double.MAX_VALUE;
            Iterator var5 = list.iterator();

            while(var5.hasNext()) {
               OrcLordEntity parent = (OrcLordEntity)var5.next();
               double distance = this.orc.m_20280_(parent);
               if (distance <= d0) {
                  d0 = distance;
                  lord = parent;
               }
            }

            if (lord == null) {
               return false;
            } else if (this.orc.m_5448_() != null && lord.m_21223_() > lord.m_21233_() / 8.0F) {
               return false;
            } else {
               boolean lordLowHealth = lord.m_21223_() < lord.m_21233_() / 4.0F;
               if (!(d0 > 100.0D) && (!lordLowHealth || !(d0 > 12.0D))) {
                  return false;
               } else {
                  this.lord = lord;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         if (this.orc.m_21824_()) {
            return false;
         } else if (!this.orc.m_21523_() && !this.orc.m_20159_()) {
            if (this.orc.m_5448_() != null) {
               return false;
            } else if (this.lord == null) {
               return false;
            } else if (!this.lord.m_6084_()) {
               return false;
            } else {
               double d0 = this.orc.m_20280_(this.lord);
               return d0 >= 16.0D && d0 <= 100.0D;
            }
         } else {
            return false;
         }
      }

      public void m_8056_() {
         this.timeToRecalcPath = 0;
         if (this.orc.m_5803_()) {
            this.orc.setSleeping(false);
         }

      }

      public void m_8041_() {
         this.lord = null;
         this.orc.m_21573_().m_26573_();
      }

      public void m_8037_() {
         if (this.lord != null) {
            if (this.lord.m_5448_() != null && this.orc.m_5448_() == null && this.lord.m_5448_() != this.orc && this.lord.m_5448_().m_20270_(this.orc) <= 30.0F) {
               this.orc.m_6710_(this.lord.m_5448_());
            }

            boolean isDisaster = this.lord instanceof OrcDisasterEntity;
            this.orc.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSPIRATION.get(), 200, isDisaster ? 1 : 0, false, false, false), this.lord);
            this.orc.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false), this.lord);
            if (isDisaster) {
               this.orc.m_147207_(new MobEffectInstance(MobEffects.f_19605_, 200, 2, false, false, false), this.lord);
               this.orc.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), 200, 1, false, false, false), this.lord);
            }
         }

         if (--this.timeToRecalcPath <= 0) {
            if (this.lord != null) {
               this.timeToRecalcPath = this.m_183277_(10);
               this.orc.m_21573_().m_5624_(this.lord, this.speedModifier);
            }
         }
      }
   }
}
