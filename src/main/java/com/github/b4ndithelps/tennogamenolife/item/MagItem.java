package com.github.b4ndithelps.tennogamenolife.item;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.network.chat.Component;
import java.util.List;

public class MagItem extends Item {
    public MagItem(Properties properties) {
        super(properties.tab(TensuraCreativeTab.MISCELLANEOUS));
    }

    /**
     * @param entity
     * @param amount
     */
    private void addEP(LivingEntity entity, double amount) {
        double currentEP = TensuraEPCapability.getEP(entity);
        double newEP = currentEP + amount;
        TensuraEPCapability.setLivingEP(entity, newEP);
        TensuraEPCapability.setCurrentLivingEP(entity, newEP);
    }

    @Override
    public InteractionResultHolder<ItemStack> use(Level level, Player player, InteractionHand hand) {
        ItemStack itemstack = player.getItemInHand(hand);
        if (!level.isClientSide) {

            Vec3 playerPos = player.position();
 
            Vec3 lookVec = player.getLookAngle();

            Vec3 targetPos = playerPos.add(lookVec.x * 3.0D, lookVec.y * 3.0D, lookVec.z * 3.0D);
            
 
            AABB aabb = new AABB(
                targetPos.x - 1.0D, targetPos.y - 1.0D, targetPos.z - 1.0D,
                targetPos.x + 1.0D, targetPos.y + 1.0D, targetPos.z + 1.0D
            );
            
       
            for (LivingEntity entity : level.getEntitiesOfClass(LivingEntity.class, aabb)) {
           
                if (entity instanceof Player) {
                    continue; 
                }
          
                if (TensuraEPCapability.getEP(entity) > 0.0D) {
        
                    addEP(entity, 10000.0D);
        
                    itemstack.shrink(1);
                    break;
                }
            }
        }
        return InteractionResultHolder.sidedSuccess(itemstack, level.isClientSide());
    }

    /**
     * 添加物品描述
     */
    @Override
    public void appendHoverText(ItemStack stack, Level level, List<Component> tooltip, TooltipFlag flag) {
        tooltip.add(Component.translatable("item.tennogamenolife.mag.desc"));
    }
} 