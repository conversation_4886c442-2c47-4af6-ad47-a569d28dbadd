package com.github.b4ndithelps.tennogamenolife.item;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.network.chat.Component;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraftforge.registries.ForgeRegistries;

import javax.annotation.Nullable;
import java.util.List;

public class MechanicalCoreItem extends Item {
    public MechanicalCoreItem(Properties properties) {
        super(properties
                .tab(TensuraCreativeTab.MISCELLANEOUS)
                .food(new FoodProperties.Builder()
                        .nutrition(4)
                        .saturationMod(0.5f)
                        .alwaysEat()
                        .build())
                .fireResistant());
    }

    @Override
    public ItemStack finishUsingItem(ItemStack stack, Level level, LivingEntity entity) {
        if (!level.isClientSide) {
            entity.addEffect(new MobEffectInstance(
                    ForgeRegistries.MOB_EFFECTS.getValue(new net.minecraft.resources.ResourceLocation("tensura", "burden")),
                    6000, 
                    0
            ));
        }
        return super.finishUsingItem(stack, level, entity);
    }

    @Override
    public void appendHoverText(ItemStack stack, @Nullable Level level, List<Component> tooltip, TooltipFlag flag) {
        tooltip.add(Component.translatable("item.tennogamenolife.mechanical_core.desc"));
    }
} 