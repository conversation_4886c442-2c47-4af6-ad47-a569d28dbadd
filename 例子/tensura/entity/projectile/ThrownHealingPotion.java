package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.awt.Color;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.ItemSupplier;
import net.minecraft.world.entity.projectile.ThrowableItemProjectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.HitResult.Type;

public class ThrownHealingPotion extends ThrowableItemProjectile implements ItemSupplier {
   public ThrownHealingPotion(EntityType<? extends ThrownHealingPotion> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public ThrownHealingPotion(Level pLevel, LivingEntity pShooter) {
      super((EntityType)TensuraEntityTypes.HEALING_POTION.get(), pShooter, pLevel);
   }

   public ThrownHealingPotion(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.HEALING_POTION.get(), pX, pY, pZ, pLevel);
   }

   protected Item m_7881_() {
      return (Item)TensuraConsumableItems.FULL_POTION.get();
   }

   protected float m_7139_() {
      return 0.05F;
   }

   protected void m_6532_(HitResult pResult) {
      super.m_6532_(pResult);
      if (!this.f_19853_.f_46443_) {
         this.applySplash(this.m_7846_(), pResult.m_6662_() == Type.ENTITY ? ((EntityHitResult)pResult).m_82443_() : null);
         this.f_19853_.m_46796_(2007, this.m_20183_(), (new Color(196, 249, 239)).getRGB());
         this.m_146870_();
      }
   }

   private void applySplash(ItemStack stack, @Nullable Entity directTarget) {
      Item var4 = stack.m_41720_();
      if (var4 instanceof HealingPotionItem) {
         HealingPotionItem potionItem = (HealingPotionItem)var4;
         if (directTarget instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)directTarget;
            this.healTarget(potionItem, target, 1.0F);
         }

         AABB aabb = this.m_20191_().m_82377_(3.0D, 3.0D, 3.0D);
         List<LivingEntity> list = this.f_19853_.m_45976_(LivingEntity.class, aabb);
         if (!list.isEmpty()) {
            Iterator var6 = list.iterator();

            while(var6.hasNext()) {
               LivingEntity target = (LivingEntity)var6.next();
               double dist = this.m_20280_(target);
               if (dist >= 9.0D) {
                  return;
               }

               this.healTarget(potionItem, target, (float)(1.0D - Math.sqrt(dist) / 3.0D));
            }

         }
      }
   }

   private void healTarget(HealingPotionItem potionItem, LivingEntity target, float multiplier) {
      potionItem.healEntity(target, multiplier);
      target.f_19853_.m_6269_((Player)null, target, SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
      TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_);
   }
}
