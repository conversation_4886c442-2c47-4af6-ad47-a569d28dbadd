package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.data.pack.GearEPCount;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.enchantment.BarrierPiercingEnchantment;
import com.github.manasmods.tensura.enchantment.BreathingSupportEnchantment;
import com.github.manasmods.tensura.enchantment.DeadEndRainbowEnchantment;
import com.github.manasmods.tensura.enchantment.ElementalResistanceEnchantment;
import com.github.manasmods.tensura.enchantment.HolyCoatEnchantment;
import com.github.manasmods.tensura.enchantment.MagicInterferenceEnchantment;
import com.github.manasmods.tensura.enchantment.SlottingEnchantment;
import com.github.manasmods.tensura.enchantment.TsukumogamiEnchantment;
import com.github.manasmods.tensura.item.templates.custom.TwoHandedLongSword;
import com.github.manasmods.tensura.item.templates.custom.TwoHandedSword;
import com.github.manasmods.tensura.registry.battlewill.UtilityArts;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeHelper;
import java.util.Iterator;
import java.util.Objects;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.living.LivingEquipmentChangeEvent;
import net.minecraftforge.event.entity.living.LivingEntityUseItemEvent.Stop;
import net.minecraftforge.event.entity.player.PlayerEvent.BreakSpeed;
import net.minecraftforge.event.entity.player.PlayerInteractEvent.RightClickItem;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.registries.ForgeRegistries;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class EnchantmentHandler {
   @SubscribeEvent
   public static void updateTwoHandedLongSwords(LivingEquipmentChangeEvent event) {
      ItemStack toStack;
      LivingEntity entity;
      label90: {
         toStack = event.getTo();
         entity = event.getEntity();
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               break label90;
            }
         }

         if (toStack.m_204117_(TensuraTags.Items.BODY_ARMOR_ITEMS)) {
            if (!SkillUtils.hasSkill(entity, (ManasSkill)IntrinsicSkills.BODY_ARMOR.get())) {
               entity.m_5496_(SoundEvents.f_12018_, 1.0F, 1.0F);
               toStack.m_41774_(1);
               entity.m_21166_(event.getSlot());
               return;
            }
         } else if (toStack.m_150930_((Item)TensuraToolItems.AURA_SHIELD.get())) {
            if (!SkillUtils.hasSkill(entity, (ManasSkill)UtilityArts.AURA_SHIELD.get())) {
               entity.m_5496_(SoundEvents.f_144242_, 1.0F, 1.0F);
               toStack.m_41774_(1);
               entity.m_21166_(event.getSlot());
               return;
            }
         } else if (event.getSlot().m_20743_().equals(Type.ARMOR) && entity instanceof Player) {
            Player player = (Player)entity;
            if (!toStack.m_204117_(TensuraTags.Items.HOLY_ARMAMENTS_ITEMS) && event.getFrom().m_204117_(TensuraTags.Items.HOLY_ARMAMENTS_ITEMS) && !player.m_7500_() && !player.m_5833_() && !SkillUtils.canFlyWithSkills(player) && player.m_150110_().f_35936_) {
               player.m_150110_().f_35936_ = false;
               player.m_150110_().f_35935_ = false;
               player.m_6885_();
            }
         }
      }

      switch(event.getSlot()) {
      case MAINHAND:
         TensuraAttributeHelper.addEnchantmentAttribute(event, (Enchantment)TensuraEnchantments.SWIFT.get(), Attributes.f_22283_, TensuraAttributeHelper.ENCHANTMENT_ATTACK_SPEED_UUID, 0.2F);
         TensuraAttributeHelper.addEnchantmentAttribute(event, (Enchantment)TensuraEnchantments.SWIFT.get(), (Attribute)ForgeMod.ATTACK_RANGE.get(), TensuraAttributeHelper.ENCHANTMENT_ATTACK_REACH_UUID, 1.0F);
         if (toStack.m_41720_() instanceof TwoHandedLongSword || toStack.m_41720_() instanceof TwoHandedSword) {
            if (toStack.m_41720_() instanceof TwoHandedLongSword) {
               TwoHandedLongSword.setTwoHandedTag(toStack, entity.m_21206_().m_41619_());
            } else {
               TwoHandedSword.setTwoHandedTag(toStack, entity.m_21206_().m_41619_());
            }
         }
         break;
      case OFFHAND:
         if (!(entity.m_21205_().m_41720_() instanceof TwoHandedLongSword) && !(entity.m_21205_().m_41720_() instanceof TwoHandedSword)) {
            return;
         }

         if (entity.m_21205_().m_41720_() instanceof TwoHandedLongSword) {
            TwoHandedLongSword.setTwoHandedTag(entity.m_21205_(), toStack.m_41619_());
         } else {
            TwoHandedSword.setTwoHandedTag(entity.m_21205_(), toStack.m_41619_());
         }
      }

      BarrierPiercingEnchantment.applyMoonlightEnchantments(toStack);
      BreathingSupportEnchantment.applyAntiMagicMask(toStack);
      ElementalResistanceEnchantment.applyHolyEnchantments(toStack);
      DeadEndRainbowEnchantment.applyDeadEndRainbow(toStack);
      HolyCoatEnchantment.applyHolyCoat(toStack);
      MagicInterferenceEnchantment.applyMagicInterference(toStack);
      TsukumogamiEnchantment.applyTsukumogami(toStack, entity);
      TsukumogamiEnchantment.updateOwnerHolding(toStack, entity);
      TsukumogamiEnchantment.updateOwnerHolding(event.getFrom(), (LivingEntity)null);
      if (toStack.m_41741_() <= 1) {
         if (toStack.m_41783_() == null || toStack.m_41783_().m_128459_("EP") <= 0.0D) {
            Iterator var6 = TensuraData.getGearEP().iterator();

            while(var6.hasNext()) {
               GearEPCount gearEPCount = (GearEPCount)var6.next();
               if (Objects.equals(ForgeRegistries.ITEMS.getKey(toStack.m_41720_()), gearEPCount.getItem())) {
                  CompoundTag tag = toStack.m_41784_();
                  if (tag.m_128459_("EP") <= (double)gearEPCount.getMinEP()) {
                     tag.m_128347_("EP", (double)gearEPCount.getMinEP());
                  }

                  if (tag.m_128459_("MaxEP") < (double)gearEPCount.getMaxEP()) {
                     tag.m_128347_("MaxEP", (double)gearEPCount.getMaxEP());
                  }
                  break;
               }
            }
         }

      }
   }

   @SubscribeEvent
   public static void onStartUse(RightClickItem e) {
      if (SlottingEnchantment.onUse(e.getEntity(), e.getHand())) {
         e.setCancellationResult(InteractionResultHolder.m_19096_(e.getEntity().m_21120_(e.getHand())).m_19089_());
      }

   }

   @SubscribeEvent
   public static void onStopUsing(Stop e) {
      if (SlottingEnchantment.onRelease(e.getItem(), e.getEntity(), e.getDuration())) {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent
   public static void onBreakingSpeed(BreakSpeed e) {
      Player player = e.getEntity();
      if (!player.m_20096_()) {
         if (player.m_6844_(EquipmentSlot.FEET).getEnchantmentLevel((Enchantment)TensuraEnchantments.STURDY.get()) > 0) {
            e.setNewSpeed(e.getNewSpeed() * 5.0F);
         }
      }
   }
}
