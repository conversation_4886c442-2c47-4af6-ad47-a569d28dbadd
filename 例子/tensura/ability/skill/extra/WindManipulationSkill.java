package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.entity.magic.projectile.WindSphereProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class WindManipulationSkill extends Skill {
   public WindManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return TensuraSkillCapability.getSpiritLevel(entity, MagicElemental.WIND.getId()) >= 1;
   }

   public double learningCost() {
      return 200.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return entity.m_6144_() ? 200.0D : 20.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      MolecularManipulationSkill.learnMolecular(entity);
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.WIND_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.WIND_DOMINATION.get())) {
            if (DamageSourceHelper.isWindDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_6144_()) {
         instance.getOrCreateTag().m_128405_("BreathEntity", 0);
         instance.markDirty();
      } else if (!SkillHelper.outOfMagicule(entity, instance)) {
         this.addMasteryPoint(instance, entity);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         WindSphereProjectile windSphere = new WindSphereProjectile(entity.m_9236_(), entity);
         windSphere.setSpeed(1.0F);
         windSphere.setDamage(12.0F);
         windSphere.m_20242_(true);
         windSphere.setKnockForce(3.0F);
         windSphere.setBurnTicks(-1);
         windSphere.setMpCost(this.magiculeCost(entity, instance));
         windSphere.setSkill(instance);
         windSphere.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(windSphere);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity.m_6144_()) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         float damage = instance.isMastered(entity) ? 8.0F : 4.0F;
         BreathEntity.spawnBreathEntity((EntityType)TensuraEntityTypes.WIND_BREATH.get(), entity, instance, damage, this.magiculeCost(entity, instance));
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
         entity.m_20095_();
         return true;
      }
   }
}
