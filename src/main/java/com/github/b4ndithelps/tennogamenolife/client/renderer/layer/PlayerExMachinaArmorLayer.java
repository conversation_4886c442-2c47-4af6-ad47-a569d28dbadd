package com.github.b4ndithelps.tennogamenolife.client.renderer.layer;

import com.github.b4ndithelps.tennogamenolife.client.model.XiubiArmorModel;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.ExMachinaDecorativeManager;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.IExMachinaDecorativeRace;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.model.PlayerModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;

/**
 * ExMachina种族玩家装饰渲染层
 * 为所有ExMachina种族玩家添加机铠装饰
 */
public class PlayerExMachinaArmorLayer extends RenderLayer<AbstractClientPlayer, PlayerModel<AbstractClientPlayer>> {
    
    private static final ResourceLocation ARMOR_TEXTURE =
        new ResourceLocation("tennogamenolife", "textures/entity/xiubi/armor.png");
    
    private final XiubiArmorModel armorModel;
    
    public PlayerExMachinaArmorLayer(RenderLayerParent<AbstractClientPlayer, PlayerModel<AbstractClientPlayer>> renderer, 
                                    XiubiArmorModel armorModel) {
        super(renderer);
        this.armorModel = armorModel;
    }
    
    @Override
    public void render(PoseStack poseStack, MultiBufferSource buffer, int packedLight,
                      AbstractClientPlayer player, float limbSwing, float limbSwingAmount,
                      float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {

        // 检查玩家是否是ExMachina种族
        if (!isExMachinaRace(player)) {
            return;
        }

        // 检查是否应该显示装饰
        if (!ExMachinaDecorativeManager.shouldShowDecorative(player)) {
            return;
        }


        
        // 更新装饰状态
        ExMachinaDecorativeManager.updateDecorativeState(player);

        // 获取玩家模型并设置动画
        PlayerModel<AbstractClientPlayer> playerModel = this.getParentModel();
        playerModel.setupAnim(player, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);

        // 设置装饰动画（但不包括头部，头部由PoseStack控制）
        this.armorModel.setupAnimForPlayer(limbSwing, limbSwingAmount, ageInTicks, 0.0F, 0.0F);

        // 应用机铠特有的动画效果
        applyArmorSpecificAnimations(player, ageInTicks);

        // 使用PoseStack变换矩阵实现完美绑定

        // 渲染身体装饰（绑定到身体）- bone4本体, bone7, bone8, zuoyi, youyi
        poseStack.pushPose();
        applyPlayerBodyTransform(poseStack, playerModel.body);
        renderBodyDecorations(poseStack, buffer, packedLight);
        poseStack.popPose();

        // 渲染头部装饰（按照精灵耳朵的方式绑定）- bone3, bone5
        poseStack.pushPose();
        applyElfEarsStyleHeadTransform(poseStack, player, netHeadYaw, headPitch);
        renderHeadDecorations(poseStack, buffer, packedLight);
        poseStack.popPose();

    }
    


    /**
     * 应用机铠特有的动画效果
     */
    private void applyArmorSpecificAnimations(Player player, float ageInTicks) {
        // 获取动画状态和计时器
        ExMachinaDecorativeManager.ArmorAnimationState animState =
            ExMachinaDecorativeManager.getArmorAnimationState(player);
        int animTimer = ExMachinaDecorativeManager.getAnimationTimer(player);

        // 根据动画状态应用相应的动画（这些动画会叠加在骨骼绑定之上）
        switch (animState) {
            case WEAPON:
                // 武器动画 - 在绑定基础上添加武器相关的动画
                applyWeaponAnimationOverlay(animTimer);
                break;
            case CANCEL_WEAPON:
                // 取消武器动画 - 在绑定基础上添加取消动画
                applyCancelWeaponAnimationOverlay(animTimer);
                break;
            case IDLE:
            default:
                // 待机动画 - 在绑定基础上添加待机效果（如翅膀轻微摆动）
                applyIdleAnimationOverlay(ageInTicks);
                break;
        }
    }

    /**
     * 武器动画叠加效果
     */
    private void applyWeaponAnimationOverlay(int timer) {
        // 应用武器动画
        this.armorModel.applyWeaponAnimation(timer);
    }

    /**
     * 取消武器动画叠加效果
     */
    private void applyCancelWeaponAnimationOverlay(int timer) {
        // 应用取消武器动画
        this.armorModel.applyCancelWeaponAnimation(timer);
    }

    /**
     * 待机动画叠加效果
     */
    private void applyIdleAnimationOverlay(float ageInTicks) {
        // 应用待机动画
        this.armorModel.applyIdleAnimation((int)ageInTicks);
    }

    /**
     * 应用玩家身体的完整变换矩阵
     * 这样装饰就会完全跟随玩家身体的所有动作
     */
    private void applyPlayerBodyTransform(PoseStack poseStack, ModelPart playerBody) {
        // 应用玩家身体的位置变换（用于蹲下等动作的位移）
        poseStack.translate(playerBody.x / 16.0F, playerBody.y / 16.0F, playerBody.z / 16.0F);

        // 应用玩家身体的旋转变换（完全跟随身体旋转）
        poseStack.mulPose(com.mojang.math.Vector3f.XP.rotation(playerBody.xRot));
        poseStack.mulPose(com.mojang.math.Vector3f.YP.rotation(playerBody.yRot));
        poseStack.mulPose(com.mojang.math.Vector3f.ZP.rotation(playerBody.zRot));

        // 应用玩家身体的缩放变换
        poseStack.scale(playerBody.xScale, playerBody.yScale, playerBody.zScale);
    }
    private void applyElfEarsStyleHeadTransform(PoseStack poseStack, AbstractClientPlayer player, float netHeadYaw, float headPitch) {
        // 获取玩家模型来获取正确的头部位置
        PlayerModel<AbstractClientPlayer> playerModel = this.getParentModel();

        // 先应用玩家头部的位置变换（包括蹲下时的位置变化）
        // 使用公共字段访问，避免混淆问题
        poseStack.translate(playerModel.head.x / 16.0F, playerModel.head.y / 16.0F, playerModel.head.z / 16.0F);

        float scaleFactor = player.getScale();
        float yawRadians = (float)Math.toRadians((double)netHeadYaw);
        float pitchRadians = (float)Math.toRadians((double)headPitch);
        poseStack.mulPose(com.mojang.math.Vector3f.YP.rotation(yawRadians));
        poseStack.mulPose(com.mojang.math.Vector3f.XP.rotation(pitchRadians));
        float yTranslation = 0.05F * scaleFactor;
        if (player.isCrouching()) {
            yTranslation -= 0.1F * scaleFactor;
        }

        poseStack.translate(0.0F, (double)yTranslation, 0.0F);
        poseStack.scale(playerModel.head.xScale, playerModel.head.yScale, playerModel.head.zScale);
    }

    /**
     * 应用玩家头部的完整变换矩阵
     * 这样头部装饰就会完全跟随玩家头部的所有动作，就像身体装饰一样
     */
    private void applyPlayerHeadTransform(PoseStack poseStack, ModelPart playerHead) {
        // 应用玩家头部的位置变换
        poseStack.translate(playerHead.x / 16.0F, playerHead.y / 16.0F, playerHead.z / 16.0F);

        // 应用玩家头部的旋转变换（完全跟随头部旋转）
        poseStack.mulPose(com.mojang.math.Vector3f.XP.rotation(playerHead.xRot));
        poseStack.mulPose(com.mojang.math.Vector3f.YP.rotation(playerHead.yRot));
        poseStack.mulPose(com.mojang.math.Vector3f.ZP.rotation(playerHead.zRot));

        // 应用玩家头部的缩放变换
        poseStack.scale(playerHead.xScale, playerHead.yScale, playerHead.zScale);
    }

    /**
     * 渲染身体装饰部位（bone7, bone8, zuoyi, youyi，以及bone4但排除头部子部位）
     */
    private void renderBodyDecorations(PoseStack poseStack, MultiBufferSource buffer, int packedLight) {
        // 使用反射访问私有字段来分别渲染身体部位
        try {
            // 获取身体装饰部位
            java.lang.reflect.Field bone4Field = this.armorModel.getClass().getDeclaredField("bone4");
            java.lang.reflect.Field bone7Field = this.armorModel.getClass().getDeclaredField("bone7");
            java.lang.reflect.Field bone8Field = this.armorModel.getClass().getDeclaredField("bone8");
            java.lang.reflect.Field zuoyiField = this.armorModel.getClass().getDeclaredField("zuoyi");
            java.lang.reflect.Field youyiField = this.armorModel.getClass().getDeclaredField("youyi");

            bone4Field.setAccessible(true);
            bone7Field.setAccessible(true);
            bone8Field.setAccessible(true);
            zuoyiField.setAccessible(true);
            youyiField.setAccessible(true);

            ModelPart bone4 = (ModelPart) bone4Field.get(this.armorModel);
            ModelPart bone7 = (ModelPart) bone7Field.get(this.armorModel);
            ModelPart bone8 = (ModelPart) bone8Field.get(this.armorModel);
            ModelPart zuoyi = (ModelPart) zuoyiField.get(this.armorModel);
            ModelPart youyi = (ModelPart) youyiField.get(this.armorModel);

            // 渲染bone4的本体部分（排除头部子部位bone3和bone5）
            renderBone4BodyOnly(bone4, poseStack, buffer, packedLight);

            // 渲染其他身体装饰部位
            if (bone7 != null) {
                bone7.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                           packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
            }

            if (bone8 != null) {
                bone8.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                           packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
            }

            if (zuoyi != null) {
                zuoyi.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                           packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
            }

            if (youyi != null) {
                youyi.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                           packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
            }

        } catch (Exception e) {
            System.err.println("Failed to render body decorations: " + e.getMessage());
        }
    }

    /**
     * 渲染头部装饰部位（bone3, bone5）
     * 独立处理但需要应用bone4的基础位置偏移
     */
    private void renderHeadDecorations(PoseStack poseStack, MultiBufferSource buffer, int packedLight) {
        try {
            // 获取bone4来获取基础位置偏移
            ModelPart bone4 = getBone4FromModel();

            poseStack.pushPose();

            try {
                // 应用bone4的基础位置偏移（bone3和bone5需要这个偏移才能在正确位置）
                if (bone4 != null) {
                    poseStack.translate(bone4.x / 16.0F, bone4.y / 16.0F, bone4.z / 16.0F);
                    // 不应用旋转，只应用位置和缩放
                    poseStack.scale(bone4.xScale, bone4.yScale, bone4.zScale);
                } else {
                    // 如果获取bone4失败，使用默认的bone4位置偏移
                    poseStack.translate(0.0F, 24.0F / 16.0F, 0.0F); // bone4的默认位置
                }

                // 直接获取bone3和bone5
                ModelPart bone3 = getDirectBone3FromModel();
                ModelPart bone5 = getDirectBone5FromModel();

                // 渲染bone3和bone5
                if (bone3 != null) {
                    bone3.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                               packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
                }

                if (bone5 != null) {
                    bone5.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                               packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);
                }
            } finally {
                poseStack.popPose();
            }
        } catch (Exception e) {
            System.err.println("Failed to render head decorations: " + e.getMessage());
        }
    }

    /**
     * 直接从模型获取bone3，不通过bone4
     */
    private ModelPart getDirectBone3FromModel() {
        try {
            // 首先尝试通过bone4获取（如果bone4存在的话）
            ModelPart bone4 = getBone4FromModel();
            if (bone4 != null) {
                ModelPart bone3 = getBone3FromBone4(bone4);
                if (bone3 != null) {
                    return bone3;
                }
            }

            // 如果通过bone4获取失败，尝试直接从模型字段获取
            String[] possibleNames = {"bone3"};
            for (String fieldName : possibleNames) {
                try {
                    java.lang.reflect.Field field = this.armorModel.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return (ModelPart) field.get(this.armorModel);
                } catch (NoSuchFieldException e) {
                    continue;
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to get bone3 directly: " + e.getMessage());
        }
        return null;
    }

    /**
     * 直接从模型获取bone5，不通过bone4
     */
    private ModelPart getDirectBone5FromModel() {
        try {
            // 首先尝试通过bone4获取（如果bone4存在的话）
            ModelPart bone4 = getBone4FromModel();
            if (bone4 != null) {
                ModelPart bone5 = getBone5FromBone4(bone4);
                if (bone5 != null) {
                    return bone5;
                }
            }

            // 如果通过bone4获取失败，尝试直接从模型字段获取
            String[] possibleNames = {"bone5"};
            for (String fieldName : possibleNames) {
                try {
                    java.lang.reflect.Field field = this.armorModel.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return (ModelPart) field.get(this.armorModel);
                } catch (NoSuchFieldException e) {
                    continue;
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to get bone5 directly: " + e.getMessage());
        }
        return null;
    }

    /**
     * 混淆安全的方法获取bone4
     */
    private ModelPart getBone4FromModel() {
        try {
            // 尝试多种可能的字段名（开发环境和生产环境）
            String[] possibleNames = {"bone4", "f_103375_", "field_103375"}; // 添加可能的混淆名

            for (String fieldName : possibleNames) {
                try {
                    java.lang.reflect.Field field = this.armorModel.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return (ModelPart) field.get(this.armorModel);
                } catch (NoSuchFieldException e) {
                    // 继续尝试下一个名称
                    continue;
                }
            }

            // 如果所有名称都失败，尝试遍历所有字段
            java.lang.reflect.Field[] fields = this.armorModel.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                if (field.getType() == ModelPart.class) {
                    field.setAccessible(true);
                    ModelPart part = (ModelPart) field.get(this.armorModel);
                    // 检查是否是bone4（通过位置判断）
                    if (Math.abs(part.y - 24.0F) < 0.1F) { // bone4的Y位置是24
                        return part;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to get bone4: " + e.getMessage());
        }
        return null;
    }

    /**
     * 混淆安全的方法获取bone3
     */
    private ModelPart getBone3FromBone4(ModelPart bone4) {
        try {
            // 使用混淆安全的方法获取children
            java.lang.reflect.Field childrenField = ModelPart.class.getDeclaredField("children");
            childrenField.setAccessible(true);

            @SuppressWarnings("unchecked")
            java.util.Map<String, ModelPart> children =
                (java.util.Map<String, ModelPart>) childrenField.get(bone4);

            return children.get("bone3");
        } catch (Exception e) {
            System.err.println("Failed to get bone3: " + e.getMessage());
            return null;
        }
    }

    /**
     * 混淆安全的方法获取bone5
     */
    private ModelPart getBone5FromBone4(ModelPart bone4) {
        try {
            // 使用混淆安全的方法获取children
            java.lang.reflect.Field childrenField = ModelPart.class.getDeclaredField("children");
            childrenField.setAccessible(true);

            @SuppressWarnings("unchecked")
            java.util.Map<String, ModelPart> children =
                (java.util.Map<String, ModelPart>) childrenField.get(bone4);

            return children.get("bone5");
        } catch (Exception e) {
            System.err.println("Failed to get bone5: " + e.getMessage());
            return null;
        }
    }

    /**
     * 只渲染bone4的本体部分，永远排除头部子部位bone3和bone5
     */
    private void renderBone4BodyOnly(ModelPart bone4, PoseStack poseStack, MultiBufferSource buffer, int packedLight) {
        if (bone4 == null) return;

        ModelPart bone3 = null;
        ModelPart bone5 = null;
        java.util.Map<String, ModelPart> children = null;

        try {
            // 临时移除头部子部位
            java.lang.reflect.Field childrenField = ModelPart.class.getDeclaredField("children");
            childrenField.setAccessible(true);

            @SuppressWarnings("unchecked")
            java.util.Map<String, ModelPart> childrenMap =
                (java.util.Map<String, ModelPart>) childrenField.get(bone4);
            children = childrenMap;

            // 永久移除头部子部位，确保它们永远不会被渲染
            bone3 = children.remove("bone3");
            bone5 = children.remove("bone5");

            // 渲染bone4本体（不包含头部子部位）
            bone4.render(poseStack, buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)),
                        packedLight, OverlayTexture.NO_OVERLAY, 1.0F, 1.0F, 1.0F, 1.0F);

        } catch (Exception e) {
            System.err.println("Failed to render bone4 body only: " + e.getMessage());
            // 即使发生异常，也不渲染bone4，避免渲染头部子部位
        } finally {
            // 在finally块中恢复头部子部位，确保总是执行
            if (children != null) {
                if (bone3 != null) {
                    children.put("bone3", bone3);
                }
                if (bone5 != null) {
                    children.put("bone5", bone5);
                }
            }
        }
    }

    /**
     * 检查玩家是否是ExMachina种族
     */
    private boolean isExMachinaRace(Player player) {
        Race race = TensuraPlayerCapability.getRace(player);
        return race instanceof IExMachinaDecorativeRace;
    }
}
