package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.PlayerModelPart;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;

public class WingsLayer<T extends LivingEntity, M extends EntityModel<T>> extends RenderLayer<T, M> {
   private static final ResourceLocation WINGS_LOCATION = new ResourceLocation("tensura", "textures/models/armor/bat_glider.png");
   private final WingsModel<T> model;

   public WingsLayer(RenderLayerParent<T, M> pRenderer) {
      super(pRenderer);
      this.model = new WingsModel(Minecraft.m_91087_().m_167973_().m_171103_(WingsModel.WINGS_LAYER));
   }

   public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float pPartialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
      ItemStack chest = entity.m_6844_(EquipmentSlot.CHEST);
      if (this.shouldRender(chest, entity)) {
         ResourceLocation resourcelocation;
         if (entity instanceof AbstractClientPlayer) {
            AbstractClientPlayer player = (AbstractClientPlayer)entity;
            if (player.m_108562_() && player.m_108563_() != null) {
               resourcelocation = player.m_108563_();
            } else if (player.m_108555_() && player.m_108561_() != null && player.m_36170_(PlayerModelPart.CAPE)) {
               resourcelocation = player.m_108561_();
            } else {
               resourcelocation = this.getWingsTexture(entity);
            }
         } else {
            resourcelocation = this.getWingsTexture(entity);
         }

         pMatrixStack.m_85836_();
         pMatrixStack.m_85837_(0.0D, 0.0D, 0.125D);
         this.m_117386_().m_102624_(this.model);
         this.model.setupAnim(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
         VertexConsumer vertexconsumer = ItemRenderer.m_115184_(pBuffer, RenderType.m_110431_(resourcelocation), false, chest.m_41790_());
         this.model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, 1.0F);
         pMatrixStack.m_85849_();
      }

   }

   public boolean shouldRender(ItemStack chest, T entity) {
      return chest.m_150930_((Item)TensuraArmorItems.BAT_GLIDER.get());
   }

   public ResourceLocation getWingsTexture(T entity) {
      return WINGS_LOCATION;
   }
}
