package com.github.manasmods.tensura.entity.magic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.ForgeEventFactory;
import org.jetbrains.annotations.NotNull;

public class TensuraProjectile extends Projectile {
   private static final EntityDataAccessor<Integer> LIFE;
   private static final EntityDataAccessor<Float> SIZE;
   private static final EntityDataAccessor<Float> VISUAL_SIZE;
   private static final EntityDataAccessor<Float> LOOK_DISTANCE;
   private static final EntityDataAccessor<Integer> DELAY_TICK;
   protected float speed = 1.0F;
   protected float damage = 0.0F;
   protected float knockForce = 0.0F;
   protected float explosionRadius = 0.0F;
   protected int burnTicks = 0;
   @Nullable
   private MobEffectInstance mobEffect = null;
   protected float effectRange = 0.0F;
   protected double apCost = 0.0D;
   protected double mpCost = 0.0D;
   protected ManasSkillInstance skill = null;
   protected boolean spiritAttack = false;
   protected boolean invis = false;
   private Vec3 delayVec;
   private Vec3 ownerOffset;
   public int age;

   public TensuraProjectile(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.delayVec = Vec3.f_82478_;
      this.ownerOffset = Vec3.f_82478_;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.setPosDirection(entity, TensuraProjectile.PositionDirection.RIGHT);
      this.shootFromRot(entity.m_20154_());
      this.setLife((Integer)TensuraConfig.INSTANCE.entitiesConfig.magicDespawn.get());
   }

   public void shootFromRot(Vec3 rotation) {
      this.m_20256_(rotation.m_82490_((double)this.getSpeed()));
      this.setLife((Integer)TensuraConfig.INSTANCE.entitiesConfig.magicDespawn.get());
   }

   public void shootFromBehind(Entity pEntity, float pVelocity, float pInaccuracy) {
      this.m_146884_(pEntity.m_146892_().m_82546_(pEntity.m_20252_(1.0F).m_82490_(2.0D)));
      Vec3 towardEntity = (new Vec3(pEntity.m_20185_() - this.m_20185_(), pEntity.m_20188_() - this.m_20186_(), pEntity.m_20189_() - this.m_20189_())).m_82490_(0.10000000149011612D);
      this.m_6686_(towardEntity.m_7096_(), towardEntity.m_7098_(), towardEntity.m_7094_(), pVelocity, pInaccuracy);
      this.setLife((Integer)TensuraConfig.INSTANCE.entitiesConfig.magicDespawn.get());
   }

   public void setPosDirection(LivingEntity entity, TensuraProjectile.PositionDirection direction) {
      if (direction == TensuraProjectile.PositionDirection.MIDDLE) {
         this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_() - this.m_20191_().m_82376_() * 0.5D, 0.0D));
      } else {
         float rot = entity.f_20885_ + (float)(direction == TensuraProjectile.PositionDirection.LEFT ? -60 : 60);
         this.m_6034_(entity.m_20185_() - (double)entity.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), entity.m_20188_() - 0.20000000298023224D, entity.m_20189_() + (double)entity.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
      }

   }

   private void updateShootVector() {
      Entity entity = this.m_37282_();
      if (this.getLookDistance() != 0.0F) {
         if (entity instanceof LivingEntity) {
            LivingEntity var10000;
            LivingEntity owner;
            label23: {
               owner = (LivingEntity)entity;
               if (entity instanceof Mob) {
                  Mob mob = (Mob)entity;
                  if (mob.m_5448_() != null) {
                     var10000 = mob.m_5448_();
                     break label23;
                  }
               }

               var10000 = SkillHelper.getTargetingEntity(owner, (double)this.getLookDistance(), false, true);
            }

            Entity target = var10000;
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, owner, Fluid.NONE, (double)this.getLookDistance());
               pos = result.m_82450_();
            }

            this.setDelayVec(pos.m_82546_(this.m_20182_()).m_82541_().m_82490_((double)this.getSpeed()));
         }
      }
   }

   protected boolean m_5603_(Entity pTarget) {
      return pTarget != this && pTarget != this.m_37282_() ? super.m_5603_(pTarget) : false;
   }

   public void m_8119_() {
      super.m_8119_();
      this.tickHandler();
      if (this.f_19853_.m_5776_()) {
         this.flyingParticles();
      } else if (this.shouldRemove()) {
         this.m_146870_();
      }

   }

   public void tickHandler() {
      if (this.getDelayTick() > 0) {
         this.setDelayTick(this.getDelayTick() - 1);
         this.updateShootVector();
         Entity owner = this.m_37282_();
         if (this.getDelayTick() == 0) {
            this.m_20256_(this.getDelayVec());
            this.f_19864_ = true;
         } else if (this.ownerOffset != Vec3.f_82478_ && owner != null && !this.f_19794_) {
            this.m_146884_(owner.m_146892_().m_82549_(owner.m_20154_().m_82541_()).m_82549_(this.ownerOffset.m_82496_(-owner.m_146909_() * 0.017453292F).m_82524_(-owner.m_146908_() * 0.017453292F)));
         }

         this.setRotation(this.getDelayVec(), true);
      } else {
         this.setRotation(this.m_20184_(), false);
      }

      Vec3 position = this.m_20182_();
      Vec3 targetPos = position.m_82549_(this.m_20184_());
      AABB box = this.m_20191_().m_82383_(this.m_20184_().m_82541_()).m_82369_(this.m_20184_());
      Iterator var4 = this.f_19853_.m_6249_(this, box, this::m_5603_).iterator();

      while(var4.hasNext()) {
         Entity entity = (Entity)var4.next();
         EntityHitResult result = new EntityHitResult(entity);
         if (!ForgeEventFactory.onProjectileImpact(this, result)) {
            this.m_5790_(result);
            this.f_19853_.m_214171_(GameEvent.f_157777_, result.m_82450_(), Context.m_223719_(this, (BlockState)null));
         }
      }

      HitResult hitResult = this.f_19853_.m_45547_(new ClipContext(position, targetPos, Block.COLLIDER, Fluid.NONE, this));
      if (hitResult.m_6662_() == Type.BLOCK && !ForgeEventFactory.onProjectileImpact(this, hitResult)) {
         this.m_6532_(hitResult);
      }

      if (this.effectRange > 0.0F && this.mobEffect != null) {
         this.applyEffectAround((double)this.effectRange);
      }

      this.updateMovement();
      this.m_20101_();
   }

   protected boolean shouldRemove() {
      if (this.m_20072_() && this.shouldDiscardInWater()) {
         return true;
      } else if (this.m_20077_() && this.shouldDiscardInLava()) {
         return true;
      } else {
         double range = (Double)TensuraConfig.INSTANCE.entitiesConfig.magicDespawnRange.get();
         if (range > 0.0D && this.m_37282_() != null && (double)this.m_20270_(this.m_37282_()) > range) {
            return true;
         } else {
            return this.age++ > this.getLife();
         }
      }
   }

   public void setRotation(Vec3 vec3, boolean constantUpdate) {
      if (constantUpdate || this.f_19860_ == 0.0F && this.f_19859_ == 0.0F) {
         double d0 = vec3.m_165924_();
         this.m_146922_((float)(Mth.m_14136_(vec3.f_82479_, vec3.f_82481_) * 57.2957763671875D));
         this.m_146926_((float)(Mth.m_14136_(vec3.f_82480_, d0) * 57.2957763671875D));
         this.f_19859_ = this.m_146908_();
         this.f_19860_ = this.m_146909_();
      }

   }

   protected void m_37283_() {
      Vec3 vec3 = this.m_20184_();
      if (this.f_19794_) {
         this.m_146922_((float)(Mth.m_14136_(-vec3.f_82479_, -vec3.f_82481_) * 57.2957763671875D));
      } else {
         this.m_146922_((float)(Mth.m_14136_(vec3.f_82479_, vec3.f_82481_) * 57.2957763671875D));
      }

      this.m_146926_((float)(Mth.m_14136_(vec3.f_82480_, vec3.m_165924_()) * 57.2957763671875D));
      this.m_146926_(m_37273_(this.f_19860_, this.m_146909_()));
      this.m_146922_(m_37273_(this.f_19859_, this.m_146908_()));
   }

   public void updateMovement() {
      this.m_146884_(this.m_20182_().m_82549_(this.m_20184_()));
      if (this.getDelayTick() <= 0) {
         ProjectileUtil.m_37284_(this, 1.0F);
         if (!this.m_20068_()) {
            Vec3 vec34 = this.m_20184_();
            this.m_20334_(vec34.f_82479_, vec34.f_82480_ - 0.05000000074505806D, vec34.f_82481_);
         }
      }

   }

   protected boolean shouldGrief() {
      return this.m_37282_() instanceof Player ? TensuraGameRules.canSkillGrief(this.f_19853_) : this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_);
   }

   protected BlockInteraction getExplosionInteraction() {
      return this.shouldGrief() ? BlockInteraction.BREAK : BlockInteraction.NONE;
   }

   public void explosion(double x, double y, double z) {
      if (!(this.getExplosionRadius() <= 0.0F)) {
         boolean mobGrief = this.shouldGrief();
         boolean fire = mobGrief && this.burnTicks > 0;
         SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), x, y, z);
         if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
            this.f_19853_.m_46518_(this.m_37282_(), x, y, z, this.getExplosionRadius(), fire, this.getExplosionInteraction());
            MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), x, y, z));
         }
      }
   }

   protected void m_8060_(BlockHitResult pResult) {
      this.explosion(this.m_20185_(), this.m_20186_(), this.m_20189_());
      super.m_8060_(pResult);
      if (!this.piercingBlock()) {
         this.hitParticles(this.f_19790_, this.f_19791_, this.f_19792_);
         if (this.hitSound().isPresent()) {
            this.playHitSound((SoundEvent)this.hitSound().get(), pResult);
         }

         this.m_146870_();
      }

   }

   protected void m_5790_(@NotNull EntityHitResult result) {
      Entity entity = result.m_82443_();
      if (this.m_5603_(entity)) {
         this.explosion(this.m_20185_(), this.m_20186_(), this.m_20189_());
         super.m_5790_(result);
         this.hitEntity(entity);
         if (!this.piercingEntity()) {
            this.hitParticles(this.f_19790_, this.f_19791_, this.f_19792_);
            if (this.hitSound().isPresent()) {
               this.playHitSound((SoundEvent)this.hitSound().get(), result);
            }

            this.m_146870_();
         }

      }
   }

   protected void hitEntity(Entity entity) {
      entity.m_20254_(Math.max(this.getBurnTicks(), 0));
      if (this.knockForce > 0.0F) {
         this.knockBack(entity, (double)this.getKnockForce(), (double)this.getKnockForce() / 3.0D);
      }

      if (this.getMobEffect() != null && entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         SkillHelper.checkThenAddEffectSource(living, this.m_37282_(), this.getMobEffect());
      }

      this.dealDamage(entity);
   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F)) {
         DamageSource damagesource = TensuraDamageSources.indirectElementalAttack(this.getMagic(), this, this.m_37282_(), this.getMpCost(), this.getSkill(), this.isSpiritAttack());
         if (this.isSpiritAttack()) {
            DamageSourceHelper.dealSplitElementalDamage(target, damagesource, 0.9F, this.getDamage());
         } else {
            target.m_6469_(damagesource, this.getDamage());
         }

      }
   }

   public void applyEffectAround(double inflateRadius) {
      if (this.getMobEffect() != null) {
         List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(inflateRadius), (entityData) -> {
            return this.m_37282_() == null || !entityData.m_7307_(this.m_37282_()) && !entityData.m_7306_(this.m_37282_());
         });
         if (!livingEntityList.isEmpty()) {
            LivingEntity target;
            for(Iterator var4 = livingEntityList.iterator(); var4.hasNext(); SkillHelper.checkThenAddEffectSource(target, this.m_37282_(), this.getMobEffect())) {
               target = (LivingEntity)var4.next();
               Entity var7 = this.m_37282_();
               if (var7 instanceof LivingEntity) {
                  LivingEntity entity = (LivingEntity)var7;
                  target.m_6703_(entity);
               }
            }

         }
      }
   }

   public void knockBack(Entity entity, double pushMultiplier, double flyUpPower) {
      double var10000;
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         var10000 = Math.max(0.0D, 1.0D - living.m_21133_(Attributes.f_22278_));
      } else {
         var10000 = 0.0D;
      }

      double d0 = var10000;
      Vec3 vec3 = this.m_20184_().m_82541_().m_82490_(pushMultiplier * d0);
      if (vec3.m_82556_() > 0.0D) {
         entity.m_5997_(vec3.f_82479_, flyUpPower + vec3.f_82480_ / 3.0D, vec3.f_82481_);
      }

   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(DELAY_TICK, 0);
      this.f_19804_.m_135372_(LOOK_DISTANCE, 0.0F);
      this.f_19804_.m_135372_(SIZE, 1.0F);
      this.f_19804_.m_135372_(VISUAL_SIZE, 1.0F);
      this.f_19804_.m_135372_(LIFE, 400);
   }

   protected void m_7380_(@NotNull CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("DelayTick", this.getDelayTick());
      compound.m_128350_("LookDistance", this.getLookDistance());
      compound.m_128350_("Size", this.getSize());
      compound.m_128350_("VisualSize", this.getVisualSize());
      compound.m_128405_("Life", this.getLife());
      compound.m_128350_("Speed", this.getSpeed());
      compound.m_128350_("Damage", this.getDamage());
      compound.m_128350_("ExplosionRadius", this.getExplosionRadius());
      compound.m_128405_("BurnTicks", this.getBurnTicks());
      compound.m_128350_("KnockBack", this.getKnockForce());
      compound.m_128350_("EffectRange", this.getEffectRange());
      compound.m_128347_("APCost", this.getApCost());
      compound.m_128347_("MPCost", this.getMpCost());
      compound.m_128379_("SpiritAttack", this.isSpiritAttack());
      compound.m_128379_("Invisible", this.isInvis());
      if (this.skill != null) {
         compound.m_128365_("skill", this.skill.toNBT());
      }

   }

   protected void m_7378_(@NotNull CompoundTag compound) {
      super.m_7378_(compound);
      this.setDelayTick(compound.m_128451_("DelayTick"));
      this.setLookDistance(compound.m_128457_("LookDistance"));
      this.setSize(compound.m_128457_("Size"));
      this.setVisualSize(compound.m_128457_("VisualSize"));
      this.setLife(compound.m_128451_("Life"));
      this.setSpeed(compound.m_128457_("Speed"));
      this.setDamage(compound.m_128457_("Damage"));
      this.setExplosionRadius(compound.m_128457_("ExplosionRadius"));
      this.setBurnTicks(compound.m_128451_("BurnTicks"));
      this.setKnockForce(compound.m_128457_("KnockBack"));
      this.setEffectRange(compound.m_128457_("EffectRange"));
      this.setApCost(compound.m_128459_("APCost"));
      this.setMpCost(compound.m_128459_("MPCost"));
      this.setSpiritAttack(compound.m_128471_("SpiritAttack"));
      this.setInvis(compound.m_128471_("Invisible"));
      if (compound.m_128441_("skill")) {
         Tag var3 = compound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

   }

   public int getLife() {
      return (Integer)this.f_19804_.m_135370_(LIFE);
   }

   public void setLife(int life) {
      this.f_19804_.m_135381_(LIFE, life);
   }

   public float getSize() {
      return (Float)this.f_19804_.m_135370_(SIZE);
   }

   public void setSize(float size) {
      this.f_19804_.m_135381_(SIZE, size);
      this.m_6210_();
      this.setVisualSize(size);
   }

   public float getVisualSize() {
      return (Float)this.f_19804_.m_135370_(VISUAL_SIZE);
   }

   public void setVisualSize(float size) {
      this.f_19804_.m_135381_(VISUAL_SIZE, size);
   }

   public int getDelayTick() {
      return (Integer)this.f_19804_.m_135370_(DELAY_TICK);
   }

   public void setDelayTick(int i) {
      this.f_19804_.m_135381_(DELAY_TICK, i);
   }

   public float getLookDistance() {
      return (Float)this.f_19804_.m_135370_(LOOK_DISTANCE);
   }

   public void setLookDistance(float i) {
      this.f_19804_.m_135381_(LOOK_DISTANCE, i);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (SIZE.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public String getMagic() {
      return "magic";
   }

   public boolean m_20145_() {
      return this.isInvis();
   }

   public boolean m_20177_(Player pPlayer) {
      if (!this.m_20145_()) {
         return super.m_20177_(pPlayer);
      } else if (pPlayer.m_5833_()) {
         return false;
      } else if (this.m_37282_() == pPlayer) {
         return false;
      } else if (pPlayer.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         return ((MobEffectInstance)Objects.requireNonNull(pPlayer.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_() < 3;
      } else {
         return true;
      }
   }

   public EntityDimensions m_6972_(Pose pPose) {
      return super.m_6972_(pPose).m_20388_(this.getSize());
   }

   public boolean m_6128_() {
      return this.getExplosionRadius() > 0.0F;
   }

   public boolean shouldDiscardInLava() {
      return true;
   }

   public boolean shouldDiscardInWater() {
      return true;
   }

   public boolean piercingBlock() {
      return false;
   }

   public boolean piercingEntity() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return null;
   }

   public Vec3 vec3Random() {
      return new Vec3(Math.random() * 2.0D - 1.0D, Math.random() * 2.0D - 1.0D, Math.random() * 2.0D - 1.0D);
   }

   public void flyingParticles() {
      if (!this.m_20096_()) {
         this.m_9236_().m_7106_(ParticleTypes.f_123751_, this.m_20185_(), this.m_20186_(), this.m_20189_(), 0.0D, 0.0D, 0.0D);
      }
   }

   public void hitParticles(double x, double y, double z) {
      this.m_9236_().m_7106_(ParticleTypes.f_123813_, x, y, z, 0.0D, 0.0D, 0.0D);
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11685_);
   }

   protected void playHitSound(SoundEvent sound, HitResult hitresult) {
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), sound, SoundSource.NEUTRAL, 2.0F, 0.9F + this.f_19853_.f_46441_.m_188501_() * 0.2F);
   }

   public float getSpeed() {
      return this.speed;
   }

   public void setSpeed(float speed) {
      this.speed = speed;
   }

   public float getDamage() {
      return this.damage;
   }

   public void setDamage(float damage) {
      this.damage = damage;
   }

   public float getKnockForce() {
      return this.knockForce;
   }

   public void setKnockForce(float knockForce) {
      this.knockForce = knockForce;
   }

   public float getExplosionRadius() {
      return this.explosionRadius;
   }

   public void setExplosionRadius(float explosionRadius) {
      this.explosionRadius = explosionRadius;
   }

   public int getBurnTicks() {
      return this.burnTicks;
   }

   public void setBurnTicks(int burnTicks) {
      this.burnTicks = burnTicks;
   }

   @Nullable
   public MobEffectInstance getMobEffect() {
      return this.mobEffect;
   }

   public void setMobEffect(@Nullable MobEffectInstance mobEffect) {
      this.mobEffect = mobEffect;
   }

   public float getEffectRange() {
      return this.effectRange;
   }

   public void setEffectRange(float effectRange) {
      this.effectRange = effectRange;
   }

   public double getApCost() {
      return this.apCost;
   }

   public void setApCost(double apCost) {
      this.apCost = apCost;
   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   public boolean isSpiritAttack() {
      return this.spiritAttack;
   }

   public void setSpiritAttack(boolean spiritAttack) {
      this.spiritAttack = spiritAttack;
   }

   public boolean isInvis() {
      return this.invis;
   }

   public void setInvis(boolean invis) {
      this.invis = invis;
   }

   public void setDelayVec(Vec3 delayVec) {
      this.delayVec = delayVec;
   }

   public Vec3 getDelayVec() {
      return this.delayVec;
   }

   public void setOwnerOffset(Vec3 ownerOffset) {
      this.ownerOffset = ownerOffset;
   }

   public Vec3 getOwnerOffset() {
      return this.ownerOffset;
   }

   static {
      LIFE = SynchedEntityData.m_135353_(TensuraProjectile.class, EntityDataSerializers.f_135028_);
      SIZE = SynchedEntityData.m_135353_(TensuraProjectile.class, EntityDataSerializers.f_135029_);
      VISUAL_SIZE = SynchedEntityData.m_135353_(TensuraProjectile.class, EntityDataSerializers.f_135029_);
      LOOK_DISTANCE = SynchedEntityData.m_135353_(TensuraProjectile.class, EntityDataSerializers.f_135029_);
      DELAY_TICK = SynchedEntityData.m_135353_(TensuraProjectile.class, EntityDataSerializers.f_135028_);
   }

   public static enum PositionDirection {
      MIDDLE,
      RIGHT,
      LEFT;

      // $FF: synthetic method
      private static TensuraProjectile.PositionDirection[] $values() {
         return new TensuraProjectile.PositionDirection[]{MIDDLE, RIGHT, LEFT};
      }
   }
}
