package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.protocol.game.ClientboundBlockUpdatePacket;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.item.FallingBlockEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.DirectionalPlaceContext;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.ConcretePowderBlock;
import net.minecraft.world.level.block.Fallable;
import net.minecraft.world.level.block.FallingBlock;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.storage.loot.LootContext.Builder;
import net.minecraft.world.level.storage.loot.parameters.LootContextParams;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;

public class TensuraFallingBlock extends FallingBlockEntity {
   public boolean indestructible;

   public TensuraFallingBlock(EntityType<? extends TensuraFallingBlock> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public TensuraFallingBlock(Level pLevel) {
      super((EntityType)TensuraEntityTypes.FALLING_BLOCK.get(), pLevel);
   }

   public TensuraFallingBlock(Level pLevel, double pX, double pY, double pZ, BlockState pState) {
      this(pLevel);
      this.f_31946_ = pState;
      this.f_19850_ = true;
      this.m_6034_(pX, pY, pZ);
      this.m_20256_(Vec3.f_82478_);
      this.f_19854_ = pX;
      this.f_19855_ = pY;
      this.f_19856_ = pZ;
      this.m_31959_(this.m_20183_());
   }

   public static TensuraFallingBlock fall(Level pLevel, BlockPos pPos, BlockState pBlockState) {
      TensuraFallingBlock core = new TensuraFallingBlock(pLevel, (double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_(), (double)pPos.m_123343_() + 0.5D, pBlockState.m_61138_(BlockStateProperties.f_61362_) ? (BlockState)pBlockState.m_61124_(BlockStateProperties.f_61362_, false) : pBlockState);
      pLevel.m_7731_(pPos, pBlockState.m_60819_().m_76188_(), 3);
      pLevel.m_7967_(core);
      return core;
   }

   protected void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128379_("Indestructible", this.isIndestructible());
   }

   protected void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setIndestructible(pCompound.m_128471_("Indestructible"));
   }

   public boolean m_6673_(DamageSource pSource) {
      if (super.m_6673_(pSource)) {
         return true;
      } else {
         return pSource.m_19378_() ? false : this.isIndestructible();
      }
   }

   public void m_8119_() {
      if (this.f_31946_.m_60795_()) {
         this.m_146870_();
      } else {
         Block block = this.f_31946_.m_60734_();
         ++this.f_31942_;
         if (!this.m_20068_()) {
            this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.04D, 0.0D));
         }

         this.m_6478_(MoverType.SELF, this.m_20184_());
         if (!this.f_19853_.f_46443_) {
            BlockPos blockpos = this.m_20183_();
            boolean flag = this.f_31946_.m_60734_() instanceof ConcretePowderBlock;
            boolean flag1 = flag && this.f_31946_.canBeHydrated(this.f_19853_, blockpos, this.f_19853_.m_6425_(blockpos), blockpos);
            double d0 = this.m_20184_().m_82556_();
            if (flag && d0 > 1.0D) {
               BlockHitResult blockhitresult = this.f_19853_.m_45547_(new ClipContext(new Vec3(this.f_19854_, this.f_19855_, this.f_19856_), this.m_20182_(), net.minecraft.world.level.ClipContext.Block.COLLIDER, Fluid.SOURCE_ONLY, this));
               if (blockhitresult.m_6662_() != Type.MISS && this.f_31946_.canBeHydrated(this.f_19853_, blockpos, this.f_19853_.m_6425_(blockhitresult.m_82425_()), blockhitresult.m_82425_())) {
                  blockpos = blockhitresult.m_82425_();
                  flag1 = true;
               }
            }

            if (!this.f_19861_ && !flag1) {
               if (!this.f_19853_.f_46443_ && (this.f_31942_ > 100 && (blockpos.m_123342_() <= this.f_19853_.m_141937_() || blockpos.m_123342_() > this.f_19853_.m_151558_()) || this.f_31942_ > 600)) {
                  if (this.f_31943_ && this.f_19853_.m_46469_().m_46207_(GameRules.f_46137_)) {
                     this.spawnBlockDrops();
                  }

                  this.m_146870_();
               }
            } else {
               BlockState blockstate = this.f_19853_.m_8055_(blockpos);
               this.m_20256_(this.m_20184_().m_82542_(0.7D, -0.5D, 0.7D));
               if (!blockstate.m_60713_(Blocks.f_50110_)) {
                  if (this.f_31947_) {
                     this.m_146870_();
                     this.m_149650_(block, blockpos);
                  } else {
                     boolean flag2 = blockstate.m_60629_(new DirectionalPlaceContext(this.f_19853_, blockpos, Direction.DOWN, ItemStack.f_41583_, Direction.UP));
                     boolean flag3 = FallingBlock.m_53241_(this.f_19853_.m_8055_(blockpos.m_7495_())) && (!flag || !flag1);
                     boolean flag4 = this.f_31946_.m_60710_(this.f_19853_, blockpos) && !flag3;
                     if (flag2 && flag4) {
                        if (this.f_31946_.m_61138_(BlockStateProperties.f_61362_) && this.f_19853_.m_6425_(blockpos).m_76152_() == Fluids.f_76193_) {
                           this.f_31946_ = (BlockState)this.f_31946_.m_61124_(BlockStateProperties.f_61362_, true);
                        }

                        if (!this.f_19853_.m_7731_(blockpos, this.f_31946_, 3)) {
                           if (this.f_31943_ && this.f_19853_.m_46469_().m_46207_(GameRules.f_46137_)) {
                              this.m_146870_();
                              this.m_149650_(block, blockpos);
                              this.spawnBlockDrops();
                           }
                        } else {
                           ((ServerLevel)this.f_19853_).m_7726_().f_8325_.m_140201_(this, new ClientboundBlockUpdatePacket(blockpos, this.f_19853_.m_8055_(blockpos)));
                           this.m_146870_();
                           if (block instanceof Fallable) {
                              ((Fallable)block).m_48792_(this.f_19853_, blockpos, this.f_31946_, blockstate, this);
                           }

                           if (this.f_31944_ != null && this.f_31946_.m_155947_()) {
                              BlockEntity blockentity = this.f_19853_.m_7702_(blockpos);
                              if (blockentity != null) {
                                 CompoundTag compoundtag = blockentity.m_187482_();
                                 Iterator var13 = this.f_31944_.m_128431_().iterator();

                                 while(var13.hasNext()) {
                                    String s = (String)var13.next();
                                    Tag tag = this.f_31944_.m_128423_(s);
                                    if (tag != null) {
                                       compoundtag.m_128365_(s, tag.m_6426_());
                                    }
                                 }

                                 blockentity.m_142466_(compoundtag);
                                 blockentity.m_6596_();
                              }
                           }
                        }
                     } else {
                        this.m_146870_();
                        if (this.f_31943_ && this.f_19853_.m_46469_().m_46207_(GameRules.f_46137_)) {
                           this.m_149650_(block, blockpos);
                           this.spawnBlockDrops();
                        }
                     }
                  }
               }
            }
         }

         this.m_20256_(this.m_20184_().m_82490_(0.98D));
      }

   }

   private void spawnBlockDrops() {
      Level var2 = this.f_19853_;
      if (var2 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var2;
         List var5 = this.f_31946_.m_60724_((new Builder(serverLevel)).m_78972_(LootContextParams.f_81460_, Vec3.m_82512_(this.m_20183_())).m_78972_(LootContextParams.f_81463_, ItemStack.f_41583_));
         Iterator var3 = var5.iterator();

         while(var3.hasNext()) {
            ItemStack drop = (ItemStack)var3.next();
            this.m_19983_(drop);
         }

      }
   }

   public boolean isIndestructible() {
      return this.indestructible;
   }

   public void setIndestructible(boolean indestructible) {
      this.indestructible = indestructible;
   }
}
