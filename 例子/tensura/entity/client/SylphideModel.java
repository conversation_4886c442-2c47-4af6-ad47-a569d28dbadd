package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SylphideEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class SylphideModel extends AnimatedGeoModel<SylphideEntity> {
   public ResourceLocation getModelResource(SylphideEntity object) {
      return new ResourceLocation("tensura", "geo/sylphide.geo.json");
   }

   public ResourceLocation getTextureResource(SylphideEntity instance) {
      return SylphideRenderer.getSylphideTexture(instance);
   }

   public ResourceLocation getAnimationResource(SylphideEntity Sylphide) {
      return new ResourceLocation("tensura", "animations/sylphide.animation.json");
   }

   public void setCustomAnimations(SylphideEntity sylphide, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(sylphide, instanceId, customPredicate);
      if (sylphide.shouldStand()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
            head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
         }

      }
   }
}
