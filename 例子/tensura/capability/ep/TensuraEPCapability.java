package com.github.manasmods.tensura.capability.ep;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.event.UpdateEPEvent;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncEPCapabilityPacket;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraEPCapability implements ITensuraEPCapability {
   private static final Logger log = LogManager.getLogger(TensuraEPCapability.class);
   public static final Capability<ITensuraEPCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<ITensuraEPCapability>() {
   });
   private static final ResourceLocation IDENTIFIER = new ResourceLocation("tensura", "ep");
   private int humanKill = 0;
   private double EP = 0.0D;
   private double currentEP = 0.0D;
   private double spiritualHealth = 20.0D;
   private double gainedEP;
   private boolean majin = false;
   private boolean chaos = false;
   private boolean skipEPDrop = false;
   private boolean nameable = false;
   private boolean harvestGift = false;
   private String name = null;
   private UUID permanentOwner = null;
   private UUID temporaryOwner = null;
   private final List<UUID> neutralTarget = new ArrayList();

   @SubscribeEvent
   public static void attach(AttachCapabilitiesEvent<Entity> e) {
      e.addCapability(IDENTIFIER, new TensuraEPCapabilityProvider());
   }

   public static LazyOptional<ITensuraEPCapability> getFrom(LivingEntity entity) {
      return entity.getCapability(CAPABILITY);
   }

   public static void sync(LivingEntity entity) {
      if (!entity.f_19853_.m_5776_()) {
         getFrom(entity).ifPresent((data) -> {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new SyncEPCapabilityPacket(data, entity.m_19879_()));
         });
      }
   }

   public void setEP(LivingEntity entity, double EP) {
      this.setEP(entity, EP, true);
   }

   public void setEP(LivingEntity entity, double EP, boolean update) {
      if (!update) {
         this.EP = EP;
         sync(entity);
      } else if (!MinecraftForge.EVENT_BUS.post(new UpdateEPEvent(entity, this.EP, EP))) {
         this.EP = EP;
         if (this.currentEP <= 0.0D || this.currentEP == EP) {
            this.currentEP = EP;
         }

         sync(entity);
      }

   }

   public void setCurrentEP(LivingEntity entity, double EP) {
      this.currentEP = EP;
      sync(entity);
   }

   public boolean isTargetNeutral(UUID target) {
      return this.neutralTarget.contains(target);
   }

   public void addNeutralTarget(UUID target) {
      this.neutralTarget.add(target);
   }

   public void removeNeutralTarget(UUID target) {
      this.neutralTarget.removeIf((uuid) -> {
         return uuid == target;
      });
   }

   public void clearNeutralTargets() {
      this.neutralTarget.clear();
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();
      tag.m_128405_("HumanKill", this.humanKill);
      tag.m_128347_("EP", this.EP);
      tag.m_128347_("gainedEP", this.gainedEP);
      tag.m_128347_("currentEP", this.currentEP);
      tag.m_128347_("SpiritualHealth", this.spiritualHealth);
      tag.m_128379_("chaos", this.chaos);
      tag.m_128379_("majin", this.majin);
      tag.m_128379_("skipEPDrop", this.skipEPDrop);
      tag.m_128379_("harvestGift", this.harvestGift);
      tag.m_128379_("nameable", this.nameable);
      if (this.name != null) {
         tag.m_128359_("name", this.name);
      } else if (tag.m_128425_("name", 8)) {
         tag.m_128473_("name");
      }

      if (this.permanentOwner != null) {
         tag.m_128362_("permanentOwner", this.permanentOwner);
      }

      if (this.temporaryOwner != null) {
         tag.m_128362_("temporaryOwner", this.temporaryOwner);
      }

      ListTag neutralList = new ListTag();
      Iterator var3 = this.neutralTarget.iterator();

      while(var3.hasNext()) {
         UUID uuid = (UUID)var3.next();
         CompoundTag target = new CompoundTag();
         target.m_128362_("target", uuid);
         neutralList.add(target);
      }

      tag.m_128365_("neutralList", neutralList);
      return tag;
   }

   public void deserializeNBT(CompoundTag tag) {
      this.humanKill = tag.m_128451_("HumanKill");
      this.EP = tag.m_128459_("EP");
      this.gainedEP = tag.m_128459_("gainedEP");
      this.currentEP = tag.m_128459_("currentEP");
      this.spiritualHealth = tag.m_128459_("SpiritualHealth");
      this.chaos = tag.m_128471_("chaos");
      this.majin = tag.m_128471_("majin");
      this.skipEPDrop = tag.m_128471_("skipEPDrop");
      this.harvestGift = tag.m_128471_("harvestGift");
      this.nameable = tag.m_128471_("nameable");
      if (tag.m_128425_("name", 8)) {
         this.name = tag.m_128461_("name");
      }

      if (tag.m_128403_("permanentOwner")) {
         this.permanentOwner = tag.m_128342_("permanentOwner");
      }

      if (tag.m_128403_("temporaryOwner")) {
         this.temporaryOwner = tag.m_128342_("temporaryOwner");
      }

      ListTag neutralList = (ListTag)tag.m_128423_("neutralList");
      if (neutralList != null) {
         this.neutralTarget.clear();
         Iterator var3 = neutralList.iterator();

         while(var3.hasNext()) {
            Tag value = (Tag)var3.next();
            if (value instanceof CompoundTag) {
               CompoundTag target = (CompoundTag)value;
               if (target.m_128403_("target")) {
                  this.neutralTarget.add(target.m_128342_("target"));
               }
            }
         }
      }

   }

   public static void updateEP(LivingEntity living) {
      updateEP(living, true);
   }

   public static void updateEP(LivingEntity living, boolean update) {
      if (living instanceof Player) {
         Player player = (Player)living;
         double statSum = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) + player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
         double statCurrent = TensuraPlayerCapability.getCurrentEP(player);
         getFrom(player).ifPresent((cap) -> {
            if (cap.getEP() != statSum) {
               cap.setEP(living, statSum, update);
            }

            if (cap.getCurrentEP() != statCurrent) {
               cap.setCurrentEP(living, statCurrent);
            }

         });
      }
   }

   public static double getCurrentEP(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0.0D : capability.getCurrentEP();
   }

   public static void setCurrentLivingEP(LivingEntity pLivingEntity, double value) {
      getFrom(pLivingEntity).ifPresent((cap) -> {
         cap.setCurrentEP(pLivingEntity, value);
      });
   }

   public static void decreaseCurrentEP(LivingEntity entity, double value) {
      getFrom(entity).ifPresent((cap) -> {
         cap.setCurrentEP(entity, cap.getCurrentEP() - value);
      });
   }

   public static double getEP(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0.0D : capability.getEP();
   }

   public static void setLivingEP(LivingEntity pLivingEntity, double value) {
      getFrom(pLivingEntity).ifPresent((cap) -> {
         cap.setEP(pLivingEntity, value);
      });
   }

   public static int getHumanKill(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? 0 : capability.getHumanKill();
   }

   public static void increaseHumanKill(LivingEntity pLivingEntity) {
      getFrom(pLivingEntity).ifPresent((cap) -> {
         cap.setHumanKill(cap.getHumanKill() + 1);
      });
      sync(pLivingEntity);
   }

   public static boolean isSkippingEPDrop(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isSkipEPDrop();
   }

   public static void setSkippingEPDrop(LivingEntity entity, boolean skip) {
      getFrom(entity).ifPresent((cap) -> {
         if (cap.isSkipEPDrop() != skip) {
            cap.setSkipEPDrop(skip);
            sync(entity);
         }
      });
   }

   public static double getSpiritualHealth(LivingEntity entity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(entity, CAPABILITY);
      return capability == null ? 0.0D : capability.getSpiritualHealth();
   }

   public static void setSpiritualHealth(LivingEntity entity, double value) {
      getFrom(entity).ifPresent((cap) -> {
         cap.setSpiritualHealth(value);
         sync(entity);
      });
   }

   public static void healSpiritualHealth(LivingEntity entity, double value) {
      double maxSHP = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
      getFrom(entity).ifPresent((cap) -> {
         if (cap.getSpiritualHealth() != maxSHP) {
            cap.setSpiritualHealth(Math.min(maxSHP, cap.getSpiritualHealth() + value));
            sync(entity);
         }
      });
   }

   public static boolean isChaos(LivingEntity entity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(entity, CAPABILITY);
      return capability == null ? false : capability.isChaos();
   }

   public static boolean isMajin(LivingEntity entity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(entity, CAPABILITY);
      return capability == null ? false : capability.isMajin();
   }

   public static void setMajin(LivingEntity pLivingEntity, boolean value) {
      getFrom(pLivingEntity).ifPresent((cap) -> {
         cap.setMajin(value);
      });
      sync(pLivingEntity);
   }

   public static boolean isNameable(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isNameable();
   }

   @Nullable
   public static String getName(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? null : capability.getName();
   }

   public static MutableComponent getDisplayName(LivingEntity entity, Component oldName) {
      String name = getName(entity);
      return name != null && entity.m_9236_().m_46469_().m_46207_(TensuraGameRules.TENSURA_DISPLAY_NAME) ? Component.m_237113_(name) : Component.m_237113_(oldName.getString()).m_6270_(oldName.m_7383_());
   }

   @Nullable
   public static UUID getPermanentOwner(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? null : capability.getPermanentOwner();
   }

   @Nullable
   public static UUID getTemporaryOwner(LivingEntity pLivingEntity) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? null : capability.getTemporaryOwner();
   }

   public static boolean isTargetNeutral(LivingEntity pLivingEntity, LivingEntity target) {
      ITensuraEPCapability capability = (ITensuraEPCapability)CapabilityHandler.getCapability(pLivingEntity, CAPABILITY);
      return capability == null ? false : capability.isTargetNeutral(target.m_20148_());
   }

   public static void resetEverything(LivingEntity entity) {
      getFrom(entity).ifPresent((cap) -> {
         cap.setName((String)null);
         cap.setNameable(false);
         cap.setMajin(false);
         cap.setChaos(false);
         cap.setTemporaryOwner((UUID)null);
         cap.setPermanentOwner((UUID)null);
         cap.clearNeutralTargets();
         cap.setSkipEPDrop(false);
         cap.setHarvestGift(false);
         cap.setGainedEP(0.0D);
         cap.setHumanKill(0);
      });
      sync(entity);
   }

   public void setHumanKill(int humanKill) {
      this.humanKill = humanKill;
   }

   public int getHumanKill() {
      return this.humanKill;
   }

   public double getEP() {
      return this.EP;
   }

   public double getCurrentEP() {
      return this.currentEP;
   }

   public double getSpiritualHealth() {
      return this.spiritualHealth;
   }

   public double getGainedEP() {
      return this.gainedEP;
   }

   public void setSpiritualHealth(double spiritualHealth) {
      this.spiritualHealth = spiritualHealth;
   }

   public void setGainedEP(double gainedEP) {
      this.gainedEP = gainedEP;
   }

   public boolean isMajin() {
      return this.majin;
   }

   public boolean isChaos() {
      return this.chaos;
   }

   public boolean isSkipEPDrop() {
      return this.skipEPDrop;
   }

   public boolean isNameable() {
      return this.nameable;
   }

   public boolean isHarvestGift() {
      return this.harvestGift;
   }

   public void setMajin(boolean majin) {
      this.majin = majin;
   }

   public void setChaos(boolean chaos) {
      this.chaos = chaos;
   }

   public void setSkipEPDrop(boolean skipEPDrop) {
      this.skipEPDrop = skipEPDrop;
   }

   public void setNameable(boolean nameable) {
      this.nameable = nameable;
   }

   public void setHarvestGift(boolean harvestGift) {
      this.harvestGift = harvestGift;
   }

   public String getName() {
      return this.name;
   }

   public void setName(String name) {
      this.name = name;
   }

   public UUID getPermanentOwner() {
      return this.permanentOwner;
   }

   public UUID getTemporaryOwner() {
      return this.temporaryOwner;
   }

   public void setPermanentOwner(UUID permanentOwner) {
      this.permanentOwner = permanentOwner;
   }

   public void setTemporaryOwner(UUID temporaryOwner) {
      this.temporaryOwner = temporaryOwner;
   }
}
