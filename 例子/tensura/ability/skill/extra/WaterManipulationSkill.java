package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.water.WaterMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.entity.magic.projectile.WaterBallProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class WaterManipulationSkill extends Skill {
   public WaterManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return TensuraSkillCapability.getSpiritLevel(entity, MagicElemental.WATER.getId()) >= 1 ? true : learnWaterManipulation(entity);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return entity.m_6144_() ? 100.0D : 10.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      MolecularManipulationSkill.learnMolecular(entity);
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.WATER_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.WATER_DOMINATION.get())) {
            if (DamageSourceHelper.isWaterDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_6144_()) {
         instance.getOrCreateTag().m_128405_("BreathEntity", 0);
         instance.markDirty();
      } else {
         if (hasWater(entity)) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            this.shootBall(instance, entity);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (!(cap.getWaterPoint() <= 0.0D)) {
                  if (!SkillHelper.outOfMagicule(entity, instance)) {
                     cap.setWaterPoint(cap.getWaterPoint() - 1.0D);
                     this.shootBall(instance, entity);
                     TensuraSkillCapability.sync(player);
                  }
               }
            });
         }

      }
   }

   public static boolean hasWater(LivingEntity entity) {
      if (entity.m_20072_()) {
         return true;
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         return TensuraSkillCapability.getSpiritLevel(player, MagicElemental.WATER.getId()) >= 1;
      } else {
         return false;
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity.m_6144_()) {
         return false;
      } else {
         if (hasWater(entity)) {
            if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
               return false;
            }

            if (heldTicks % 60 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            waterBreath(instance, entity, this.magiculeCost(entity, instance));
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               if (!(cap.getWaterPoint() <= 0.0D) || cap.getSpiritLevel(MagicElemental.WATER.getId()) >= 1) {
                  if (heldTicks % 20 != 0 || !SkillHelper.outOfMagicule(entity, instance)) {
                     if (heldTicks % 100 == 0) {
                        if (heldTicks % 100 == 0 && heldTicks > 0) {
                           this.addMasteryPoint(instance, entity);
                        }

                        cap.setWaterPoint(cap.getWaterPoint() - 1.0D);
                        TensuraSkillCapability.sync(player);
                     }

                     waterBreath(instance, entity, this.magiculeCost(entity, instance));
                  }
               }
            });
         }

         return true;
      }
   }

   public static void waterBreath(ManasSkillInstance instance, LivingEntity entity, double cost) {
      if (!WaterMagic.isWaterEvaporated(entity, entity.f_19853_)) {
         float damage = instance.isMastered(entity) ? 8.0F : 4.0F;
         BreathEntity.spawnBreathEntity((EntityType)TensuraEntityTypes.WATER_BREATH.get(), entity, instance, damage, cost);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12324_, SoundSource.PLAYERS, 1.0F, 1.0F);
         entity.m_20095_();
      }
   }

   private void shootBall(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21011_(InteractionHand.MAIN_HAND, true);
      if (!WaterMagic.isWaterEvaporated(entity, entity.f_19853_)) {
         this.addMasteryPoint(instance, entity);
         WaterBallProjectile waterBall = new WaterBallProjectile(entity.m_9236_(), entity);
         waterBall.setSpeed(1.5F);
         waterBall.setDamage(12.0F);
         waterBall.setKnockForce(1.0F);
         waterBall.setBurnTicks(-1);
         waterBall.setMpCost(this.magiculeCost(entity, instance));
         waterBall.setSkill(instance);
         waterBall.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(waterBall);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12324_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public static boolean learnWaterManipulation(LivingEntity entity) {
      int skills = 0;
      Iterator var2 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var2.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var2.next();
         if (isWaterSKillMastered(skill, entity)) {
            ++skills;
         }
      }

      return skills >= 2;
   }

   private static boolean isWaterSKillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isMastered(entity)) {
         return false;
      } else if (instance.getSkill().equals(IntrinsicSkills.WATER_TRANSFORM.get())) {
         return true;
      } else if (instance.getSkill().equals(CommonSkills.WATER_BLADE.get())) {
         return true;
      } else {
         return instance.getSkill().equals(CommonSkills.HYDRAULIC_PROPULSION.get()) ? true : instance.getSkill().equals(CommonSkills.WATER_CURRENT_CONTROL.get());
      }
   }
}
