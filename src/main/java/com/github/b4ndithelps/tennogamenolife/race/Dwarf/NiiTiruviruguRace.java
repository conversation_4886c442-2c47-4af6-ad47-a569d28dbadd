package com.github.b4ndithelps.tennogamenolife.race.Dwarf;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 尼依・缇儿威尔古是地精族的进化种族
 */
public class NiiTiruviruguRace extends Race {

    // 种族基础属性
    private double baseHealth = 220.0;          // 基础生命值
    private double baseAttackDamage = 2.5;     // 基础攻击伤害
    private double baseAttackSpeed = 4.5;      // 基础攻击速度
    private double knockbackResistance = 0.6;   // 击退抗性
    private double jumpHeight = 1.2;           // 跳跃高度
    private double movementSpeed = 0.12;       // 移动速度
    private double sprintSpeed = 0.18;         // 冲刺速度
    private double auraMin = 1200.0;           // 最小灵气值
    private double auraMax = 2000.0;           // 最大灵气值
    private double startingMagiculeMin = 300.0; // 最小初始魔素值
    private double startingMagiculeMax = 600.0; // 最大初始魔素值

    private float playerSize = 1.5f;           // 玩家大小

    public NiiTiruviruguRace() {
        super(Difficulty.INTERMEDIATE);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(IntrinsicSkills.BODY_ARMOR.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.MAGIC_RESISTANCE.get());
        list.add(ResistanceSkills.PIERCE_RESISTANCE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        double ores = 0.0D;
        if (player instanceof LocalPlayer) {
            LocalPlayer localPlayer = (LocalPlayer)player;
            ores = (double)localPlayer.getStats().getValue(Stats.ITEM_USED.get(TensuraMaterialItems.MAGIC_ORE.get()));
        } else if (player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)player;
            ores = (double)serverPlayer.getStats().getValue(Stats.ITEM_USED.get(TensuraMaterialItems.MAGIC_ORE.get()));
        }

        return ores * 100.0D / 50.0D;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.consume_requirement", 
                TensuraMaterialItems.MAGIC_ORE.get().getDescription()));
        return list;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.DWARF.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.VEIGU_DORAUVUNIRU.get());
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }
} 