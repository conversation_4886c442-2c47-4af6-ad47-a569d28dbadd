package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantCodEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class GiantCodRenderer extends GeoEntityRenderer<GiantCodEntity> {
   public GiantCodRenderer(Context renderManager) {
      super(renderManager, new GiantCodModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(GiantCodEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/giant_cod/giant_cod.png");
   }

   public RenderType getRenderType(GiantCodEntity animatable, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(2.0F, 2.0F, 2.0F);
      return super.getRenderType(animatable, partialTicks, stack, renderTypeBuffer, vertexBuilder, packedLightIn, textureLocation);
   }
}
