package com.github.manasmods.tensura.entity.magic.lightning;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.network.PacketDistributor;

public class BlackLightningBolt extends TenuraLightningBolt {
   public BlackLightningBolt(Level pLevel) {
      super((EntityType)TensuraEntityTypes.BLACK_LIGHTNING_BOLT.get(), pLevel);
   }

   public BlackLightningBolt(Level pLevel, Entity owner) {
      super((EntityType)TensuraEntityTypes.BLACK_LIGHTNING_BOLT.get(), pLevel, owner);
   }

   protected BlockState getFireBlock() {
      return ((Block)TensuraBlocks.BLACK_FIRE.get()).m_49966_();
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return this;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:black_strike"), this.m_19879_(), 0.0D, -1.0D, 0.0D, true));
         if (this.f_20860_ == 1) {
            for(int i = 0; i < this.getAdditionalVisual(); ++i) {
               double xOff = (double)(this.f_19796_.m_188501_() * 2.0F - 1.0F);
               double zOff = (double)(this.f_19796_.m_188501_() * 2.0F - 1.0F);
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return this;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:light_strike"), this.m_19879_(), xOff, -1.0D, zOff, true));
            }

         }
      }
   }

   protected void doAoEDamage() {
      float electricRadius = this.m_20072_() ? this.getRadius() * 1.5F : this.getRadius();
      List<Entity> list = this.f_19853_.m_6249_(this, this.m_20191_().m_82400_((double)electricRadius), (entityx) -> {
         return this.canHit(entityx) && entityx.m_20270_(this) < electricRadius;
      });
      DamageSource damageSource = this.getOwner() == null ? TensuraDamageSources.BLACK_LIGHTNING : TensuraDamageSources.blackLightning(this.getOwner());
      Iterator var4 = list.iterator();

      while(var4.hasNext()) {
         Entity entity = (Entity)var4.next();
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.BLACK_LIGHTNING_SPARK.get(), 1.0D);
         DamageSourceHelper.dealSplitElementalDamage(entity, DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), 0.9F, this.getTensuraDamage());
         Level var7 = this.f_19853_;
         if (var7 instanceof ServerLevel) {
            ServerLevel serverLevel = (ServerLevel)var7;
            entity.m_8038_(serverLevel, this);
         }
      }

      if (TensuraGameRules.canSkillGrief(this.f_19853_)) {
         this.placeFireAndMeltGlass(this.m_20182_(), (int)this.getRadius());
      }

   }
}
