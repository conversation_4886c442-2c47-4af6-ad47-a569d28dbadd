package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.common.ThoughtCommunicationSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class SpearheadSkill extends Skill {
   public SpearheadSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return this.isInSlot(entity);
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(20.0D), (living) -> {
         return !living.m_7306_(entity) && living.m_6084_() && living.m_7307_(entity);
      });
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SPEARHEAD.get(), 240, instance.isMastered(entity) ? 1 : 0, false, false, false), entity);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (!player.m_36341_()) {
            List<Mob> list = player.m_9236_().m_6443_(Mob.class, player.m_20191_().m_82400_(20.0D), (living) -> {
               return SkillHelper.isSubordinate(player, living);
            });
            if (list.isEmpty()) {
               player.m_5661_(Component.m_237115_("tensura.telepathy.subordinate_all.not_found").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
               return;
            }

            CompoundTag tag = instance.getOrCreateTag();
            int command = tag.m_128451_("command");
            command = command == 4 ? 1 : command + 1;
            tag.m_128405_("command", command);
            Iterator var7 = list.iterator();

            while(var7.hasNext()) {
               Mob mob = (Mob)var7.next();
               MutableComponent message;
               switch(command) {
               case 2:
                  SkillHelper.setFollow(mob);
                  message = Component.m_237115_("tensura.telepathy.subordinate_all.follow");
                  break;
               case 3:
                  SkillHelper.setWander(mob);
                  message = Component.m_237115_("tensura.telepathy.subordinate_all.wander");
                  break;
               case 4:
                  SkillHelper.setFollow(mob);
                  SkillHelper.addEffectWithSource(mob, entity, (MobEffect)TensuraMobEffects.SPEARHEAD.get(), 6000, instance.isMastered(entity) ? 1 : 0, true);
                  message = Component.m_237115_("tensura.telepathy.subordinate_all.meat_shield");
                  break;
               default:
                  SkillHelper.setStay(mob);
                  TensuraEffectsCapability.setEffectSource(mob, (LivingEntity)null, (MobEffect)TensuraMobEffects.SPEARHEAD.get());
                  message = Component.m_237115_("tensura.telepathy.subordinate_all.stay");
               }

               player.m_21011_(InteractionHand.MAIN_HAND, true);
               player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
               player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         } else {
            ThoughtCommunicationSkill.movementTelepathy(instance, entity);
         }

      }
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
      LivingEntity entity = e.getEntity();
      if (!entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
         if (!(entity instanceof Player) || entity.m_9236_().m_6106_().m_5466_()) {
            List<ManasSkillInstance> targetSkills = List.copyOf(SkillAPI.getSkillsFrom(entity).getLearnedSkills().stream().filter((skillInstance) -> {
               return this.canCollect(skillInstance.getSkill());
            }).toList());
            if (!targetSkills.isEmpty()) {
               Iterator var6 = targetSkills.iterator();

               while(var6.hasNext()) {
                  ManasSkillInstance targetInstance = (ManasSkillInstance)var6.next();
                  if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this) {
                     SkillPlunderEvent event = new SkillPlunderEvent(entity, owner, true, targetInstance.getSkill());
                     if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(owner, event.getSkill(), instance.getRemoveTime())) {
                        SkillAPI.getSkillsFrom(entity).forgetSkill(event.getSkill());
                        this.addMasteryPoint(instance, owner);
                        if (owner instanceof Player) {
                           Player player = (Player)owner;
                           player.m_5661_(Component.m_237110_("tensura.skill.acquire_fallen", new Object[]{event.getSkill().getName(), entity.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                           player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                        }
                     }
                  }
               }

            }
         }
      }
   }

   public boolean canCollect(ManasSkill manasSkill) {
      if (manasSkill instanceof Skill) {
         Skill skill = (Skill)manasSkill;
         return !skill.getType().equals(Skill.SkillType.ULTIMATE);
      } else {
         return false;
      }
   }
}
