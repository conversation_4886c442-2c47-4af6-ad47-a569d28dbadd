package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.event.EngravingEvent;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.IForgeRegistry;
import org.jetbrains.annotations.NotNull;

public class EngravingEnchantment extends Enchantment {
   private static List<Enchantment> common;
   private static List<Enchantment> uncommon;
   private static List<Enchantment> rare;
   private static List<Enchantment> veryRare;

   protected EngravingEnchantment(Rarity pRarity, EnchantmentCategory pCategory, EquipmentSlot... pApplicableSlots) {
      super(pRarity, pCategory, pApplicableSlots);
   }

   public int m_6183_(int pEnchantmentLevel) {
      return pEnchantmentLevel * 50;
   }

   @NotNull
   public Component m_44700_(int pLevel) {
      MutableComponent mutablecomponent = Component.m_237115_(this.m_44704_());
      mutablecomponent.m_130940_(this.getNameFormatting());
      if (pLevel != 1 || this.m_6586_() != 1) {
         mutablecomponent.m_130946_(" ").m_7220_(Component.m_237115_("enchantment.level." + pLevel));
      }

      return mutablecomponent;
   }

   public ChatFormatting getNameFormatting() {
      return ChatFormatting.DARK_AQUA;
   }

   public boolean m_6594_() {
      return false;
   }

   public boolean m_6592_() {
      return false;
   }

   public boolean canApplyAtEnchantingTable(@NotNull ItemStack stack) {
      return false;
   }

   public boolean isAllowedOnBooks() {
      return false;
   }

   public boolean m_5975_(@NotNull Enchantment enchantment) {
      return enchantment instanceof EngravingEnchantment ? false : super.m_5975_(enchantment);
   }

   public float getDamageBonus(int pLevel, DamageSource source, LivingEntity attacker, LivingEntity target, EquipmentSlot slot, float damage) {
      return 0.0F;
   }

   public float getDamageProtection(int pLevel, DamageSource source, LivingEntity wearer, EquipmentSlot slot, float damage) {
      return 0.0F;
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity target, int pLevel, float damage) {
   }

   public static void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity target, float damage) {
      Map<Enchantment, Integer> map = stack.getAllEnchantments();
      Iterator var5 = map.keySet().iterator();

      while(var5.hasNext()) {
         Enchantment enchantment = (Enchantment)var5.next();
         if (enchantment instanceof EngravingEnchantment) {
            EngravingEnchantment engrave = (EngravingEnchantment)enchantment;
            engrave.doAdditionalAttack(stack, attacker, target, (Integer)map.get(enchantment), damage);
         }
      }

   }

   public static void loadConfig() {
      Stream var10000 = ((List)TensuraConfig.INSTANCE.enchantmentsConfig.commonEnchantments.get()).stream().map(ResourceLocation::new);
      IForgeRegistry var10001 = ForgeRegistries.ENCHANTMENTS;
      Objects.requireNonNull(var10001);
      common = var10000.map(var10001::getValue).toList();
      var10000 = ((List)TensuraConfig.INSTANCE.enchantmentsConfig.uncommonEnchantments.get()).stream().map(ResourceLocation::new);
      var10001 = ForgeRegistries.ENCHANTMENTS;
      Objects.requireNonNull(var10001);
      uncommon = var10000.map(var10001::getValue).toList();
      var10000 = ((List)TensuraConfig.INSTANCE.enchantmentsConfig.rareEnchantments.get()).stream().map(ResourceLocation::new);
      var10001 = ForgeRegistries.ENCHANTMENTS;
      Objects.requireNonNull(var10001);
      rare = var10000.map(var10001::getValue).toList();
      var10000 = ((List)TensuraConfig.INSTANCE.enchantmentsConfig.veryRareEnchantments.get()).stream().map(ResourceLocation::new);
      var10001 = ForgeRegistries.ENCHANTMENTS;
      Objects.requireNonNull(var10001);
      veryRare = var10000.map(var10001::getValue).toList();
   }

   public static void engrave(ItemStack itemstack, Enchantment enchantment, int level) {
      Map<Enchantment, Integer> map = (Map)EnchantmentHelper.m_44831_(itemstack).entrySet().stream().filter((entry) -> {
         return entry.getKey() != enchantment;
      }).collect(Collectors.toMap(Entry::getKey, Entry::getValue));
      itemstack.m_41749_("Enchantments");
      itemstack.m_41749_("StoredEnchantments");
      if (level > 0) {
         map.put(enchantment, level);
      }

      EnchantmentHelper.m_44865_(map, itemstack);
   }

   private static boolean shouldAddEngraving(ItemStack stack, int tier) {
      CompoundTag tag = stack.m_41783_();
      if (tag == null) {
         return false;
      } else if (tier == 3 && !tag.m_128441_("GodEngraving")) {
         tag.m_128379_("GodEngraving", true);
         return true;
      } else if (tier == 2 && !tag.m_128441_("LegendEngraving")) {
         tag.m_128379_("LegendEngraving", true);
         return true;
      } else if (tier == 1 && !tag.m_128441_("UniqueEngraving")) {
         tag.m_128379_("UniqueEngraving", true);
         return true;
      } else {
         return false;
      }
   }

   public static void randomEngraving(LivingEntity living, ItemStack stack, double epAfter) {
      boolean toUnique = epAfter >= (double)(Integer)TensuraConfig.INSTANCE.enchantmentsConfig.unique.get() && shouldAddEngraving(stack, 1);
      boolean toLegend = !toUnique && epAfter >= (double)(Integer)TensuraConfig.INSTANCE.enchantmentsConfig.legendary.get() && shouldAddEngraving(stack, 2);
      boolean toGod = !toUnique && !toLegend && epAfter >= (double)(Integer)TensuraConfig.INSTANCE.enchantmentsConfig.god.get() && shouldAddEngraving(stack, 3);
      if (toUnique || toLegend || toGod) {
         Enchantment engraving = null;
         int level = 0;
         Rarity rarity = getRarity(toGod ? 3 : (toLegend ? 2 : 1));
         switch(rarity) {
         case COMMON:
            engraving = getRandomEngravingFromList(common, stack);
            break;
         case UNCOMMON:
            engraving = getRandomEngravingFromList(uncommon, stack);
            break;
         case RARE:
            engraving = getRandomEngravingFromList(rare, stack);
            level = 2;
            break;
         case VERY_RARE:
            engraving = getRandomEngravingFromList(veryRare, stack);
            level = 3;
         }

         if (engraving != null) {
            int oldLevel = stack.getEnchantmentLevel(engraving);
            if (oldLevel == level) {
               ++level;
            }

            int newLevel = Math.min(oldLevel + level, engraving.m_6586_());
            EngravingEvent event = new EngravingEvent(stack, living, engraving, newLevel);
            if (!MinecraftForge.EVENT_BUS.post(event)) {
               engrave(stack, event.getEnchantment(), event.getLevel());
            }
         }
      }
   }

   private static Enchantment getRandomEngravingFromList(List<Enchantment> list, ItemStack stack) {
      List<Enchantment> filtered = list.stream().filter((enchantment) -> {
         if (enchantment.f_44672_.equals(EnchantmentCategory.WEAPON)) {
            return !EnchantmentCategory.WEARABLE.m_7454_(stack.m_41720_());
         } else {
            return enchantment.f_44672_.m_7454_(stack.m_41720_());
         }
      }).toList();
      return filtered.isEmpty() ? null : (Enchantment)filtered.get((new Random()).nextInt(filtered.size()));
   }

   private static Rarity getRarity(int tier) {
      int chance = (new Random()).nextInt(100);
      if (tier == 3) {
         return chance < 60 ? Rarity.RARE : Rarity.VERY_RARE;
      } else if (tier == 2) {
         if (chance < 40) {
            return Rarity.UNCOMMON;
         } else {
            return chance < 80 ? Rarity.RARE : Rarity.VERY_RARE;
         }
      } else if (chance < 50) {
         return Rarity.COMMON;
      } else if (chance < 80) {
         return Rarity.UNCOMMON;
      } else {
         return chance < 95 ? Rarity.RARE : Rarity.VERY_RARE;
      }
   }
}
