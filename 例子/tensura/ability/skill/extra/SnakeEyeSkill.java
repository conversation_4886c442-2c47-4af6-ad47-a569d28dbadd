package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SnakeEyeSkill extends Skill {
   public SnakeEyeSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 800.0D;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 5 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 5 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.snake_eye.corrosion");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.snake_eye.poison");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.snake_eye.paralysis");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.snake_eye.petrification");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.snake_eye.insanity");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 80.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 20.0D, false);
         double ownerEP = TensuraEPCapability.getEP(entity);
         if (target != null) {
            if (target instanceof Player) {
               Player player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return false;
               }
            }

            if (heldTicks % 100 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12268_, SoundSource.PLAYERS, 1.0F, 1.0F);
            MobEffect var10000;
            switch(instance.getMode()) {
            case 1:
               var10000 = (MobEffect)TensuraMobEffects.CORROSION.get();
               break;
            case 2:
               var10000 = (MobEffect)TensuraMobEffects.FATAL_POISON.get();
               break;
            case 3:
               var10000 = (MobEffect)TensuraMobEffects.PARALYSIS.get();
               break;
            case 4:
               var10000 = (MobEffect)TensuraMobEffects.PETRIFICATION.get();
               break;
            default:
               var10000 = (MobEffect)TensuraMobEffects.INSANITY.get();
            }

            MobEffect effect = var10000;
            int duration = 2;
            int level = 0;
            MobEffectInstance effectInstance = target.m_21124_(effect);
            if (effectInstance != null) {
               duration = effectInstance.m_19557_() + 2;
               level = duration / 300;
               if (TensuraEPCapability.getEP(target) / ownerEP < 0.5D) {
                  level *= 2;
               }
            }

            SkillHelper.checkThenAddEffectSource(target, entity, effect, duration, level, false, false, false, true);
         }

         return true;
      }
   }
}
