package com.github.manasmods.tensura.capability.skill;

import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;
import net.minecraftforge.common.util.LazyOptional;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TensuraSkillCapabilityProvider implements ICapabilitySerializable<CompoundTag> {
   private final ITensuraSkillCapability data = new TensuraSkillCapability();
   private final LazyOptional<ITensuraSkillCapability> lazyOptional = LazyOptional.of(() -> {
      return this.data;
   });

   public CompoundTag serializeNBT() {
      return (CompoundTag)this.data.serializeNBT();
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.data.deserializeNBT(nbt);
   }

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap, @Nullable Direction side) {
      return this.getCapability(cap);
   }

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap) {
      return TensuraSkillCapability.CAPABILITY.orEmpty(cap, this.lazyOptional);
   }
}
