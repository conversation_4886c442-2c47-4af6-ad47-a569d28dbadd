package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.DragonPeacockEntity;
import com.github.manasmods.tensura.entity.variant.PeacockVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class DragonPeacockModel extends AnimatedGeoModel<DragonPeacockEntity> {
   public ResourceLocation getModelResource(DragonPeacockEntity object) {
      return new ResourceLocation("tensura", "geo/dragon_peacock.geo.json");
   }

   public ResourceLocation getTextureResource(DragonPeacockEntity object) {
      return object.m_6162_() ? new ResourceLocation("tensura", "textures/entity/dragon_peacock/dragon_peacock_baby.png") : (ResourceLocation)PeacockVariant.LOCATION_BY_VARIANT.get(object.getVariant());
   }

   public ResourceLocation getAnimationResource(DragonPeacockEntity animatable) {
      return new ResourceLocation("tensura", "animations/dragon_peacock.animation.json");
   }
}
