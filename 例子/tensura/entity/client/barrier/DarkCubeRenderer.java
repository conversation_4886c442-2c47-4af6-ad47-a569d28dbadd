package com.github.manasmods.tensura.entity.client.barrier;

import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;

public class Dark<PERSON>ubeRenderer extends BarrierRenderer {
   public DarkCubeRenderer(Context context) {
      super(context);
   }

   public ResourceLocation getTextureLocation(BarrierEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/barrier/dark_cube.png");
   }
}
