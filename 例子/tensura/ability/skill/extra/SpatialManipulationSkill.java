package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class SpatialManipulationSkill extends Skill {
   public SpatialManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return TensuraSkillCapability.getSpiritLevel(entity, MagicElemental.SPACE.getId()) >= 1;
   }

   public double learningCost() {
      return 200.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public String modeLearningId(int mode) {
      return mode == 1 ? "WarpShot" : "None";
   }

   public Component getModeName(int mode) {
      return Component.m_237115_("tensura.skill.mode.spatial_domination.warp_shot");
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      MolecularManipulationSkill.learnMolecular(entity);
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get())) {
            if (DamageSourceHelper.isSpatialDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

            this.addMasteryPoint(instance, living);
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int learnPoint = tag.m_128451_("WarpShot");
      if (learnPoint < 100) {
         this.addMasteryPoint(instance, entity);
         tag.m_128405_("WarpShot", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (tag.m_128451_("WarpShot") >= 100) {
               player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(0)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            } else {
               instance.setCoolDown(10);
               SkillUtils.learningFailPenalty(entity);
               player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(0)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
            }

            player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         instance.markDirty();
      }

   }
}
