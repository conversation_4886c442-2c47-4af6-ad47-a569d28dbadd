package com.github.b4ndithelps.tennogamenolife.client.renderer.entity;

import com.github.b4ndithelps.tennogamenolife.client.model.XiubiHaloModel;
import com.github.b4ndithelps.tennogamenolife.client.model.XiubiArmorModel;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.XiubiHaloLayer;
import com.github.b4ndithelps.tennogamenolife.client.renderer.layer.XiubiArmorLayer;
import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import com.github.manasmods.tensura.entity.client.player.OtherworlderRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.resources.ResourceLocation;

/**
 * 休比异世界人的渲染器
 * 基于Tensura的异世界人渲染器
 */
public class XiubiRenderer extends OtherworlderRenderer<XiubiEntity> {

    public XiubiRenderer(EntityRendererProvider.Context context) {
        super(context, true); // true表示使用slim模型

        // 添加光环渲染层
        this.addLayer(new XiubiHaloLayer(this, new XiubiHaloModel(context.bakeLayer(XiubiHaloModel.LAYER_LOCATION))));

        // 添加机铠装饰渲染层
        this.addLayer(new XiubiArmorLayer(this, new XiubiArmorModel(context.bakeLayer(XiubiArmorModel.LAYER_LOCATION))));
    }

    /**
     * 隐藏名字显示
     * 重写此方法返回false来隐藏"机铠体"名字
     */
    @Override
    protected boolean shouldShowName(XiubiEntity pEntity) {
        return false; // 直接返回false，隐藏名字
    }

    /**
     * 获取休比的材质位置
     */
    @Override
    public ResourceLocation getTextureLocation(XiubiEntity entity) {
        return entity.getTextureLocation();
    }
}
