package com.github.manasmods.tensura.ability.magic.spiritual.light;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SolarFlareMagic extends SpiritualMagic {
   public SolarFlareMagic() {
      super(MagicElemental.LIGHT, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 120;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 15000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 10 : 20);
            instance.getOrCreateTag().m_128405_("HeldTicks", 0);
            instance.markDirty();
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get());
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 4.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 6.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 8.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 10.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 12.0D);
            List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               int duration = instance.isMastered(entity) ? 600 : 300;
               Iterator var6 = list.iterator();

               while(true) {
                  LivingEntity target;
                  Player player;
                  do {
                     if (!var6.hasNext()) {
                        return;
                     }

                     target = (LivingEntity)var6.next();
                     if (!(target instanceof Player)) {
                        break;
                     }

                     player = (Player)target;
                  } while(player.m_150110_().f_35934_);

                  target.m_6469_(TensuraDamageSources.elementalAttack("tensura.light_attack", entity, true), 200.0F);
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19610_, 3, duration, false, false, false));
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19604_, 0, duration, false, false, false));
                  target.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 0, duration, false, false, false));
               }
            }
         }
      }
   }
}
