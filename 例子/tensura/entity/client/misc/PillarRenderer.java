package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.magic.spike.PillarEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.Iterator;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.block.BlockRenderDispatcher;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.client.renderer.texture.TextureAtlas;
import net.minecraft.client.resources.model.BakedModel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.RandomSource;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.client.model.data.ModelData;

public class PillarRenderer extends EntityRenderer<PillarEntity> {
   private final BlockRenderDispatcher dispatcher;

   public PillarRenderer(Context context) {
      super(context);
      this.f_114477_ = 0.5F;
      this.dispatcher = context.m_234597_();
   }

   public void render(PillarEntity spike, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !spike.m_20177_(minecraft.f_91074_)) {
         BlockState blockstate = spike.getBlockState().isEmpty() ? Blocks.f_50493_.m_49966_() : (BlockState)spike.getBlockState().get();
         if (blockstate.m_60799_() == RenderShape.MODEL) {
            Level level = spike.m_9236_();
            if (blockstate != level.m_8055_(spike.m_20183_()) && blockstate.m_60799_() != RenderShape.INVISIBLE) {
               pMatrixStack.m_85836_();
               pMatrixStack.m_85837_(-0.5D, 0.0D, -0.5D);
               float radius = (float)Math.min(spike.getTickCount(), spike.getExtendingTick()) / (float)spike.getExtendingTick();
               pMatrixStack.m_85841_(1.0F, radius, 1.0F);

               for(int i = 0; (float)i < spike.getHeight(); ++i) {
                  BakedModel model = this.dispatcher.m_110910_(blockstate);
                  Iterator var13 = model.getRenderTypes(blockstate, spike.f_19853_.m_213780_(), ModelData.EMPTY).iterator();

                  while(var13.hasNext()) {
                     RenderType renderType = (RenderType)var13.next();
                     this.dispatcher.m_110937_().tesselateBlock(level, model, blockstate, spike.m_20183_().m_6630_(i), pMatrixStack, pBuffer.m_6299_(renderType), false, RandomSource.m_216327_(), blockstate.m_60726_(spike.m_20183_().m_6630_(i)), OverlayTexture.f_118083_, ModelData.EMPTY, renderType);
                  }

                  pMatrixStack.m_85837_(0.0D, 1.0D, 0.0D);
               }

               pMatrixStack.m_85849_();
               super.m_7392_(spike, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
            }
         }

      }
   }

   public ResourceLocation getTextureLocation(PillarEntity instance) {
      return TextureAtlas.f_118259_;
   }
}
