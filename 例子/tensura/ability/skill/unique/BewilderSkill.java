package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class BewilderSkill extends Skill {
   public BewilderSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public int modes() {
      return 4;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 4 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 4 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.bewilder.target");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.bewilder.area");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.bewilder.charm");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.bewilder.kill");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 50.0D;
         break;
      case 2:
         var10000 = 80.0D;
         break;
      case 3:
         var10000 = 200.0D;
         break;
      case 4:
         var10000 = 100.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      UUID uuid = entity.m_20148_();
      float radius;
      List list;
      Iterator var7;
      LivingEntity target;
      switch(instance.getMode()) {
      case 1:
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
         if (target == null) {
            return;
         }

         if (entity.m_6144_()) {
            TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
               if (Objects.equals(cap.getTemporaryOwner(), uuid)) {
                  cap.setTemporaryOwner((UUID)null);
                  target.m_21195_((MobEffect)TensuraMobEffects.MIND_CONTROL.get());
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123798_);
                  TensuraEPCapability.sync(target);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  UUID owner = cap.getPermanentOwner();
                  if (target instanceof TensuraTamableEntity) {
                     TensuraTamableEntity tamable = (TensuraTamableEntity)target;
                     tamable.resetOwner(owner);
                  } else if (target instanceof TensuraHorseEntity) {
                     TensuraHorseEntity horse = (TensuraHorseEntity)target;
                     horse.resetOwner(owner);
                  } else if (target instanceof TamableAnimal) {
                     TamableAnimal animal = (TamableAnimal)target;
                     animal.m_21816_(owner);
                     if (owner == null) {
                        animal.m_7105_(false);
                     }
                  }
               }

            });
         } else {
            if (target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
               return;
            }

            if (CharmSkill.isMindControlFailed(entity, target, level)) {
               return;
            }

            if (RaceHelper.isSpiritualLifeForm(target) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               return;
            }

            TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
               if (!Objects.equals(cap.getPermanentOwner(), uuid)) {
                  if (!SkillHelper.outOfMagicule(entity, instance)) {
                     if (!Objects.equals(cap.getTemporaryOwner(), uuid)) {
                        this.addMasteryPoint(instance, entity);
                        int duration = 12000;
                        if (SkillUtils.hasSkill(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
                           duration /= 2;
                        }

                        SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.MIND_CONTROL.get(), duration, 0);
                        if (target.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get())) {
                           cap.setTemporaryOwner(uuid);
                           if (entity instanceof Player) {
                              Player player = (Player)entity;
                              if (target instanceof TamableAnimal) {
                                 TamableAnimal animal = (TamableAnimal)target;
                                 animal.m_21828_(player);
                              } else if (target instanceof TensuraHorseEntity) {
                                 TensuraHorseEntity horse = (TensuraHorseEntity)target;
                                 horse.m_30637_(player);
                              }
                           }

                           TensuraEPCapability.sync(target);
                           entity.m_21011_(InteractionHand.MAIN_HAND, true);
                           level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11733_, SoundSource.PLAYERS, 1.0F, 1.0F);
                           TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123798_);
                        }
                     }
                  }
               }
            });
         }
         break;
      case 2:
         radius = instance.isMastered(entity) ? 15.0F : 10.0F;
         list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_((double)radius), (livingEntity) -> {
            return !livingEntity.m_7306_(entity) && livingEntity.m_6084_();
         });
         if (list.isEmpty()) {
            return;
         }

         if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * (double)list.size())) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11733_, SoundSource.PLAYERS, 1.0F, 1.0F);
         var7 = list.iterator();

         while(var7.hasNext()) {
            target = (LivingEntity)var7.next();
            if (CharmSkill.canMindControl(target, level) && !target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()) && !RaceHelper.isSpiritualLifeForm(target) && !SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
               int duration = SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get()) ? 3000 : 6000;
               SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.MIND_CONTROL.get(), duration, 0);
               if (target.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get())) {
                  TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                     if (!Objects.equals(cap.getTemporaryOwner(), uuid)) {
                        cap.setTemporaryOwner(uuid);
                        if (entity instanceof Player) {
                           Player player = (Player)entity;
                           if (target instanceof TamableAnimal) {
                              TamableAnimal animal = (TamableAnimal)target;
                              animal.m_21828_(player);
                           } else if (target instanceof TensuraHorseEntity) {
                              TensuraHorseEntity horse = (TensuraHorseEntity)target;
                              horse.m_30637_(player);
                           }
                        }

                        TensuraEPCapability.sync(target);
                        entity.m_21011_(InteractionHand.MAIN_HAND, true);
                        TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123798_);
                     }

                  });
               }
            }
         }

         return;
      case 3:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         entity.m_7292_(new MobEffectInstance(MobEffects.f_19595_, 2400, 4, false, false, true));
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123798_);
         break;
      case 4:
         radius = instance.isMastered(entity) ? 15.0F : 10.0F;
         list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_((double)radius), (livingEntity) -> {
            return !livingEntity.m_7306_(entity) && livingEntity.m_6084_() && livingEntity.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get());
         });
         if (list.isEmpty()) {
            return;
         }

         if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * (double)list.size())) {
            return;
         }

         instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         this.addMasteryPoint(instance, entity);
         var7 = list.iterator();

         while(var7.hasNext()) {
            target = (LivingEntity)var7.next();
            if (Objects.equals(uuid, TensuraEPCapability.getTemporaryOwner(target))) {
               float damage = target.m_21223_();
               if (SkillUtils.hasSkill(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
                  damage /= 2.0F;
               }

               if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.selfKill(entity), entity, instance), damage)) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_);
               }
            }
         }
      }

   }
}
