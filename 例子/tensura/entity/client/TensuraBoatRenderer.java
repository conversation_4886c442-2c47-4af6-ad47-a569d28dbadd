package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.template.TensuraBoatEntity;
import com.google.common.collect.ImmutableMap;
import com.mojang.datafixers.util.Pair;
import java.util.Map;
import java.util.stream.Stream;
import net.minecraft.client.model.BoatModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.renderer.entity.BoatRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.vehicle.Boat;

public class TensuraBoatRenderer extends BoatRenderer {
   private final Map<TensuraBoatEntity.Type, Pair<ResourceLocation, BoatModel>> boatResources;

   public TensuraBoatRenderer(Context context) {
      super(context, false);
      this.f_114477_ = 0.8F;
      this.boatResources = (Map)Stream.of(TensuraBoatEntity.Type.values()).collect(ImmutableMap.toImmutableMap((type) -> {
         return type;
      }, (type) -> {
         return Pair.of(new ResourceLocation("tensura", "textures/entity/boat/" + type.getName() + ".png"), new BoatModel(context.m_174023_(new ModelLayerLocation(new ResourceLocation("minecraft", "boat/oak"), "main")), false));
      }));
   }

   public ResourceLocation m_5478_(Boat pEntity) {
      if (pEntity instanceof TensuraBoatEntity) {
         TensuraBoatEntity TensuraBoat = (TensuraBoatEntity)pEntity;
         return (ResourceLocation)((Pair)this.boatResources.get(TensuraBoat.getTensuraBoatType())).getFirst();
      } else {
         return new ResourceLocation("minecraft", "boat/oak");
      }
   }

   public Pair<ResourceLocation, BoatModel> getModelWithLocation(Boat boat) {
      if (boat instanceof TensuraBoatEntity) {
         TensuraBoatEntity TensuraBoat = (TensuraBoatEntity)boat;
         return (Pair)this.boatResources.get(TensuraBoat.getTensuraBoatType());
      } else {
         return null;
      }
   }
}
