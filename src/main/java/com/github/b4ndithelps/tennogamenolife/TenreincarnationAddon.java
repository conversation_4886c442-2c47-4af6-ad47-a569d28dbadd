package com.github.b4ndithelps.tennogamenolife;

import com.mojang.logging.LogUtils;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class TenreincarnationAddon {
    private static final Logger LOGGER = LogUtils.getLogger();

    @SubscribeEvent
    public void onCommonSetup(FMLCommonSetupEvent event) {
        LOGGER.info("TenNoGameNoLife Common Setup");
        if (this.isFirstLaunch()) {
            this.editTOMLFile();
            this.markAsEdited();
            LOGGER.info("TenNoGameNoLife TOML file was edited.");
        } else {
            LOGGER.info("TenNoGameNoLife TOML file already edited.");
        }
    }

    private boolean isFirstLaunch() {
        File markerFile = new File("defaultconfigs/tensura-reincarnated/tennogamenolife_first_launch_marker");
        return !markerFile.exists();
    }

    private void markAsEdited() {
        File markerFile = new File("defaultconfigs/tensura-reincarnated/tennogamenolife_first_launch_marker");

        try {
            if (markerFile.createNewFile()) {
                System.out.println("TenNoGameNoLife marker file created: " + markerFile.getAbsolutePath());
            } else {
                System.out.println("TenNoGameNoLife marker file already exists.");
            }
        } catch (IOException var3) {
            var3.printStackTrace();
            System.out.println("Error creating TenNoGameNoLife marker file: " + var3.getMessage());
        }
    }

    public void editTOMLFile() {
        File tomlFile = new File("defaultconfigs/tensura-reincarnated/common.toml");
        StringBuilder contentBuilder = new StringBuilder();

        try {
            BufferedReader reader = new BufferedReader(new FileReader(tomlFile));

            try {
                String line;
                while((line = reader.readLine()) != null) {
                    contentBuilder.append(line).append(System.lineSeparator());
                }

                reader.close();
            } catch (Throwable var17) {
                try {
                    reader.close();
                } catch (Throwable var13) {
                    var17.addSuppressed(var13);
                }

                throw var17;
            }
        } catch (IOException var18) {
            var18.printStackTrace();
            System.out.println("Error reading the TOML file: " + var18.getMessage());
            return;
        }

        String content = contentBuilder.toString();
        String[] newStarting = new String[]{"tennogamenolife:machine", "tennogamenolife:flugel", "tennogamenolife:dwarf", "tennogamenolife:dragonia"};
        String[] newRandom = new String[]{"tennogamenolife:machine", "tennogamenolife:flugel", "tennogamenolife:dwarf", "tennogamenolife:dragonia"};
        String[] newReincarnationSkills = new String[]{"tennogamenolife:chain_mining", "tennogamenolife:chain_felling", "tennogamenolife:chain_shovel"};
        String[] newSkillCreatorSkills = new String[]{"tennogamenolife:chain_mining", "tennogamenolife:chain_felling", "tennogamenolife:chain_shovel"};
        
        String startingRacesKey = "startingRaces = [";
        String randomRacesKey = "possibleRandomRaces = [";
        String reincarnationSkillsKey = "reincarnationSkills = [";
        String skillCreatorSkillsKey = "skillCreatorSkills = [";
        
        content = this.addItemsToTOMLList(content, startingRacesKey, newStarting);
        content = this.addItemsToTOMLList(content, randomRacesKey, newRandom);
        content = this.addItemsToTOMLList(content, reincarnationSkillsKey, newReincarnationSkills);
        content = this.addItemsToTOMLList(content, skillCreatorSkillsKey, newSkillCreatorSkills);

        try {
            BufferedWriter writer = new BufferedWriter(new FileWriter(tomlFile));

            try {
                writer.write(content);
                writer.close();
            } catch (Throwable var15) {
                try {
                    writer.close();
                } catch (Throwable var14) {
                    var15.addSuppressed(var14);
                }

                throw var15;
            }
        } catch (IOException var16) {
            var16.printStackTrace();
            System.out.println("Error writing to the TOML file: " + var16.getMessage());
        }

        System.out.println("TenNoGameNoLife items added to TOML lists successfully.");
    }

    private String addItemsToTOMLList(String content, String listKey, String[] newItems) {
        int index = content.indexOf(listKey);
        if (index == -1) {
            System.out.println("List identifier '" + listKey + "' not found.");
            return content;
        } else {
            int endIndex = content.indexOf("]", index) + 1;
            if (endIndex == 0) {
                System.out.println("Closing bracket not found for list: " + listKey);
                return content;
            } else {
                String listContent = content.substring(index, endIndex);
                String[] var7 = newItems;
                int var8 = newItems.length;

                for(int var9 = 0; var9 < var8; ++var9) {
                    String newItem = var7[var9];
                    if (!listContent.contains(newItem)) {
                        listContent = listContent.replace("]", ", \"" + newItem + "\"]");
                    }
                }

                return content.replace(content.substring(index, endIndex), listContent);
            }
        }
    }
} 