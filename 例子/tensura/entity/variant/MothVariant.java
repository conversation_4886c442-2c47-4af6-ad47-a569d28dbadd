package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum MothVariant {
   HELL(0),
   GEHENNA(1),
   MOTHRA(2);

   private static final MothVariant[] BY_ID = (MothVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(MothVariant::getId)).toArray((x$0) -> {
      return new MothVariant[x$0];
   });
   private final int id;
   public static final Map<MothVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(MothVariant.class), (variant) -> {
      variant.put(HELL, new ResourceLocation("tensura", "textures/entity/hell_moth_line/hell_moth.png"));
      variant.put(GEHENNA, new ResourceLocation("tensura", "textures/entity/hell_moth_line/gehenna_moth.png"));
      variant.put(MOTHRA, new ResourceLocation("tensura", "textures/entity/hell_moth_line/mothra.png"));
   });

   private MothVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static MothVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static MothVariant[] $values() {
      return new MothVariant[]{HELL, GEHENNA, MOTHRA};
   }
}
