package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.TempestSerpentBody;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class TempestSerpentBodyModel extends AnimatedGeoModel<TempestSerpentBody> {
   public ResourceLocation getModelResource(TempestSerpentBody object) {
      return object.isEndSegment() ? new ResourceLocation("tensura", "geo/tempest_serpent_tail.geo.json") : new ResourceLocation("tensura", "geo/tempest_serpent_body.geo.json");
   }

   public ResourceLocation getTextureResource(TempestSerpentBody instance) {
      return new ResourceLocation("tensura", "textures/entity/tempest_serpent/tempest_serpent.png");
   }

   public ResourceLocation getAnimationResource(TempestSerpentBody entity) {
      return entity.isEndSegment() ? new ResourceLocation("tensura", "animations/multipart/tempest_serpent_tail.animation.json") : new ResourceLocation("tensura", "animations/multipart/tempest_serpent_body.animation.json");
   }

   public void setCustomAnimations(TempestSerpentBody entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      if (!entity.isEndSegment()) {
         IBone chest = this.getAnimationProcessor().getBone("Chest");
         if (entity.isChested() == chest.isHidden()) {
            chest.setHidden(!entity.isChested());
         }

      }
   }
}
