package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.block.Blocks;

public class MagicEarthTransformSkill extends MagicElementalTransformSkill {
   protected MagicElemental getMagicElemental() {
      return MagicElemental.EARTH;
   }

   protected ManasSkill getElementalTransform() {
      return (ManasSkill)IntrinsicSkills.EARTH_TRANSFORM.get();
   }

   protected MobEffect getMagicElementalEffect() {
      return (MobEffect)TensuraMobEffects.MAGIC_EARTH.get();
   }

   protected void doVisualEffect(LivingEntity entity) {
      TensuraParticleHelper.spawnServerParticles(entity.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
   }
}
