package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.magic.lightning.LightningBolt;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Matrix4f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.TextureAtlas;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.RandomSource;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class LightningBoltRenderer extends EntityRenderer<LightningBolt> {
   public LightningBoltRenderer(Context pContext) {
      super(pContext);
   }

   public void render(<PERSON>Bolt pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      VertexConsumer vertexconsumer = pBuffer.m_6299_(RenderType.m_110502_());
      Matrix4f matrix4f = pMatrixStack.m_85850_().m_85861_();

      int j;
      for(j = 0; j < 4; ++j) {
         for(int k = 0; k < 3; ++k) {
            int l = 7;
            int i1 = 0;
            if (k > 0) {
               l = 7 - k;
            }

            if (k > 0) {
               i1 = l - 2;
            }

            for(int y = l; y >= i1; --y) {
               float f10 = 0.1F + (float)j * 0.2F;
               if (k == 0) {
                  f10 *= (float)y * 0.1F + 1.0F;
               }

               float f11 = 0.1F + (float)j * 0.2F;
               if (k == 0) {
                  f11 *= ((float)y - 1.0F) * 0.1F + 1.0F;
               }

               float red = 0.45F;
               float green = 0.45F;
               float blue = 0.5F;
               quad(matrix4f, vertexconsumer, 0.0F, 0.0F, y, 0.0F, 0.0F, red, green, blue, f10, f11, false, false, true, false);
               quad(matrix4f, vertexconsumer, 0.0F, 0.0F, y, 0.0F, 0.0F, red, green, blue, f10, f11, true, false, true, true);
               quad(matrix4f, vertexconsumer, 0.0F, 0.0F, y, 0.0F, 0.0F, red, green, blue, f10, f11, true, true, false, true);
               quad(matrix4f, vertexconsumer, 0.0F, 0.0F, y, 0.0F, 0.0F, red, green, blue, f10, f11, false, true, false, false);
            }
         }
      }

      for(j = 0; j < pEntity.getAdditionalVisual(); ++j) {
         renderAdditionalBolt(pMatrixStack, pBuffer, pEntity.m_9236_().m_213780_().m_188505_());
      }

   }

   protected static void renderAdditionalBolt(PoseStack pMatrixStack, MultiBufferSource pBuffer, long seed) {
      float[] afloat = new float[8];
      float[] afloat1 = new float[8];
      float f = 0.0F;
      float f1 = 0.0F;
      RandomSource source = RandomSource.m_216335_(seed);

      for(int i = 7; i >= 0; --i) {
         afloat[i] = f;
         afloat1[i] = f1;
         f += (float)(source.m_188503_(11) - 5);
         f1 += (float)(source.m_188503_(11) - 5);
      }

      VertexConsumer vertexconsumer = pBuffer.m_6299_(RenderType.m_110502_());
      Matrix4f matrix4f = pMatrixStack.m_85850_().m_85861_();

      for(int j = 0; j < 4; ++j) {
         RandomSource randomSource = RandomSource.m_216335_(seed);

         for(int k = 0; k < 3; ++k) {
            int l = 7;
            int i1 = 0;
            if (k > 0) {
               l = 7 - k;
            }

            if (k > 0) {
               i1 = l - 2;
            }

            float z = afloat[l] - f;
            float x = afloat1[l] - f1;

            for(int y = l; y >= i1; --y) {
               float z2 = z;
               float x2 = x;
               if (k == 0) {
                  z += (float)(randomSource.m_188503_(11) - 5);
                  x += (float)(randomSource.m_188503_(11) - 5);
               } else {
                  z += (float)(randomSource.m_188503_(31) - 15);
                  x += (float)(randomSource.m_188503_(31) - 15);
               }

               float f10 = 0.1F + (float)j * 0.2F;
               if (k == 0) {
                  f10 *= (float)y * 0.1F + 1.0F;
               }

               float f11 = 0.1F + (float)j * 0.2F;
               if (k == 0) {
                  f11 *= ((float)y - 1.0F) * 0.1F + 1.0F;
               }

               float red = 0.45F;
               float green = 0.45F;
               float blue = 0.5F;
               quad(matrix4f, vertexconsumer, z, x, y, z2, x2, red, green, blue, f10, f11, false, false, true, false);
               quad(matrix4f, vertexconsumer, z, x, y, z2, x2, red, green, blue, f10, f11, true, false, true, true);
               quad(matrix4f, vertexconsumer, z, x, y, z2, x2, red, green, blue, f10, f11, true, true, false, true);
               quad(matrix4f, vertexconsumer, z, x, y, z2, x2, red, green, blue, f10, f11, false, true, false, false);
            }
         }
      }

   }

   private static void quad(Matrix4f matrix4f, VertexConsumer consumer, float z, float x, int y, float z2, float x2, float red, float green, float blue, float v7, float v8, boolean b, boolean b1, boolean b2, boolean b3) {
      consumer.m_85982_(matrix4f, z + (b ? v8 : -v8), (float)(y * 16), x + (b1 ? v8 : -v8)).m_85950_(red, green, blue, 1.0F).m_7421_(1.0F, 1.0F).m_85969_(15728880).m_5752_();
      consumer.m_85982_(matrix4f, z2 + (b ? v7 : -v7), (float)((y + 1) * 16), x2 + (b1 ? v7 : -v7)).m_85950_(red, green, blue, 1.0F).m_7421_(1.0F, 1.0F).m_85969_(15728880).m_5752_();
      consumer.m_85982_(matrix4f, z2 + (b2 ? v7 : -v7), (float)((y + 1) * 16), x2 + (b3 ? v7 : -v7)).m_85950_(red, green, blue, 1.0F).m_7421_(1.0F, 1.0F).m_85969_(15728880).m_5752_();
      consumer.m_85982_(matrix4f, z + (b2 ? v8 : -v8), (float)(y * 16), x + (b3 ? v8 : -v8)).m_85950_(red, green, blue, 1.0F).m_7421_(1.0F, 1.0F).m_85969_(15728880).m_5752_();
   }

   public ResourceLocation getTextureLocation(LightningBolt pEntity) {
      return TextureAtlas.f_118259_;
   }
}
