package com.github.manasmods.tensura.ability.magic.spiritual.earth;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Holder;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BiomeTags;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class EarthMagic extends SpiritualMagic {
   public EarthMagic() {
      super(MagicElemental.EARTH, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 10;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.isMastered(entity) ? 20.0D : 50.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         Level level = entity.m_9236_();
         if (TensuraGameRules.canSkillGrief(level)) {
            this.addMasteryPoint(instance, entity);
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 6.0D);
            if (result.m_6662_() == Type.BLOCK) {
               BlockPos pos = result.m_82425_();
               BlockPos relative = pos.m_121945_(result.m_82434_());
               if (level.m_8055_(pos).m_60767_().m_76336_()) {
                  relative = pos;
               }

               if (level.m_8055_(relative).m_60767_().m_76336_()) {
                  if (SkillHelper.outOfMagicule(entity, instance)) {
                     return;
                  }

                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, relative);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  BlockState earthBlock = this.biomeBlock(level, relative).m_49966_();
                  level.m_7731_(relative, earthBlock, 11);
                  level.m_142346_(entity, GameEvent.f_157797_, relative);
                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, relative));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_144137_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               }
            }

         }
      }
   }

   private Block biomeBlock(Level level, BlockPos pos) {
      Holder<Biome> biome = level.m_204166_(pos);
      if (((Biome)biome.get()).toString().contains("desert")) {
         return Blocks.f_49992_;
      } else if (!biome.m_203656_(BiomeTags.f_207604_) && !biome.m_203656_(BiomeTags.f_207603_) && !biome.m_203656_(BiomeTags.f_207602_)) {
         if (((Biome)biome.get()).toString().contains("cave")) {
            return Blocks.f_50069_;
         } else if (biome.m_203656_(BiomeTags.f_207606_)) {
            return Blocks.f_50069_;
         } else if (biome.m_203656_(BiomeTags.f_207612_)) {
            return Blocks.f_50134_;
         } else {
            return biome.m_203656_(BiomeTags.f_215818_) ? Blocks.f_50259_ : Blocks.f_50493_;
         }
      } else {
         return Blocks.f_49992_;
      }
   }
}
