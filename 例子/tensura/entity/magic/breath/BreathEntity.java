package com.github.manasmods.tensura.entity.magic.breath;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.entity.PartEntity;
import org.jetbrains.annotations.NotNull;

public class BreathEntity extends Projectile {
   private static final EntityDataAccessor<Float> LENGTH;
   private static final EntityDataAccessor<Integer> LIFE;
   protected double mpCost;
   protected float damage;
   protected ManasSkillInstance skill;
   protected int age;
   protected float prevLength;
   public BreathPart[] parts;

   public BreathEntity(EntityType<? extends BreathEntity> entityType, Level level, LivingEntity entity) {
      this(entityType, level);
      this.m_5602_(entity);
   }

   public BreathEntity(EntityType<? extends BreathEntity> entityType, Level level) {
      super(entityType, level);
      this.mpCost = 0.0D;
      this.damage = 0.0F;
      this.skill = null;
      this.prevLength = 0.0F;
      this.parts = new BreathPart[]{new BreathPart(this, "Breath 1", 1.0F, 1.0F), new BreathPart(this, "Breath 2", 2.0F, 1.5F), new BreathPart(this, "Breath 3", 3.0F, 2.0F), new BreathPart(this, "Breath 4", 4.0F, 2.5F)};
      this.m_20242_(true);
      this.f_19850_ = false;
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(LENGTH, 10.0F);
      this.f_19804_.m_135372_(LIFE, 5);
   }

   protected void m_7380_(@NotNull CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128347_("MPCost", this.getMpCost());
      pCompound.m_128350_("Damage", this.getDamage());
      pCompound.m_128350_("Length", this.getLength());
      pCompound.m_128405_("Life", this.getLife());
      if (this.skill != null) {
         pCompound.m_128365_("skill", this.skill.toNBT());
      }

   }

   protected void m_7378_(@NotNull CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setMpCost(pCompound.m_128459_("MPCost"));
      this.setDamage(pCompound.m_128457_("Damage"));
      this.f_19804_.m_135381_(LENGTH, pCompound.m_128457_("Length"));
      this.f_19804_.m_135381_(LIFE, pCompound.m_128451_("Life"));
      if (pCompound.m_128441_("skill")) {
         Tag var3 = pCompound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

   }

   public float getLength() {
      return (Float)this.f_19804_.m_135370_(LENGTH);
   }

   public void setLength(float length) {
      this.f_19804_.m_135381_(LENGTH, length);
      this.buildParts();
   }

   public int getLife() {
      return (Integer)this.f_19804_.m_135370_(LIFE);
   }

   public void setLife(int life) {
      this.f_19804_.m_135381_(LIFE, life);
   }

   public void increaseLife(int life) {
      this.setLife(Math.min(this.getLife() + life, 100));
   }

   public boolean m_6060_() {
      return false;
   }

   public boolean isMultipartEntity() {
      return true;
   }

   public PartEntity<?>[] getParts() {
      return this.parts;
   }

   public void buildParts() {
      if (!(this.getLength() <= 0.0F)) {
         float f = this.getLength();

         int l;
         for(l = 1; f > 0.0F; ++l) {
            f -= (float)l;
         }

         BreathPart[] temp = new BreathPart[l - 1];
         float length = this.getLength();

         for(int i = 1; length > 0.0F; ++i) {
            float partLength = (float)i;
            if (length < (float)i) {
               partLength = Math.max(length, 2.0F);
            }

            length -= (float)i;
            temp[i - 1] = new BreathPart(this, "part" + i, partLength, 0.5F + (float)i * 0.5F);
         }

         this.parts = temp;
      }
   }

   protected static boolean hasLineOfSight(Entity source, Entity target) {
      Vec3 sourceEye = new Vec3(source.m_20185_(), source.m_20186_() + (double)(source.m_20206_() / 2.0F), source.m_20189_());
      Vec3 targetEye = new Vec3(target.m_20185_(), target.m_20186_() + (double)(target.m_20206_() / 2.0F), target.m_20189_());
      return source.m_9236_().m_45547_(new ClipContext(sourceEye, targetEye, Block.COLLIDER, Fluid.NONE, source)).m_6662_() == Type.MISS;
   }

   protected Set<Entity> getPartCollision() {
      List<Entity> list = new ArrayList();
      BreathPart[] var2 = this.parts;
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         Entity entity = var2[var4];
         list.addAll(this.m_9236_().m_45933_(entity, entity.m_20191_()));
      }

      return (Set)list.stream().filter(this::canCollide).collect(Collectors.toSet());
   }

   protected boolean canCollide(Entity entity) {
      Entity owner = this.m_37282_();
      if (owner != null) {
         if (entity == owner) {
            return false;
         }

         if (!hasLineOfSight(this, entity)) {
            return false;
         }

         Entity vehicle = entity.m_20202_();
         boolean var10000;
         Mob mob;
         if (entity == vehicle) {
            if (vehicle instanceof Mob) {
               mob = (Mob)vehicle;
               if (mob.m_5448_() == owner) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         }

         if (owner.m_20197_().contains(entity)) {
            if (entity instanceof Mob) {
               mob = (Mob)entity;
               if (mob.m_5448_() == owner) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         }
      }

      return hasLineOfSight(this, entity);
   }

   public void m_20234_(int id) {
      super.m_20234_(id);

      for(int i = 0; i < this.parts.length; ++i) {
         if (this.parts[i] != null) {
            this.parts[i].m_20234_(id + i + 1);
         }
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (this.prevLength != this.getLength()) {
         this.prevLength = this.getLength();
         this.buildParts();
      }

      Entity owner = this.m_37282_();
      if (owner != null) {
         Vec3 eye = owner.m_20299_(1.0F).m_82492_(0.0D, 0.8D, 0.0D);
         this.m_146884_(eye);
         this.m_146926_(owner.m_146909_());
         this.m_146922_(owner.m_146908_());
         this.f_19859_ = this.m_146908_();
         this.f_19860_ = this.m_146909_();
         double distance = 1.0D;
         BreathPart[] var5 = this.parts;
         int var6 = var5.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            BreathPart part = var5[var7];
            if (part != null) {
               distance += (double)(part.m_6972_((Pose)null).f_20377_ * 4.0F / 5.0F);
               Vec3 newVector = eye.m_82549_(owner.m_20252_(0.5F).m_82542_(distance, distance, distance));
               part.m_146884_(newVector);
               part.m_20256_(eye.m_82549_(owner.m_20252_(1.0F).m_82542_(distance, distance, distance)));
               Vec3 vec3 = new Vec3(part.m_20185_(), part.m_20186_(), part.m_20189_());
               part.f_19854_ = vec3.f_82479_;
               part.f_19855_ = vec3.f_82480_;
               part.f_19856_ = vec3.f_82481_;
               part.f_19790_ = vec3.f_82479_;
               part.f_19791_ = vec3.f_82480_;
               part.f_19792_ = vec3.f_82481_;
            }
         }
      }

      if (!this.m_9236_().m_5776_()) {
         Iterator var11 = this.getPartCollision().iterator();

         while(var11.hasNext()) {
            Entity entity = (Entity)var11.next();
            this.m_5790_(new EntityHitResult(entity));
         }
      } else {
         this.spawnParticle();
      }

      if (++this.age > this.getLife()) {
         this.m_146870_();
      }

   }

   public void spawnParticle() {
   }

   public static void spawnBreathEntity(EntityType<? extends BreathEntity> entityType, LivingEntity owner, ManasSkillInstance instance, double cost) {
      spawnBreathEntity(entityType, owner, instance, 0.0F, cost);
   }

   public static void spawnBreathEntity(EntityType<? extends BreathEntity> entityType, LivingEntity owner, ManasSkillInstance instance, float damage, double cost) {
      CompoundTag tag = instance.getOrCreateTag();
      Level level = owner.m_9236_();
      if (tag.m_128451_("BreathEntity") == 0) {
         BreathEntity breath = (BreathEntity)entityType.m_20615_(level);
         if (breath == null) {
            return;
         }

         breath.setDamage(damage);
         breath.m_5602_(owner);
         breath.m_146884_(owner.m_20182_().m_82520_(0.0D, (double)(owner.m_20206_() / 2.0F), 0.0D));
         breath.setMpCost(cost);
         breath.setSkill(instance);
         owner.m_9236_().m_7967_(breath);
         tag.m_128405_("BreathEntity", breath.m_19879_());
      } else {
         Entity entity = owner.m_9236_().m_6815_(tag.m_128451_("BreathEntity"));
         if (entity instanceof BreathEntity) {
            BreathEntity breath = (BreathEntity)entity;
            breath.increaseLife(1);
         } else {
            tag.m_128405_("BreathEntity", 0);
         }
      }

      instance.markDirty();
   }

   public static void spawnPredationMist(EntityType<? extends PredatorMistProjectile> entityType, LivingEntity owner, ManasSkillInstance instance, double cost, float length, int blockMode, boolean projectile) {
      CompoundTag tag = instance.getOrCreateTag();
      Level level = owner.m_9236_();
      if (tag.m_128451_("BreathEntity") == 0) {
         PredatorMistProjectile breath = (PredatorMistProjectile)entityType.m_20615_(level);
         if (breath == null) {
            return;
         }

         breath.m_5602_(owner);
         breath.setLength(length);
         breath.setBlockMode(blockMode);
         breath.setConsumeProjectile(projectile);
         breath.m_146884_(owner.m_20182_().m_82520_(0.0D, (double)(owner.m_20206_() / 2.0F), 0.0D));
         breath.setMpCost(cost);
         breath.setSkill(instance);
         owner.m_9236_().m_7967_(breath);
         tag.m_128405_("BreathEntity", breath.m_19879_());
      } else {
         Entity entity = owner.m_9236_().m_6815_(tag.m_128451_("BreathEntity"));
         if (entity instanceof PredatorMistProjectile) {
            PredatorMistProjectile breath = (PredatorMistProjectile)entity;
            breath.increaseLife(1);
            breath.setLength(length);
         } else {
            tag.m_128405_("BreathEntity", 0);
         }
      }

      instance.markDirty();
   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public float getDamage() {
      return this.damage;
   }

   public void setDamage(float damage) {
      this.damage = damage;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   static {
      LENGTH = SynchedEntityData.m_135353_(BreathEntity.class, EntityDataSerializers.f_135029_);
      LIFE = SynchedEntityData.m_135353_(BreathEntity.class, EntityDataSerializers.f_135028_);
   }
}
