package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class MadOgreSpheresModel extends AnimatedGeoModel<MadOgreOrbsEntity> {
   public ResourceLocation getModelResource(MadOgreOrbsEntity object) {
      return new ResourceLocation("tensura", "geo/projectile/mad_ogre_orbs.geo.json");
   }

   public ResourceLocation getTextureResource(MadOgreOrbsEntity instance) {
      return new ResourceLocation("tensura", "textures/blank_texture.png");
   }

   public ResourceLocation getAnimationResource(MadOgreOrbsEntity entity) {
      return new ResourceLocation("tensura", "animations/beast_gnome.animation.json");
   }
}
