package com.github.b4ndithelps.tennogamenolife.client.renderer.layer;

import com.github.b4ndithelps.tennogamenolife.client.model.XiubiHaloModel;
import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;

/**
 * 休比光环渲染层
 * 在休比实体上渲染光环效果
 */
@SuppressWarnings("rawtypes")
public class XiubiHaloLayer extends RenderLayer {

    private static final ResourceLocation HALO_TEXTURE =
        new ResourceLocation("tennogamenolife", "textures/entity/xiubi/halo.png");

    private final XiubiHaloModel haloModel;

    @SuppressWarnings("unchecked")
    public XiubiHaloLayer(RenderLayerParent renderer, XiubiHaloModel haloModel) {
        super(renderer);
        this.haloModel = haloModel;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void render(PoseStack poseStack, MultiBufferSource buffer, int packedLight,
                      net.minecraft.world.entity.Entity entity, float limbSwing, float limbSwingAmount,
                      float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {

        if (!(entity instanceof XiubiEntity xiubiEntity)) {
            return;
        }

        // 设置光环动画
        this.haloModel.setupAnim(xiubiEntity, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);
        
        // 渲染光环
        this.haloModel.renderToBuffer(
            poseStack, 
            buffer.getBuffer(RenderType.entityTranslucent(HALO_TEXTURE)), 
            packedLight, 
            OverlayTexture.NO_OVERLAY, 
            1.0F, 1.0F, 1.0F, 0.8F  // 设置透明度为0.8
        );
    }
}
