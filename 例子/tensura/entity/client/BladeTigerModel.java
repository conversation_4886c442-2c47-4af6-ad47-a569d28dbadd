package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BladeTigerEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class BladeTigerModel extends AnimatedGeoModel<BladeTigerEntity> {
   public ResourceLocation getModelResource(BladeTigerEntity object) {
      return new ResourceLocation("tensura", "geo/blade_tiger.geo.json");
   }

   public ResourceLocation getTextureResource(BladeTigerEntity instance) {
      return instance.isWhite() ? new ResourceLocation("tensura", "textures/entity/blade_tiger/blade_tiger_white.png") : new ResourceLocation("tensura", "textures/entity/blade_tiger/blade_tiger.png");
   }

   public ResourceLocation getAnimationResource(BladeTigerEntity tiger) {
      return new ResourceLocation("tensura", "animations/blade_tiger.animation.json");
   }

   public void setCustomAnimations(BladeTigerEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      IBone chests = this.getAnimationProcessor().getBone("Chests");
      if (entity.isChested() == chests.isHidden()) {
         chests.setHidden(!entity.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (entity.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!entity.isSaddled());
      }

      IBone saddleSpike = this.getAnimationProcessor().getBone("SaddleSpike");
      if (entity.isSaddled() == !saddleSpike.isHidden()) {
         saddleSpike.setHidden(entity.isSaddled());
      }

      if (!entity.m_5803_()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            float pitch = entity.m_21825_() ? extraData.headPitch - 45.0F : extraData.headPitch;
            head.setRotationX(pitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
