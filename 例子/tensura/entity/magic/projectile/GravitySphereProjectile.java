package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class GravitySphereProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/gravity_sphere/gravity_sphere_5.png")};

   public GravitySphereProjectile(EntityType<? extends GravitySphereProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public GravitySphereProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.GRAVITY_SPHERE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.gravity_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean piercingEntity() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void m_8060_(@NotNull BlockHitResult result) {
      if (this.shouldGrief()) {
         SkillHelper.launchBlock(this, result.m_82450_(), 3, 1, 0.5F, 0.2F, (blockState) -> {
            return this.f_19796_.m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
         }, (blockPos) -> {
            return true;
         }, this.getSkill());
      }

      super.m_8060_(result);
   }

   protected void hitEntity(Entity entity) {
      super.hitEntity(entity);
      if (entity instanceof LivingEntity) {
         LivingEntity pLivingEntity = (LivingEntity)entity;
         StoneShotProjectile.breakTargetArmor(pLivingEntity, 20);
      }
   }

   public void applyEffectAround(double inflateRadius) {
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(inflateRadius), (entityData) -> {
         return this.m_37282_() == null || !entityData.m_7307_(this.m_37282_()) && !entityData.m_7306_(this.m_37282_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var4 = livingEntityList.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            DamageSource damagesource = TensuraDamageSources.indirectElementalAttack(this.getMagic(), this, this.m_37282_(), this.getMpCost() / 10.0D, this.getSkill(), false);
            DamageSourceHelper.dealSplitElementalDamage(target, damagesource, 0.9F, this.getDamage() / 10.0F);
            if (target.m_20206_() <= 3.0F && target.m_20205_() <= 3.0F && !target.m_6095_().m_204039_(TensuraTags.EntityTypes.FULL_GRAVITY_CONTROL) && !SkillUtils.isSkillToggled(target, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
               Vec3 vec3 = new Vec3(this.m_20185_() - target.m_20185_(), this.m_20186_() - target.m_20186_(), this.m_20189_() - target.m_20189_());
               target.m_20256_(target.m_20184_().m_82549_(vec3.m_82490_(0.05000000074505806D)));
            }
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123747_, x, y, z, 3, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.DARK_PURPLE_LIGHTNING_SPARK.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.DARK_PURPLE_LIGHTNING_SPARK.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
