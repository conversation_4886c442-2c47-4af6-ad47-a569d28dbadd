package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.unique.MercilessSkill;
import com.github.manasmods.tensura.api.magicule.MagiculeAPI;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.pack.GearEPCount;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.event.EnergyRegenerateTickEvent;
import com.github.manasmods.tensura.event.ItemDamageEvent;
import com.github.manasmods.tensura.menu.HumanoidNPCMenu;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.registries.ForgeRegistries;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class WeaponEPHandler {
   @SubscribeEvent
   public static void onGearUseEP(ItemDamageEvent e) {
      ItemStack stack = e.getStack();
      if (stack.m_41741_() <= 1) {
         CompoundTag tag = stack.m_41783_();
         if (tag != null) {
            double durabilityEP = tag.m_128459_("DurabilityEP");
            int multiplier = 10;
            int regen = (int)Math.min(durabilityEP, (double)(e.getAmount() * multiplier));
            if (regen / multiplier > 0) {
               e.setAmount(e.getAmount() - regen / multiplier);
               tag.m_128347_("DurabilityEP", durabilityEP - (double)regen);
            }
         }
      }
   }

   @SubscribeEvent
   public static void onGearRegenerateEP(EnergyRegenerateTickEvent e) {
      LivingEntity user = e.getEntity();
      EquipmentSlot[] var2 = EquipmentSlot.values();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         EquipmentSlot slot = var2[var4];
         ItemStack stack = user.m_6844_(slot);
         if (stack.m_41741_() <= 1) {
            CompoundTag tag = stack.m_41783_();
            if (tag != null) {
               double EP = tag.m_128459_("EP");
               if (!(EP <= 0.0D) && (!user.f_20911_ || !(user instanceof Player) || !slot.m_20743_().equals(Type.HAND))) {
                  int damage = stack.m_41773_();
                  double durabilityEP = tag.m_128459_("DurabilityEP");
                  int multiplier = 10;
                  int regen = (int)Math.min(durabilityEP, (double)(damage * multiplier));
                  if (damage > 0 && regen / multiplier > 0) {
                     stack.m_41721_(damage - regen / multiplier);
                     tag.m_128347_("DurabilityEP", durabilityEP - (double)regen);
                  } else if (durabilityEP < EP) {
                     tag.m_128347_("DurabilityEP", Math.min(EP, durabilityEP + (double)((int)(MagiculeAPI.getMagicule(user) * 0.01D))));
                  }

                  if (user instanceof HumanoidNPCEntity) {
                     HumanoidNPCEntity npc = (HumanoidNPCEntity)user;
                     int id = HumanoidNPCMenu.getEquipmentSlotId(slot);
                     if (npc.inventory.m_8020_(id).m_150930_(stack.m_41720_())) {
                        npc.inventory.m_6836_(HumanoidNPCMenu.getEquipmentSlotId(slot), stack);
                        npc.inventory.m_6596_();
                     }
                  }
               }
            }
         }
      }

   }

   @SubscribeEvent(
      priority = EventPriority.LOW
   )
   public static void onEntityKill(LivingDeathEvent e) {
      LivingEntity pTarget = e.getEntity();
      if (!e.isCanceled() && !pTarget.m_6084_()) {
         TensuraEffectsCapability.resetEverything(pTarget, true, false);
         Entity pAttacker = e.getSource().m_7639_();
         if (pAttacker instanceof CloneEntity) {
            CloneEntity clone = (CloneEntity)pAttacker;
            pAttacker = clone.m_21826_();
         }

         if (pAttacker instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)pAttacker;
            if (pAttacker != pTarget) {
               double EP = SkillUtils.getEPGain(pTarget, living, false);
               if (!(EP <= 0.0D)) {
                  if (!TensuraEPCapability.isSkippingEPDrop(pTarget)) {
                     entityGetEP(living, pTarget, SkillUtils.getEPGain(pTarget, living, true));
                     EquipmentSlot[] var6 = EquipmentSlot.values();
                     int var7 = var6.length;

                     for(int var8 = 0; var8 < var7; ++var8) {
                        EquipmentSlot equipmentSlot = var6[var8];
                        gearGetEP(living, equipmentSlot, EP);
                     }

                  }
               }
            }
         }
      }
   }

   protected static void entityGetEP(LivingEntity living, LivingEntity target, double totalEP) {
      double maxMP = (double)living.m_9236_().m_46469_().m_46215_(TensuraGameRules.MAX_MP_GAIN);
      double maxAP = (double)living.m_9236_().m_46469_().m_46215_(TensuraGameRules.MAX_AP_GAIN);
      if (living instanceof Player) {
         Player player = (Player)living;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            if (cap.isDemonLordSeed()) {
               double souls = SkillUtils.getEPGain(target, living, false) * (Double)TensuraConfig.INSTANCE.awakeningConfig.epToSoulRate.get() / 100.0D;
               souls = Math.min(souls, maxMP + maxAP);
               cap.setSoulPoints((int)Math.min((double)cap.getSoulPoints() + souls, 2.147483647E9D));
               if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.RIMURU_MODE) && cap.getSoulPoints() >= 20000000 && SkillUtils.learnSkill(player, (ManasSkill)((ManasSkill)UniqueSkills.MERCILESS.get()))) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{((MercilessSkill)UniqueSkills.MERCILESS.get()).getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }
            }

            Race race = cap.getRace();
            if (race != null) {
               double MP = Math.min(totalEP * (double)SkillUtils.getMagiculeGain(player, TensuraEPCapability.isMajin(living)), maxMP);
               double AP = Math.min(totalEP * (double)SkillUtils.getAuraGain(player, TensuraEPCapability.isMajin(living)), maxAP);
               cap.setBaseMagicule(cap.getBaseMagicule() + MP, player);
               cap.setBaseAura(cap.getBaseAura() + AP, player);
               TensuraPlayerCapability.sync(player);
            }
         });
         TensuraEPCapability.updateEP(player);
      } else if (totalEP * (double)TensuraGameRules.getEPGain(living.f_19853_) >= 1.0D) {
         TensuraEPCapability.setLivingEP(living, (double)Math.round(TensuraEPCapability.getEP(living) + Math.min(totalEP * (double)TensuraGameRules.getEPGain(living.f_19853_), maxMP + maxAP)));
         LivingEntity var10 = SkillHelper.getSubordinateOwner(living);
         if (var10 instanceof Player) {
            Player player = (Player)var10;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.isDemonLordSeed()) {
                  double newSoul = (double)cap.getSoulPoints() + Math.min(totalEP * (Double)TensuraConfig.INSTANCE.awakeningConfig.epToSoulRate.get() / 100.0D, maxMP + maxAP);
                  cap.setSoulPoints((int)Math.min(newSoul, 2.147483647E9D));
                  TensuraPlayerCapability.sync(player);
               }

            });
         }
      }

   }

   private static void gearGetEP(LivingEntity living, EquipmentSlot slot, double totalEP) {
      ItemStack stack = living.m_6844_(slot);
      if (stack.m_41741_() <= 1) {
         CompoundTag tag = stack.m_41783_();
         if (tag != null) {
            Iterator var6 = TensuraData.getGearEP().iterator();

            while(var6.hasNext()) {
               GearEPCount gearEPCount = (GearEPCount)var6.next();
               if (Objects.equals(ForgeRegistries.ITEMS.getKey(stack.m_41720_()), gearEPCount.getItem())) {
                  if (tag.m_128459_("EP") <= (double)gearEPCount.getMinEP()) {
                     tag.m_128347_("EP", (double)gearEPCount.getMinEP());
                  }

                  if (tag.m_128459_("MaxEP") < (double)gearEPCount.getMaxEP()) {
                     tag.m_128347_("MaxEP", (double)gearEPCount.getMaxEP());
                  }

                  double maxEP = tag.m_128459_("MaxEP");
                  double gainAmount = (double)Math.round(totalEP * gearEPCount.getGainEP());
                  ItemStack evolvingItem = ((Item)Objects.requireNonNull((Item)ForgeRegistries.ITEMS.getValue(gearEPCount.getEvolvingItem()))).m_7968_();
                  if (tag.m_128459_("EP") + gainAmount < maxEP) {
                     tag.m_128347_("EP", tag.m_128459_("EP") + gainAmount);
                     EngravingEnchantment.randomEngraving(living, stack, tag.m_128459_("EP"));
                  } else if (!evolvingItem.m_150930_(Items.f_41852_)) {
                     tag.m_128347_("EP", tag.m_128459_("EP") + gainAmount);
                     EngravingEnchantment.randomEngraving(living, stack, tag.m_128459_("EP"));
                     if (stack.m_41783_() != null) {
                        evolvingItem.m_41751_(stack.m_41783_().m_6426_());
                     }

                     initiateItemEP(evolvingItem);
                     living.m_8061_(slot, evolvingItem);
                     if (living instanceof HumanoidNPCEntity) {
                        HumanoidNPCEntity npc = (HumanoidNPCEntity)living;
                        int id = HumanoidNPCMenu.getEquipmentSlotId(slot);
                        if (npc.inventory.m_8020_(id).m_150930_(stack.m_41720_())) {
                           npc.inventory.m_6836_(HumanoidNPCMenu.getEquipmentSlotId(slot), evolvingItem);
                           npc.inventory.m_6596_();
                        }
                     }
                  } else if (tag.m_128459_("EP") != maxEP) {
                     tag.m_128347_("EP", maxEP);
                     EngravingEnchantment.randomEngraving(living, stack, tag.m_128459_("EP"));
                  }
                  break;
               }
            }

         }
      }
   }

   private static void initiateItemEP(ItemStack stack) {
      Iterator var1 = TensuraData.getGearEP().iterator();

      while(var1.hasNext()) {
         GearEPCount gearEPCount = (GearEPCount)var1.next();
         if (Objects.equals(ForgeRegistries.ITEMS.getKey(stack.m_41720_()), gearEPCount.getItem())) {
            CompoundTag tag = stack.m_41784_();
            if (tag.m_128459_("EP") <= (double)gearEPCount.getMinEP()) {
               tag.m_128347_("EP", (double)gearEPCount.getMinEP());
            }

            if (tag.m_128459_("MaxEP") < (double)gearEPCount.getMaxEP()) {
               tag.m_128347_("MaxEP", (double)gearEPCount.getMaxEP());
            }
            break;
         }
      }

   }
}
