package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestPartHurtAnimation {
   public int part;
   public int parent;

   public RequestPartHurtAnimation(int part, int parent) {
      this.part = part;
      this.parent = parent;
   }

   public RequestPartHurtAnimation(FriendlyByteBuf buf) {
      this.part = buf.readInt();
      this.parent = buf.readInt();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.part);
      buf.writeInt(this.parent);
   }

   public static void handle(RequestPartHurtAnimation message, Supplier<Context> context) {
      ((Context)context.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.updatePartHurtAnimation(message);
            };
         });
      });
      ((Context)context.get()).setPacketHandled(true);
   }
}
