package com.github.b4ndithelps.tennogamenolife.client.render;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.ChatFormatting;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate.TengekiUltimateSkill;
import com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate.FarCryUltimateSkill;

@Mod.EventBusSubscriber(modid = "tennogamenolife", value = Dist.CLIENT)
public class ChargeBarRenderer {
    private static boolean isCharging = false;
    private static float chargeProgress = 0.0f;
    private static long chargeStartTime = 0;
    private static String chargingSkill = "tengeki"; // 默认为天击技能

    public static void startCharging() {
        startCharging("tengeki");
    }

    public static void startCharging(String skillName) {
        isCharging = true;
        chargeStartTime = System.currentTimeMillis();
        chargingSkill = skillName;
    }

    public static void stopCharging() {
        isCharging = false;
        chargeProgress = 0.0f;
    }

    @SubscribeEvent
    public static void onRenderGui(RenderGuiOverlayEvent.Post event) {
        if (!isCharging) return;

        Minecraft mc = Minecraft.getInstance();
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();

        // 计算蓄力时间（秒）
        long currentTime = System.currentTimeMillis();
        long chargeTime = (currentTime - chargeStartTime) / 1000; // 转换为秒

        // 绘制文字
        Component component;

        if ("far_cry".equals(chargingSkill)) {
            // 崩哮技能的蓄力显示
            long farCryChargeTime = 10; // 10秒蓄力时间
            if (chargeTime >= farCryChargeTime) {
                component = Component.translatable("skill.far_cry.charge_complete")
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.GREEN).withBold(true));
            } else {
                component = Component.translatable("skill.far_cry.charging", chargeTime, farCryChargeTime)
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.YELLOW).withBold(true));
            }
        } else {
            // 天击技能的蓄力显示
            if (chargeTime >= TengekiUltimateSkill.MAX_CHARGE_TIME / 1000) {
                // 如果达到最大蓄力时间，显示"最强一击充能完毕"
                component = Component.translatable("tennogamenolife.charging_max")
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.YELLOW).withBold(true));
            } else if (chargeTime >= TengekiUltimateSkill.MIN_CHARGE_TIME / 1000) {
                // 如果超过最小蓄力时间，显示"蓄力增强中：X秒"
                component = Component.translatable("tennogamenolife.charging_complete", chargeTime)
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.RED).withBold(true));
            } else {
                // 如果未达到最小蓄力时间，显示"蓄力中：X秒"
                component = Component.translatable("tennogamenolife.charging", chargeTime)
                    .withStyle(Style.EMPTY.withColor(ChatFormatting.RED).withBold(true));
            }
        }
        
        mc.font.draw(event.getPoseStack(), component, (screenWidth - mc.font.width(component)) / 2, screenHeight - 50, 0xFFFFFF);
    }
}