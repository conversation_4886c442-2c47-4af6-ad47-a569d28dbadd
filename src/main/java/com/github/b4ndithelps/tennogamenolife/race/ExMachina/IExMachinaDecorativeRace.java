package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import net.minecraft.world.entity.player.Player;
import net.minecraft.nbt.CompoundTag;

/**
 * ExMachina装饰种族接口
 * 为所有ExMachina种族提供统一的装饰功能
 */
public interface IExMachinaDecorativeRace {

    /**
     * 种族tick更新 - 更新装饰状态
     * 应该在玩家tick时调用
     */
    default void onPlayerTick(Player player) {
        ExMachinaDecorativeManager.updateDecorativeState(player);
    }

    /**
     * 获取机铠动画状态
     */
    default ExMachinaDecorativeManager.ArmorAnimationState getArmorAnimationState(Player player) {
        return ExMachinaDecorativeManager.getArmorAnimationState(player);
    }

    /**
     * 获取动画计时器
     */
    default int getAnimationTimer(Player player) {
        return ExMachinaDecorativeManager.getAnimationTimer(player);
    }

    /**
     * 检查是否应该显示装饰
     */
    default boolean shouldShowDecorative(Player player) {
        return ExMachinaDecorativeManager.shouldShowDecorative(player);
    }

    /**
     * 检查是否持有机铠种之械
     */
    default boolean isHoldingExMachinasWeapon(Player player) {
        return ExMachinaDecorativeManager.isHoldingExMachinasWeapon(player);
    }

    /**
     * 获取装饰数据
     */
    default ExMachinaDecorativeData getDecorativeData(Player player) {
        return ExMachinaDecorativeManager.getDecorativeData(player);
    }

    /**
     * 保存装饰数据
     */
    default void saveDecorativeData(Player player, CompoundTag nbt) {
        ExMachinaDecorativeManager.saveDecorativeData(player, nbt);
    }

    /**
     * 读取装饰数据
     */
    default void loadDecorativeData(Player player, CompoundTag nbt) {
        ExMachinaDecorativeManager.loadDecorativeData(player, nbt);
    }

    /**
     * 获取种族装饰描述
     */
    default String getDecorativeDescription() {
        return "ExMachina种族 - 具备机铠装饰显示功能";
    }

    /**
     * 获取装饰状态信息（用于调试）
     */
    default String getDecorativeStatusInfo(Player player) {
        ExMachinaDecorativeData data = getDecorativeData(player);
        return String.format(
            "装饰状态: %s | 动画: %s | 武器: %s",
            shouldShowDecorative(player) ? "启用" : "禁用",
            data.getAnimationStateDescription(),
            isHoldingExMachinasWeapon(player) ? "持有" : "未持有"
        );
    }
}
