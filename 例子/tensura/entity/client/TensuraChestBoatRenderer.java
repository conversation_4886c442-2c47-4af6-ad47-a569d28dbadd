package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.template.TensuraChestBoatEntity;
import com.google.common.collect.ImmutableMap;
import com.mojang.datafixers.util.Pair;
import java.util.Map;
import java.util.stream.Stream;
import net.minecraft.client.model.BoatModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.renderer.entity.BoatRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.vehicle.Boat;

public class TensuraChestBoatRenderer extends BoatRenderer {
   private final Map<TensuraChestBoatEntity.Type, Pair<ResourceLocation, BoatModel>> boatResources;

   public TensuraChestBoatRenderer(Context context) {
      super(context, false);
      this.f_114477_ = 0.8F;
      this.boatResources = (Map)Stream.of(TensuraChestBoatEntity.Type.values()).collect(ImmutableMap.toImmutableMap((type) -> {
         return type;
      }, (type) -> {
         return Pair.of(new ResourceLocation("tensura", "textures/entity/chest_boat/" + type.getName() + ".png"), new BoatModel(context.m_174023_(new ModelLayerLocation(new ResourceLocation("minecraft", "chest_boat/oak"), "main")), true));
      }));
   }

   public ResourceLocation m_5478_(Boat pEntity) {
      if (pEntity instanceof TensuraChestBoatEntity) {
         TensuraChestBoatEntity TensuraChestBoat = (TensuraChestBoatEntity)pEntity;
         return (ResourceLocation)((Pair)this.boatResources.get(TensuraChestBoat.getTensuraChestBoatType())).getFirst();
      } else {
         return new ResourceLocation("minecraft", "chest_boat/oak");
      }
   }

   public Pair<ResourceLocation, BoatModel> getModelWithLocation(Boat boat) {
      if (boat instanceof TensuraChestBoatEntity) {
         TensuraChestBoatEntity TensuraChestBoat = (TensuraChestBoatEntity)boat;
         return (Pair)this.boatResources.get(TensuraChestBoat.getTensuraChestBoatType());
      } else {
         return null;
      }
   }
}
