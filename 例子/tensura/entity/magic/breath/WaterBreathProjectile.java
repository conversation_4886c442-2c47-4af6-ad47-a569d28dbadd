package com.github.manasmods.tensura.entity.magic.breath;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.tags.BlockTags;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class WaterBreathProjectile extends BreathEntity {
   public WaterBreathProjectile(EntityType<? extends WaterBreathProjectile> entityType, Level level) {
      super(entityType, level);
      this.m_20242_(true);
   }

   public WaterBreathProjectile(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.WATER_BREATH.get(), level);
      this.m_5602_(entity);
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.m_9236_().m_5776_()) {
         this.clearLava();
      }

   }

   protected void m_5790_(EntityHitResult entityHitResult) {
      Entity entity = entityHitResult.m_82443_();
      if (entity instanceof LivingEntity) {
         entity.m_6469_(DamageSourceHelper.addSkillAndCost(TensuraDamageSources.waterBreath(this, this.m_37282_()), this.getMpCost(), this.getSkill()), this.getDamage());
      }

      if (entity.m_6060_()) {
         entity.m_20095_();
      }

      if (this.m_37282_() != null) {
         SkillHelper.pushBackFromPos(new BlockPos(this.m_37282_().m_146892_()), entity, 0.2F);
      }

   }

   public void clearLava() {
      if (this.m_37282_() != null) {
         if (TensuraGameRules.canSkillGrief(this.m_9236_())) {
            float range = 0.2617994F;

            for(int i = 0; i < 3; ++i) {
               Vec3 cast = this.m_37282_().m_20154_().m_82541_().m_82496_(this.m_9236_().f_46441_.m_188501_() * range * 2.0F - range).m_82524_(this.m_9236_().f_46441_.m_188501_() * range * 2.0F - range);
               HitResult hitResult = this.m_9236_().m_45547_(new ClipContext(this.m_37282_().m_146892_(), this.m_37282_().m_146892_().m_82549_(cast.m_82490_((double)this.getLength())), Block.COLLIDER, Fluid.NONE, this));
               if (hitResult.m_6662_() == Type.BLOCK) {
                  Vec3 pos = hitResult.m_82450_().m_82546_(cast.m_82490_(0.5D));
                  BlockPos blockPos = new BlockPos(pos.f_82479_, pos.f_82480_, pos.f_82481_);
                  SkillGriefEvent.Pre preGrief;
                  if (this.m_9236_().m_8055_(blockPos).m_204336_(BlockTags.f_13076_)) {
                     preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), blockPos);
                     if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                        this.m_9236_().m_7471_(blockPos, false);
                        MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), blockPos));
                     }
                  } else if (this.m_9236_().m_8055_(blockPos).m_60713_(Blocks.f_49991_)) {
                     preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), blockPos);
                     if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                        this.m_9236_().m_46597_(blockPos, this.m_9236_().m_6425_(blockPos).m_76170_() ? Blocks.f_50080_.m_49966_() : Blocks.f_50652_.m_49966_());
                        MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), blockPos));
                     }
                  }
               }
            }

         }
      }
   }

   public void spawnParticle() {
      Entity var2 = this.m_37282_();
      if (var2 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var2;
         Vec3 rotation = owner.m_20154_().m_82541_();
         Vec3 pos = owner.m_20182_().m_82549_(rotation.m_82490_(1.6D));
         double x = pos.f_82479_;
         double y = pos.f_82480_ + (double)(owner.m_20192_() * 0.9F);
         double z = pos.f_82481_;
         double speed = owner.m_217043_().m_188500_() * 0.35D + 0.35D;

         for(int i = 0; i < 20; ++i) {
            double ox = Math.random() * 0.3D - 0.15D;
            double oy = Math.random() * 0.3D - 0.15D;
            double oz = Math.random() * 0.3D - 0.15D;
            Vec3 randomVec = (new Vec3(Math.random() - 0.5D, Math.random() - 0.5D, Math.random() - 0.5D)).m_82541_();
            Vec3 result = rotation.m_82490_(3.0D).m_82549_(randomVec).m_82541_().m_82490_(speed);
            owner.m_9236_().m_7106_(Math.random() < 0.75D ? (ParticleOptions)TensuraParticles.WATER_EFFECT.get() : (ParticleOptions)TensuraParticles.WATER_BUBBLE.get(), x + ox, y + oy, z + oz, result.f_82479_, result.f_82480_, result.f_82481_);
         }

      }
   }
}
