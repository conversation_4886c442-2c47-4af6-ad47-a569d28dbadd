package com.github.manasmods.tensura.entity.client.player;

import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.PlayerModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.world.entity.TamableAnimal;

public class PlayerLikeModel<T extends TamableAnimal> extends PlayerModel<T> {
   public PlayerLikeModel(ModelPart pRoot, boolean pSlim) {
      super(pRoot, pSlim);
   }

   public void setupAnim(T pEntity, float pLimbSwing, float pLimbSwingAmount, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
      super.m_6973_(pEntity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
      sittingPose(pEntity, this);
   }

   public static void sittingPose(TamableAnimal pEntity, HumanoidModel<?> model) {
      if (!pEntity.m_5803_()) {
         if (pEntity.m_21825_()) {
            ModelPart var10000 = model.f_102811_;
            var10000.f_104203_ += -0.62831855F;
            var10000 = model.f_102812_;
            var10000.f_104203_ += -0.62831855F;
            model.f_102813_.f_104203_ = -1.4137167F;
            model.f_102813_.f_104204_ = 0.31415927F;
            model.f_102813_.f_104205_ = 0.07853982F;
            model.f_102814_.f_104203_ = -1.4137167F;
            model.f_102814_.f_104204_ = -0.31415927F;
            model.f_102814_.f_104205_ = -0.07853982F;
            if (model instanceof PlayerModel) {
               PlayerModel<?> playerModel = (PlayerModel)model;
               playerModel.f_103376_.m_104315_(playerModel.f_102814_);
               playerModel.f_103377_.m_104315_(playerModel.f_102813_);
               playerModel.f_103374_.m_104315_(playerModel.f_102812_);
               playerModel.f_103375_.m_104315_(playerModel.f_102811_);
            }
         }

      }
   }
}
