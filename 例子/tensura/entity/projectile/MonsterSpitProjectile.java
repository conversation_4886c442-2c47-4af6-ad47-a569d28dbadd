package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.event.ForgeEventFactory;

public class MonsterSpitProjectile extends Projectile {
   public MonsterSpitProjectile(EntityType<? extends MonsterSpitProjectile> type, Level level) {
      super(type, level);
   }

   public MonsterSpitProjectile(Level worldIn, LivingEntity monster) {
      this((EntityType)TensuraEntityTypes.MONSTER_SPIT.get(), worldIn);
      this.m_5602_(monster);
   }

   public void m_8119_() {
      super.m_8119_();
      Entity var2 = this.m_37282_();
      if (var2 instanceof SpittingRangedMonster) {
         SpittingRangedMonster entity = (SpittingRangedMonster)var2;
         entity.spitParticle(this);
      }

      HitResult rayTraceResult = ProjectileUtil.m_37294_(this, (x$0) -> {
         return this.m_5603_(x$0);
      });
      if (rayTraceResult.m_6662_() != Type.MISS && !ForgeEventFactory.onProjectileImpact(this, rayTraceResult)) {
         this.m_6532_(rayTraceResult);
      }

      if (!this.m_20077_() && !this.m_20072_()) {
         this.m_20256_(this.m_20184_().m_82490_(0.9900000095367432D));
         this.m_146884_(this.m_20182_().m_82549_(this.m_20184_()));
         ProjectileUtil.m_37284_(this, 1.0F);
         if (!this.m_20068_()) {
            this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.019999999552965164D, 0.0D));
         }
      } else {
         this.m_142687_(RemovalReason.DISCARDED);
      }

      this.m_20101_();
   }

   protected void m_8097_() {
   }

   public RandomSource getRandom() {
      return this.f_19796_;
   }

   protected void m_6532_(HitResult result) {
      if (!this.f_19853_.f_46443_) {
         Entity var3 = this.m_37282_();
         if (var3 instanceof SpittingRangedMonster) {
            SpittingRangedMonster mob = (SpittingRangedMonster)var3;
            double x = this.f_19790_;
            double y = this.f_19791_;
            double z = this.f_19792_;
            mob.impactEffect(this, x, y, z);
         }
      }

      super.m_6532_(result);
   }

   protected void m_5790_(EntityHitResult pResult) {
      Entity entity = pResult.m_82443_();
      if (entity != this.m_37282_()) {
         Entity var5 = this.m_37282_();
         if (var5 instanceof SpittingRangedMonster) {
            SpittingRangedMonster monster = (SpittingRangedMonster)var5;
            if (entity instanceof LivingEntity) {
               LivingEntity livingEntity = (LivingEntity)entity;
               monster.spitHit(livingEntity);
            }
         }

         this.m_146870_();
      }
   }

   protected void m_8060_(BlockHitResult pResult) {
      super.m_8060_(pResult);
      this.m_146870_();
   }
}
