package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class DimensionCutProjectile extends SpaceCutProjectile {
   public DimensionCutProjectile(EntityType<? extends DimensionCutProjectile> type, Level level) {
      super(type, level);
      this.setSize(2.0F);
   }

   public DimensionCutProjectile(Level worldIn, LivingEntity shooter) {
      this((EntityType)TensuraEntityTypes.DIMENSION_CUT.get(), worldIn);
      this.m_5602_(shooter);
      this.setSize(2.0F);
   }

   public boolean piercingBlock() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_6.png"), new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/dimension_cut_7.png")};
   }
}
