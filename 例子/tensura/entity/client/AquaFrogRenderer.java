package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.AquaFrogEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class AquaFrogRenderer extends GeoEntityRenderer<AquaFrogEntity> {
   public AquaFrogRenderer(Context renderManager) {
      super(renderManager, new AquaFrogModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(AquaFrogEntity instance) {
      return instance.m_7770_() != null && instance.m_7770_().getString().equals("Kermit") ? new ResourceLocation("tensura", "textures/entity/aqua_frog/kermit.png") : new ResourceLocation("tensura", "textures/entity/aqua_frog/aqua_frog.png");
   }

   public RenderType getRenderType(AquaFrogEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
