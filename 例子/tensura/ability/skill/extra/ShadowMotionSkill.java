package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Registry;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;

public class ShadowMotionSkill extends Skill {
   public ShadowMotionSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 200.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (!instance.isMastered(entity)) {
         return instance.getMode() == 1 ? 0 : 1;
      } else if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_motion.default");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_motion.step");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.shadow_motion.storage");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 10.0D;
         break;
      case 2:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
         return false;
      } else {
         return entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get()) ? true : super.canInteractSkill(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      if (instance.getMode() != 1) {
         return false;
      } else if (MobEffectHelper.noTeleportation(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         return false;
      } else {
         if (heldTicks <= 2) {
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 2.0D);
         }

         if (level.m_46803_(entity.m_20183_()) <= 10 || this.isMastered(instance, entity) || entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get())) {
            if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
               return false;
            }

            if (heldTicks % 100 == 0 && heldTicks > 0) {
               this.addMasteryPoint(instance, entity);
            }

            if (shouldConsumeAir(entity)) {
               entity.m_20301_(entity.m_20146_() - 1);
               if (entity.m_20146_() <= -20) {
                  entity.m_20301_(0);
                  entity.m_6469_(TensuraDamageSources.SUFFOCATE, 1.0F);
               }
            }

            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SHADOW_STEP.get(), 5, 0, false, false, false));
         }

         return true;
      }
   }

   private boolean canShadowStorage(LivingEntity target, LivingEntity entity) {
      if (target instanceof Player) {
         return false;
      } else if (!SkillHelper.isSubordinate(entity, target)) {
         return false;
      } else {
         if (target instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)target;
            if (spirit.getSummoningTick() > 0) {
               return false;
            }
         }

         if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.MONSTER)) {
            return true;
         } else {
            return !shouldConsumeAir(target);
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 1) {
         if (MobEffectHelper.noTeleportation(entity)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

         } else {
            Level level = entity.m_9236_();
            LivingEntity target;
            Player player;
            if (instance.getMode() != 3) {
               if (!SkillHelper.outOfMagicule(entity, instance)) {
                  target = SkillHelper.getTargetingEntity(entity, 20.0D, false);
                  if (target != null && target.m_6084_()) {
                     float radius = -1.5F;
                     float angle = 0.017453292F * target.f_20885_;
                     double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
                     double extraZ = (double)(radius * Mth.m_14089_(angle));
                     BlockPos behindPos = new BlockPos(target.m_20185_() + extraX, target.m_20186_(), target.m_20189_() + extraZ);
                     if (level.m_8055_(behindPos).m_60804_(level, behindPos) && level.m_8055_(behindPos.m_7494_()).m_60804_(level, behindPos.m_7494_())) {
                        behindPos = target.m_20183_();
                     }

                     SkillHelper.removeSpecificTargetInRadius(entity, 40.0D, (mob) -> {
                        return true;
                     });
                     TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D, 10);
                     entity.m_19877_();
                     if (entity instanceof ServerPlayer) {
                        ServerPlayer player = (ServerPlayer)entity;
                        double d0 = target.m_20185_() - (double)behindPos.m_123341_();
                        double d2 = target.m_20189_() - (double)behindPos.m_123343_();
                        float yRot = Mth.m_14177_((float)(Mth.m_14136_(d2, d0) * 57.2957763671875D) - 90.0F);
                        player.m_8999_((ServerLevel)level, (double)behindPos.m_123341_(), (double)behindPos.m_123342_(), (double)behindPos.m_123343_(), yRot, entity.m_146909_());
                     } else {
                        entity.m_20324_((double)behindPos.m_123341_(), (double)behindPos.m_123342_(), (double)behindPos.m_123343_());
                     }

                     entity.f_19812_ = true;
                     TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123765_, 1.0D);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  } else {
                     if (entity instanceof Player) {
                        player = (Player)entity;
                        player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     }

                  }
               }
            } else {
               target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
               if (target == null) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_targeted").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

               } else if (!this.canShadowStorage(target, entity)) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }

               } else {
                  label61: {
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     ItemStack shadow = new ItemStack((ItemLike)TensuraMaterialItems.SHADOW_STORAGE.get());
                     shadow.m_41714_(Component.m_237110_("tooltip.tensura.shadow_storage.name", new Object[]{target.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)));
                     CompoundTag compound = shadow.m_41784_();
                     compound.m_128365_("ShadowData", target.serializeNBT());
                     compound.m_128347_("ShadowEP", TensuraEPCapability.getEP(target));
                     compound.m_128347_("ShadowHP", (double)target.m_21223_());
                     compound.m_128347_("ShadowSHP", TensuraEPCapability.getSpiritualHealth(target));
                     compound.m_128359_("EntityType", String.valueOf(Registry.f_122826_.m_7981_(target.m_6095_())));
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        if (player.m_36356_(shadow)) {
                           break label61;
                        }
                     }

                     SkillHelper.dropItem(entity, entity.m_217043_(), shadow, 10, 0.5F);
                  }

                  target.m_146870_();
               }
            }
         }
      }
   }

   public static boolean shouldConsumeAir(LivingEntity entity) {
      if (TensuraEPCapability.isMajin(entity)) {
         return false;
      } else if (RaceHelper.isSpiritualLifeForm(entity)) {
         return false;
      } else if (entity.m_6844_(EquipmentSlot.HEAD).getEnchantmentLevel((Enchantment)TensuraEnchantments.BREATHING_SUPPORT.get()) >= 1) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               return false;
            }
         }

         return !entity.m_5833_();
      }
   }
}
