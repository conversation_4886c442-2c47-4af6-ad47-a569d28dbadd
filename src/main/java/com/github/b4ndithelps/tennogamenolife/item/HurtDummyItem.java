package com.github.b4ndithelps.tennogamenolife.item;

import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;

import java.util.List;

public class HurtDummyItem extends Item {
    
    public HurtDummyItem() {
        super(new Item.Properties()
                .stacksTo(16)
                .rarity(Rarity.UNCOMMON)
                .tab(net.minecraft.world.item.CreativeModeTab.TAB_MISC));
    }
    
    @Override
    public InteractionResult useOn(UseOnContext context) {
        Level level = context.getLevel();
        if (!(level instanceof ServerLevel)) {
            return InteractionResult.SUCCESS;
        }
        
        BlockPos clickedPos = context.getClickedPos();
        Direction clickedFace = context.getClickedFace();
        BlockPos spawnPos = clickedPos.relative(clickedFace);
        Player player = context.getPlayer();
        
        if (clickedFace == Direction.UP) {
            if (level.isEmptyBlock(clickedPos)) {
                return InteractionResult.FAIL;
            }
        } else if (clickedFace != Direction.DOWN) {
            if (level.isEmptyBlock(spawnPos.below())) {
                return InteractionResult.FAIL;
            }
        }
        
        AABB checkBox = new AABB(spawnPos).inflate(0.1);
        List<Entity> entities = level.getEntities((Entity)null, checkBox);
        if (!entities.isEmpty()) {
            for (Entity entity : entities) {
                if (entity instanceof HurtDummyEntity) {
                    return InteractionResult.FAIL;
                }
            }
        }
        
        HurtDummyEntity dummy = TenreincarnationEntities.HURT_DUMMY.get().create(level);
        if (dummy != null) {
            float yRot = 0f;
            
            if (player != null) {
                double dX = player.getX() - (spawnPos.getX() + 0.5);
                double dZ = player.getZ() - (spawnPos.getZ() + 0.5);
                yRot = (float) (Math.atan2(dZ, dX) * 180.0 / Math.PI) - 90f;
                while (yRot < 0f) yRot += 360f;
                while (yRot > 360f) yRot -= 360f;
            }
            
            dummy.absMoveTo(
                    spawnPos.getX() + 0.5, 
                    spawnPos.getY(), 
                    spawnPos.getZ() + 0.5, 
                    yRot, 0);
            
            dummy.setYRot(yRot);
            dummy.setYBodyRot(yRot);
            dummy.setYHeadRot(yRot);
            dummy.yRotO = yRot;
            
            level.addFreshEntity(dummy);
            
            level.playSound(null, spawnPos, SoundEvents.ARMOR_STAND_HIT, SoundSource.BLOCKS, 0.75F, 0.8F);
            
            if (player != null && !player.getAbilities().instabuild) {
                context.getItemInHand().shrink(1);
            }
            
            return InteractionResult.CONSUME;
        }
        
        return InteractionResult.FAIL;
    }
} 