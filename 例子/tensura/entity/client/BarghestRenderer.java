package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BarghestEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import java.util.Arrays;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class BarghestRenderer extends GeoEntityRenderer<BarghestEntity> {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/barghest/barghest_0.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_1.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_2.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_3.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_4.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_5.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_6.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_7.png")};
   protected static final ResourceLocation[] TEXTURES_BABY = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_0.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_1.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_2.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_3.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_4.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_5.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_6.png"), new ResourceLocation("tensura", "textures/entity/barghest/barghest_baby_7.png")};

   public BarghestRenderer(Context renderManager) {
      super(renderManager, new BarghestModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(BarghestEntity instance) {
      return instance.m_6162_() ? (ResourceLocation)Arrays.stream(TEXTURES_BABY).toList().get(instance.f_19797_ / 2 % TEXTURES_BABY.length) : (ResourceLocation)Arrays.stream(TEXTURES).toList().get(instance.f_19797_ / 2 % TEXTURES.length);
   }

   public RenderType getRenderType(BarghestEntity dog, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (dog.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(dog));
   }
}
