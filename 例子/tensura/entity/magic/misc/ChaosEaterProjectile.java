package com.github.manasmods.tensura.entity.magic.misc;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class ChaosEaterProjectile extends TensuraProjectile {
   private static final EntityDataAccessor<Boolean> REACHED;
   private static final EntityDataAccessor<Float> START_OFFSET_X;
   private static final EntityDataAccessor<Float> START_OFFSET_Y;
   private static final EntityDataAccessor<Float> START_OFFSET_Z;
   private static final EntityDataAccessor<Float> START_DISTANCE;
   private static final EntityDataAccessor<Integer> MAX_COUNT;
   private static final EntityDataAccessor<Integer> NUMBER;
   private int capturedTarget = 0;
   @Nullable
   private LivingEntity target;

   public ChaosEaterProjectile(EntityType<? extends ChaosEaterProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(1.25F);
   }

   public ChaosEaterProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.CHAOS_EATER.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(1.25F);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(REACHED, false);
      this.f_19804_.m_135372_(START_OFFSET_X, 0.0F);
      this.f_19804_.m_135372_(START_OFFSET_Y, 0.0F);
      this.f_19804_.m_135372_(START_OFFSET_Z, 0.0F);
      this.f_19804_.m_135372_(START_DISTANCE, 2.0F);
      this.f_19804_.m_135372_(MAX_COUNT, 4);
      this.f_19804_.m_135372_(NUMBER, 0);
   }

   protected void m_7380_(@NotNull CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("Reached", this.isReached());
      compound.m_128350_("xStart", (float)this.getStartOffset().m_7096_());
      compound.m_128350_("yStart", (float)this.getStartOffset().m_7098_());
      compound.m_128350_("zStart", (float)this.getStartOffset().m_7094_());
   }

   protected void m_7378_(@NotNull CompoundTag compound) {
      super.m_7378_(compound);
      this.setReached(compound.m_128471_("Reached"));
      this.setStartOffset(compound.m_128457_("xStart"), compound.m_128457_("yStart"), compound.m_128457_("zStart"));
   }

   public boolean isReached() {
      return (Boolean)this.f_19804_.m_135370_(REACHED);
   }

   public void setReached(boolean reached) {
      this.f_19804_.m_135381_(REACHED, reached);
   }

   public void setStartOffset(float x, float y, float z) {
      this.m_20088_().m_135381_(START_OFFSET_X, x);
      this.m_20088_().m_135381_(START_OFFSET_Y, y);
      this.m_20088_().m_135381_(START_OFFSET_Z, z);
   }

   public Vec3 getStartOffset() {
      return new Vec3((double)(Float)this.m_20088_().m_135370_(START_OFFSET_X), (double)(Float)this.m_20088_().m_135370_(START_OFFSET_Y), (double)(Float)this.m_20088_().m_135370_(START_OFFSET_Z));
   }

   public float getStartDistance() {
      return (Float)this.f_19804_.m_135370_(START_DISTANCE);
   }

   public void setStartDistance(float distance) {
      this.f_19804_.m_135381_(START_DISTANCE, distance);
   }

   public int getMaxCount() {
      return (Integer)this.f_19804_.m_135370_(MAX_COUNT);
   }

   public void setMaxCount(int count) {
      this.f_19804_.m_135381_(MAX_COUNT, count);
   }

   public int getCount() {
      return (Integer)this.f_19804_.m_135370_(NUMBER);
   }

   public void setCount(int count) {
      this.f_19804_.m_135381_(NUMBER, count);
   }

   public void setUpStartPos(int maxCount, int count, float startDistance) {
      this.setMaxCount(maxCount);
      this.setCount(count);
      this.setStartDistance(startDistance);
   }

   @Nullable
   public LivingEntity getTarget() {
      if (this.target == null) {
         Entity var2 = this.m_37282_();
         if (var2 instanceof Mob) {
            Mob mob = (Mob)var2;
            return mob.m_5448_();
         }
      }

      return this.target;
   }

   public void setTarget(@Nullable LivingEntity pTarget) {
      this.target = pTarget;
   }

   public boolean piercingBlock() {
      return true;
   }

   public boolean piercingEntity() {
      return true;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public void m_7332_(@NotNull Entity entity) {
      if (this.m_20363_(entity)) {
         entity.m_183634_();
         entity.m_6034_(this.m_20185_(), this.m_20186_() - (double)(entity.m_20206_() / 2.0F), this.m_20189_());
      }
   }

   public boolean shouldRiderSit() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/blank_texture.png")};
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.f_19797_ % 5 == 0) {
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
      }

      Entity owner = this.m_37282_();
      if (owner != null) {
         double distance = Math.sqrt(this.m_20238_(owner.m_146892_()));
         if (!this.m_20197_().isEmpty() && !this.f_19853_.m_5776_()) {
            if (this.capturedTarget > 0) {
               --this.capturedTarget;
               if (this.capturedTarget <= 0) {
                  this.m_20153_();
               }
            } else if (distance <= 2.0D) {
               this.m_20153_();
            }
         }

         LivingEntity target = this.getTarget();
         if (target != null && !target.m_6084_()) {
            this.setTarget((LivingEntity)null);
         }

         this.updateStartPos(owner);
         if (target != null && !this.isReached()) {
            double f = (double)this.m_20270_(target);
            double d0 = (target.m_20185_() - this.m_20185_()) / f;
            double d1 = (target.m_20186_() + (double)(target.m_20206_() / 2.0F) - this.m_20186_()) / f;
            double d2 = (target.m_20189_() - this.m_20189_()) / f;
            this.m_20256_(new Vec3(d0, d1, d2));
            if (distance >= 50.0D) {
               this.setReached(true);
            }
         } else {
            double f;
            double d1;
            double d2;
            if (!this.isReached() && owner instanceof Player) {
               Player player = (Player)owner;
               Vec3 result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, player, Fluid.ANY, 20.0D).m_82450_();
               f = Math.sqrt(this.m_20238_(result));
               d1 = (result.f_82479_ - this.m_20185_()) / f;
               d2 = (result.f_82480_ - this.m_20186_()) / f;
               double d2 = (result.f_82481_ - this.m_20189_()) / f;
               this.m_20256_(new Vec3(d1, d2, d2));
               if (distance >= 20.0D || f <= 0.5D) {
                  this.setReached(true);
               }
            } else {
               float multiplier = this.m_20197_().isEmpty() ? 1.0F : 0.4F;
               f = (owner.m_20185_() - this.m_20185_()) / distance * (double)multiplier;
               d1 = (owner.m_20188_() - this.m_20186_()) / distance * (double)multiplier;
               d2 = (owner.m_20189_() - this.m_20189_()) / distance * (double)multiplier;
               this.m_20256_(new Vec3(f, d1, d2));
               if (distance <= 2.5D && !this.f_19853_.m_5776_()) {
                  this.m_146870_();
               }

               Iterator var13 = owner.m_9236_().m_45976_(ItemEntity.class, this.m_20191_().m_82400_(2.0D)).iterator();

               while(var13.hasNext()) {
                  ItemEntity item = (ItemEntity)var13.next();
                  item.m_146884_(this.m_20182_());
               }
            }
         }

      }
   }

   protected void updateStartPos(Entity owner) {
      int rot = 360 / this.getMaxCount();

      for(int i = 0; i < this.getMaxCount(); ++i) {
         Vec3 offset = (new Vec3(0.0D, (double)this.getStartDistance(), 0.0D)).m_82535_(((float)(rot * i) - (float)rot / 2.0F) * 0.017453292F).m_82496_(-owner.m_146909_() * 0.017453292F).m_82524_(-owner.m_146908_() * 0.017453292F);
         if (this.getCount() == i) {
            this.setStartOffset((float)offset.f_82479_, (float)offset.f_82480_, (float)offset.f_82481_);
         }
      }

      if (this.f_19797_ % 5 == 0) {
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, owner.m_146892_().m_82549_(this.getStartOffset()), (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get(), 0.5D);
      }

   }

   protected void hitEntity(Entity entity) {
      entity.m_20254_(Math.max(this.getBurnTicks(), 0));
      if (this.knockForce > 0.0F) {
         this.knockBack(entity, (double)this.getKnockForce(), (double)this.getKnockForce() / 3.0D);
      }

      if (this.getMobEffect() != null && entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (!this.isAlly(living)) {
            SkillHelper.checkThenAddEffectSource(living, this.m_37282_(), this.getMobEffect());
         }
      }

      this.dealDamage(entity);
   }

   protected void dealDamage(Entity target) {
      if (!target.f_19853_.m_5776_()) {
         if (this.isReached() && target == this.m_37282_()) {
            this.m_146870_();
         }

         if (this.getTarget() == null || this.getTarget() == target) {
            this.setReached(true);
         }

         if (!(this.damage <= 0.0F)) {
            if (target instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)target;
               if (this.isAlly(living)) {
                  return;
               }
            }

            if (target.m_20202_() != this) {
               if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.FULL_GRAVITY_CONTROL)) {
                  DamageSource damagesource = this.m_37282_() != null ? TensuraDamageSources.corrosion(this.m_37282_()) : TensuraDamageSources.CORROSION;
                  if (target.m_6469_(DamageSourceHelper.addSkillAndCost(damagesource, this.getMpCost(), this.getSkill()), this.getDamage()) && this.capturedTarget <= 0 && !(target.m_20202_() instanceof ChaosEaterProjectile)) {
                     target.m_7998_(this, true);
                     this.capturedTarget = 40;
                  }

               }
            }
         }
      }
   }

   public void applyEffectAround(double inflateRadius) {
      if (this.getMobEffect() != null) {
         List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(inflateRadius), (entityx) -> {
            return !this.isAlly(entityx);
         });
         if (!livingEntityList.isEmpty()) {
            LivingEntity entity;
            for(Iterator var4 = livingEntityList.iterator(); var4.hasNext(); SkillHelper.checkThenAddEffectSource(entity, this.m_37282_(), this.getMobEffect())) {
               entity = (LivingEntity)var4.next();
               Entity var7 = this.m_37282_();
               if (var7 instanceof LivingEntity) {
                  LivingEntity player = (LivingEntity)var7;
                  entity.m_6703_(player);
               }
            }

         }
      }
   }

   public boolean isAlly(LivingEntity entity) {
      if (entity == this.m_37282_()) {
         return true;
      } else {
         return this.m_37282_() != null && this.m_37282_().m_7307_(entity);
      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123765_, x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
   }

   static {
      REACHED = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135035_);
      START_OFFSET_X = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135029_);
      START_OFFSET_Y = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135029_);
      START_OFFSET_Z = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135029_);
      START_DISTANCE = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135029_);
      MAX_COUNT = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135028_);
      NUMBER = SynchedEntityData.m_135353_(ChaosEaterProjectile.class, EntityDataSerializers.f_135028_);
   }
}
