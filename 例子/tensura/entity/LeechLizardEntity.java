package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.LeechLizardVariant;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.EnumSet;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Holder;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BiomeTags;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.Tags.Biomes;
import net.minecraftforge.common.Tags.Items;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class LeechLizardEntity extends TensuraTamableEntity implements IAnimatable, HasCustomInventoryScreen {
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> TACKLING;
   private static final EntityDataAccessor<Boolean> CHESTED;
   public float tackleProgress;
   public int miscAnimationTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public LeechLizardEntity(EntityType<? extends LeechLizardEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19793_ = 1.0F;
      this.f_21364_ = 10;
      this.initInventory();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 8.0D).m_22268_(Attributes.f_22276_, 35.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.10000000149011612D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(1, new BreedGoal(this, 1.2D));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(3, new LeechLizardEntity.LeechLizardAttackGoal(this));
      this.f_21345_.m_25352_(4, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(10, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY) && entity.m_6095_() != EntityType.f_20555_ && entity.m_6095_() != EntityType.f_20517_;
      }));
      this.f_21346_.m_25352_(7, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(TACKLING, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128379_("Chested", this.isChested());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.setChested(compound.m_128471_("Chested"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public LeechLizardVariant getVariant() {
      return LeechLizardVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(LeechLizardVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public boolean isTackling() {
      return (Boolean)this.f_19804_.m_135370_(TACKLING);
   }

   public void setTackling(boolean bar) {
      this.f_19804_.m_135381_(TACKLING, bar);
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   protected float m_6108_() {
      return 0.95F;
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity)) {
         if (pEntity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)pEntity;
            if (this.m_217043_().m_188503_(3) == 0) {
               int poison = living.m_21023_(MobEffects.f_19614_) ? 1 : 0;
               living.m_147207_(new MobEffectInstance(MobEffects.f_19614_, 200, poison, false, false), this);
            } else {
               living.m_147207_(new MobEffectInstance(MobEffects.f_19604_, 200, 0, false, false), this);
            }

            this.m_5634_(3.0F);
         }

         return true;
      } else {
         return false;
      }
   }

   protected float m_6118_() {
      return 0.52F * this.m_20098_();
   }

   protected void m_6135_() {
      double d0 = (double)this.m_6118_() + this.m_182332_();
      Vec3 vec3 = this.m_20184_();
      this.m_20334_(vec3.f_82479_, d0, vec3.f_82481_);
      float f = this.m_146908_() * 0.017453292F;
      this.m_20256_(this.m_20184_().m_82520_((double)(-Mth.m_14031_(f) * 0.2F), 0.0D, (double)(Mth.m_14089_(f) * 0.2F)));
      this.f_19812_ = true;
      ForgeHooks.onLivingJump(this);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 7.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 7.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      }
   }

   public double m_20204_() {
      float threshold = this.m_6162_() ? 0.15F : 0.3F;
      return super.m_20204_() + (double)threshold;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.isTackling() && this.tackleProgress < 5.0F) {
         ++this.tackleProgress;
      }

      if (!this.isTackling() && this.tackleProgress > 0.0F) {
         --this.tackleProgress;
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 10) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11799_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11804_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11802_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      LeechLizardEntity lizard = (LeechLizardEntity)((EntityType)TensuraEntityTypes.LEECH_LIZARD.get()).m_20615_(pLevel);
      if (lizard == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            lizard.m_21816_(uuid);
            lizard.m_7105_(true);
         }

         int i = this.f_19796_.m_188503_(9);
         if (i < 4) {
            lizard.setVariant(this.getVariant());
         } else if (i < 8 && pOtherParent instanceof LeechLizardEntity) {
            LeechLizardEntity lizardEntity = (LeechLizardEntity)pOtherParent;
            lizard.setVariant(lizardEntity.getVariant());
         } else {
            this.biomesBasedVariant(pLevel);
         }

         return lizard;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }
            }

            if (!player.m_36341_() && this.isChested()) {
               if (this.isChested()) {
                  this.m_213583_(player);
               }
            } else {
               this.commanding(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(3.0F);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(3);
            return InteractionResult.SUCCESS;
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_27595_(player);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-i), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(3);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(27) {
         public boolean m_6542_(Player player) {
            return LeechLizardEntity.this.m_6084_() && !LeechLizardEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39959_, menu, inventory, LeechLizardEntity.this.inventory, 3);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.leechLizardSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      this.biomesBasedVariant(pLevel);
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public void biomesBasedVariant(ServerLevelAccessor pLevel) {
      Holder<Biome> biomes = pLevel.m_204166_(this.m_20097_());
      if (biomes.m_203656_(BiomeTags.f_207609_)) {
         this.setVariant(LeechLizardVariant.BLUE);
      } else if (biomes.m_203656_(Biomes.IS_COLD)) {
         this.setVariant(LeechLizardVariant.WHITE);
      } else if (biomes.m_203656_(BiomeTags.f_215816_)) {
         this.setVariant(LeechLizardVariant.YELLOW);
      } else if (biomes.m_203656_(Biomes.IS_HOT)) {
         this.setVariant(LeechLizardVariant.TAN);
      } else {
         this.setVariant(LeechLizardVariant.GREEN);
      }

   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_20069_() && this.f_19853_.m_8055_(this.m_20183_().m_6625_(1)).m_60713_(Blocks.f_49990_)) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.swim", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         if (!this.m_21660_() && (this.m_6688_() == null || !this.m_6688_().m_20142_())) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.run", EDefaultLoopTypes.LOOP));
         }
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.idle_tail_swing", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         if (this.getMiscAnimation() != 1 && !this.f_20911_) {
            if (this.getMiscAnimation() == 2) {
               event.getController().markNeedsReload();
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.leap", EDefaultLoopTypes.PLAY_ONCE));
            } else if (this.getMiscAnimation() == 3) {
               event.getController().markNeedsReload();
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.eat", EDefaultLoopTypes.PLAY_ONCE));
            }
         } else {
            event.getController().markNeedsReload();
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.leech_lizard.bite", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(LeechLizardEntity.class, EntityDataSerializers.f_135028_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(LeechLizardEntity.class, EntityDataSerializers.f_135028_);
      TACKLING = SynchedEntityData.m_135353_(LeechLizardEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(LeechLizardEntity.class, EntityDataSerializers.f_135035_);
   }

   static class LeechLizardAttackGoal extends Goal {
      private final LeechLizardEntity lizard;
      private boolean willJump = false;
      private boolean hasJumped = false;
      private boolean clockwise = false;
      private int pursuitTime = 0;
      private int maxPursuitTime = 0;
      private BlockPos pursuitPos = null;
      private int startingOrbit = 0;

      public LeechLizardAttackGoal(LeechLizardEntity leechLizard) {
         this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
         this.lizard = leechLizard;
      }

      public boolean m_8036_() {
         return this.lizard.m_5448_() != null && this.lizard.m_5448_().m_6084_();
      }

      public void m_8056_() {
         this.willJump = this.lizard.m_217043_().m_188503_(2) == 0;
         this.hasJumped = false;
         this.clockwise = this.lizard.m_217043_().m_188499_();
         this.pursuitPos = null;
         this.pursuitTime = 0;
         this.maxPursuitTime = this.lizard.m_217043_().m_188503_(40);
         this.startingOrbit = this.lizard.m_217043_().m_188503_(180);
      }

      public void m_8037_() {
         LivingEntity target = this.lizard.m_5448_();
         boolean flag = false;
         if ((this.hasJumped || this.lizard.isTackling()) && this.lizard.m_20096_()) {
            this.hasJumped = false;
            this.willJump = false;
            this.lizard.setTackling(false);
         }

         if (target != null && target.m_6084_()) {
            if (this.pursuitTime < this.maxPursuitTime) {
               ++this.pursuitTime;
               this.pursuitPos = this.getBlockNearTarget(target);
               float extraSpeed = 0.2F * Math.max(5.0F - this.lizard.m_20270_(target), 0.0F);
               if (this.pursuitPos != null) {
                  this.lizard.m_21573_().m_26519_((double)this.pursuitPos.m_123341_(), (double)this.pursuitPos.m_123342_(), (double)this.pursuitPos.m_123343_(), (double)(1.0F + extraSpeed));
               } else {
                  this.lizard.m_21573_().m_5624_(target, 1.0D);
               }
            } else if (this.willJump && this.pursuitTime == this.maxPursuitTime) {
               this.lizard.m_21391_(target, 180.0F, 10.0F);
               if (this.lizard.m_20270_(target) > 10.0F) {
                  this.lizard.m_21573_().m_5624_(target, 1.0D);
               } else if (this.lizard.m_20096_() && this.lizard.m_142582_(target)) {
                  this.lizard.setTackling(true);
                  this.lizard.setMiscAnimation(2);
                  this.hasJumped = true;
                  Vec3 vector3d = this.lizard.m_20184_();
                  Vec3 vector3d1 = new Vec3(target.m_20185_() - this.lizard.m_20185_(), 0.0D, target.m_20189_() - this.lizard.m_20189_());
                  if (vector3d1.m_82556_() > 1.0E-7D) {
                     vector3d1 = vector3d1.m_82541_().m_82490_(0.9D).m_82549_(vector3d.m_82490_(0.8D));
                  }

                  this.lizard.m_20334_(vector3d1.f_82479_, 0.6000000238418579D, vector3d1.f_82481_);
               } else {
                  flag = true;
               }
            } else if (!this.lizard.isTackling()) {
               this.lizard.m_21573_().m_5624_(target, 1.0D);
            }

            if (this.lizard.isTackling() && this.lizard.m_20270_(target) <= this.lizard.m_20205_() + target.m_20205_() + 1.1F && this.lizard.m_142582_(target)) {
               target.m_6469_(DamageSource.m_19370_(this.lizard), (float)this.lizard.m_21133_(Attributes.f_22281_));
               this.lizard.setMiscAnimation(1);
               this.m_8056_();
            }

            if (!flag && this.lizard.m_20270_(target) <= this.lizard.m_20205_() + target.m_20205_() + 1.1F && this.lizard.m_142582_(target) && this.pursuitTime == this.maxPursuitTime) {
               if (!this.lizard.isTackling()) {
                  this.lizard.m_7327_(target);
               }

               this.lizard.setMiscAnimation(2);
               this.m_8056_();
               this.lizard.m_6135_();
            }
         }

         if (target != null && !this.lizard.m_20096_()) {
            this.lizard.m_21391_(target, 180.0F, 10.0F);
            this.lizard.f_20883_ = this.lizard.m_146908_();
         }

      }

      @Nullable
      public BlockPos getBlockNearTarget(LivingEntity target) {
         float radius = (float)(this.lizard.m_217043_().m_188503_(5) + 3) + target.m_20205_();
         int orbit = (int)((float)this.startingOrbit + (float)this.pursuitTime / (float)this.maxPursuitTime * 360.0F);
         float angle = 0.017453292F * (float)(this.clockwise ? -orbit : orbit);
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));

         BlockPos circlePos;
         for(circlePos = new BlockPos(target.m_20185_() + extraX, target.m_20188_(), target.m_20189_() + extraZ); !this.lizard.f_19853_.m_8055_(circlePos).m_60795_() && circlePos.m_123342_() < this.lizard.f_19853_.m_151558_(); circlePos = circlePos.m_7494_()) {
         }

         while(!this.lizard.f_19853_.m_8055_(circlePos.m_7495_()).m_60634_(this.lizard.f_19853_, circlePos.m_7495_(), this.lizard) && circlePos.m_123342_() > 1) {
            circlePos = circlePos.m_7495_();
         }

         return this.lizard.m_21692_(circlePos) > -1.0F ? circlePos : null;
      }

      public void m_8041_() {
         this.lizard.setTackling(false);
      }
   }
}
