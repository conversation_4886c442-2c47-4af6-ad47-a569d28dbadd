package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantSalmonEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class GiantSalmonModel extends AnimatedGeoModel<GiantSalmonEntity> {
   public ResourceLocation getModelResource(GiantSalmonEntity object) {
      return new ResourceLocation("tensura", "geo/giant_salmon.geo.json");
   }

   public ResourceLocation getTextureResource(GiantSalmonEntity object) {
      return new ResourceLocation("tensura", "textures/entity/giant_salmon/giant_salmon.png");
   }

   public ResourceLocation getAnimationResource(GiantSalmonEntity animatable) {
      return new ResourceLocation("tensura", "animations/giant_salmon.animation.json");
   }
}
