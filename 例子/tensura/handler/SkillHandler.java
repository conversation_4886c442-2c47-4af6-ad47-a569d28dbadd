package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.attribute.event.CriticalChanceEvent;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.RemoveSkillEvent;
import com.github.manasmods.manascore.api.skills.event.SkillActivationEvent;
import com.github.manasmods.manascore.api.skills.event.SkillCooldownUpdateEvent;
import com.github.manasmods.manascore.api.skills.event.SkillReleaseEvent;
import com.github.manasmods.manascore.api.skills.event.SkillScrollEvent;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.manascore.api.skills.event.SkillDamageEvent.Calculation;
import com.github.manasmods.manascore.api.skills.event.SkillDamageEvent.PreCalculation;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.extra.FlameManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.LightningManipulationSkill;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.ability.skill.unique.TunerSkill;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.api.entity.subclass.ISummonable;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.skill.InstantRegenerationEffect;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.entity.human.IOtherworlder;
import com.github.manasmods.tensura.entity.magic.barrier.RangedBarrierEntity;
import com.github.manasmods.tensura.entity.magic.misc.WarpPortalEntity;
import com.github.manasmods.tensura.item.custom.VortexSpearItem;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestClientSkillRemovePacket;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.race.human.HumanRace;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.dimensions.LabyrinthTeleporter;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.event.TensuraGameEvents;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.StatType;
import net.minecraft.stats.Stats;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.event.PlayLevelSoundEvent.AtEntity;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.event.entity.living.LivingFallEvent;
import net.minecraftforge.event.entity.living.LivingUseTotemEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.registries.ForgeRegistries;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class SkillHandler {
   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void onSkillLearnt(UnlockSkillEvent e) {
      ManasSkillInstance instance = e.getSkillInstance();
      if (instance.getMastery() >= 0) {
         Entity pEntity = e.getEntity();
         if (pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            if (!instance.isTemporarySkill()) {
               CompoundTag tag = instance.getTag();
               if (tag != null && tag.m_128471_("NoMagiculeCost")) {
                  tag.m_128473_("NoMagiculeCost");
                  if (tag.m_128456_()) {
                     instance.setTag((CompoundTag)null);
                  }
               } else if (pEntity instanceof Player) {
                  Player player = (Player)pEntity;
                  if (!player.m_7500_()) {
                     ManasSkill var7 = instance.getSkill();
                     if (var7 instanceof TensuraSkill) {
                        TensuraSkill skill = (TensuraSkill)var7;
                        TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                           if (cap.getRace() == null || !cap.getRace().isIntrinsicSkill(player, skill)) {
                              double cost = skill.getObtainingEpCost() * (double)player.f_19853_.m_46469_().m_46215_(TensuraGameRules.MP_SKILL_COST) / 100.0D;
                              if (cap.getBaseMagicule() <= cost) {
                                 player.m_5661_(Component.m_237110_("tensura.skill.acquire_failed.mp", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                                 e.setCanceled(true);
                              } else {
                                 cap.setBaseMagicule(cap.getBaseMagicule() - cost, player);
                                 cap.setMagicule(Math.min(cap.getMagicule(), cap.getBaseMagicule()));
                                 TensuraPlayerCapability.sync(player);
                              }
                           }

                        });
                     }
                  }
               }
            } else {
               SkillStorage storage = SkillAPI.getSkillsFrom(pEntity);
               Optional<ManasSkillInstance> optionalCreator = storage.getSkill((ManasSkill)UniqueSkills.CREATOR.get());
               if (optionalCreator.isEmpty()) {
                  return;
               }

               if (instance.getTag() == null || !instance.getTag().m_128441_("CreatorSkill")) {
                  return;
               }

               if (!instance.getTag().m_128471_("CreatorSkill")) {
                  return;
               }

               ResourceLocation location = new ResourceLocation(((ManasSkillInstance)optionalCreator.get()).getOrCreateTag().m_128461_("created_skill"));
               if (instance.getSkill() == SkillAPI.getSkillRegistry().getValue(location)) {
                  return;
               }

               e.setCanceled(true);
            }

            if (!e.isCanceled()) {
               FlameManipulationSkill.learnFlameManipulation(instance, entity);
               LightningManipulationSkill.learnLightningManipulation(instance, entity);
            }
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onSkillRemove(RemoveSkillEvent e) {
      ManasSkillInstance instance = e.getSkillInstance();
      if (!e.getEntity().f_19853_.m_5776_()) {
         Entity var3 = e.getEntity();
         if (var3 instanceof ServerPlayer) {
            ServerPlayer player = (ServerPlayer)var3;
            instance.onToggleOff(player);
            ManasSkill var4;
            if (instance.shouldRemove()) {
               player.m_5661_(Component.m_237110_("tensura.skill.temporary.remove", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               if (instance.getTag() != null && instance.getTag().m_128451_("OldRemoval") == -1 && instance.getTag().m_128441_("OldMastery")) {
                  instance.setRemoveTime(-1);
                  instance.setMastery(instance.getTag().m_128451_("OldMastery"));
                  if (instance.getMastery() < 0) {
                     instance.setToggled(false);
                  }

                  instance.getTag().m_128473_("OldMastery");
                  e.setCanceled(true);
                  return;
               }
            } else {
               var4 = instance.getSkill();
               if (var4 instanceof ResistSkill) {
                  ResistSkill resist = (ResistSkill)var4;
                  if (resist.getResistType() == ResistSkill.ResistType.RESISTANCE && resist.pointRequirement() != -1) {
                     instance.setMastery(resist.pointRequirement() * -1);
                     instance.setRemoveTime(-1);
                     instance.setToggled(false);
                     e.setCanceled(true);
                     return;
                  }
               }
            }

            TensuraSkillCapability.removeSkillFromSlots(player, instance.getSkill());
            var4 = instance.getSkill();
            if (var4 instanceof ISpatialStorage) {
               ISpatialStorage storage = (ISpatialStorage)var4;
               storage.dropAllItems(instance, player);
            }

            TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
               return player;
            }), new RequestClientSkillRemovePacket(SkillUtils.getSkillId(instance.getSkill()), player.m_19879_()));
         }

      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void skillCooldown(SkillCooldownUpdateEvent e) {
      ManasSkillInstance instance = e.getSkillInstance();
      if (e.getCurrentCooldown() == 1) {
         if (instance.getSkill().equals(UniqueSkills.TUNER.get())) {
            TunerSkill.clearDeathTypes(instance, e.getEntity());
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void skillActivation(SkillActivationEvent e) {
      Player player = e.getEntity();
      ManasSkillInstance instance = e.getSkillInstance();
      if (!TensuraSkillCapability.isSkillInSlot(player, instance.getSkill(), e.getKeyNumber())) {
         e.setCanceled(true);
      } else {
         ManasSkill manasSkill = instance.getSkill();
         if (instance.onCoolDown() && !instance.canIgnoreCoolDown(player)) {
            player.m_5661_(Component.m_237110_("tensura.skill.cooldown", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            return;
         }

         if (instance.getMastery() < 0 && manasSkill instanceof TensuraSkill) {
            TensuraSkill skill = (TensuraSkill)manasSkill;
            if (skill.canLearnSkill(instance, player)) {
               skill.addLearnPoint(instance, player);
               if (fail(player, instance.getMastery()) && SkillUtils.learningFailPenalty(player) && instance.getMastery() < 0) {
                  instance.setMastery(Math.max(instance.getMastery() - player.m_217043_().m_216339_(1, 3), -100));
               }

               e.setCanceled(true);
            }
         }
      }

   }

   private static boolean fail(Player player, int mastery) {
      if (mastery <= -80) {
         return (double)player.m_217043_().m_188501_() >= 0.05D;
      } else if (mastery <= -60) {
         return (double)player.m_217043_().m_188501_() >= 0.1D;
      } else if (mastery <= -40) {
         return (double)player.m_217043_().m_188501_() >= 0.2D;
      } else if (mastery <= -20) {
         return (double)player.m_217043_().m_188501_() >= 0.3D;
      } else {
         return (double)player.m_217043_().m_188501_() >= 0.4D;
      }
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void skillRelease(SkillReleaseEvent e) {
      Player player = e.getEntity();
      ManasSkillInstance instance = e.getSkillInstance();
      if (!TensuraSkillCapability.isSkillInSlot(player, instance.getSkill(), e.getKeyNumber())) {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void skillScroll(SkillScrollEvent e) {
      Player player = e.getEntity();
      ManasSkillInstance instance = e.getSkillInstance();
      if (TensuraKeybinds.ACTIVATE_SLOT_1.m_90857_()) {
         if (!TensuraSkillCapability.isSkillInSlot(player, instance.getSkill(), 0)) {
            e.setCanceled(true);
         }
      } else if (TensuraKeybinds.ACTIVATE_SLOT_2.m_90857_()) {
         if (!TensuraSkillCapability.isSkillInSlot(player, instance.getSkill(), 1)) {
            e.setCanceled(true);
         }
      } else if (TensuraKeybinds.ACTIVATE_SLOT_3.m_90857_()) {
         if (!TensuraSkillCapability.isSkillInSlot(player, instance.getSkill(), 2)) {
            e.setCanceled(true);
         }
      } else {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void barrierHandler(Calculation e) {
      LivingEntity entity = e.getEntity();
      if (!SkillUtils.noInteractiveMode(entity)) {
         AttributeInstance instance = entity.m_21051_((Attribute)TensuraAttributeRegistry.BARRIER.get());
         if (instance != null) {
            double barrier = instance.m_22135_();
            if (barrier > 0.0D) {
               Entity var6 = e.getSource().m_7639_();
               if (var6 instanceof LivingEntity) {
                  LivingEntity attacker = (LivingEntity)var6;
                  if (RangedBarrierEntity.shouldInstaBreak(attacker, entity)) {
                     instance.m_22132_();
                     entity.m_9236_().m_5594_((Player)null, entity.m_20183_(), SoundEvents.f_11983_, SoundSource.AMBIENT, 1.0F, 1.0F);
                     return;
                  }
               }

               DamageSource var7 = e.getSource();
               float var10000;
               if (var7 instanceof TensuraEntityDamageSource) {
                  TensuraEntityDamageSource tensuraSource = (TensuraEntityDamageSource)var7;
                  var10000 = Math.min(e.getAmount() * tensuraSource.getIgnoreBarrier(), e.getAmount());
               } else {
                  var10000 = 0.0F;
               }

               float ignoredDamage = var10000;
               float bypassDamage = (float)Math.max((double)(e.getAmount() - ignoredDamage) - barrier, 0.0D);
               if (bypassDamage > 0.0F) {
                  instance.m_22132_();
                  entity.m_9236_().m_5594_((Player)null, entity.m_20183_(), SoundEvents.f_11983_, SoundSource.AMBIENT, 1.0F, 1.0F);
               }

               float takenDamage = bypassDamage + ignoredDamage;
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  double mpCost = (double)((e.getAmount() - takenDamage) * 5.0F);
                  double lackedMP = SkillHelper.outOfMagiculeStillConsume(player, (double)((int)mpCost));
                  if (lackedMP > 0.0D) {
                     takenDamage = (float)((double)takenDamage + lackedMP / 5.0D);
                     instance.m_22132_();
                     entity.m_9236_().m_5594_((Player)null, entity.m_20183_(), SoundEvents.f_11983_, SoundSource.AMBIENT, 1.0F, 1.0F);
                  }
               }

               e.setAmount(takenDamage);
            }

         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void critChance(CriticalChanceEvent e) {
      double chance = e.getCritChance();
      if (!(chance <= 0.0D)) {
         if (SkillUtils.canNegateCritChance(e.getTarget())) {
            e.setCanceled(true);
         }

      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOW
   )
   public static void preCalculation(PreCalculation e) {
      LivingEntity target = e.getEntity();
      if (DamageSourceHelper.isHoly(e.getSource()) && RaceHelper.isUndead(target)) {
         e.setAmount(e.getAmount() * 3.0F);
      }

      Entity var3 = e.getSource().m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity attacker = (LivingEntity)var3;
         if (attacker.m_21255_() && VortexSpearItem.onHit(attacker, target, target.m_20182_())) {
            double d7 = attacker.m_20184_().m_165924_();
            e.setAmount(e.getAmount() + (float)(d7 * 10.0D));
         }
      }

   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void cancelingDamage(LivingAttackEvent e) {
      LivingEntity target = e.getEntity();
      Player player;
      if (target instanceof Player) {
         player = (Player)target;
         if (TensuraPlayerCapability.isSpiritualForm(player) && !MobEffectHelper.inSpiritualWorld(player.m_9236_().m_46472_()) && DamageSourceHelper.isPhysicalAttack(e.getSource())) {
            e.setCanceled(true);
         }
      }

      if (TensuraGameRules.isLabyrinthPvpOff(target.m_9236_())) {
         if (e.getSource().equals(DamageSource.f_19307_)) {
            e.setCanceled(true);
            return;
         }

         if (TensuraGameRules.isLabyrinthPvpOff(target.m_9236_(), target, e.getSource().m_7639_())) {
            e.setCanceled(true);
            return;
         }
      }

      if ((e.getSource().equals(DamageSource.f_19316_) || e.getSource().m_146707_()) && VortexSpearItem.onHit(target, (LivingEntity)null, target.m_20182_())) {
         e.setCanceled(true);
      } else {
         Entity var3 = e.getSource().m_7639_();
         if (var3 instanceof LivingEntity) {
            boolean var10000;
            LivingEntity attacker;
            label74: {
               attacker = (LivingEntity)var3;
               if (attacker instanceof Player) {
                  Player player = (Player)attacker;
                  if (player.m_7500_()) {
                     var10000 = true;
                     break label74;
                  }
               }

               var10000 = false;
            }

            boolean creativeSource = var10000;
            if (!creativeSource) {
               if (TensuraEPCapability.isTargetNeutral(attacker, target)) {
                  e.setCanceled(true);
               }

               if (SkillHelper.isSubordinate(target, attacker)) {
                  e.setCanceled(true);
               }

               LivingEntity targetOwner = SkillHelper.getSubordinateOwner(target);
               if (targetOwner != null && SkillHelper.isSubordinate(targetOwner, attacker)) {
                  e.setCanceled(true);
               }
            }

            TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
               if (cap.isTargetNeutral(attacker.m_20148_())) {
                  cap.removeNeutralTarget(attacker.m_20148_());
                  TensuraEPCapability.sync(target);
               }

            });
         }

         var3 = e.getSource().m_7639_();
         if (var3 instanceof Player) {
            player = (Player)var3;
            if (SkillUtils.noInteractiveMode(player)) {
               e.setCanceled(true);
            }

            if (TensuraPlayerCapability.isSpiritualForm(player) && !MobEffectHelper.inSpiritualWorld(player.m_9236_().m_46472_()) && DamageSourceHelper.isPhysicalAttack(e.getSource())) {
               e.setCanceled(true);
            }
         }

         if (target.m_21023_((MobEffect)TensuraMobEffects.ALLY_BOOST.get()) && target.m_217043_().m_188503_(4) == 1 && !SkillUtils.canNegateDodge(target, e.getSource())) {
            target.m_9236_().m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
            e.setCanceled(true);
         }

      }
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void cancelingDamage(LivingDamageEvent e) {
      LivingEntity entity = e.getEntity();
      TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
         if (DamageSourceHelper.noDyingAnimation(e.getSource())) {
            cap.setNoDyingAnimation(true);
            TensuraEffectsCapability.sync(entity);
         } else if (cap.isNoDyingAnimation()) {
            cap.setNoDyingAnimation(false);
            TensuraEffectsCapability.sync(entity);
         }

      });
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onDamaged(LivingDamageEvent e) {
      if (e.getAmount() > Float.MAX_VALUE || Float.isNaN(e.getAmount())) {
         e.setAmount(Float.MAX_VALUE);
      }

   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void onProjectileImpact(ProjectileImpactEvent e) {
      HitResult var3 = e.getRayTraceResult();
      if (var3 instanceof EntityHitResult) {
         EntityHitResult entityHitResult = (EntityHitResult)var3;
         Entity var4 = entityHitResult.m_82443_();
         if (var4 instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)var4;
            if (SkillUtils.noInteractiveMode(target)) {
               e.setCanceled(true);
            }

            if (target.m_21023_((MobEffect)TensuraMobEffects.ALLY_BOOST.get()) && target.m_217043_().m_188503_(4) == 1) {
               target.m_9236_().m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               e.setCanceled(true);
            }

            if (target instanceof IOtherworlder) {
               IOtherworlder entity = (IOtherworlder)target;
               entity.onProjectileImpact(e);
            }
         }
      }

   }

   @SubscribeEvent
   public static void onTotemUse(LivingUseTotemEvent e) {
      LivingEntity entity = e.getEntity();
      SkillMobEffect.removeAllEffects(entity);
      TensuraEffectsCapability.resetEverything(entity, false, false);
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void onRespawn(PlayerRespawnEvent e) {
      Player player = e.getEntity();
      if (!player.m_9236_().m_5776_() && !e.isEndConquered()) {
         TensuraEPCapability.setSkippingEPDrop(player, false);
         TensuraEffectsCapability.resetEverything(player, true, false);
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setSleepMode(0);
            if (cap.getRace() != null) {
               cap.setSpiritualForm(MobEffectHelper.inSpiritualWorld(cap.getRace().getRespawnDimension()));
               RaceHelper.handleRespawnDimension(player, cap.getRace());
            }

            TensuraPlayerCapability.sync(player);
         });
         TensuraEPCapability.getFrom(player).ifPresent((cap) -> {
            cap.setSpiritualHealth(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()));
            cap.setTemporaryOwner((UUID)null);
            cap.clearNeutralTargets();
            cap.setHumanKill(0);
         });
         TensuraEPCapability.sync(player);
         SkillStorage storage = SkillAPI.getSkillsFrom(player);
         List<ManasSkillInstance> list = List.copyOf(storage.getLearnedSkills());
         Iterator var4 = list.iterator();

         while(true) {
            while(true) {
               while(true) {
                  Optional optional;
                  do {
                     if (!var4.hasNext()) {
                        return;
                     }

                     ManasSkillInstance copy = (ManasSkillInstance)var4.next();
                     optional = storage.getSkill(copy.getSkill());
                  } while(optional.isEmpty());

                  ManasSkillInstance instance = (ManasSkillInstance)optional.get();
                  if (instance.isTemporarySkill()) {
                     if (instance.getTag() != null && instance.getTag().m_128451_("OldRemoval") == -1) {
                        if (instance.getTag().m_128441_("OldMastery")) {
                           instance.setRemoveTime(-1);
                           instance.setMastery(instance.getTag().m_128451_("OldMastery"));
                           instance.getTag().m_128473_("OldMastery");
                        }
                     } else {
                        storage.forgetSkill(instance);
                     }
                  } else if (instance.isToggled()) {
                     if (instance.canInteractSkill(player)) {
                        instance.onToggleOn(player);
                     } else {
                        instance.setToggled(false);
                     }
                  }
               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void onPlaySound(AtEntity e) {
      Entity entity = e.getEntity();
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.MURDERER.get())) {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent
   public static void onFall(LivingFallEvent e) {
      LivingEntity entity = e.getEntity();
      if (!MobEffectHelper.noTeleportation(entity)) {
         if (!entity.m_6144_() && !entity.m_20092_()) {
            List<WarpPortalEntity> list = entity.m_9236_().m_6443_(WarpPortalEntity.class, entity.m_20191_().m_82400_(0.20000000298023224D), (portal) -> {
               return portal.getLife() > 0 && portal.isInstant();
            });
            if (!list.isEmpty()) {
               e.setCanceled(true);
            }

         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.HIGHEST
   )
   public static void onDeathHighPriority(LivingDeathEvent e) {
      LivingEntity entity = e.getEntity();
      if (entity.f_20890_) {
         e.setCanceled(true);
      } else {
         DamageSource source = e.getSource();
         Level level = entity.m_9236_();
         if (!Float.isNaN(entity.m_21223_()) && !(entity.m_21223_() < 0.0F)) {
            MobEffectInstance instance = entity.m_21124_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
            boolean instantRegen = instance != null && instance.m_19564_() >= 1 && InstantRegenerationEffect.canStopDeath(source, entity);
            if (!instantRegen && entity instanceof ElementalColossusEntity) {
               ElementalColossusEntity colossus = (ElementalColossusEntity)entity;
               Entity var17 = source.m_7639_();
               if (var17 instanceof LivingEntity) {
                  LivingEntity living = (LivingEntity)var17;
                  colossus.markAsPassedAndTeleport(living, false, true);
               } else if (colossus.m_21188_() != null) {
                  colossus.markAsPassedAndTeleport(colossus.m_21188_(), false, true);
               }
            } else if (instantRegen) {
               entity.m_21153_(0.01F);
               e.setCanceled(true);
            } else if (level.m_46472_().equals(TensuraDimensions.LABYRINTH)) {
               boolean canDie;
               boolean var10000;
               label113: {
                  canDie = source.m_19378_();
                  if (!canDie && !entity.m_6095_().m_204039_(TensuraTags.EntityTypes.CAN_DIE_IN_LABYRINTH)) {
                     label112: {
                        if (entity instanceof ISummonable) {
                           ISummonable summonable = (ISummonable)entity;
                           if (summonable.getSummoningTick() > 0) {
                              break label112;
                           }
                        }

                        var10000 = false;
                        break label113;
                     }
                  }

                  var10000 = true;
               }

               canDie = var10000;
               if (!canDie && !level.m_46469_().m_46207_(TensuraGameRules.LABYRINTH_DEATH)) {
                  entity.m_21153_(0.01F);
                  e.setCanceled(true);
                  if (level instanceof ServerLevel) {
                     ServerLevel serverLevel = (ServerLevel)level;
                     entity.f_19802_ = 100;
                     SkillHelper.removePredicateEffect(entity, (effectx) -> {
                        return true;
                     });
                     Entity var11 = source.m_7639_();
                     if (var11 instanceof ElementalColossusEntity) {
                        ElementalColossusEntity colossus = (ElementalColossusEntity)var11;
                        colossus.markAsPassedAndTeleport(entity, true, false);
                     } else {
                        LivingEntity var21 = entity.m_21188_();
                        if (var21 instanceof ElementalColossusEntity) {
                           ElementalColossusEntity colossus = (ElementalColossusEntity)var21;
                           colossus.markAsPassedAndTeleport(entity, true, false);
                        } else {
                           entity.changeDimension(serverLevel.m_7654_().m_129783_(), new LabyrinthTeleporter());
                        }
                     }
                  }
               } else {
                  LabyrinthHandler.handleGameMode(entity, true);
               }
            }

            if (!e.isCanceled()) {
               Entity var14 = source.m_7639_();
               if (var14 instanceof LivingEntity) {
                  LivingEntity attacker = (LivingEntity)var14;
                  Iterator var15 = attacker.m_21220_().iterator();

                  while(var15.hasNext()) {
                     MobEffectInstance effectInstance = (MobEffectInstance)var15.next();
                     MobEffect var20 = effectInstance.m_19544_();
                     if (var20 instanceof DamageAction) {
                        DamageAction effect = (DamageAction)var20;
                        effect.onKillEntity(attacker, e);
                     }
                  }

                  if (e.isCanceled()) {
                     return;
                  }

                  TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
                     if (Objects.equals(cap.getPermanentOwner(), attacker.m_20148_())) {
                        cap.setPermanentOwner((UUID)null);
                        TensuraEPCapability.sync(entity);
                     }

                  });
               }

            }
         } else {
            entity.revive();
            entity.m_21153_(0.0F);
            if (!(entity instanceof Player)) {
               entity.m_146870_();
            }

         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.NORMAL
   )
   public static void onDeathNormalPriority(LivingDeathEvent e) {
      if (!e.isCanceled()) {
         if (!e.getEntity().f_19853_.m_5776_()) {
            e.getEntity().m_146850_((GameEvent)TensuraGameEvents.AFTER_CHEAT_DEATH.get());
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onDeathLowPriority(LivingDeathEvent e) {
      LivingEntity entity = e.getEntity();
      if (!e.isCanceled()) {
         if (!entity.f_19853_.m_5776_()) {
            if (!entity.m_6084_()) {
               LivingEntity owner = SkillHelper.getSubordinateOwner(entity);
               if (owner != null) {
                  onSubordinateDeath(owner, e);
               }

               label42: {
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     if (e.getSource().equals(TensuraDamageSources.MAGICULE_POISON)) {
                        RaceHelper.applyMajinChance(player);
                        break label42;
                     }
                  }

                  Entity var5 = e.getSource().m_7639_();
                  if (var5 instanceof LivingEntity) {
                     LivingEntity attacker = (LivingEntity)var5;
                     addStatistic(entity, attacker);
                  }
               }

               if (entity.m_9236_().m_46469_().m_46207_(GameRules.f_46135_)) {
                  if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.DROP_CRYSTAL)) {
                     if (entity.m_9236_().m_46469_().m_46215_(TensuraGameRules.VANILLA_EP) == 0) {
                        ResourceLocation id = ForgeRegistries.ENTITY_TYPES.getKey(entity.m_6095_());
                        if (id == null) {
                           return;
                        }

                        if (id.m_135827_().equals("minecraft")) {
                           return;
                        }
                     }

                     IRanking.dropMagicCrystal(entity);
                  }

               }
            }
         }
      }
   }

   private static void addStatistic(LivingEntity target, LivingEntity attacker) {
      LivingEntity owner = attacker;
      LivingEntity subOwner = SkillHelper.getSubordinateOwner(attacker);
      if (subOwner != null && !(attacker instanceof Player)) {
         owner = subOwner;
      }

      EntityType<?> type = target.m_6095_();
      if (type.m_204039_(TensuraTags.EntityTypes.HUMAN) || TensuraPlayerCapability.getRace(target) instanceof HumanRace) {
         TensuraEPCapability.increaseHumanKill(owner);
      }

      if (owner instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)owner;
         if (SkillHelper.getSubordinateOwner(target) == null) {
            if (type == TensuraEntityTypes.HINATA_SAKAGUCHI.get()) {
               TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.GREAT_SAINT_OF_THE_WEST);
            }

            if (type.m_204039_(TensuraTags.EntityTypes.HERO_BOSS)) {
               if (type == TensuraEntityTypes.IFRIT.get()) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.CONQUEROR_OF_FLAMES);
               }

               player.m_36246_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_(type));
            }
         }

         if (attacker instanceof CloneEntity) {
            player.m_36246_(Stats.f_12986_.m_12902_(type));
            player.m_36220_(Stats.f_12936_);
         }
      }

   }

   private static void onSubordinateDeath(LivingEntity owner, LivingDeathEvent e) {
      SkillStorage skillStorage = SkillAPI.getSkillsFrom(owner);
      List<ManasSkillInstance> list = List.copyOf(skillStorage.getLearnedSkills());
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            ManasSkillInstance copy = (ManasSkillInstance)var4.next();
            Optional<ManasSkillInstance> optional = skillStorage.getSkill(copy.getSkill());
            if (!optional.isEmpty()) {
               Object var8 = optional.get();
               if (var8 instanceof TensuraSkillInstance) {
                  TensuraSkillInstance skillInstance = (TensuraSkillInstance)var8;
                  if (skillInstance.canInteractSkill(owner)) {
                     skillInstance.onSubordinateDeath(owner, e);
                  }
               }
            }
         }

         skillStorage.syncChanges();
      }
   }
}
