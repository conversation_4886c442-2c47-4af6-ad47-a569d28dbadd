package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class MusicianSkill extends Skill {
   public MusicianSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double learningCost() {
      return 2000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = this.isMastered(instance, entity) ? 3 : 2;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = this.isMastered(instance, entity) ? 3 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.musician.sonic_blast");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.musician.sound_wave");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.musician.mind_requiem");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 50.0D;
         break;
      case 2:
         var10000 = 100.0D;
         break;
      case 3:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), 200, 0, false, false, false));
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get());
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Level level = entity.m_9236_();
         switch(instance.getMode()) {
         case 1:
            this.sonicBlast(instance, entity, level);
            break;
         case 2:
            this.soundWave(instance, entity, level);
            break;
         case 3:
            this.mindRequiem(instance, entity, level);
         }

      }
   }

   public void sonicBlast(ManasSkillInstance instance, LivingEntity entity, Level level) {
      this.addMasteryPoint(instance, entity);
      instance.setCoolDown(1);
      double range = instance.isMastered(entity) ? 12.0D : 8.0D;
      Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_(range));
      Vec3 source = entity.m_20182_().m_82520_(0.0D, 1.600000023841858D, 0.0D);
      Vec3 offSetToTarget = target.m_82546_(source);
      Vec3 normalizes = offSetToTarget.m_82541_();
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

      for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
         Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
         ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SONIC_BLAST.get(), particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
         AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(2.0D);
         List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
            return !entityData.m_7306_(entity);
         });
         if (!livingEntityList.isEmpty()) {
            Iterator var14 = livingEntityList.iterator();

            while(var14.hasNext()) {
               LivingEntity pLivingEntity = (LivingEntity)var14.next();
               if (!RaceHelper.isSpiritualLifeForm(pLivingEntity)) {
                  DamageSource damagesource = TensuraDamageSources.sonicBlast(entity);
                  pLivingEntity.m_6469_(this.sourceWithMP(damagesource, entity, instance), this.isMastered(instance, entity) ? 150.0F : 75.0F);
               }
            }
         }
      }

   }

   public void soundWave(ManasSkillInstance instance, LivingEntity entity, Level level) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);
      List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(5.0D), (living) -> {
         return !living.m_7306_(entity) && living.m_6084_();
      });
      if (!list.isEmpty()) {
         this.addMasteryPoint(instance, entity);
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            LivingEntity target = (LivingEntity)var5.next();
            if (!RaceHelper.isSpiritualLifeForm(target)) {
               DamageSource damagesource = TensuraDamageSources.sonicBlast(entity);
               target.m_6469_(this.sourceWithMP(damagesource, entity, instance), this.isMastered(instance, entity) ? 70.0F : 40.0F);
            }
         }
      }

      instance.setCoolDown(1);
      ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SOUND_GIANT.get(), entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_(), 1, 0.0D, 0.0D, 0.0D, 0.0D);
      ((ServerLevel)level).m_8767_(ParticleTypes.f_123747_, entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_(), 1, 0.0D, 0.0D, 0.0D, 0.0D);
      ((ServerLevel)level).m_8767_(ParticleTypes.f_123813_, entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_(), 1, 0.0D, 0.0D, 0.0D, 0.0D);
   }

   public void mindRequiem(ManasSkillInstance instance, LivingEntity entity, Level level) {
      this.addMasteryPoint(instance, entity);
      instance.setCoolDown(3);
      Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_(10.0D));
      Vec3 source = entity.m_20182_().m_82520_(0.0D, 1.600000023841858D, 0.0D);
      Vec3 offSetToTarget = target.m_82546_(source);
      Vec3 normalizes = offSetToTarget.m_82541_();
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

      for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
         Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
         ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SOUND_REQUIEM.get(), particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
         AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(3.0D);
         List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
            return !entityData.m_7306_(entity);
         });
         if (!livingEntityList.isEmpty()) {
            Iterator var12 = livingEntityList.iterator();

            while(var12.hasNext()) {
               LivingEntity living = (LivingEntity)var12.next();
               DamageSource damagesource = TensuraDamageSources.mindRequiem(entity);
               if (living.m_6469_(this.sourceWithMP(damagesource, entity, instance), 300.0F)) {
                  DamageSourceHelper.directSpiritualHurt(living, entity, 100.0F);
               }
            }
         }
      }

   }
}
