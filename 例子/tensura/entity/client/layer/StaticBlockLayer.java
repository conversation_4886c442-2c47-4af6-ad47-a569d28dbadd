package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import javax.annotation.Nullable;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.geo.render.built.GeoModel;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.GeoModelProvider;
import software.bernie.geckolib3.renderers.geo.GeoLayerRenderer;
import software.bernie.geckolib3.renderers.geo.IGeoRenderer;

public class StaticBlockLayer<P extends LivingEntity & IAnimatable> extends GeoLayerRenderer<P> {
   public StaticBlockLayer(IGeoRenderer entityRendererIn) {
      super(entityRendererIn);
   }

   public void render(PoseStack matrixStackIn, MultiBufferSource bufferIn, int packedLightIn, P entity, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
      ResourceLocation chargingTexture = getBlockTexture(entity);
      if (chargingTexture != null) {
         RenderType renderType = getBlockRenderType(chargingTexture);
         VertexConsumer vertexconsumer = bufferIn.m_6299_(renderType);
         matrixStackIn.m_85836_();
         GeoModelProvider var15 = this.getEntityModel();
         if (var15 instanceof AnimatedGeoModel) {
            AnimatedGeoModel<P> model = (AnimatedGeoModel)var15;
            GeoModel geoModel = model.getModel(model.getModelResource(entity));
            geoModel.getBone("body").ifPresent((rootBone) -> {
               rootBone.childBones.forEach((bone) -> {
                  bone.setHidden(rootBone.isHidden());
                  bone.setScale(1.1F, 1.1F, 1.1F);
               });
            });
            if (entity instanceof SlimeEntity) {
               SlimeEntity slime = (SlimeEntity)entity;
               float scale = 1.0F / (0.175F * (float)slime.getSize());
               matrixStackIn.m_85841_(scale, scale, scale);
            }

            this.getRenderer().render(geoModel, entity, partialTicks, renderType, matrixStackIn, bufferIn, vertexconsumer, packedLightIn, OverlayTexture.f_118083_, 1.0F, 1.0F, 1.0F, getAlphaValue(entity));
         }

         matrixStackIn.m_85849_();
      }
   }

   public static RenderType getBlockRenderType(ResourceLocation texture) {
      return RenderType.m_110473_(texture);
   }

   public static float getAlphaValue(LivingEntity entity) {
      return TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.FROST.get()) ? 0.5F : 1.0F;
   }

   @Nullable
   public static ResourceLocation getBlockTexture(LivingEntity entity) {
      if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.PETRIFICATION.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/block/stone.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.FROST.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/block/ice.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.WEBBED.get()) && TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.SILENCE.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/block/web.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.SHADOW_BIND.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/block/shadow.png");
      } else {
         return TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.DIAMOND_PATH.get()) ? new ResourceLocation("tensura", "textures/models/layer/block/diamond.png") : null;
      }
   }
}
