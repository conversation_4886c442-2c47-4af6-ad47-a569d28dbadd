package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.barrier.AcidRainEntity;
import com.github.manasmods.tensura.entity.magic.projectile.PoisonBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WaterBallProjectile;
import com.github.manasmods.tensura.entity.magic.skill.WaterBladeProjectile;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class UndineEntity extends GreaterSpiritEntity {
   public int stayingTicks = 0;

   public UndineEntity(EntityType<? extends UndineEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.BLUE, BossBarOverlay.NOTCHED_20)).m_7005_(true);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 40.0D).m_22268_(Attributes.f_22276_, 400.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true) {
         public boolean m_8036_() {
            return !super.m_8036_() ? false : UndineEntity.this.usingMeleeWeapon();
         }
      });
      this.f_21345_.m_25352_(4, new UndineEntity.UndineAttackGoal(this));
      this.f_21345_.m_25352_(3, new GreaterSpiritEntity.FollowHinataGoal(this, 1.0D, HinataSakaguchiEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new GreaterSpiritEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 12));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{UndineEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      if (this.m_5803_()) {
         return entitydimensions;
      } else {
         return this.m_20096_() && this.stayingTicks >= 1200 ? entitydimensions.m_20390_(1.0F, 0.5F) : entitydimensions;
      }
   }

   public static boolean shouldDistinguishProjectile(@Nullable Entity entity) {
      return entity == null ? false : entity.m_6095_().m_204039_(TensuraTags.EntityTypes.CAN_DISTINGUISH);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (pSource.m_7639_() instanceof AquaFrogEntity) {
         return false;
      } else {
         if (DamageSourceHelper.isCold(pSource)) {
            pAmount *= 0.05F;
         }

         if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         } else {
            pAmount *= this.getPhysicalAttackInput(pSource);
         }

         if (shouldDistinguishProjectile(pSource.m_7640_())) {
            this.m_5496_(SoundEvents.f_12031_, 10.0F, 0.8F);
            pSource.m_7640_().m_142687_(RemovalReason.KILLED);
            return false;
         } else {
            boolean hurt = super.m_6469_(pSource, pAmount);
            if (hurt) {
               Entity var5 = pSource.m_7639_();
               if (var5 instanceof LivingEntity) {
                  LivingEntity damageSource = (LivingEntity)var5;
                  if (!damageSource.m_6084_()) {
                     return true;
                  }

                  if (damageSource instanceof Player) {
                     Player player = (Player)damageSource;
                     if (player.m_7500_() || player.m_5833_()) {
                        return true;
                     }
                  }

                  List<AquaFrogEntity> list = this.f_19853_.m_6443_(AquaFrogEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
                     return !entity.m_21824_();
                  });
                  if (!list.isEmpty()) {
                     list.forEach((frog) -> {
                        frog.m_6710_(damageSource);
                     });
                  }
               }
            }

            return hurt;
         }
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof AquaFrogEntity) {
         AquaFrogEntity frog = (AquaFrogEntity)entity;
         return frog.m_21824_() == this.m_21824_();
      } else if (entity instanceof UndineEntity) {
         UndineEntity undine = (UndineEntity)entity;
         return undine.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.m_21825_()) {
         ++this.stayingTicks;
         if (this.stayingTicks == 1200) {
            this.m_6210_();
         }
      } else if (this.stayingTicks != 0) {
         this.stayingTicks = 0;
         this.m_6210_();
      }

      if (!this.f_19853_.m_5776_()) {
         this.m_7311_(Math.min(60, this.m_20094_()));
      } else if (this.m_6084_() && this.f_19797_ % 10 == 0) {
         TensuraParticleHelper.addParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), 2.0D);
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if ((this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2) && this.miscAnimationTicks == 10) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               shootWaterBlade(this);
               this.m_5496_(SoundEvents.f_12520_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() != 3) {
               if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 22) {
                  this.tailSlam();
                  TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), this.m_20182_().m_7096_(), this.m_20182_().m_7098_(), this.m_20182_().m_7094_(), 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
                  TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SNOWFLAKE.get(), this.m_20182_().m_7096_(), this.m_20182_().m_7098_(), this.m_20182_().m_7094_(), 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
                  this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11807_, SoundSource.NEUTRAL, 5.0F, 1.0F);
                  this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11983_, SoundSource.NEUTRAL, 5.0F, 1.0F);
               } else if (this.getMiscAnimation() == 5) {
                  this.m_21573_().m_26573_();
                  if (this.miscAnimationTicks == 35) {
                     this.acidRain();
                     this.m_5496_(SoundEvents.f_12521_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 6) {
                  this.m_21573_().m_26573_();
                  if (this.miscAnimationTicks == 20) {
                     this.combust();
                     this.m_5496_(SoundEvents.f_11913_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                  }
               } else if (this.getMiscAnimation() == 7) {
                  this.m_21573_().m_26573_();
                  switch(this.miscAnimationTicks) {
                  case 10:
                     this.summonAquaFrog(3, 7);
                     break;
                  case 20:
                     this.summonAquaFrog(5, 10);
                     break;
                  case 30:
                     this.summonAquaFrog(7, 15);
                  }

                  this.m_5496_(SoundEvents.f_11917_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 25) {
                  this.waterBall();
               } else {
                  this.setMagicID(0);
               }

               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               this.m_5496_(SoundEvents.f_11862_, 8.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 1:
      case 2:
      default:
         var10000 = 15;
         break;
      case 3:
         var10000 = 60;
         break;
      case 4:
         var10000 = 35;
         break;
      case 5:
         var10000 = 50;
         break;
      case 6:
         var10000 = 30;
         break;
      case 7:
         var10000 = 40;
      }

      return var10000;
   }

   public static void shootWaterBlade(LivingEntity undine) {
      WaterBladeProjectile bolt = new WaterBladeProjectile(undine.f_19853_, undine);
      bolt.setSkill(SkillUtils.getSkillOrNull(undine, (ManasSkill)SpiritualMagics.WATER_CUTTER.get()));
      float angle = 0.017453292F * undine.f_20883_;
      double xOffset = (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
      double zOffset = (double)Mth.m_14089_(angle);
      bolt.m_7678_(undine.m_20185_() + xOffset, undine.m_20188_() - 0.2D, undine.m_20189_() + zOffset, undine.m_146908_(), undine.m_146909_());
      bolt.setDamage((float)undine.m_21133_(Attributes.f_22281_));
      bolt.setSpiritAttack(true);
      bolt.setSpeed(2.0F);
      bolt.m_20242_(true);
      bolt.shootFromRot(undine.m_20154_());
      undine.f_19853_.m_7967_(bolt);
   }

   private void waterBall() {
      int ballId = this.getMagicID();
      if (ballId == 0) {
         PoisonBallProjectile ball = new PoisonBallProjectile(this.f_19853_, this);
         ball.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
         ball.setSpiritAttack(true);
         ball.setMpCost(500.0D);
         ball.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.WATER_MANIPULATION.get()).orElse((Object)null));
         ball.m_146884_(this.m_146892_().m_82520_(0.0D, 4.0D, 0.0D));
         ball.setOwnerOffset(new Vec3(0.0D, 3.0D, 0.0D));
         ball.setLookDistance(30.0F);
         ball.setDelayTick(15);
         ball.m_20242_(true);
         ball.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 200, 1, false, false, false));
         ball.setEffectRange(3.0F);
         this.m_9236_().m_7967_(ball);
         this.setMagicID(ball.m_19879_());
      } else {
         Entity entity = this.m_9236_().m_6815_(ballId);
         if (entity instanceof WaterBallProjectile) {
            WaterBallProjectile ball = (WaterBallProjectile)entity;
            if (ball.getDelayTick() > 0) {
               ball.setSize(ball.getSize() + 0.3F);
            }
         } else {
            this.setMagicID(0);
            this.waterBall();
         }
      }

      this.m_5496_(SoundEvents.f_12521_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
   }

   public void tailSlam() {
      AABB aabb = this.m_20191_().m_82400_(5.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, this::shouldAttack);
      if (!list.isEmpty()) {
         double damageMultiplier = 1.5D;
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 20.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var6 = list.iterator();

         while(var6.hasNext()) {
            LivingEntity target = (LivingEntity)var6.next();
            target.m_6469_(damageSource, (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier));
            target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 200, 1, false, false, false));
            SkillHelper.knockBack(this, target, 1.0F);
         }

      }
   }

   private void acidRain() {
      AcidRainEntity rain = new AcidRainEntity(this.m_9236_(), this);
      rain.m_146884_(this.m_20182_());
      rain.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 0.5D));
      rain.setMpCost(1000.0D);
      rain.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.ACID_RAIN.get()).orElse((Object)null));
      if (this.m_5448_() != null) {
         rain.setTarget(this.m_5448_());
      }

      rain.setLife(200);
      rain.setRadius(2.5F);
      this.m_9236_().m_7967_(rain);
   }

   public void combust() {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SNOWFLAKE.get(), this.m_20185_(), this.m_20188_(), this.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.2D, true);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123795_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123795_, 3.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123795_, 4.0D);
      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 10.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, this::shouldAttack);
      if (!list.isEmpty()) {
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 100.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            target.m_6469_(damageSource, (float)this.m_21133_(Attributes.f_22281_) * 3.0F);
            target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.CHILL.get(), 200, 2, false, false, false));
            target.m_20334_(0.0D, 0.1D, 0.0D);
            SkillHelper.knockBack(this, target, 2.0F);
         }

      }
   }

   private void summonAquaFrog(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         AquaFrogEntity frog = new AquaFrogEntity((EntityType)TensuraEntityTypes.AQUA_FROG.get(), serverLevel);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            frog.m_6034_((double)i1, (double)j1, (double)k1);
            if (serverLevel.m_45784_(frog) && serverLevel.m_45786_(frog)) {
               frog.m_6518_(serverLevel, serverLevel.m_6436_(frog.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
               frog.m_6710_(this.m_5448_());
               serverLevel.m_47205_(frog);
               frog.setSummonerUUID(this.getSummonerUUID());
               TensuraParticleHelper.addServerParticlesAroundSelf(frog, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(frog, ParticleTypes.f_123774_, 2.0D);
               break;
            }
         }

      }
   }

   public MagicElemental getElemental() {
      return MagicElemental.WATER;
   }

   public Item getElementalCore() {
      return (Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get();
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 39) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         this.spawnDeathParticles();
      }

   }

   protected void spawnDeathParticles() {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.WATER_EFFECT.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SNOWFLAKE.get(), 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 2.0D);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11878_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11884_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11881_;
   }

   public boolean shouldStand() {
      if (this.m_21525_()) {
         return true;
      } else {
         return this.getMiscAnimation() >= 3 && this.getMiscAnimation() != 5 ? true : this.m_20096_();
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.relax", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_() && this.getMiscAnimation() == -1) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.idle_train", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving() && (this.getMiscAnimation() < 3 || this.getMiscAnimation() == 5) && !this.m_21525_()) {
         if (!this.m_20096_()) {
            if (this.m_21660_()) {
               String name = "animation.undine.swim_swift";
               if (this.getMiscAnimation() == 1) {
                  name = "animation.undine.swim_with_right_attack";
               } else if (this.getMiscAnimation() == 2) {
                  name = "animation.undine.swim_with_left_attack";
               }

               event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.swim_slow", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.walk", EDefaultLoopTypes.LOOP));
         }
      } else if (this.shouldStand()) {
         if (this.m_20096_() && this.stayingTicks >= 1200) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.idle_staring", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.idle", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.idle_swim", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.water_ball_right", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.water_ball_left", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.water_ball_massive", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.tail_slam", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.acid_rain", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.burst", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 7) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.summon", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.undine.death", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static class UndineAttackGoal extends Goal {
      private final UndineEntity undine;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private int attackCooldown;

      public UndineAttackGoal(UndineEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.undine = entity;
      }

      public boolean m_8036_() {
         if (this.undine.m_21827_()) {
            return false;
         } else if (this.undine.usingMeleeWeapon()) {
            return false;
         } else {
            LivingEntity target = this.undine.m_5448_();
            if (target != null && target.m_6084_()) {
               this.startOrbitFrom = target.m_146892_();
               return true;
            } else {
               return false;
            }
         }
      }

      public void m_8037_() {
         LivingEntity target = this.undine.m_5448_();
         if (target != null && target.m_6084_()) {
            this.undine.setFlying(true);
            if (this.orbitTime < this.maxOrbitTime) {
               if (this.undine.getMiscAnimation() == 0) {
                  ++this.orbitTime;
                  --this.attackCooldown;
               }

               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(15.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.undine.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, 1.2D);
               if (this.isTimeToAttack()) {
                  this.resetAttackCooldown();
                  this.undine.setMiscAnimation(this.randomAttack((double)this.undine.m_20270_(target)));
                  this.undine.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else {
               this.orbitTime = 0;
               if (this.canSummonFrogs()) {
                  this.undine.setMiscAnimation(7);
               }
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 80 + this.undine.m_217043_().m_188503_(80);
         this.undine.m_21561_(true);
         this.attackCooldown = 0;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)(Math.toRadians((double)this.orbitTime) * 3.0D);
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }

      private boolean canSummonFrogs() {
         if (this.undine.m_21824_()) {
            return false;
         } else {
            List<AquaFrogEntity> list = this.undine.f_19853_.m_45971_(AquaFrogEntity.class, TargetingConditions.m_148353_().m_26883_(20.0D).m_148355_().m_26893_(), this.undine, this.undine.m_20191_().m_82400_(20.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  AquaFrogEntity entity = (AquaFrogEntity)var2.next();
                  entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false));
               }
            }

            return this.undine.f_19796_.m_188503_(3) + 1 > list.size();
         }
      }

      private void resetAttackCooldown() {
         this.attackCooldown = this.m_183277_(30);
      }

      private boolean isTimeToAttack() {
         return this.attackCooldown <= 0;
      }

      private int randomAttack(double distance) {
         if (distance < 10.0D) {
            if ((double)this.undine.f_19796_.m_188501_() <= 0.2D) {
               return 6;
            }

            if ((double)this.undine.f_19796_.m_188501_() <= 0.3D) {
               return 4;
            }
         }

         if (distance < 20.0D && (double)this.undine.f_19796_.m_188501_() <= 0.1D) {
            return 5;
         } else if ((double)this.undine.f_19796_.m_188501_() <= 0.4D) {
            return 3;
         } else {
            return this.undine.m_217043_().m_188499_() ? 1 : 2;
         }
      }
   }
}
