package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import javax.annotation.Nullable;
import net.minecraft.advancements.Advancement;
import net.minecraft.advancements.Criterion;
import net.minecraft.advancements.CriterionTriggerInstance;
import net.minecraft.advancements.FrameType;
import net.minecraft.advancements.RequirementsStrategy;
import net.minecraft.advancements.Advancement.Builder;
import net.minecraft.advancements.critereon.EntityPredicate;
import net.minecraft.advancements.critereon.EntityTypePredicate;
import net.minecraft.advancements.critereon.ItemPredicate;
import net.minecraft.advancements.critereon.ImpossibleTrigger.TriggerInstance;
import net.minecraft.data.DataGenerator;
import net.minecraft.data.advancements.AdvancementProvider;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.common.data.ExistingFileHelper;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.registries.ForgeRegistries;

public class TensuraAdvancementsProvider extends AdvancementProvider {
   private ExistingFileHelper fileHelper;
   private Consumer<Advancement> consumer;

   public TensuraAdvancementsProvider(GatherDataEvent gatherDataEvent) {
      this(gatherDataEvent.getGenerator(), gatherDataEvent.getExistingFileHelper());
   }

   public TensuraAdvancementsProvider(DataGenerator generator, ExistingFileHelper fileHelper) {
      super(generator, fileHelper);
   }

   protected void registerAdvancements(Consumer<Advancement> consumer, ExistingFileHelper fileHelper) {
      this.consumer = consumer;
      this.fileHelper = fileHelper;
      Advancement REINCARNATED = Builder.m_138353_().m_138371_((ItemLike)TensuraMaterialItems.RACE_RESET_SCROLL.get(), Component.m_237115_("tensura.advancements.reincarnated.title"), Component.m_237115_("tensura.advancements.reincarnated.description"), new ResourceLocation("tensura", "textures/block/dark_labyrinth_stone.png"), FrameType.GOAL, true, true, false).m_138386_("impossible", new TriggerInstance()).m_138389_(consumer, String.format("%s:reincarnated", "tensura"));
      Advancement TAME_A_SLIME = this.add(TensuraAdvancementsHelper.Advancements.TAMED_A_SLIME, (ItemLike)TensuraMobDropItems.SLIME_CHUNK.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of("slime", "metal_slime")), new ArrayList(List.of(this.tameAnimalTrigger((EntityType)TensuraEntityTypes.SLIME.get()), this.tameAnimalTrigger((EntityType)TensuraEntityTypes.METAL_SLIME.get())))));
      Advancement GET_BUCKETED = this.add(TensuraAdvancementsHelper.Advancements.GET_BUCKETED, (ItemLike)TensuraMaterialItems.SLIME_IN_A_BUCKET.get(), (Advancement)TAME_A_SLIME, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.TRAITOR, (ItemLike)TensuraConsumableItems.CHILLED_SLIME.get(), (Advancement)GET_BUCKETED, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement GROW_A_SLIME = this.add(TensuraAdvancementsHelper.Advancements.GROW_A_SLIME, (ItemLike)TensuraMobDropItems.SLIME_CORE.get(), (Advancement)GET_BUCKETED, (ResourceLocation)null, FrameType.GOAL, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.KING_SLIME, (ItemLike)TensuraBlocks.SLIME_CHUNK_BLOCK.get(), (Advancement)GROW_A_SLIME, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.SLIME_ARMY, (ItemLike)TensuraToolItems.SLIME_STAFF.get(), (Advancement)GROW_A_SLIME, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.SLIME_STAFF.get())))));
      List<Map<String, CriterionTriggerInstance>> mainList = new ArrayList();
      List<Item> itemList = ForgeRegistries.ITEMS.getValues().stream().filter(Item::m_41472_).toList();
      List<String> stringList = itemList.stream().map(Item::m_5524_).toList();
      List<net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance> criterionTriggerInstanceList = itemList.stream().map(this::consumeTrigger).toList();

      for(int i = 0; i < itemList.size(); ++i) {
         HashMap<String, CriterionTriggerInstance> map = new HashMap();
         map.put((String)stringList.get(i), (CriterionTriggerInstance)criterionTriggerInstanceList.get(i));
         mainList.add(map);
      }

      this.add(TensuraAdvancementsHelper.Advancements.GETCHA_LEATHERS, Items.f_42454_, (ResourceLocation)(new ResourceLocation("story/root")), (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)Items.f_42454_)))));
      this.add(TensuraAdvancementsHelper.Advancements.GOLD_RUSH, Items.f_42417_, (ResourceLocation)(new ResourceLocation("story/iron_tools")), (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)Items.f_42417_)))));
      Advancement ACQUIRE_SILVERWARE = this.add(TensuraAdvancementsHelper.Advancements.ACQUIRE_SILVERWARE, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.SILVER_INGOT.get())))));
      Advancement GETCHA_BETTER_LEATHERS = this.add(TensuraAdvancementsHelper.Advancements.GETCHA_BETTER_LEATHERS, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get(), (Advancement)ACQUIRE_SILVERWARE, (ResourceLocation)null, FrameType.TASK, true, true, false, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of("monster_leather_d", "monster_leather_c", "monster_leather_b", "monster_leather_a", "monster_leather_special_a")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()), this.obtainItemTrigger((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()), this.obtainItemTrigger((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_B.get()), this.obtainItemTrigger((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get()), this.obtainItemTrigger((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A.get())))));
      Advancement BELIEVE_TO_FLY = this.add(TensuraAdvancementsHelper.Advancements.BELIEVE_T0_FLY, (ItemLike)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get(), (Advancement)GETCHA_BETTER_LEATHERS, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.RIPOFF_ELYTRA, (ItemLike)TensuraArmorItems.BAT_GLIDER.get(), (Advancement)BELIEVE_TO_FLY, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraArmorItems.BAT_GLIDER.get())))));
      Advancement MAGIC_ORE = this.add(TensuraAdvancementsHelper.Advancements.MAGIC_ORE, (ItemLike)TensuraMaterialItems.MAGIC_ORE.get(), (Advancement)GETCHA_BETTER_LEATHERS, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.MAGIC_ORE.get())))));
      Advancement LOW_MAGISTEEL = this.add(TensuraAdvancementsHelper.Advancements.LOW_MAGISTEEL, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get(), (Advancement)MAGIC_ORE, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get())))));
      Advancement HIGH_MAGISTEEL = this.add(TensuraAdvancementsHelper.Advancements.HIGH_MAGISTEEL, (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get(), (Advancement)LOW_MAGISTEEL, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get())))));
      Advancement MITHRIL = this.add(TensuraAdvancementsHelper.Advancements.MITHRIL, (ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get(), (Advancement)HIGH_MAGISTEEL, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get())))));
      Advancement ORICHALCUM = this.add(TensuraAdvancementsHelper.Advancements.ORICHALCUM, (ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get(), (Advancement)MITHRIL, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get())))));
      Advancement PURE_MAGISTEEL = this.add(TensuraAdvancementsHelper.Advancements.PURE_MAGISTEEL, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), (Advancement)ORICHALCUM, (ResourceLocation)null, FrameType.CHALLENGE, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get())))));
      Advancement ADAMANTITE = this.add(TensuraAdvancementsHelper.Advancements.ADAMANTITE, (ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get(), (Advancement)PURE_MAGISTEEL, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of(new String[]{"adamantite_ingot", "adamantite_sword", "adamantite_katana", "adamantite_short_sword", "adamantite_kodachi", "adamantite_long_sword", "adamantite_tachi", "adamantite_great_sword", "adamantite_odachi", "adamantite_pickaxe", "adamantite_axe", "adamantite_shovel", "adamantite_hoe", "adamantite_sickle", "adamantite_spear", "adamantite_scythe", "adamantite_helmet", "adamantite_chestplate", "adamantite_leggings", "adamantite_boots"})), new ArrayList(List.of(new net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance[]{this.obtainItemTrigger((ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_KATANA.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SHORT_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_KODACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_LONG_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_TACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_GREAT_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_ODACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_PICKAXE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_AXE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SHOVEL.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_HOE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SICKLE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SPEAR.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.ADAMANTITE_SCYTHE.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.ADAMANTITE_HELMET.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.ADAMANTITE_CHESTPLATE.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.ADAMANTITE_LEGGINGS.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.ADAMANTITE_BOOTS.get())}))));
      this.add(TensuraAdvancementsHelper.Advancements.HIHIIROKANE, (ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get(), (Advancement)ADAMANTITE, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of(new String[]{"hihiirokane_ingot", "hihiirokane_sword", "hihiirokane_katana", "hihiirokane_short_sword", "hihiirokane_kodachi", "hihiirokane_long_sword", "hihiirokane_tachi", "hihiirokane_great_sword", "hihiirokane_odachi", "hihiirokane_pickaxe", "hihiirokane_axe", "hihiirokane_shovel", "hihiirokane_hoe", "hihiirokane_sickle", "hihiirokane_spear", "hihiirokane_scythe", "hihiirokane_helmet", "hihiirokane_chestplate", "hihiirokane_leggings", "hihiirokane_boots"})), new ArrayList(List.of(new net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance[]{this.obtainItemTrigger((ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_KATANA.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SHORT_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_KODACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_LONG_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_TACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_GREAT_SWORD.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_ODACHI.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_PICKAXE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_AXE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SHOVEL.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_HOE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SICKLE.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SPEAR.get()), this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_SCYTHE.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.HIHIIROKANE_HELMET.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.HIHIIROKANE_CHESTPLATE.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.HIHIIROKANE_LEGGINGS.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.HIHIIROKANE_BOOTS.get())}))));
      Advancement VIGILANT = this.add(TensuraAdvancementsHelper.Advancements.VIGILANT, (ItemLike)TensuraMobDropItems.GIANT_ANT_CARAPACE.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.GIANT_ANT_CARAPACE.get())))));
      Advancement GOODNIGHT_SPIDER = this.add(TensuraAdvancementsHelper.Advancements.GOODNIGHT_SPIDER, (ItemLike)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get(), (Advancement)VIGILANT, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get())))));
      Advancement ARACHNOPHOBIC = this.add(TensuraAdvancementsHelper.Advancements.ARACHNOPHOBIC, (ItemLike)TensuraMobDropItems.SPIDER_FANG.get(), (Advancement)GOODNIGHT_SPIDER, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("entities")), new ArrayList(List.of(this.playerKilledEntityTrigger((EntityType)TensuraEntityTypes.BLACK_SPIDER.get())))));
      Advancement SHELL_LIZARD = this.add(TensuraAdvancementsHelper.Advancements.SHELL_LIZARD, (ItemLike)TensuraMobDropItems.ARMOURSAURUS_SHELL.get(), (Advancement)ARACHNOPHOBIC, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("armoursaurus_scale", "armoursaurus_shell")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.ARMOURSAURUS_SCALE.get()), this.obtainItemTrigger((ItemLike)TensuraMobDropItems.ARMOURSAURUS_SHELL.get())))));
      Advancement HISS_TORY = this.add(TensuraAdvancementsHelper.Advancements.HISS_TORY, (ItemLike)TensuraMobDropItems.SERPENT_SCALE.get(), (Advancement)SHELL_LIZARD, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMobDropItems.SERPENT_SCALE.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.EAT_OR_BE_EATEN, (ItemLike)TensuraBlocks.Items.ORC_DISASTER_HEAD.get(), (Advancement)HISS_TORY, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("entities")), new ArrayList(List.of(this.playerKilledEntityTrigger((EntityType)TensuraEntityTypes.ORC_DISASTER.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.CONQUEROR_OF_FLAMES, (ItemLike)TensuraArmorItems.ANTI_MAGIC_MASK.get(), (Advancement)HISS_TORY, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement RULER_OF_THE_SKIES = this.add(TensuraAdvancementsHelper.Advancements.RULER_OF_THE_SKIES, (ItemLike)TensuraBlocks.CHARYBDIS_CORE.get(), (Advancement)HISS_TORY, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("entities")), new ArrayList(List.of(this.playerKilledEntityTrigger((EntityType)TensuraEntityTypes.CHARYBDIS.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.NANODA, (ItemLike)TensuraMaterialItems.MUSIC_DISC_NANODA.get(), (Advancement)RULER_OF_THE_SKIES, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.MUSIC_DISC_NANODA.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.GREAT_SAINT_OF_THE_WEST, (ItemLike)TensuraToolItems.MOONLIGHT.get(), (Advancement)HISS_TORY, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement LABYRINTH = this.add(TensuraAdvancementsHelper.Advancements.LABYRINTH, (ItemLike)TensuraBlocks.LABYRINTH_BRICK.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.GOAL, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("dimensions")), new ArrayList(List.of(this.changeDimensionTrigger(TensuraDimensions.LABYRINTH)))));
      Advancement JUST_A_TEST = this.add(TensuraAdvancementsHelper.Advancements.JUST_A_TEST, (ItemLike)TensuraBlocks.LABYRINTH_STONE.get(), (Advancement)LABYRINTH, (ResourceLocation)null, FrameType.TASK, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("entities")), new ArrayList(List.of(this.entityKilledPlayerTrigger((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.SPIRIT_PROTECTOR, (ItemLike)TensuraBlocks.PURE_MAGISTEEL_BLOCK.get(), (Advancement)JUST_A_TEST, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("entities")), new ArrayList(List.of(this.playerKilledEntityTrigger((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get())))));
      Advancement ELEMENTALIST = this.add(TensuraAdvancementsHelper.Advancements.ELEMENTALIST, (ItemLike)TensuraBlocks.LABYRINTH_PRAYING_PATH.get(), (Advancement)JUST_A_TEST, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.BLESSED_ONE, (ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get(), (Advancement)ELEMENTALIST, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.INFINITY_CORES, (ItemLike)TensuraMaterialItems.ELEMENT_CORE_EMPTY.get(), (Advancement)ELEMENTALIST, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement D_RANK = this.add(TensuraAdvancementsHelper.Advancements.D_RANK, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_NUGGET.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement C_RANK = this.add(TensuraAdvancementsHelper.Advancements.C_RANK, (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET.get(), (Advancement)D_RANK, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement B_RANK = this.add(TensuraAdvancementsHelper.Advancements.B_RANK, (ItemLike)TensuraMaterialItems.MITHRIL_NUGGET.get(), (Advancement)C_RANK, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement A_RANK = this.add(TensuraAdvancementsHelper.Advancements.A_RANK, (ItemLike)TensuraMaterialItems.ORICHALCUM_NUGGET.get(), (Advancement)B_RANK, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement SA_RANK = this.add(TensuraAdvancementsHelper.Advancements.SA_RANK, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_NUGGET.get(), (Advancement)A_RANK, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement S_RANK = this.add(TensuraAdvancementsHelper.Advancements.S_RANK, (ItemLike)TensuraMaterialItems.ADAMANTITE_NUGGET.get(), (Advancement)SA_RANK, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.SS_RANK, (ItemLike)TensuraMaterialItems.HIHIIROKANE_NUGGET.get(), (Advancement)S_RANK, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement GROWTH_SPURT = this.add(TensuraAdvancementsHelper.Advancements.GROWTH_SPURT, (ItemLike)TensuraMobDropItems.ROYAL_BLOOD.get(), (Advancement)D_RANK, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.INFAMY_FAMOUS, (ItemLike)TensuraMobDropItems.DEMON_ESSENCE.get(), (Advancement)GROWTH_SPURT, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.HIGHER_FORM, (ItemLike)TensuraMobDropItems.DRAGON_ESSENCE.get(), (Advancement)S_RANK, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement HEAR_ME_DIREWOLVES = this.add(TensuraAdvancementsHelper.Advancements.HEAR_ME_DIREWOLVES, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get(), (Advancement)TAME_A_SLIME, (ResourceLocation)null, FrameType.GOAL, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("direwolves")), new ArrayList(List.of(this.tameAnimalTrigger((EntityType)TensuraEntityTypes.DIREWOLF.get())))));
      Advancement NAME_A_MOB = this.add(TensuraAdvancementsHelper.Advancements.NAME_A_MOB, Items.f_42656_, (Advancement)HEAR_ME_DIREWOLVES, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.RULER_OF_MONSTERS, (ItemLike)TensuraArmorItems.DARK_JACKET.get(), (Advancement)NAME_A_MOB, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, RequirementsStrategy.f_15978_, this.createList(new ArrayList(List.of("direwolf", "goblin", "lizardman", "orc", "slime")), new ArrayList(List.of(this.tameAnimalTrigger((EntityType)TensuraEntityTypes.DIREWOLF.get()), this.tameAnimalTrigger((EntityType)TensuraEntityTypes.GOBLIN.get()), this.tameAnimalTrigger((EntityType)TensuraEntityTypes.LIZARDMAN.get()), this.tameAnimalTrigger((EntityType)TensuraEntityTypes.ORC.get()), this.tameAnimalTrigger((EntityType)TensuraEntityTypes.SLIME.get())))));
      Advancement START_SMITHING = this.add(TensuraAdvancementsHelper.Advancements.START_SMITHING, (ItemLike)TensuraSmithingSchematicItems.SPIDER_BOWS.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement BECOME_NINJA = this.add(TensuraAdvancementsHelper.Advancements.BECOME_NINJA, (ItemLike)TensuraToolItems.KUNAI.get(), (Advancement)START_SMITHING, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.KUNAI.get())))));
      Advancement UNHEALABLE_WOUND = this.add(TensuraAdvancementsHelper.Advancements.UNHEALABLE_WOUND, (ItemLike)TensuraToolItems.SPATIAL_BLADE.get(), (Advancement)BECOME_NINJA, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.SPATIAL_BLADE.get())))));
      Advancement A_BIT_COLD = this.add(TensuraAdvancementsHelper.Advancements.A_BIT_COLD, (ItemLike)TensuraToolItems.ICE_BLADE.get(), (Advancement)UNHEALABLE_WOUND, (ResourceLocation)null, FrameType.CHALLENGE, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.ICE_BLADE.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.MASTER_SMITH, (ItemLike)TensuraSmithingSchematicItems.DARK_SET.get(), (Advancement)A_BIT_COLD, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.NO_NO_SQUARE, (ItemLike)TensuraBlocks.STONE_BRICK_MAGIC_ENGINE.get(), (Advancement)START_SMITHING, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement BETTER_SMELTER = this.add(TensuraAdvancementsHelper.Advancements.BETTER_SMELTER, (ItemLike)TensuraBlockEntities.Blocks.KILN.get(), (Advancement)START_SMITHING, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraBlockEntities.Blocks.KILN.get())))));
      Advancement PIERROT_MASK = this.add(TensuraAdvancementsHelper.Advancements.PIERROT_MASK, (ItemLike)TensuraArmorItems.CRAZY_PIERROT_MASK.get(), (Advancement)BETTER_SMELTER, (ResourceLocation)null, FrameType.GOAL, true, true, false, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of("angry", "crazy", "teary", "wonder")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraArmorItems.ANGRY_PIERROT_MASK.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.CRAZY_PIERROT_MASK.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.TEARY_PIERROT_MASK.get()), this.obtainItemTrigger((ItemLike)TensuraArmorItems.WONDER_PIERROT_MASK.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.TOO_STRONG, (ItemLike)TensuraToolItems.DRAGON_KNUCKLE.get(), (Advancement)PIERROT_MASK, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.DRAGON_KNUCKLE.get())))));
      Advancement MAGIC_SEEDY_PLACE = this.add(TensuraAdvancementsHelper.Advancements.MAGIC_SEEDY_PLACE, (ItemLike)TensuraMaterialItems.HIPOKUTE_SEEDS.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("seeds")), new ArrayList(List.of(net.minecraft.advancements.critereon.PlacedBlockTrigger.TriggerInstance.m_59505_((Block)TensuraBlocks.HIPOKUTE_GRASS.get())))));
      Advancement HIPOKUTE_FLOWER = this.add(TensuraAdvancementsHelper.Advancements.HIPOKUTE_FLOWER, (ItemLike)TensuraMaterialItems.HIPOKUTE_FLOWER.get(), (Advancement)MAGIC_SEEDY_PLACE, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.HIPOKUTE_FLOWER.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.GOOD_AS_NEW, (ItemLike)TensuraConsumableItems.FULL_POTION.get(), (Advancement)HIPOKUTE_FLOWER, (ResourceLocation)null, FrameType.GOAL, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("potions")), new ArrayList(List.of(this.consumeItem((ItemLike)TensuraConsumableItems.FULL_POTION.get())))));
      Advancement FAST_LEARNER = this.add(TensuraAdvancementsHelper.Advancements.FAST_LEARNER, (ItemLike)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, (List)null);
      Advancement MASTER_SKILL = this.add(TensuraAdvancementsHelper.Advancements.MASTER_SKILL, (ItemLike)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), (Advancement)FAST_LEARNER, (ResourceLocation)null, FrameType.GOAL, true, true, false, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.MASTER_UNIQUE_SKILL, (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), (Advancement)MASTER_SKILL, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.FORBIDDEN_MANUAL, (ItemLike)TensuraMaterialItems.BATTLEWILL_MANUAL.get(), (Advancement)FAST_LEARNER, (ResourceLocation)null, FrameType.GOAL, true, true, true, (RequirementsStrategy)null, (List)null);
      Advancement MONSTER_RIDER = this.add(TensuraAdvancementsHelper.Advancements.MONSTER_RIDER, (ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get(), (Advancement)GETCHA_BETTER_LEATHERS, (ResourceLocation)null, FrameType.TASK, true, true, false, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get())))));
      Advancement CHOO_CHOO = this.add(TensuraAdvancementsHelper.Advancements.CHOO_CHOO, (ItemLike)TensuraMobDropItems.CENTIPEDE_STINGER.get(), (Advancement)MONSTER_RIDER, (ResourceLocation)null, FrameType.GOAL, true, true, true, RequirementsStrategy.f_15979_, this.createList(new ArrayList(List.of("centipede", "centipede_body", "serpent", "serpent_body")), new ArrayList(List.of(this.rideAnimalTrigger((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE.get()), this.rideAnimalTrigger((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE_BODY.get()), this.rideAnimalTrigger((EntityType)TensuraEntityTypes.TEMPEST_SERPENT.get()), this.rideAnimalTrigger((EntityType)TensuraEntityTypes.TEMPEST_SERPENT_BODY.get())))));
      this.add(TensuraAdvancementsHelper.Advancements.GOOD_BOY, Items.f_42500_, (Advancement)CHOO_CHOO, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.REWIND_TIME, (ItemLike)TensuraMaterialItems.CHARACTER_RESET_SCROLL.get(), (Advancement)REINCARNATED, (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, (List)null);
      this.add(TensuraAdvancementsHelper.Advancements.OBTAIN_HIHIIROKANE_HOE, (ItemLike)TensuraToolItems.HIHIIROKANE_HOE.get(), (ResourceLocation)(new ResourceLocation("husbandry/obtain_netherite_hoe")), (ResourceLocation)null, FrameType.CHALLENGE, true, true, true, (RequirementsStrategy)null, this.createList(new ArrayList(List.of("items")), new ArrayList(List.of(this.obtainItemTrigger((ItemLike)TensuraToolItems.HIHIIROKANE_HOE.get())))));
   }

   private Builder addBuilder(ResourceLocation resourceLocation, ItemLike displayItem, @Nullable Advancement parent, Component title, Component description, @Nullable ResourceLocation background, @Nullable FrameType frameType, boolean showToast, boolean announceInChat, boolean hidden, @Nullable RequirementsStrategy requirements, @Nullable List<Map<String, CriterionTriggerInstance>> criterion) {
      Builder advancementBuilder = Builder.m_138353_();
      if (parent != null) {
         advancementBuilder.m_138398_(parent);
      }

      advancementBuilder.m_138371_(displayItem, title, description, parent == null ? background : null, frameType == null ? FrameType.GOAL : frameType, showToast, announceInChat, hidden);
      if (requirements != null) {
         advancementBuilder.m_138360_(requirements);
      }

      if (criterion != null && !criterion.isEmpty()) {
         Iterator var14 = criterion.iterator();

         while(var14.hasNext()) {
            Map<String, CriterionTriggerInstance> map = (Map)var14.next();
            map.forEach((key, value) -> {
               advancementBuilder.m_138383_(key, new Criterion(value));
            });
         }
      } else {
         advancementBuilder.m_138386_("custom", new TriggerInstance());
      }

      advancementBuilder.save(this.consumer, new ResourceLocation(String.format("%s:%s", resourceLocation.m_135827_(), resourceLocation.m_135815_())), this.fileHelper);
      return advancementBuilder;
   }

   private Advancement add(ResourceLocation location, ItemLike displayItem, @Nullable Advancement parent, @Nullable ResourceLocation background, @Nullable FrameType frameType, boolean showToast, boolean announceInChat, boolean hidden, @Nullable RequirementsStrategy requirements, @Nullable List<Map<String, CriterionTriggerInstance>> criterion) {
      Component title = Component.m_237115_("tensura.advancements." + location.m_135815_() + ".title");
      Component description = Component.m_237115_("tensura.advancements." + location.m_135815_() + ".description");
      return this.addBuilder(location, displayItem, parent, title, description, background, frameType, showToast, announceInChat, hidden, requirements, criterion).m_138403_(location);
   }

   private Advancement add(ResourceLocation location, ItemLike displayItem, @Nullable ResourceLocation parent, @Nullable ResourceLocation background, @Nullable FrameType frameType, boolean showToast, boolean announceInChat, boolean hidden, @Nullable RequirementsStrategy requirements, @Nullable List<Map<String, CriterionTriggerInstance>> criterion) {
      Component title = Component.m_237115_("tensura.advancements." + location.m_135815_() + ".title");
      Component description = Component.m_237115_("tensura.advancements." + location.m_135815_() + ".description");
      return this.add(location, displayItem, parent, title, description, background, frameType, showToast, announceInChat, hidden, requirements, criterion);
   }

   private Advancement add(ResourceLocation resourceLocation, ItemLike displayItem, @Nullable ResourceLocation parent, Component title, Component description, @Nullable ResourceLocation background, @Nullable FrameType frameType, boolean showToast, boolean announceInChat, boolean hidden, @Nullable RequirementsStrategy requirements, @Nullable List<Map<String, CriterionTriggerInstance>> criterion) {
      Builder advancementBuilder = Builder.m_138353_();
      if (parent != null) {
         advancementBuilder.m_138396_(parent);
      }

      advancementBuilder.m_138371_(displayItem, title, description, parent == null ? background : null, frameType == null ? FrameType.GOAL : frameType, showToast, announceInChat, hidden);
      if (requirements != null) {
         advancementBuilder.m_138360_(requirements);
      }

      if (criterion != null && !criterion.isEmpty()) {
         Iterator var14 = criterion.iterator();

         while(var14.hasNext()) {
            Map<String, CriterionTriggerInstance> map = (Map)var14.next();
            map.forEach((key, value) -> {
               advancementBuilder.m_138383_(key, new Criterion(value));
            });
         }
      } else {
         advancementBuilder.m_138386_("custom", new TriggerInstance());
      }

      advancementBuilder.save(this.consumer, new ResourceLocation(String.format("%s:%s", resourceLocation.m_135827_(), resourceLocation.m_135815_())), this.fileHelper);
      return advancementBuilder.m_138403_(resourceLocation);
   }

   private List<Map<String, CriterionTriggerInstance>> createList(List<String> keys, List<CriterionTriggerInstance> values) {
      if (keys.size() != values.size()) {
         throw new ArrayIndexOutOfBoundsException(String.format("Array sizes do not match: 'keys' is %s, 'values' is %s", keys.size(), values.size()));
      } else {
         List<Map<String, CriterionTriggerInstance>> list = new ArrayList();

         for(int i = 0; i < keys.size(); ++i) {
            Map<String, CriterionTriggerInstance> map = new HashMap();
            map.put((String)keys.get(i), (CriterionTriggerInstance)values.get(i));
            list.add(map);
         }

         return list;
      }
   }

   private net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance consumeTrigger(String modId, String location) {
      assert ForgeRegistries.ITEMS.getValue(new ResourceLocation(modId, location)) != null;

      return net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance.m_23703_((ItemLike)Objects.requireNonNull((Item)ForgeRegistries.ITEMS.getValue(new ResourceLocation(modId, location))));
   }

   private net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance consumeTrigger(String location) {
      assert ForgeRegistries.ITEMS.getValue(new ResourceLocation(location)) != null;

      return net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance.m_23703_((ItemLike)Objects.requireNonNull((Item)ForgeRegistries.ITEMS.getValue(new ResourceLocation(location))));
   }

   private net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance consumeTrigger(ItemLike item) {
      return net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance.m_23703_(item);
   }

   private net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance consumeTrigger(ItemPredicate item) {
      return net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance.m_148081_(item);
   }

   private net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance obtainItemTrigger(ItemLike item) {
      return net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance.m_43199_(new ItemLike[]{item});
   }

   private net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance obtainItemTrigger(ItemPredicate item) {
      return net.minecraft.advancements.critereon.InventoryChangeTrigger.TriggerInstance.m_43197_(new ItemPredicate[]{item});
   }

   private net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance consumeItem(ItemLike item) {
      return net.minecraft.advancements.critereon.ConsumeItemTrigger.TriggerInstance.m_23703_(item);
   }

   private net.minecraft.advancements.critereon.KilledTrigger.TriggerInstance playerKilledEntityTrigger(EntityType<?> entity) {
      EntityPredicate predicate = net.minecraft.advancements.critereon.EntityPredicate.Builder.m_36633_().m_36636_(entity).m_36662_();
      return net.minecraft.advancements.critereon.KilledTrigger.TriggerInstance.m_152108_(predicate);
   }

   private net.minecraft.advancements.critereon.KilledTrigger.TriggerInstance entityKilledPlayerTrigger(EntityType<?> entity) {
      EntityPredicate predicate = net.minecraft.advancements.critereon.EntityPredicate.Builder.m_36633_().m_36636_(entity).m_36662_();
      return net.minecraft.advancements.critereon.KilledTrigger.TriggerInstance.m_152124_(predicate);
   }

   private net.minecraft.advancements.critereon.TameAnimalTrigger.TriggerInstance tameAnimalTrigger(EntityType<?> type) {
      return net.minecraft.advancements.critereon.TameAnimalTrigger.TriggerInstance.m_68848_(net.minecraft.advancements.critereon.EntityPredicate.Builder.m_36633_().m_36646_(EntityTypePredicate.m_37647_(type)).m_36662_());
   }

   private net.minecraft.advancements.critereon.StartRidingTrigger.TriggerInstance rideAnimalTrigger(EntityType<?> type) {
      return this.rideAnimalTrigger(net.minecraft.advancements.critereon.EntityPredicate.Builder.m_36633_().m_36636_(type).m_36662_());
   }

   private net.minecraft.advancements.critereon.StartRidingTrigger.TriggerInstance rideAnimalTrigger(EntityPredicate pVehicle) {
      return net.minecraft.advancements.critereon.StartRidingTrigger.TriggerInstance.m_160401_(net.minecraft.advancements.critereon.EntityPredicate.Builder.m_36633_().m_36644_(pVehicle));
   }

   private net.minecraft.advancements.critereon.ChangeDimensionTrigger.TriggerInstance changeDimensionTrigger(ResourceKey<Level> level) {
      return net.minecraft.advancements.critereon.ChangeDimensionTrigger.TriggerInstance.m_19782_(level);
   }
}
