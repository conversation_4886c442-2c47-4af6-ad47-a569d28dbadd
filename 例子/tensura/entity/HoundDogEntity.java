package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ISummonable;
import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.HoundDogVariant;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.phys.Vec3;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class HoundDogEntity extends TensuraTamableEntity implements IAnimatable, SpittingRangedMonster, ISummonable {
   private static final EntityDataAccessor<Integer> VARIANT;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   private static final EntityDataAccessor<Boolean> SPIT_ATTACK;
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   protected boolean prevSnakeControlled = false;
   protected int spitCoolDown = 0;
   public int miscAnimationTicks = 0;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public HoundDogEntity(EntityType<? extends HoundDogEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 10;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22276_, 30.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.10000000149011612D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new HoundDogEntity.HoundDogAttackGoal(this, 1.5D, 40, 15.0F, true));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.0D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(6, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(7, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(7, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(VARIANT, 0);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(SPIT_ATTACK, false);
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
      compound.m_128379_("Spit", this.getSpitAttack());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      this.f_19804_.m_135381_(VARIANT, compound.m_128451_("Variant"));
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
      this.setSpitAttack(compound.m_128471_("Spit"));
   }

   public HoundDogVariant getVariant() {
      return HoundDogVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(VARIANT);
   }

   public void setVariant(HoundDogVariant variant) {
      this.f_19804_.m_135381_(VARIANT, variant.getId() & 255);
      this.updateStatsByVariant();
      this.m_21153_(this.m_21233_());
   }

   public void updateStatsByVariant() {
      if (this.getVariant().equals(HoundDogVariant.EVOLVED)) {
         if (this.isSnakeControlled()) {
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22276_))).m_22100_(60.0D);
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22281_))).m_22100_(4.0D);
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22279_))).m_22100_(0.20000000298023224D);
         } else {
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22276_))).m_22100_(60.0D);
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22281_))).m_22100_(6.0D);
            ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22279_))).m_22100_(0.30000001192092896D);
         }
      }

   }

   public boolean isSnakeControlled() {
      return this.getVariant().equals(HoundDogVariant.EVOLVED) && this.m_21223_() <= this.m_21233_() * 2.0F / 3.0F;
   }

   public boolean getSpitAttack() {
      return (Boolean)this.f_19804_.m_135370_(SPIT_ATTACK);
   }

   public void setSpitAttack(boolean ranged) {
      this.f_19804_.m_135381_(SPIT_ATTACK, ranged);
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity m_21826_() {
      return this.getSummonerUUID() != null ? null : super.m_21826_();
   }

   public boolean canSleep() {
      return !this.m_21525_();
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   protected float m_6118_() {
      return 0.5F * this.m_20098_();
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return this.m_9236_().m_46472_().equals(TensuraDimensions.HELL) ? false : super.m_6785_(pDistanceToClosestPlayer);
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == TensuraDamageSources.FATAL_POISON || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public boolean m_7301_(MobEffectInstance instance) {
      return instance.m_19544_() == MobEffects.f_19614_ ? false : super.m_7301_(instance);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      int spawnRate = (Integer)SpawnRateConfig.INSTANCE.houndDogSpawnRate.get();
      if (pLevel instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)pLevel;
         if (serverLevel.m_46472_() == TensuraDimensions.HELL) {
            spawnRate *= 3;
         }
      }

      return SpawnRateConfig.rollSpawn(spawnRate, this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.houndDogSnakeChance.get(), pLevel.m_213780_())) {
         this.setVariant(HoundDogVariant.EVOLVED);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 5.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 5.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity)) {
         if (pEntity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)pEntity;
            if (this.getVariant().equals(HoundDogVariant.EVOLVED)) {
               if (this.isSnakeControlled()) {
                  living.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 200, 0), this);
                  this.m_5634_(2.0F);
               } else {
                  int poison = living.m_21023_(MobEffects.f_19614_) ? 1 : 0;
                  living.m_147207_(new MobEffectInstance(MobEffects.f_19614_, 200, poison), this);
                  this.m_5634_(1.0F);
               }
            }
         }

         return true;
      } else {
         return false;
      }
   }

   public void spitParticle(MonsterSpitProjectile projectile) {
      this.particleSpawning(projectile, new DustParticleOptions(new Vector3f(new Vec3(0.0D, 255.0D, 0.0D)), 1.0F), 5);
   }

   public void spitHit(LivingEntity pTarget) {
      if (!(pTarget instanceof HoundDogEntity)) {
         pTarget.m_6469_(DamageSource.m_19370_(this), 3.0F);
         this.setSpitAttack(Boolean.FALSE);
      }
   }

   public void impactEffect(MonsterSpitProjectile spit, double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.ACID_EFFECT.get(), x, y, z, 55, 0.04D, 0.04D, 0.04D, 0.05D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.ACID_BUBBLE.get(), x, y, z, 25, 0.04D, 0.04D, 0.04D, 0.05D, false);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, spit.m_20191_().m_82400_(3.0D), (entityData) -> {
         return !entityData.m_7307_(this) && !entityData.m_7306_(this) && entityData != this.m_21826_();
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var9 = livingEntityList.iterator();

         while(var9.hasNext()) {
            LivingEntity pTarget = (LivingEntity)var9.next();
            pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 100, 0, false, false, true), this);
         }

      }
   }

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this, 10.0D);
      }

      if (this.prevSnakeControlled != this.isSnakeControlled()) {
         this.prevSnakeControlled = this.isSnakeControlled();
         this.updateStatsByVariant();
      }

      if (!this.getSpitAttack() && this.isSnakeControlled() && ++this.spitCoolDown == 100) {
         this.spitCoolDown = 0;
         this.setSpitAttack(Boolean.TRUE);
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 15) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_5634_(5.0F);
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         this.setMiscAnimation(1);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   protected SoundEvent m_7515_() {
      if (this.m_5448_() != null) {
         return SoundEvents.f_12619_;
      } else if (this.f_19796_.m_188503_(3) != 0) {
         return SoundEvents.f_12617_;
      } else {
         return this.m_21824_() && this.m_21223_() < 10.0F ? SoundEvents.f_12625_ : SoundEvents.f_12622_;
      }
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12621_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12618_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      String name;
      if (this.m_21223_() <= 0.0F) {
         name = "animation.hound_dog.lay";
      } else if (this.isSnakeControlled()) {
         if (this.getMiscAnimation() != 2 && this.getMiscAnimation() != 4) {
            if (event.isMoving()) {
               name = "animation.hound_dog.walk_snake";
            } else {
               name = "animation.hound_dog.idle_snake";
            }
         } else {
            name = "animation.hound_dog.idle";
         }
      } else if (this.m_5803_()) {
         name = "animation.hound_dog.sleep";
      } else if (this.m_21825_()) {
         name = "animation.hound_dog.sit";
      } else if (event.isMoving()) {
         if (!this.m_20069_() && !this.m_20077_()) {
            if (this.m_21660_()) {
               name = "animation.hound_dog.run";
            } else {
               name = "animation.hound_dog.walk";
            }
         } else {
            name = "animation.hound_dog.swim";
         }
      } else {
         name = "animation.hound_dog.idle";
      }

      event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.LOOP));
      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         String name = null;
         if (!this.m_5803_()) {
            if (this.getMiscAnimation() == 1 && !this.isSnakeControlled()) {
               name = "animation.hound_dog.eat";
            } else if (this.getMiscAnimation() == 2) {
               name = this.isSnakeControlled() ? "animation.hound_dog.bite_snake" : "animation.hound_dog.bite";
            } else if (this.getMiscAnimation() == 3) {
               name = "animation.hound_dog.leap_attack";
            } else if (this.getMiscAnimation() == 4) {
               name = "animation.hound_dog.snake_spit";
            }
         }

         if (name != null) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      VARIANT = SynchedEntityData.m_135353_(HoundDogEntity.class, EntityDataSerializers.f_135028_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(HoundDogEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(HoundDogEntity.class, EntityDataSerializers.f_135028_);
      SPIT_ATTACK = SynchedEntityData.m_135353_(HoundDogEntity.class, EntityDataSerializers.f_135035_);
      SUMMONER_UUID = SynchedEntityData.m_135353_(HoundDogEntity.class, EntityDataSerializers.f_135041_);
   }

   static class HoundDogAttackGoal extends MeleeAttackGoal {
      private final int attackInterval;
      private final float attackRadius;
      private int attackTime = -1;
      private final HoundDogEntity houndDog;

      public HoundDogAttackGoal(HoundDogEntity dog, double pSpeedModifier, int pAttackInterval, float pAttackRadius, boolean pFollowingTargetEvenIfNotSeen) {
         super(dog, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.attackInterval = pAttackInterval;
         this.attackRadius = pAttackRadius;
         this.houndDog = dog;
      }

      public boolean m_8036_() {
         if (this.houndDog.m_21827_()) {
            return false;
         } else if (!this.houndDog.getSpitAttack()) {
            return super.m_8036_();
         } else {
            LivingEntity livingentity = this.f_25540_.m_5448_();
            return livingentity != null && livingentity.m_6084_();
         }
      }

      public boolean m_8045_() {
         if (this.houndDog.m_21827_()) {
            return false;
         } else if (this.houndDog.getSpitAttack()) {
            LivingEntity target = this.f_25540_.m_5448_();
            return target != null && target.m_6084_() && !this.f_25540_.m_21573_().m_26571_() ? true : this.m_8036_();
         } else {
            return super.m_8045_();
         }
      }

      public void m_8041_() {
         this.attackTime = -1;
         super.m_8041_();
      }

      public void m_8037_() {
         if (this.houndDog.getSpitAttack()) {
            LivingEntity target = this.houndDog.m_5448_();
            if (target != null) {
               this.f_25540_.m_21563_().m_24960_(target, 30.0F, 30.0F);
               double d0 = this.f_25540_.m_20275_(target.m_20185_(), target.m_20186_(), target.m_20189_());
               boolean flag = this.f_25540_.m_21574_().m_148306_(target);
               if (--this.attackTime == 0) {
                  if (!flag) {
                     return;
                  }

                  this.houndDog.setMiscAnimation(4);
                  this.houndDog.performRangedAttack(target, 2.0D, 2.0D);
                  this.attackTime = Mth.m_14143_((float)this.attackInterval);
               } else if (this.attackTime < 0) {
                  this.attackTime = Mth.m_14107_(Mth.m_14139_(Math.sqrt(d0) / (double)this.attackRadius, (double)this.attackInterval, (double)this.attackInterval));
               }
            }
         }

         super.m_8037_();
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (!((double)this.houndDog.f_19796_.m_188501_() >= 0.2D) && !this.houndDog.isSnakeControlled()) {
            if (pDistToEnemySqr <= d0 + 4.0D && this.m_25564_()) {
               this.m_25563_();
               this.houndDog.setMiscAnimation(3);
               pEnemy.m_6469_(DamageSource.m_19370_(this.houndDog), (float)(this.houndDog.m_21133_(Attributes.f_22281_) * 1.5D));
            }
         } else if (pDistToEnemySqr <= d0 && this.m_25564_()) {
            this.m_25563_();
            this.f_25540_.m_7327_(pEnemy);
            this.houndDog.setMiscAnimation(2);
         }

      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         double reachSqr = super.m_6639_(pAttackTarget);
         if (this.houndDog.isSnakeControlled()) {
            reachSqr += 2.0D;
         }

         return reachSqr;
      }
   }
}
