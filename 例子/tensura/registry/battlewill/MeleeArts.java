package com.github.manasmods.tensura.registry.battlewill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.battlewill.melee.AuraSlashSkill;
import com.github.manasmods.tensura.ability.battlewill.melee.AuraSwordArt;
import com.github.manasmods.tensura.ability.battlewill.melee.EarthshatterKickArt;
import com.github.manasmods.tensura.ability.battlewill.melee.HeavySlashArt;
import com.github.manasmods.tensura.ability.battlewill.melee.OgreSwordGuillotineArt;
import com.github.manasmods.tensura.ability.battlewill.melee.RoaringLionPunchArt;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class MeleeArts {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AuraSlashSkill> AURA_SLASH;
   public static final RegistryObject<AuraSwordArt> AURA_SWORD;
   public static final RegistryObject<EarthshatterKickArt> EARTHSHATTER_KICK;
   public static final RegistryObject<HeavySlashArt> HEAVY_SLASH;
   public static final RegistryObject<OgreSwordGuillotineArt> OGRE_SWORD_GUILLOTINE;
   public static final RegistryObject<RoaringLionPunchArt> ROARING_LION_PUNCH;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      AURA_SLASH = registry.register("aura_slash", AuraSlashSkill::new);
      AURA_SWORD = registry.register("aura_sword", AuraSwordArt::new);
      EARTHSHATTER_KICK = registry.register("earthshatter_kick", EarthshatterKickArt::new);
      HEAVY_SLASH = registry.register("heavy_slash", HeavySlashArt::new);
      OGRE_SWORD_GUILLOTINE = registry.register("ogre_sword_guillotine", OgreSwordGuillotineArt::new);
      ROARING_LION_PUNCH = registry.register("roaring_lion_punch", RoaringLionPunchArt::new);
   }
}
