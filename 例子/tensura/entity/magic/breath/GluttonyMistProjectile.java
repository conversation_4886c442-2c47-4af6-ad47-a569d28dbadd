package com.github.manasmods.tensura.entity.magic.breath;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.mojang.math.Vector3f;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;

public class GluttonyMistProjectile extends PredatorMistProjectile {
   public GluttonyMistProjectile(EntityType<? extends GluttonyMistProjectile> entityType, Level level) {
      super(entityType, level);
      this.m_20242_(true);
   }

   public GluttonyMistProjectile(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.GLUTTONY_MIST.get(), level);
      this.m_5602_(entity);
   }

   protected void m_5790_(EntityHitResult entityHitResult) {
      if (!this.f_19853_.m_5776_()) {
         Entity entity = entityHitResult.m_82443_();
         if (this.isConsumeProjectile() && entity instanceof Projectile) {
            Projectile projectile = (Projectile)entity;
            this.devourProjectile(projectile);
         } else if (entity.m_6084_()) {
            if (entity instanceof LivingEntity) {
               LivingEntity target = (LivingEntity)entity;
               if (target instanceof Player) {
                  Player player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               DamageSource damageSource = TensuraDamageSources.DEVOURED;
               Entity var6 = this.m_37282_();
               LivingEntity owner;
               if (var6 instanceof LivingEntity) {
                  owner = (LivingEntity)var6;
                  damageSource = TensuraDamageSources.devour(owner);
               }

               if (entity.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), 50.0F)) {
                  var6 = this.m_37282_();
                  if (var6 instanceof LivingEntity) {
                     owner = (LivingEntity)var6;
                     if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                        SkillHelper.drainMP(target, owner, 1000.0D, false);
                     }

                     if (target.m_6084_()) {
                        if ((double)target.m_217043_().m_188501_() < 0.3D) {
                           this.devourRandomSkill(target, owner);
                        }

                        SkillHelper.checkThenAddEffectSource(target, owner, (MobEffect)TensuraMobEffects.CORROSION.get(), 100, 1, false, false, true, true);
                     } else {
                        this.devourAllSkills(target, owner);
                        this.devourEP(target, owner, 0.8F);
                        if (owner instanceof Player) {
                           Player player = (Player)owner;
                           List<ItemEntity> list = owner.f_19853_.m_45976_(ItemEntity.class, AABB.m_165882_(target.m_20182_(), 2.0D, 2.0D, 2.0D));
                           Iterator var8 = list.iterator();

                           while(var8.hasNext()) {
                              ItemEntity item = (ItemEntity)var8.next();
                              if (this.addItemToSpatialStorage(player, item.m_32055_())) {
                                 item.m_146870_();
                              } else if (player.m_36356_(item.m_32055_())) {
                                 item.m_146870_();
                              } else {
                                 item.m_20219_(owner.m_20182_());
                              }
                           }
                        }
                     }

                  }
               }
            }
         }
      }
   }

   protected void devourRandomSkill(LivingEntity target, LivingEntity owner) {
      if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
         List<ManasSkillInstance> collection = new ArrayList(SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter(this::canDevour).toList());

         for(int i = 0; i <= 2; ++i) {
            if (collection.isEmpty()) {
               return;
            }

            ManasSkillInstance instance = (ManasSkillInstance)collection.get(target.m_217043_().m_188503_(collection.size()));
            SkillPlunderEvent event = new SkillPlunderEvent(target, owner, false, instance.getSkill());
            if (!MinecraftForge.EVENT_BUS.post(event)) {
               if (SkillUtils.learnSkill(owner, event.getSkill(), this.getSkill().getRemoveTime())) {
                  if (owner instanceof Player) {
                     Player player = (Player)owner;
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }

                  owner.m_9236_().m_6263_((Player)null, owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               collection.remove(instance);
            }
         }

      }
   }

   public void spawnParticle() {
      BreathPart[] var1 = this.parts;
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         BreathPart part = var1[var3];
         TensuraParticleHelper.addParticlesAroundSelf(part, ParticleTypes.f_123765_, 0.7D);
         if (this.f_19796_.m_188499_()) {
            TensuraParticleHelper.addParticlesAroundSelf(part, new DustParticleOptions(new Vector3f(Vec3.m_82501_(12659166)), 1.0F), 0.7D);
         }
      }

   }
}
