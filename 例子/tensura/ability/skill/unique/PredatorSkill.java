package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.AbsorbDissolveSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.breath.PredatorMistProjectile;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import java.util.Optional;
import java.util.function.Predicate;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;

public class PredatorSkill extends Skill implements ISpatialStorage {
   public PredatorSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 5 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 5 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.predation");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.analysis");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.stomach");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.mimicry");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.isolation");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.getMode() == 1 && entity.m_6144_();
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      if (!storage.getSkill((ManasSkill)UniqueSkills.STARVED.get()).isEmpty()) {
         ManasSkill skill = (ManasSkill)UniqueSkills.GLUTTONY.get();
         if (SkillUtils.learnSkill(entity, skill) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      switch(instance.getMode()) {
      case 1:
         if (entity.m_6144_() && entity instanceof Player) {
            Player player = (Player)entity;
            byte newMode;
            switch(tag.m_128451_("blockMode")) {
            case 1:
               newMode = 2;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.blocks", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 2:
               newMode = 3;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.fluid", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 3:
               newMode = 4;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.all", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            default:
               newMode = 1;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.none", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            tag.m_128405_("blockMode", newMode);
            instance.markDirty();
         } else {
            PredatorMistProjectile breath = new PredatorMistProjectile(entity.m_9236_(), entity);
            breath.setLength(3.0F);
            breath.setBlockMode(instance.getOrCreateTag().m_128451_("blockMode"));
            if (instance.isMastered(entity)) {
               breath.setConsumeProjectile(true);
            }

            ManasSkillInstance gluttony = this.getGluttony(entity);
            breath.setSkill(gluttony != null ? gluttony : instance);
            breath.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_() * 0.7D, 0.0D));
            entity.m_9236_().m_7967_(breath);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         }
         break;
      case 2:
         SkillHelper.comingSoonMessage(entity, "Analysis");
         break;
      case 3:
         this.openSpatialStorage(entity, instance);
         break;
      case 4:
         SkillHelper.comingSoonMessage(entity, "Mimicry");
         break;
      case 5:
         ItemStack itemStack = entity.m_21205_();
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         if (itemStack.m_41619_()) {
            LivingEntity living = SkillHelper.getTargetingEntity(entity, 3.0D, false);
            boolean success;
            Predicate predicate;
            if (living != null) {
               success = TensuraEffectsCapability.getSeverance(living) > 0.0D;
               TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
                  cap.setSeveranceAmount(0.0D);
               });
               predicate = (effect) -> {
                  return effect.m_19483_() == MobEffectCategory.HARMFUL;
               };
               success = success || SkillHelper.removePredicateEffect(living, predicate, this.magiculeCost(entity, instance));
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123749_, 2.0D);
               }
            } else {
               success = TensuraEffectsCapability.getSeverance(entity) > 0.0D;
               TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                  cap.setSeveranceAmount(0.0D);
               });
               predicate = (effect) -> {
                  return effect.m_19483_() == MobEffectCategory.HARMFUL;
               };
               success = success || SkillHelper.removePredicateEffect(entity, predicate, this.magiculeCost(entity, instance));
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 2.0D);
               }
            }

            if (success) {
               instance.setCoolDown(instance.isMastered(entity) ? 5 : 3);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         } else {
            AbsorbDissolveSkill.Dissolving[] var13 = AbsorbDissolveSkill.Dissolving.values();
            int var12 = var13.length;

            for(int var17 = 0; var17 < var12; ++var17) {
               AbsorbDissolveSkill.Dissolving dissolving = var13[var17];
               if (dissolving.getItem().equals(itemStack.m_41720_())) {
                  this.addMasteryPoint(instance, entity);
                  if (entity instanceof ServerPlayer) {
                     ServerPlayer serverPlayer = (ServerPlayer)entity;
                     if (itemStack.m_41720_().equals(TensuraMaterialItems.SLIME_IN_A_BUCKET.get())) {
                        TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.TRAITOR);
                     }

                     serverPlayer.m_36246_(Stats.f_12982_.m_12902_(itemStack.m_41720_()));
                  }

                  itemStack.m_41774_(1);
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                        cap.setMagicule(cap.getMagicule() + (double)(dissolving.getMagicule() * 2));
                        if (cap.getMagicule() > player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get())) {
                           cap.setMagicule(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()));
                        }

                     });
                  }

                  if (dissolving.getHeal() > 0.0F) {
                     entity.m_5634_(dissolving.getHeal());
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12321_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      }

   }

   private ManasSkillInstance getGluttony(LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> gluttonyOptional = storage.getSkill((ManasSkill)UniqueSkills.GLUTTONY.get());
      return (ManasSkillInstance)gluttonyOptional.orElse((Object)null);
   }

   public void openSpatialStorage(LivingEntity entity, ManasSkillInstance instance) {
      ManasSkillInstance gluttony = this.getGluttony(entity);
      if (gluttony != null) {
         this.moveItemsToSpatialStorage(instance, gluttony, entity, true);
      } else {
         ISpatialStorage.super.openSpatialStorage(entity, instance);
      }

   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(63, 128);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }
}
