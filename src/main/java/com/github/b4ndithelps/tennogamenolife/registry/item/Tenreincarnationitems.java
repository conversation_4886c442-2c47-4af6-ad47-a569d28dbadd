package com.github.b4ndithelps.tennogamenolife.registry.item;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.item.ChainFellingSkillBook;
import com.github.b4ndithelps.tennogamenolife.item.ChainMiningSkillBook;
import com.github.b4ndithelps.tennogamenolife.item.ChainShovelSkillBook;
import com.github.b4ndithelps.tennogamenolife.item.MagItem;
import com.github.b4ndithelps.tennogamenolife.item.MechanicalCoreItem;
import com.github.b4ndithelps.tennogamenolife.item.SickleItem;
import com.github.b4ndithelps.tennogamenolife.item.StarEssence;
import com.github.b4ndithelps.tennogamenolife.item.HurtDummyItem;
import com.github.b4ndithelps.tennogamenolife.item.ExMachinasWeaponItem;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;

import net.minecraft.world.item.Item;
import net.minecraftforge.common.ForgeSpawnEggItem;
import java.awt.Color;
import net.minecraft.world.item.Rarity;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

/**
 * 物品注册类
 * 用于注册模组中的所有物品
 */
public class Tenreincarnationitems {
    /**
     * 创建物品注册器
     */
    public static final DeferredRegister<Item> ITEMS = DeferredRegister.create(ForgeRegistries.ITEMS, Tenreincarnation.MODID);

    /**
     * 注册混沌魔力物品
     * 设置为史诗品质（紫色）
     */
    public static final RegistryObject<Item> MAG = ITEMS.register("mag",
            () -> new MagItem(new Item.Properties().rarity(Rarity.EPIC)));

    /**
     * 注册机械核心物品
     * 设置为史诗品质（紫色）
     */
    public static final RegistryObject<Item> MECHANICAL_CORE = ITEMS.register("mechanical_core",
            () -> new MechanicalCoreItem(new Item.Properties().rarity(Rarity.EPIC)));

    /**
     * 注册星之精华物品
     * 设置为史诗品质（紫色）
     */
    public static final RegistryObject<Item> STAR_ESSENCE = ITEMS.register("star_essence", 
        () -> new StarEssence(new Item.Properties().rarity(Rarity.EPIC)));

    /**
     * 注册镰刀武器
     * 设置为史诗品质（紫色）
     */
    public static final RegistryObject<Item> SICKLE = ITEMS.register("sickle",
            () -> new SickleItem());

    /**
     * 注册伤害假人物品
     * 设置为史诗品质（紫色）
     */
    public static final RegistryObject<Item> HURT_DUMMY_ITEM = ITEMS.register("hurt_dummy", HurtDummyItem::new);
    
    /**
     * 注册连锁挖矿技能书物品
     * 设置为史诗品质（紫色），归类到杂项物品栏中
     */
    public static final RegistryObject<Item> CHAIN_MINING_SKILL_BOOK = ITEMS.register("chain_mining_skill_book",
            () -> new ChainMiningSkillBook(new Item.Properties()
                    .tab(TensuraCreativeTab.MISCELLANEOUS)
                    .stacksTo(1)
                    .rarity(Rarity.EPIC)));
                    
    /**
     * 注册连锁砍伐技能书物品
     * 设置为史诗品质（紫色），归类到杂项物品栏中
     */
    public static final RegistryObject<Item> CHAIN_FELLING_SKILL_BOOK = ITEMS.register("chain_felling_skill_book",
            () -> new ChainFellingSkillBook(new Item.Properties()
                    .tab(TensuraCreativeTab.MISCELLANEOUS)
                    .stacksTo(1)
                    .rarity(Rarity.EPIC)));
                    
    /**
     * 注册连锁铲技能书物品
     * 设置为史诗品质（紫色），归类到杂项物品栏中
     */
    public static final RegistryObject<Item> CHAIN_SHOVEL_SKILL_BOOK = ITEMS.register("chain_shovel_skill_book",
            () -> new ChainShovelSkillBook(new Item.Properties()
                    .tab(TensuraCreativeTab.MISCELLANEOUS)
                    .stacksTo(1)
                    .rarity(Rarity.EPIC)));

    /**
     * 注册机铠种之械
     * 设置为史诗品质（紫色），归类到武器物品栏中
     */
    public static final RegistryObject<Item> EX_MACHINAS_WEAPON = ITEMS.register("ex_machinas_weapon",
            () -> new ExMachinasWeaponItem());

    /**
     * 注册休比生物蛋
     * 机铠种休比的生物蛋，归类到Tensura生物蛋标签页中
     * 主色调：银灰色 (机铠种的金属色)
     * 次色调：蓝色 (机铠种的能量色)
     */
    public static final RegistryObject<Item> XIUBI_SPAWN_EGG = ITEMS.register("xiubi_spawn_egg",
            () -> new ForgeSpawnEggItem(TenreincarnationEntities.XIUBI,
                    new Color(192, 192, 192).getRGB(), // 银灰色主色
                    new Color(64, 128, 255).getRGB(),   // 蓝色次色
                    new Item.Properties().tab(TensuraCreativeTab.SPAWN_EGGS)));

    /**
     * 注册所有物品
     * @param modEventBus 模组事件总线
     */
    public static void register(IEventBus modEventBus) {
        ITEMS.register(modEventBus);
    }
} 