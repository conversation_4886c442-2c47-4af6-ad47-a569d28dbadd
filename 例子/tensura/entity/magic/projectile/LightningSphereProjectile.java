package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.lightning.LightningBolt;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.HitResult;
import org.jetbrains.annotations.NotNull;

public class LightningSphereProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/lightning_sphere/lightning_ball_5.png")};

   public LightningSphereProjectile(EntityType<? extends LightningSphereProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public LightningSphereProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.LIGHTNING_SPHERE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.lightning_attack";
   }

   public boolean piercingEntity() {
      return true;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   protected void m_6532_(@NotNull HitResult hitresult) {
      super.m_6532_(hitresult);
      Entity entity = this.m_37282_();
      LightningBolt bolt = new LightningBolt(this.f_19853_, entity);
      ServerPlayer var10001;
      if (entity instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)entity;
         var10001 = serverPlayer;
      } else {
         var10001 = null;
      }

      bolt.m_20879_(var10001);
      bolt.setMpCost(this.getMpCost());
      bolt.setTensuraDamage(this.getDamage());
      bolt.setSkill(this.getSkill());
      float radius = this.getSize();
      bolt.setAdditionalVisual((int)radius);
      bolt.setRadius(radius);
      bolt.m_146884_(this.m_20182_());
      this.f_19853_.m_7967_(bolt);
   }

   public void applyEffectAround(double inflateRadius) {
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(inflateRadius), (entityData) -> {
         return this.m_37282_() == null || !entityData.m_7307_(this.m_37282_()) && !entityData.m_7306_(this.m_37282_());
      });
      if (!list.isEmpty()) {
         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            if (this.getMobEffect() != null) {
               SkillHelper.checkThenAddEffectSource(target, this.m_37282_(), this.getMobEffect());
            }

            DamageSource damagesource = TensuraDamageSources.indirectElementalAttack(this.getMagic(), this, this.m_37282_(), this.getMpCost() / 10.0D, this.getSkill(), false);
            DamageSourceHelper.dealSplitElementalDamage(target, damagesource, 0.9F, this.getDamage() / 10.0F);
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12089_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
