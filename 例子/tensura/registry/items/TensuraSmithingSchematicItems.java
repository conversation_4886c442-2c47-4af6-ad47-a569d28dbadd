package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.item.templates.custom.SmithingSchematicItem;
import net.minecraft.world.item.Item;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@GenerateItemModels
public class TensuraSmithingSchematicItems {
   private static final DeferredRegister<Item> registry;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> BASIC_BOWS;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SPIDER_BOWS;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> JAPANESE_SWORD;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> HUNTING_KNIFE;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SHORT_SWORD;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> LONG_SWORD;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> GREAT_SWORD;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SPEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> KUNAI;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SHIELD;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> WINGED_SHOES;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> LEATHER_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> MONSTER_LEATHER_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> GOLD_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> IRON_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SILVER_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> ANT_CARAPACE_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SERPENT_SCALEMAIL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> DIAMOND_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> KNIGHT_SPIDER_CARAPACE_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> LOW_MAGISTEEL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> ARMOURSAURUS_SCALEMAIL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> HIGH_MAGISTEEL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> CHARYBDIS_SCALEMAIL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> MITHRIL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> ORICHALCUM_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> PURE_MAGISTEEL_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> ADAMANTITE_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> HIHIIROKANE_GEAR;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> ANTI_MAGIC_MASK;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> DARK_SET;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> PIERROT_MASK;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> SPATIAL_BLADE;
   @SingleTextureModel
   public static final RegistryObject<SmithingSchematicItem> WEB_GUN;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      BASIC_BOWS = registry.register("basic_bows_schematic", SmithingSchematicItem::new);
      SPIDER_BOWS = registry.register("spider_bows_schematic", SmithingSchematicItem::new);
      JAPANESE_SWORD = registry.register("japanese_sword_schematic", SmithingSchematicItem::new);
      HUNTING_KNIFE = registry.register("hunting_knife_schematic", SmithingSchematicItem::new);
      SHORT_SWORD = registry.register("short_sword_schematic", SmithingSchematicItem::new);
      LONG_SWORD = registry.register("long_sword_schematic", SmithingSchematicItem::new);
      GREAT_SWORD = registry.register("great_sword_schematic", SmithingSchematicItem::new);
      SPEAR = registry.register("spear_schematic", SmithingSchematicItem::new);
      KUNAI = registry.register("kunai_schematic", SmithingSchematicItem::new);
      SHIELD = registry.register("shield_schematic", SmithingSchematicItem::new);
      WINGED_SHOES = registry.register("winged_shoes_schematic", SmithingSchematicItem::new);
      LEATHER_GEAR = registry.register("leather_gear_schematic", SmithingSchematicItem::new);
      MONSTER_LEATHER_GEAR = registry.register("monster_leather_gear_schematic", SmithingSchematicItem::new);
      GOLD_GEAR = registry.register("gold_gear_schematic", SmithingSchematicItem::new);
      IRON_GEAR = registry.register("iron_gear_schematic", SmithingSchematicItem::new);
      SILVER_GEAR = registry.register("silver_gear_schematic", SmithingSchematicItem::new);
      ANT_CARAPACE_GEAR = registry.register("ant_carapace_gear_schematic", SmithingSchematicItem::new);
      SERPENT_SCALEMAIL_GEAR = registry.register("serpent_scalemail_gear_schematic", SmithingSchematicItem::new);
      DIAMOND_GEAR = registry.register("diamond_gear_schematic", SmithingSchematicItem::new);
      KNIGHT_SPIDER_CARAPACE_GEAR = registry.register("knight_spider_carapace_gear_schematic", SmithingSchematicItem::new);
      LOW_MAGISTEEL_GEAR = registry.register("low_magisteel_gear_schematic", SmithingSchematicItem::new);
      ARMOURSAURUS_SCALEMAIL_GEAR = registry.register("armoursaurus_scalemail_gear_schematic", SmithingSchematicItem::new);
      HIGH_MAGISTEEL_GEAR = registry.register("high_magisteel_gear_schematic", SmithingSchematicItem::new);
      CHARYBDIS_SCALEMAIL_GEAR = registry.register("charybdis_scalemail_gear_schematic", SmithingSchematicItem::new);
      MITHRIL_GEAR = registry.register("mithril_gear_schematic", SmithingSchematicItem::new);
      ORICHALCUM_GEAR = registry.register("orichalcum_gear_schematic", SmithingSchematicItem::new);
      PURE_MAGISTEEL_GEAR = registry.register("pure_magisteel_gear_schematic", SmithingSchematicItem::new);
      ADAMANTITE_GEAR = registry.register("adamantite_gear_schematic", SmithingSchematicItem::new);
      HIHIIROKANE_GEAR = registry.register("hihiirokane_gear_schematic", SmithingSchematicItem::new);
      ANTI_MAGIC_MASK = registry.register("anti_magic_mask_schematic", SmithingSchematicItem::new);
      DARK_SET = registry.register("dark_set_schematic", SmithingSchematicItem::new);
      PIERROT_MASK = registry.register("pierrot_mask_schematic", SmithingSchematicItem::new);
      SPATIAL_BLADE = registry.register("spatial_blade_schematic", SmithingSchematicItem::new);
      WEB_GUN = registry.register("web_gun_schematic", SmithingSchematicItem::new);
   }
}
