package com.github.b4ndithelps.tennogamenolife.race.Flügel;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.network.chat.Component;
import net.minecraft.stats.Stats;
import net.minecraft.resources.ResourceLocation;
import org.jetbrains.annotations.Nullable;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;

import java.util.ArrayList;
import java.util.List;

/**
 * 阿尔特修 - Arutoshu
 */
public class ArutoshuRace extends Race {

    // 种族基础属性
    private double baseHealth = 5000.0;          // 基础生命值
    private double baseAttackDamage = 35.0;     // 基础攻击伤害
    private double baseAttackSpeed = 5.0;      // 基础攻击速度
    private double knockbackResistance = 0.8;   // 击退抗性
    private double jumpHeight = 3.0;           // 跳跃高度
    private double movementSpeed = 0.35;        // 移动速度
    private double sprintSpeed = 0.4;         // 冲刺速度
    private double auraMin = 150000.0;           // 最小灵气值
    private double auraMax = 200000.0;           // 最大灵气值
    private double startingMagiculeMin = 80000.0; // 最小初始魔素值
    private double startingMagiculeMax = 110000.0; // 最大初始魔素值

    private float playerSize = 3.0f;          // 玩家大小

    public ArutoshuRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(ExtraSkills.HEAVENLY_EYE.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ExtraSkills.ULTRASPEED_REGENERATION.get());
        list.add(ResistanceSkills.PARALYSIS_NULLIFICATION.get());
        list.add(ExtraSkills.WIND_MANIPULATION.get());
        list.add(TenreincarnationSkill.TENGEKI.get());
        list.add(ResistanceSkills.MAGIC_NULLIFICATION.get());
        list.add(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
        list.add(ResistanceSkills.PIERCE_RESISTANCE.get());
        list.add(ResistanceSkills.SPATIAL_ATTACK_RESISTANCE.get());
        list.add(IntrinsicSkills.DIVINE_KI_RELEASE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return true;
    }

    @Override
    public boolean isDivine() {
        return true;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 200, 2, false, true));
            player.addEffect(new MobEffectInstance(MobEffects.DAMAGE_BOOST, 200, 2, false, true));
            
            if (player.getAbilities().mayfly) {
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        double epPercentage = Math.min(50.0, TensuraPlayerCapability.getBaseEP(player) * 100.0 / 1500000.0);

        if (epPercentage >= 50.0) {
            double essenceCount = 0.0;
            if (player instanceof net.minecraft.client.player.LocalPlayer) {
                net.minecraft.client.player.LocalPlayer localPlayer = (net.minecraft.client.player.LocalPlayer)player;
                essenceCount = (double)localPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.STAR_ESSENCE.get()));
            } else if (player instanceof net.minecraft.server.level.ServerPlayer) {
                net.minecraft.server.level.ServerPlayer serverPlayer = (net.minecraft.server.level.ServerPlayer)player;
                essenceCount = (double)serverPlayer.getStats().getValue(Stats.ITEM_USED.get(Tenreincarnationitems.STAR_ESSENCE.get()));
            }
            
            double essencePercentage = essenceCount * 50.0;
            return Math.min(100.0, epPercentage + essencePercentage);
        }
        
        return epPercentage;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        list.add(Component.translatable("tensura.evolution_menu.star_essence_requirement"));
        return list;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.AZRIL.get());
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        return new ArrayList<>();
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return null;
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }
} 