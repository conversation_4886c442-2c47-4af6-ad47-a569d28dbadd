package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.List;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class AntiSkill extends Skill {
   public AntiSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (instance.isToggled() || this.isInSlot(entity)) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource instanceof TensuraDamageSource) {
                  TensuraDamageSource source = (TensuraDamageSource)damageSource;
                  if (source.getIgnoreBarrier() >= 2.0F) {
                     return;
                  }
               }

               boolean var10000;
               label52: {
                  if (damageSource instanceof TensuraDamageSource) {
                     TensuraDamageSource source = (TensuraDamageSource)damageSource;
                     if (source.getSkill() != null || source.getMpCost() != 0.0D || source.getApCost() != 0.0D) {
                        var10000 = true;
                        break label52;
                     }
                  }

                  var10000 = false;
               }

               boolean anti = var10000;
               if (damageSource.m_19387_() || anti) {
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                  event.setCanceled(true);
               }
            }
         }
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (instance.isToggled() || this.isInSlot(entity)) {
         if (e.getSource().m_7640_() == entity) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource())) {
               if (entity.m_21205_().m_41619_() && entity.m_21206_().m_41619_()) {
                  LivingEntity target = e.getEntity();
                  AttributeInstance barrier = target.m_21051_((Attribute)TensuraAttributeRegistry.BARRIER.get());
                  if (barrier != null && !(barrier.m_22135_() <= 0.0D)) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11983_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     barrier.m_22132_();
                  }
               }
            }
         }
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (instance.isToggled() || this.isInSlot(entity)) {
         if (e.getSource().m_7640_() == entity) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource())) {
               if (instance.isMastered(entity)) {
                  if (!entity.m_21205_().m_41619_()) {
                     return;
                  }
               } else if (!entity.m_21205_().m_41619_() || !entity.m_21206_().m_41619_()) {
                  return;
               }

               LivingEntity target = e.getEntity();
               target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ANTI_SKILL.get(), 100, 0, false, false, false));
               SkillHelper.removePredicateEffect(target, (effect) -> {
                  return effect.m_19486_() && effect instanceof SkillMobEffect && !this.getNonSkillMobEffects().contains(effect);
               });
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_, 1.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      }
   }

   private List<MobEffect> getNonSkillMobEffects() {
      return List.of((MobEffect)TensuraMobEffects.AURA_SWORD.get(), (MobEffect)TensuraMobEffects.DIAMOND_PATH.get(), (MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get(), (MobEffect)TensuraMobEffects.BATS_MODE.get(), (MobEffect)TensuraMobEffects.MAGICULE_REGENERATION.get());
   }
}
