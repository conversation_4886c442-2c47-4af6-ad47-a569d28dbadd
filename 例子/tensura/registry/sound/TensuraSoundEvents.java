package com.github.manasmods.tensura.registry.sound;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraSoundEvents {
   private static final DeferredRegister<SoundEvent> registry;
   public static RegistryObject<SoundEvent> BIRD_DEATH;
   public static RegistryObject<SoundEvent> BIRD_FLAP;
   public static RegistryObject<SoundEvent> BIRD_HURT;
   public static RegistryObject<SoundEvent> CATERPILLAR_AMBIENT;
   public static RegistryObject<SoundEvent> LIZARD_AMBIENT;
   public static RegistryObject<SoundEvent> LIZARD_BABY_AMBIENT;
   public static RegistryObject<SoundEvent> LIZARD_HURT;
   public static RegistryObject<SoundEvent> LIZARD_ANGRY;
   public static RegistryObject<SoundEvent> MOTH_AMBIENT;
   public static RegistryObject<SoundEvent> MOTH_HURT;
   public static RegistryObject<SoundEvent> MOTH_DEATH;
   public static RegistryObject<SoundEvent> OWL_AMBIENT;
   public static RegistryObject<SoundEvent> PEACOCK_AMBIENT;
   public static RegistryObject<SoundEvent> EATING;
   public static RegistryObject<SoundEvent> SMALL_CHEW;
   public static RegistryObject<SoundEvent> SMALL_JUMP_IMPACT;
   public static RegistryObject<SoundEvent> NANODA;
   public static RegistryObject<SoundEvent> HEARTBEAT1;
   public static RegistryObject<SoundEvent> HEARTBEAT2;
   public static RegistryObject<SoundEvent> MC_DRIPLETS;
   public static RegistryObject<SoundEvent> MC_CAVE19;
   public static RegistryObject<SoundEvent> MC_ANIMAL1;
   public static RegistryObject<SoundEvent> MC_DARK1;
   public static RegistryObject<SoundEvent> MC_DARK4;
   public static RegistryObject<SoundEvent> MC_VOICES3;
   public static RegistryObject<SoundEvent> MC_VOICES4;
   public static RegistryObject<SoundEvent> MC_WHISPER1;
   public static RegistryObject<SoundEvent> MC_ADDITION6;

   private static RegistryObject<SoundEvent> registerSoundEvent(String name) {
      return registry.register(name, () -> {
         return new SoundEvent(new ResourceLocation("tensura", name));
      });
   }

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.SOUND_EVENTS, "tensura");
      BIRD_DEATH = registerSoundEvent("bird_death");
      BIRD_FLAP = registerSoundEvent("bird_flap");
      BIRD_HURT = registerSoundEvent("bird_hurt");
      CATERPILLAR_AMBIENT = registerSoundEvent("caterpillar_ambient");
      LIZARD_AMBIENT = registerSoundEvent("lizard_ambient");
      LIZARD_BABY_AMBIENT = registerSoundEvent("lizard_baby_ambient");
      LIZARD_HURT = registerSoundEvent("lizard_hurt");
      LIZARD_ANGRY = registerSoundEvent("lizard_angry");
      MOTH_AMBIENT = registerSoundEvent("moth_ambient");
      MOTH_HURT = registerSoundEvent("moth_hurt");
      MOTH_DEATH = registerSoundEvent("moth_death");
      OWL_AMBIENT = registerSoundEvent("owl_ambient");
      PEACOCK_AMBIENT = registerSoundEvent("peacock_ambient");
      EATING = registerSoundEvent("eat");
      SMALL_CHEW = registerSoundEvent("small_chew");
      SMALL_JUMP_IMPACT = registerSoundEvent("small_jump_impact");
      NANODA = registerSoundEvent("nanoda");
      HEARTBEAT1 = registerSoundEvent("heartbeat1");
      HEARTBEAT2 = registerSoundEvent("heartbeat2");
      MC_DRIPLETS = registerSoundEvent("driplets1");
      MC_CAVE19 = registerSoundEvent("cave19");
      MC_ANIMAL1 = registerSoundEvent("animal1");
      MC_DARK1 = registerSoundEvent("dark1");
      MC_DARK4 = registerSoundEvent("dark4");
      MC_VOICES3 = registerSoundEvent("voices3");
      MC_VOICES4 = registerSoundEvent("voices4");
      MC_WHISPER1 = registerSoundEvent("whisper1");
      MC_ADDITION6 = registerSoundEvent("addition6");
   }
}
