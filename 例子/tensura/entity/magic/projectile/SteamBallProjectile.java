package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class SteamBallProjectile extends WaterBallProjectile {
   public SteamBallProjectile(EntityType<? extends SteamBallProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public SteamBallProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.STEAM_BALL.get(), levelIn);
      this.m_5602_(shooter);
   }

   public boolean shouldDiscardInWater() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/steam_ball.png")};
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12317_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123796_, x, y, z, 10, 0.08D, 0.08D, 0.08D, 0.1D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.STEAM_EFFECT.get(), x, y, z, 40, 0.08D, 0.08D, 0.08D, 0.1D, true);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.STEAM_EFFECT.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);

      for(int i = 0; i < 2; ++i) {
         Vec3 random = this.vec3Random().m_82490_(0.05000000074505806D);
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.STEAM_EFFECT.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, random.f_82479_, random.f_82480_, random.f_82481_);
      }

   }
}
