package com.github.manasmods.tensura.ability.magic.spiritual.light;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SolarBeamMagic extends SpiritualMagic {
   public SolarBeamMagic() {
      super(MagicElemental.LIGHT, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 40;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 200.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("BeamID", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         if (heldTicks % 100 == 0 && heldTicks > castTime) {
            this.addMasteryPoint(instance, entity);
         }

         if (heldTicks >= castTime) {
            double cost = this.magiculeCost(entity, instance);
            BeamProjectile.spawnLastingBeam((EntityType)TensuraEntityTypes.SOLAR_BEAM.get(), 20.0F, 1.0F, entity, instance, cost, cost, heldTicks);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 0.8F, 0.5F);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            this.addCastingParticle(instance, player, heldTicks);
         }

         return true;
      }
   }
}
