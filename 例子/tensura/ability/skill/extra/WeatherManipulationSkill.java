package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class WeatherManipulationSkill extends Skill {
   public WeatherManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get())) {
         return false;
      } else {
         return !SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.WIND_MANIPULATION.get()) ? false : SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.LIGHTNING_MANIPULATION.get());
      }
   }

   public double learningCost() {
      return 1000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.clear");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.rain");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.weather_manipulation.thunder");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 1000.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.WEATHER_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level var4 = entity.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)var4;
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            if (entity.f_19853_.m_46472_().equals(Level.f_46428_)) {
               if (entity.f_19853_.m_45527_(new BlockPos(entity.m_146892_()))) {
                  boolean success = false;
                  switch(instance.getMode()) {
                  case 1:
                     if (level.m_46471_() || level.m_46470_()) {
                        level.m_8606_(12000, 0, false, false);
                        success = true;
                     }
                     break;
                  case 2:
                     if (!level.m_46471_() || level.m_46470_()) {
                        level.m_8606_(0, 12000, true, false);
                        success = true;
                     }
                     break;
                  case 3:
                     if (!level.m_46470_()) {
                        level.m_8606_(0, 12000, true, true);
                        success = true;
                     }
                  }

                  if (success) {
                     this.addMasteryPoint(instance, entity);
                     instance.setCoolDown(instance.isMastered(entity) ? 5 : 10);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 0.5F, 1.0F);
                     TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                        return entity;
                     }), new RequestFxSpawningPacket(new ResourceLocation("tensura:weather_manipulation"), entity.m_19879_(), 0.0D, (double)entity.m_20192_(), 0.0D, true));
                  }

               }
            }
         }
      }
   }
}
