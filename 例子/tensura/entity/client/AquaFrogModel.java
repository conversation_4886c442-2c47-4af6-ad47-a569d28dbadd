package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.AquaFrogEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class AquaFrogModel extends AnimatedGeoModel<AquaFrogEntity> {
   public ResourceLocation getModelResource(AquaFrogEntity object) {
      return new ResourceLocation("tensura", "geo/aqua_frog.geo.json");
   }

   public ResourceLocation getTextureResource(AquaFrogEntity instance) {
      return instance.m_7770_() != null && instance.m_7770_().getString().equalsIgnoreCase("Kermit") ? new ResourceLocation("tensura", "textures/entity/aqua_frog/kermit.png") : new ResourceLocation("tensura", "textures/entity/aqua_frog/aqua_frog.png");
   }

   public ResourceLocation getAnimationResource(AquaFrogEntity entity) {
      return new ResourceLocation("tensura", "animations/aqua_frog.animation.json");
   }
}
