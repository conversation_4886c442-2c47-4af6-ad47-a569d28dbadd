package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum PeacockVariant {
   DEFAULT(0),
   ALTERNATIVE(1);

   private static final PeacockVariant[] BY_ID = (PeacockVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(PeacockVariant::getId)).toArray((x$0) -> {
      return new PeacockVariant[x$0];
   });
   private final int id;
   public static final Map<PeacockVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(PeacockVariant.class), (variant) -> {
      variant.put(DEFAULT, new ResourceLocation("tensura", "textures/entity/dragon_peacock/dragon_peacock_1.png"));
      variant.put(ALTERNATIVE, new ResourceLocation("tensura", "textures/entity/dragon_peacock/dragon_peacock_2.png"));
   });

   private PeacockVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static PeacockVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static PeacockVariant[] $values() {
      return new PeacockVariant[]{DEFAULT, ALTERNATIVE};
   }
}
