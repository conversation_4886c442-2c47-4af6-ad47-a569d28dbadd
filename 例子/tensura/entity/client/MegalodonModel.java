package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.MegalodonEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class MegalodonModel extends AnimatedGeoModel<MegalodonEntity> {
   public ResourceLocation getModelResource(MegalodonEntity object) {
      return new ResourceLocation("tensura", "geo/megalodon.geo.json");
   }

   public ResourceLocation getTextureResource(MegalodonEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/megalodon/megalodon.png");
   }

   public ResourceLocation getAnimationResource(MegalodonEntity entity) {
      return new ResourceLocation("tensura", "animations/megalodon.animation.json");
   }

   public void setCustomAnimations(MegalodonEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      IBone chests = this.getAnimationProcessor().getBone("Chests");
      if (entity.isChested() == chests.isHidden()) {
         chests.setHidden(!entity.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (entity.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!entity.isSaddled());
      }

      IBone saddleSpikes = this.getAnimationProcessor().getBone("SaddleSpike");
      if (entity.isSaddled() == !saddleSpikes.isHidden()) {
         saddleSpikes.setHidden(entity.isSaddled());
      }

   }
}
