package com.github.manasmods.tensura.entity.variant;

import com.github.manasmods.tensura.entity.SlimeEntity;
import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.DyeColor;

public enum SlimeVariant {
   DEFAULT(0),
   RED(1),
   YELLOW(2),
   LIME(3),
   CYAN(4),
   PINK(5),
   WHITE(6),
   <PERSON><PERSON>UE(7),
   <PERSON>RE<PERSON>(8),
   ORANGE(9),
   <PERSON><PERSON><PERSON>(10),
   LIGHT_GRAY(11),
   MAGENTA(12),
   PURPLE(13),
   BROWN(14),
   BLACK(15);

   private static final SlimeVariant[] BY_ID = (SlimeVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(SlimeVariant::getId)).toArray((x$0) -> {
      return new SlimeVariant[x$0];
   });
   private final int id;
   public static final Map<SlimeVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(SlimeVariant.class), (variant) -> {
      variant.put(DEFAULT, new ResourceLocation("tensura", "textures/entity/slime/slime.png"));
      variant.put(RED, new ResourceLocation("tensura", "textures/entity/slime/slime_red.png"));
      variant.put(YELLOW, new ResourceLocation("tensura", "textures/entity/slime/slime_yellow.png"));
      variant.put(GREEN, new ResourceLocation("tensura", "textures/entity/slime/slime_green.png"));
      variant.put(WHITE, new ResourceLocation("tensura", "textures/entity/slime/slime_white.png"));
      variant.put(ORANGE, new ResourceLocation("tensura", "textures/entity/slime/slime_orange.png"));
      variant.put(MAGENTA, new ResourceLocation("tensura", "textures/entity/slime/slime_magenta.png"));
      variant.put(BLUE, new ResourceLocation("tensura", "textures/entity/slime/slime_blue.png"));
      variant.put(LIME, new ResourceLocation("tensura", "textures/entity/slime/slime_lime.png"));
      variant.put(PINK, new ResourceLocation("tensura", "textures/entity/slime/slime_pink.png"));
      variant.put(GRAY, new ResourceLocation("tensura", "textures/entity/slime/slime_gray.png"));
      variant.put(LIGHT_GRAY, new ResourceLocation("tensura", "textures/entity/slime/slime_light_gray.png"));
      variant.put(CYAN, new ResourceLocation("tensura", "textures/entity/slime/slime_cyan.png"));
      variant.put(PURPLE, new ResourceLocation("tensura", "textures/entity/slime/slime_purple.png"));
      variant.put(BROWN, new ResourceLocation("tensura", "textures/entity/slime/slime_brown.png"));
      variant.put(BLACK, new ResourceLocation("tensura", "textures/entity/slime/slime_black.png"));
   });
   public static final Map<SlimeVariant, DyeColor> DYE_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(SlimeVariant.class), (variant) -> {
      variant.put(DEFAULT, DyeColor.LIGHT_BLUE);
      variant.put(RED, DyeColor.RED);
      variant.put(YELLOW, DyeColor.YELLOW);
      variant.put(GREEN, DyeColor.GREEN);
      variant.put(WHITE, DyeColor.WHITE);
      variant.put(ORANGE, DyeColor.ORANGE);
      variant.put(MAGENTA, DyeColor.MAGENTA);
      variant.put(BLUE, DyeColor.BLUE);
      variant.put(LIME, DyeColor.LIME);
      variant.put(PINK, DyeColor.PINK);
      variant.put(GRAY, DyeColor.GRAY);
      variant.put(LIGHT_GRAY, DyeColor.LIGHT_GRAY);
      variant.put(CYAN, DyeColor.CYAN);
      variant.put(PURPLE, DyeColor.PURPLE);
      variant.put(BROWN, DyeColor.BROWN);
      variant.put(BLACK, DyeColor.BLACK);
   });

   private SlimeVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static SlimeVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   public static void setVariantFromColor(SlimeEntity slimeEntity, DyeColor color) {
      Iterator var2 = DYE_BY_VARIANT.entrySet().iterator();

      while(var2.hasNext()) {
         Entry<SlimeVariant, DyeColor> slimeVariantItemEntry = (Entry)var2.next();
         if (color.equals(slimeVariantItemEntry.getValue())) {
            slimeEntity.setVariant((SlimeVariant)slimeVariantItemEntry.getKey());
         }
      }

   }

   // $FF: synthetic method
   private static SlimeVariant[] $values() {
      return new SlimeVariant[]{DEFAULT, RED, YELLOW, LIME, CYAN, PINK, WHITE, BLUE, GREEN, ORANGE, GRAY, LIGHT_GRAY, MAGENTA, PURPLE, BROWN, BLACK};
   }
}
