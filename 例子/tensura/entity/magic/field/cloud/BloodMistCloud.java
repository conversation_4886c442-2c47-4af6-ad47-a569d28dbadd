package com.github.manasmods.tensura.entity.magic.field.cloud;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;

public class BloodMistCloud extends AreaCloud {
   public BloodMistCloud(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public BloodMistCloud(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.BLOOD_MIST.get(), level);
      this.m_5602_(entity);
   }

   public void applyEffect(LivingEntity target, boolean instant) {
      if (!RaceHelper.hasNoBlood(target)) {
         Entity owner = this.m_37282_();
         if (owner instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)owner;
            if (target.m_7307_(living) || target.m_7306_(living)) {
               return;
            }
         }

         DamageSource source = owner == null ? TensuraDamageSources.BLOOD_DRAIN : TensuraDamageSources.bloodDrain(owner);
         if (target.m_6469_(DamageSourceHelper.addSkillAndCost(source, this.getMpCost() / 30.0D, this.getSkill()), this.getDamage()) && owner instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)owner;
            living.m_5634_(this.getDamage());
         }

      }
   }

   public void bloodExplosion() {
      if (!this.m_9236_().m_5776_()) {
         List<LivingEntity> targets = this.m_9236_().m_45976_(LivingEntity.class, this.m_20191_());
         Iterator var2 = targets.iterator();

         while(true) {
            LivingEntity target;
            Entity owner;
            LivingEntity living;
            do {
               do {
                  do {
                     if (!var2.hasNext()) {
                        this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
                        ((ServerLevel)this.f_19853_).m_8767_(ParticleTypes.f_123747_, this.m_20185_(), this.m_20186_(), this.m_20189_(), 3, 0.08D, 0.08D, 0.08D, 0.15D);
                        TensuraParticleHelper.serverParticleCloud(this.m_9236_(), this.f_19796_, ParticleTypes.f_123813_, this.m_20185_(), this.m_20186_() + 0.5D, this.m_20189_(), 0.01D, 0.5D, 4.0D);
                        this.m_146870_();
                        return;
                     }

                     target = (LivingEntity)var2.next();
                  } while(!this.m_5603_(target));
               } while(!(target.m_20275_(this.m_20185_(), this.m_20186_(), this.m_20189_()) < (double)(this.getRadius() * this.getRadius())));

               owner = this.m_37282_();
               if (!(owner instanceof LivingEntity)) {
                  break;
               }

               living = (LivingEntity)owner;
            } while(target.m_7307_(living) || target.m_7306_(living));

            DamageSource source = owner == null ? TensuraDamageSources.BLOOD_DRAIN : TensuraDamageSources.bloodDrain(owner);
            target.m_6469_(DamageSourceHelper.addSkillAndCost(source, this.getMpCost(), this.getSkill()), this.getDamage() * 10.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
         }
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.m_9236_().m_5776_()) {
         TensuraParticleHelper.particleCloud(this.m_9236_(), this.f_19796_, DustParticleOptions.f_123656_, this.m_20185_(), this.m_20186_() + 0.5D, this.m_20189_(), 0.01D, 0.15D, (double)this.getVisualRadius());
      }

   }
}
