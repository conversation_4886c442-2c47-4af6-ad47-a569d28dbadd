package com.github.b4ndithelps.tennogamenolife.ability.skill.unique;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import net.minecraft.client.Minecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ShovelItem;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraft.network.chat.Component;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.LevelRenderer;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 连锁铲独特技能
 * 当玩家开启技能后使用铲子挖掘泥土、沙子等方块时，会连锁挖掘相同类型的方块
 */
@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID)
public class ChainShovelSkill extends Skill {

    // 基础配置
    private final double magiculeCost = 1000.0; // 每次连锁挖掘消耗的魔素
    
    // 技能模式枚举
    public enum ShovelMode {
        CHAIN("chain"), // 默认连锁模式
        HORIZONTAL("horizontal"), // 平挖模式 
        VERTICAL("vertical"); // 垂直挖模式
        
        private final String id;
        
        ShovelMode(String id) {
            this.id = id;
        }
        
        public String getId() {
            return id;
        }
        
        public String getTranslationKey() {
            return "skill.tennogamenolife.chain_shovel.mode." + id;
        }
    }

    // 存储每个玩家的技能激活状态
    private static final Map<UUID, Boolean> playerActiveStates = new HashMap<>();
    
    // 存储将被连锁挖掘的方块位置，用于渲染预览
    private static Set<BlockPos> highlightedBlocks = new HashSet<>();

    public ChainShovelSkill() {
        super(SkillType.UNIQUE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/unique/chain_shovel.png");
    }

    @Override
    public boolean meetEPRequirement(Player entity, double curEP) {
        return false; // 无EP要求
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return magiculeCost;
    }
    
    /**
     * 获取技能模式数量
     */
    @Override
    public int modes() {
        return 3; // 三种模式: 连锁、水平、垂直
    }
    
    /**
     * 返回下一个模式的索引
     */
    @Override
    public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
        // 检查是否已熟练
        if (!instance.isMastered(entity)) {
            // 未熟练时只能使用默认模式
            return 1;
        }
        
        // 根据当前模式和反转标志决定下一个模式
        if (reverse) {
            // 反向切换
            switch (instance.getMode()) {
                case 1: return 3; // 连锁 -> 垂直
                case 2: return 1; // 水平 -> 连锁
                case 3: return 2; // 垂直 -> 水平
                default: return 1;
            }
        } else {
            // 正向切换
            switch (instance.getMode()) {
                case 1: return 2; // 连锁 -> 水平
                case 2: return 3; // 水平 -> 垂直
                case 3: return 1; // 垂直 -> 连锁
                default: return 1;
            }
        }
    }
    
    /**
     * 获取当前模式的名称
     */
    @Override
    public Component getModeName(int mode) {
        return switch (mode) {
            case 1 -> Component.translatable("skill.tennogamenolife.chain_shovel.mode.chain");
            case 2 -> Component.translatable("skill.tennogamenolife.chain_shovel.mode.horizontal");
            case 3 -> Component.translatable("skill.tennogamenolife.chain_shovel.mode.vertical");
            default -> Component.translatable("skill.tennogamenolife.chain_shovel.mode.chain");
        };
    }
    
    /**
     * 获取当前挖掘模式
     */
    private ShovelMode getCurrentMode(ManasSkillInstance instance) {
        if (instance instanceof TensuraSkillInstance tensuraInstance) {
            int modeIndex = tensuraInstance.getMode();
            return switch (modeIndex) {
                case 2 -> ShovelMode.HORIZONTAL;
                case 3 -> ShovelMode.VERTICAL;
                default -> ShovelMode.CHAIN;
            };
        }
        
        // 旧方法作为后备
        CompoundTag nbt = instance.getOrCreateTag();
        if (!nbt.contains("ShovelMode")) {
            nbt.putString("ShovelMode", ShovelMode.CHAIN.getId());
        }
        String modeStr = nbt.getString("ShovelMode");
        for (ShovelMode mode : ShovelMode.values()) {
            if (mode.getId().equals(modeStr)) {
                return mode;
            }
        }
        return ShovelMode.CHAIN; // 默认值
    }
    
    /**
     * 设置当前挖掘模式
     */
    private void setCurrentMode(ManasSkillInstance instance, ShovelMode mode) {
        if (instance instanceof TensuraSkillInstance tensuraInstance) {
            int modeIndex = switch (mode) {
                case HORIZONTAL -> 2;
                case VERTICAL -> 3;
                default -> 1;
            };
            tensuraInstance.setMode(modeIndex);
        } else {
            // 旧方法作为后备
            CompoundTag nbt = instance.getOrCreateTag();
            nbt.putString("ShovelMode", mode.getId());
            instance.markDirty();
        }
    }
    
    /**
     * 切换到下一个挖掘模式 - 保留作为兼容性方法
     */
    private void cycleToNextMode(ManasSkillInstance instance, LivingEntity entity) {
        if (!(entity instanceof Player player)) return;
        
        // 检查熟练度，确保已达到满级
        int currentMastery = instance.getMastery();
        int maxMastery = instance.getMaxMastery();
        boolean isMastered = currentMastery >= maxMastery;
        
        if (!isMastered) {
            if (player instanceof ServerPlayer) {
                player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_shovel.mode.locked"), true);
            }
            return;
        }
        
        ShovelMode currentMode = getCurrentMode(instance);
        ShovelMode nextMode;
        
        // 切换到下一个模式
        switch (currentMode) {
            case CHAIN:
                nextMode = ShovelMode.HORIZONTAL;
                break;
            case HORIZONTAL:
                nextMode = ShovelMode.VERTICAL;
                break;
            case VERTICAL:
            default:
                nextMode = ShovelMode.CHAIN;
                break;
        }
        
        setCurrentMode(instance, nextMode);
        
        // 通知玩家
        if (player instanceof ServerPlayer) {
            player.displayClientMessage(
                Component.translatable("skill.tennogamenolife.chain_shovel.mode.switch", 
                                       Component.translatable(nextMode.getTranslationKey())), 
                true
            );
        }
        
        // 播放模式切换音效
        Level level = entity.getLevel();
        level.playSound(
            null, entity.getX(), entity.getY(), entity.getZ(),
            SoundEvents.UI_BUTTON_CLICK, SoundSource.PLAYERS, 0.6F, 1.2F
        );
    }

    /**
     * 当技能按键被按下时触发
     */
    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (entity instanceof Player player) {
            if (player.isShiftKeyDown()) {
                // Shift+技能键：切换模式
                // 注意：这个已经被核心模组的模式切换机制取代，但保留作为后备
                cycleToNextMode(instance, entity);
            } else {
                // 普通技能键：切换该玩家的技能激活状态
                UUID playerId = player.getUUID();
                boolean currentState = playerActiveStates.getOrDefault(playerId, false);
                boolean newState = !currentState;
                playerActiveStates.put(playerId, newState);

                // 清除高亮方块
                highlightedBlocks.clear();

                // 播放音效
                Level level = entity.getLevel();
                level.playSound(
                    null, entity.getX(), entity.getY(), entity.getZ(),
                    newState ? SoundEvents.BEACON_ACTIVATE : SoundEvents.BEACON_DEACTIVATE,
                    SoundSource.PLAYERS, 0.6F, 1.0F
                );

                // 通知玩家
                if (player instanceof ServerPlayer) {
                    String messageKey = newState ? "skill.tennogamenolife.chain_shovel.enabled" : "skill.tennogamenolife.chain_shovel.disabled";
                    player.displayClientMessage(Component.translatable(messageKey), true);
                }
            }
        }
    }

    /**
     * 静态方法，处理方块破坏事件
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        // 获取玩家
        Player player = event.getPlayer();
        if (player == null) return;

        // 检查该玩家的技能是否激活
        UUID playerId = player.getUUID();
        if (!playerActiveStates.getOrDefault(playerId, false)) return;
        
        // 检查玩家手中的工具是否是铲子
        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof ShovelItem)) return;
        
        // 获取当前破坏的方块信息
        BlockPos pos = event.getPos();
        Level level = player.getLevel();
        BlockState targetState = event.getState();
        Block targetBlock = targetState.getBlock();
        
        // 检查方块是否是铲子可挖掘的方块
        if (!isDirtLike(targetState, mainHandItem)) return;
        
        // 获取技能实例
        ChainShovelSkill skill = (ChainShovelSkill) TenreincarnationSkill.CHAIN_SHOVEL.get();
        
        // 尝试获取玩家的技能实例
        SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
            // 如果魔素不足，不执行连锁挖掘
            if (SkillHelper.outOfMagicule(player, instance)) {
                if (player instanceof ServerPlayer) {
                    player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_shovel.no_magicule"), true);
                }
                return;
            }
            
            // 根据当前模式执行不同的挖掘行为
            ShovelMode currentMode = skill.getCurrentMode(instance);
            Set<BlockPos> blocksToBreak = new HashSet<>();
            
            // 获取玩家视角方向
            Direction hitFace = Direction.UP; // 默认值
            
            // 尝试获取玩家瞄准的方块面
            HitResult hitResult = player.pick(20.0, 0.0F, false);
            if (hitResult instanceof BlockHitResult blockHit) {
                hitFace = blockHit.getDirection();
            }
            
            switch (currentMode) {
                case CHAIN:
                    // 连锁模式：找到所有连接的相同方块
                    int maxBlocks = instance.isMastered(player) ? 24 : 16;
                    blocksToBreak = findChainedBlocks(level, pos, targetBlock, maxBlocks);
                    break;
                case HORIZONTAL:
                    // 平面模式：根据玩家视角决定挖掘平面
                    int planeRange = instance.isMastered(player) ? 3 : 2;
                    blocksToBreak = findPlaneBlocks(level, pos, targetBlock, planeRange, hitFace);
                    break;
                case VERTICAL:
                    // 垂直挖模式：垂直方向的方块
                    int verticalRange = instance.isMastered(player) ? 5 : 3;
                    blocksToBreak = findVerticalBlocks(level, pos, targetBlock, verticalRange);
                    break;
            }
            
            // 移除起始位置（已经由原始事件处理）
            blocksToBreak.remove(pos);
            
            // 如果找到了需要挖掘的方块，消耗魔素
            if (!blocksToBreak.isEmpty()) {
                // 消耗魔素（只有真正进行了挖掘才消耗）
                // 由于消耗魔素的机制是通过冷却时间来实现的，所以设置一个短暂的冷却
                instance.setCoolDown(1);
                
                // 获取铲子的附魔效果
                int fortuneLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_FORTUNE, mainHandItem);
                int silkTouchLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.SILK_TOUCH, mainHandItem);
                
                // 破坏找到的方块
                for (BlockPos blockPos : blocksToBreak) {
                    // 检查方块是否可以被当前工具破坏
                    BlockState state = level.getBlockState(blockPos);
                    
                    // 跳过不是目标类型的方块
                    if (!isDirtLike(state, mainHandItem)) continue;
                    
                    // 使用铲子破坏方块，同时应用附魔效果
                    if (level instanceof ServerLevel serverLevel) {
                        // 使用适当的方法来模拟正确的掉落物
                        Block block = state.getBlock();
                        
                        // 应用时运或精准采集
                        if (silkTouchLevel > 0) {
                            // 精准采集 - 直接掉落方块本身
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        } else {
                            // 时运 - 增加掉落数量
                            Block.dropResources(state, level, blockPos, null, player, mainHandItem);
                        }
                        
                        // 破坏方块但不生成掉落物(因为我们已经手动处理了掉落物)
                        level.removeBlock(blockPos, false);
                    }
                    
                    // 对工具造成耐久损耗
                    if (!player.isCreative()) {
                        mainHandItem.hurtAndBreak(1, player, (p) -> p.broadcastBreakEvent(player.getUsedItemHand()));
                        
                        // 如果工具损坏了，终止挖掘
                        if (mainHandItem.isEmpty()) break;
                    }
                }
                
                // 播放挖掘成功的声音
                level.playSound(null, player.getX(), player.getY(), player.getZ(), 
                    SoundEvents.GRAVEL_BREAK, SoundSource.BLOCKS, 0.5F, 0.8F);
                
                // 增加技能熟练度
                if (!player.isCreative()) {
                    // 熟练度增加量 = 基础值1 + 额外连锁方块数量 / 10（向下取整，最小为1）
                    int masteryToAdd = 1 + Math.max(1, blocksToBreak.size() / 10);
                    addSkillMasteryPoints(instance, player, masteryToAdd);
                }
            }
        });
    }

    /**
     * 检查方块是否是铲子可挖掘的方块类型
     */
    private static boolean isDirtLike(BlockState state, ItemStack tool) {
        // 检查方块是否适合用铲子挖掘
        float destroySpeed = state.getDestroySpeed(null, BlockPos.ZERO);
        
        // 方块硬度为-1表示无法破坏
        if (destroySpeed < 0) return false;
        
        // 使用标签系统检查方块是否属于铲子可挖掘类型
        return state.is(net.minecraft.tags.BlockTags.MINEABLE_WITH_SHOVEL) ||
               // 检查特定方块类型，常见的泥土类方块
               state.is(net.minecraft.tags.BlockTags.DIRT) ||
               state.is(net.minecraft.tags.BlockTags.SAND) ||
               // 对于其他情况，尝试获取方块破坏速度
               tool.getItem().getDestroySpeed(tool, state) > 1.0F;
    }
    
    /**
     * 增加技能熟练度（静态方法版本）
     */
    private static void addSkillMasteryPoints(ManasSkillInstance instance, LivingEntity entity, int amount) {
        // 获取当前和最大熟练度
        int currentMastery = instance.getMastery();
        int maxMastery = instance.getMaxMastery();
        
        // 如果已经达到满级，不再增加
        if (currentMastery >= maxMastery) {
            return;
        }
        
        // 计算新熟练度
        int newMastery = Math.min(currentMastery + amount, maxMastery);
        instance.setMastery(newMastery);
        
        if (newMastery >= maxMastery && entity instanceof Player player) {
            player.displayClientMessage(Component.translatable("skill.tennogamenolife.chain_shovel.mastered"), true);
            
            entity.getLevel().playSound(null, entity.getX(), entity.getY(), entity.getZ(),
                SoundEvents.PLAYER_LEVELUP, SoundSource.PLAYERS, 0.75F, 1.0F);
        }
    }

    /**
     * 查找连锁的相同类型方块
     */
    private static Set<BlockPos> findChainedBlocks(Level level, BlockPos startPos, Block targetBlock, int maxBlocks) {
        Set<BlockPos> result = new HashSet<>();
        Set<BlockPos> visited = new HashSet<>();
        List<BlockPos> queue = new ArrayList<>();
        
        queue.add(startPos);
        visited.add(startPos);
        
        while (!queue.isEmpty() && result.size() < maxBlocks) {
            BlockPos current = queue.remove(0);
            
            if (level.getBlockState(current).getBlock() == targetBlock) {
                result.add(current);
                
                for (int x = -1; x <= 1; x++) {
                    for (int y = -1; y <= 1; y++) {
                        for (int z = -1; z <= 1; z++) {
                            if (x == 0 && y == 0 && z == 0) continue;
                            
                            BlockPos neighbor = current.offset(x, y, z);
                            if (!visited.contains(neighbor)) {
                                visited.add(neighbor);
                                if (level.getBlockState(neighbor).getBlock() == targetBlock) {
                                    queue.add(neighbor);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找平面上的相同类型方块
     * 根据玩家视角决定挖掘平面
     */
    private static Set<BlockPos> findPlaneBlocks(Level level, BlockPos startPos, Block targetBlock, int range, Direction hitFace) {
        Set<BlockPos> result = new HashSet<>();
        
        result.add(startPos);
        
        Direction.Axis excludedAxis;
        
        if (hitFace.getAxis() == Direction.Axis.Y) {
            excludedAxis = Direction.Axis.Y;
        } else if (hitFace.getAxis() == Direction.Axis.X) {
            excludedAxis = Direction.Axis.X;
        } else {
            excludedAxis = Direction.Axis.Z;
        }

        for (int dx = -range; dx <= range; dx++) {
            for (int dy = -range; dy <= range; dy++) {
                for (int dz = -range; dz <= range; dz++) {
                    if (dx == 0 && dy == 0 && dz == 0) continue;

                    if (excludedAxis == Direction.Axis.X && dx != 0) continue;
                    if (excludedAxis == Direction.Axis.Y && dy != 0) continue;
                    if (excludedAxis == Direction.Axis.Z && dz != 0) continue;
                    
                    BlockPos pos = startPos.offset(dx, dy, dz);

                    if (level.getBlockState(pos).getBlock() == targetBlock) {
                        result.add(pos);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找垂直线上的相同类型方块
     */
    private static Set<BlockPos> findVerticalBlocks(Level level, BlockPos startPos, Block targetBlock, int range) {
        Set<BlockPos> result = new HashSet<>();

        result.add(startPos);

        for (int y = 1; y <= range; y++) {
            BlockPos pos = startPos.above(y);
            if (level.getBlockState(pos).getBlock() == targetBlock) {
                result.add(pos);
            } else {

                break;
            }
        }
        
        // 向下搜索
        for (int y = 1; y <= range; y++) {
            BlockPos pos = startPos.below(y);
            if (level.getBlockState(pos).getBlock() == targetBlock) {
                result.add(pos);
            } else {

                break;
            }
        }
        
        return result;
    }
    
    /**
     * 客户端Tick事件，用于更新高亮方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;
        
        Minecraft mc = Minecraft.getInstance();
        Player player = mc.player;

        highlightedBlocks.clear();

        // 如果玩家不存在，直接返回
        if (player == null) return;

        // 检查该玩家的技能是否激活
        UUID playerId = player.getUUID();
        if (!playerActiveStates.getOrDefault(playerId, false)) return;

        ItemStack mainHandItem = player.getMainHandItem();
        if (!(mainHandItem.getItem() instanceof ShovelItem)) return;

        HitResult hitResult = mc.hitResult;
        if (hitResult == null || hitResult.getType() != HitResult.Type.BLOCK) return;
        
        BlockHitResult blockHit = (BlockHitResult) hitResult;
        BlockPos hitPos = blockHit.getBlockPos();
        Level level = player.getLevel();
        BlockState hitState = level.getBlockState(hitPos);

        if (isDirtLike(hitState, mainHandItem)) {
            Block targetBlock = hitState.getBlock();

            ChainShovelSkill skill = (ChainShovelSkill) TenreincarnationSkill.CHAIN_SHOVEL.get();
            SkillAPI.getSkillsFrom(player).getSkill(skill).ifPresent(instance -> {
                ShovelMode currentMode = skill.getCurrentMode(instance);

                Direction hitFace = Direction.UP;
                if (blockHit != null) {
                    hitFace = blockHit.getDirection();
                }
                
                switch (currentMode) {
                    case CHAIN:
                        int chainMaxBlocks = instance.isMastered(player) ? 24 : 16;
                        highlightedBlocks = findChainedBlocks(level, hitPos, targetBlock, chainMaxBlocks);
                        break;
                    case HORIZONTAL:
                        int planeRange = instance.isMastered(player) ? 3 : 2;
                        highlightedBlocks = findPlaneBlocks(level, hitPos, targetBlock, planeRange, hitFace);
                        break;
                    case VERTICAL:
                        int verticalRange = instance.isMastered(player) ? 5 : 3;
                        highlightedBlocks = findVerticalBlocks(level, hitPos, targetBlock, verticalRange);
                        break;
                }
            });
        }
    }
    
    /**
     * 渲染事件，用于高亮连锁挖掘的方块
     */
    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onRenderLevel(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_TRANSLUCENT_BLOCKS) return;
        if (highlightedBlocks.isEmpty()) return;
        
        Minecraft mc = Minecraft.getInstance();
        PoseStack poseStack = event.getPoseStack();

        double cameraX = mc.gameRenderer.getMainCamera().getPosition().x;
        double cameraY = mc.gameRenderer.getMainCamera().getPosition().y;
        double cameraZ = mc.gameRenderer.getMainCamera().getPosition().z;

        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.disableTexture();

        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer builder = bufferSource.getBuffer(RenderType.lines());

        for (BlockPos pos : highlightedBlocks) {
            poseStack.pushPose();
            poseStack.translate(pos.getX() - cameraX, pos.getY() - cameraY, pos.getZ() - cameraZ);

            LevelRenderer.renderLineBox(
                poseStack,
                builder,
                0, 0, 0, 1, 1, 1,
                1.0f, 1.0f, 1.0f, 0.5f // RGBA: 白色，50%透明
            );
            
            poseStack.popPose();
        }

        bufferSource.endBatch(RenderType.lines());
        RenderSystem.enableTexture();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }
} 