package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HoundDogEntity;
import com.github.manasmods.tensura.entity.variant.HoundDogVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class HoundDogModel extends AnimatedGeoModel<HoundDogEntity> {
   public ResourceLocation getModelResource(HoundDogEntity object) {
      return new ResourceLocation("tensura", "geo/hound_dog.geo.json");
   }

   public ResourceLocation getTextureResource(HoundDogEntity instance) {
      return instance.isSnakeControlled() ? new ResourceLocation("tensura", "textures/entity/hound_dog/hound_dog_snake_controlled.png") : (ResourceLocation)HoundDogVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public ResourceLocation getAnimationResource(HoundDogEntity moth) {
      return new ResourceLocation("tensura", "animations/hound_dog.animation.json");
   }

   public void setCustomAnimations(HoundDogEntity dog, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(dog, instanceId, customPredicate);
      if (!dog.isSnakeControlled()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
