package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.text.DecimalFormat;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

public class HakiSkill extends Skill {
   private static final String HAKI = "892b7bc5-af0a-4d04-860c-cd7ead8dc2d7";
   private static final DecimalFormat decimalFormat = new DecimalFormat("#.#");

   public HakiSkill() {
      super(Skill.SkillType.EXTRA);
      this.addHeldAttributeModifier(Attributes.f_22279_, "892b7bc5-af0a-4d04-860c-cd7ead8dc2d7", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)CommonSkills.COERCION.get())) {
         return false;
      } else {
         return newEP > 100000.0D;
      }
   }

   public double learningCost() {
      return 250.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 25.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      ManasSkill skill = (ManasSkill)ExtraSkills.MORTAL_FEAR.get();
      ManasSkillInstance fear = new TensuraSkillInstance(skill);
      fear.setMastery(-100);
      if (SkillAPI.getSkillsFrom(entity).learnSkill(fear) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return entity;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:haki"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (entityData) -> {
            return !entityData.m_7306_(entity) && entityData.m_6084_() && !entity.m_7307_(entityData);
         });
         if (!list.isEmpty()) {
            double scale = instance.getTag() == null ? 0.0D : instance.getTag().m_128459_("scale");
            double multiplier = scale == 0.0D ? 1.0D : Math.min(scale, 1.0D);
            double ownerEP = TensuraEPCapability.getEP(entity) * multiplier;
            Iterator var11 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var11.hasNext()) {
                     return true;
                  }

                  target = (LivingEntity)var11.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               double targetEP = TensuraEPCapability.getEP(target);
               double difference = ownerEP / targetEP;
               if (!(difference <= 2.0D)) {
                  int fearLevel = (int)(difference * 0.25D - 0.5D);
                  fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
                  SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
                  hakiPush(target, entity, fearLevel);
               }
            }
         } else {
            return true;
         }
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (this.hasAttributeApplied(entity, Attributes.f_22279_, "892b7bc5-af0a-4d04-860c-cd7ead8dc2d7")) {
         instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
      }
   }

   public static void hakiPush(LivingEntity target, LivingEntity source, int fearLevel) {
      if (fearLevel >= 1) {
         double knockResist = 1.0D - target.m_21133_(Attributes.f_22278_);
         double multiplier = Math.min(0.04D * (double)fearLevel, 0.2D) * knockResist;
         Vec3 vec3 = target.m_146892_().m_82546_(source.m_146892_()).m_82541_().m_82490_(multiplier);
         target.m_5997_(vec3.m_7096_(), vec3.m_7098_(), vec3.m_7094_());
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      changeEPUsed(instance, entity, delta);
   }

   public static void changeEPUsed(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.isMastered(entity)) {
         CompoundTag tag = instance.getOrCreateTag();
         double oldScale = tag.m_128459_("scale");
         double newScale = getNewScale(delta, oldScale);
         if (tag.m_128459_("scale") != newScale) {
            tag.m_128347_("scale", newScale);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{decimalFormat.format(newScale * 100.0D) + "%"}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            instance.markDirty();
         }

      }
   }

   private static double getNewScale(double delta, double oldScale) {
      double newScale;
      if (oldScale == 0.1D) {
         if (delta >= 0.0D) {
            newScale = 0.2D;
         } else {
            newScale = 0.05D;
         }
      } else if (oldScale == 0.05D) {
         if (delta >= 0.0D) {
            newScale = 0.1D;
         } else {
            newScale = 0.01D;
         }
      } else if (oldScale == 0.01D) {
         if (delta >= 0.0D) {
            newScale = 0.05D;
         } else {
            newScale = 0.001D;
         }
      } else if (oldScale <= 0.001D) {
         if (delta >= 0.0D) {
            newScale = 0.01D;
         } else {
            newScale = 1.0D;
         }
      } else {
         newScale = oldScale + delta * 0.1D;
         if (newScale > 1.0D) {
            newScale = 0.001D;
         }
      }

      return newScale;
   }
}
