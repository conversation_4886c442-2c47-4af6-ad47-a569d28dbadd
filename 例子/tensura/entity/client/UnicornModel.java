package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.UnicornEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.model.ChestedHorseModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.animal.Sheep;
import net.minecraft.world.item.DyeColor;

public class UnicornModel<T extends UnicornEntity> extends ChestedHorseModel<T> {
   public static final ModelLayerLocation UNICORN = new ModelLayerLocation(new ResourceLocation("tensura", "unicorn"), "unicorn");
   private final ModelPart horn;

   public UnicornModel(ModelPart root) {
      super(root);
      this.horn = root.m_171324_("horn");
   }

   public static LayerDefinition m_170483_() {
      MeshDefinition meshdefinition = ChestedHorseModel.m_170669_(CubeDeformation.f_171458_);
      PartDefinition partdefinition = meshdefinition.m_171576_();
      partdefinition.m_171599_("horn", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(-0.5F, -21.0F, 0.5F, 1.0F, 5.0F, 1.0F, CubeDeformation.f_171458_).m_171514_(0, 6).m_171488_(-1.0F, -16.0F, 0.0F, 2.0F, 5.0F, 2.0F, CubeDeformation.f_171458_), PartPose.m_171419_(0.0F, -4.0F, -11.0F));
      PartDefinition child = partdefinition.m_171597_("body");
      CubeListBuilder cubelistbuilder = CubeListBuilder.m_171558_().m_171514_(26, 21).m_171481_(-4.0F, 0.0F, -2.0F, 8.0F, 8.0F, 3.0F);
      child.m_171599_("left_chest", cubelistbuilder, PartPose.m_171423_(6.0F, -8.0F, 0.0F, 0.0F, -1.5707964F, 0.0F));
      child.m_171599_("right_chest", cubelistbuilder, PartPose.m_171423_(-6.0F, -8.0F, 0.0F, 0.0F, 1.5707964F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 64, 64);
   }

   public void renderHorn(T entity, PoseStack poseStack, VertexConsumer vertexConsumer, int packedLightIn, int packedOverlayIn, float partialTick) {
      this.horn.m_104315_(this.f_102752_);
      float newRed = 1.0F;
      float newGreen = 1.0F;
      float newBlue = 1.0F;
      if (this.shouldRainbowHorn(entity)) {
         int i = entity.f_19797_ / 25 + entity.m_19879_();
         int j = DyeColor.values().length;
         float v = ((float)(entity.f_19797_ % 25) + partialTick) / 25.0F;
         float[] afloat1 = Sheep.m_29829_(DyeColor.m_41053_(i % j));
         float[] afloat2 = Sheep.m_29829_(DyeColor.m_41053_((i + 1) % j));
         float r = afloat1[0] * (1.0F - v) + afloat2[0] * v;
         float g = afloat1[1] * (1.0F - v) + afloat2[1] * v;
         float b = afloat1[2] * (1.0F - v) + afloat2[2] * v;
         newRed = Math.min(1.0F, r + 0.5F);
         newGreen = Math.min(1.0F, g + 0.5F);
         newBlue = Math.min(1.0F, b + 0.5F);
      }

      this.horn.m_104306_(poseStack, vertexConsumer, packedLightIn, packedOverlayIn, newRed, newBlue, newGreen, 1.0F);
   }

   public boolean shouldRainbowHorn(UnicornEntity unicorn) {
      if (unicorn.getRainbowHornTicks() > 0) {
         return true;
      } else {
         return unicorn.m_8077_() && "jeb_".equals(unicorn.m_7755_().getString());
      }
   }
}
