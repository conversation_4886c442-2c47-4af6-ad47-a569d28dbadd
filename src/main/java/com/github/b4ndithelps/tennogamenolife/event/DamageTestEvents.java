package com.github.b4ndithelps.tennogamenolife.event;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import com.github.b4ndithelps.tennogamenolife.util.DamageTestManager;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Mod.EventBusSubscriber(modid = Tenreincarnation.MODID)
public class DamageTestEvents {
    @SubscribeEvent
    public static void onLivingDamage(LivingDamageEvent event) {
        if (event.getSource().getEntity() instanceof Player player) {
            if (!(event.getEntity() instanceof HurtDummyEntity) && DamageTestManager.isDamageTestModeEnabled(player.getUUID())) {
                LivingEntity target = event.getEntity();
                float damage = event.getAmount();
                double roundedDamage = new BigDecimal(damage).setScale(1, RoundingMode.HALF_UP).doubleValue();
                player.sendSystemMessage(Component.translatable("message." + Tenreincarnation.MODID + ".damage_dealt", 
                        String.format("%.1f", roundedDamage)));
            }
        }
    }
} 