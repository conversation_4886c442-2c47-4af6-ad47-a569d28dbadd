package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.DivineKiReleaseSkill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class DivineBerserkerSkill extends Skill {
   public DivineBerserkerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return this.canTick(instance, entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance ogre = entity.m_21124_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
      return ogre != null && ogre.m_19564_() >= 1;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (this.canTick(instance, entity)) {
         entity.m_21195_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
      }

   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent e) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get())) {
         if (DivineKiReleaseSkill.isBattlewillDamage(e.getSource(), entity)) {
            float multiplier = instance.isMastered(entity) ? 8.0F : 6.0F;
            e.setAmount(e.getAmount() * multiplier);
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
         instance.setCoolDown(600);
      } else {
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         instance.setCoolDown(this.isMastered(instance, entity) ? 960 : 780);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12363_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (this.isMastered(instance, entity)) {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get(), 7200, 3, false, false, false));
         } else {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get(), 3600, 1, false, false, false));
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 3.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 2.0D);
         TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.PURPLE_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
      }

   }
}
