package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.world.tree.trunk.PalmTrunkPlacer;
import net.minecraft.core.Registry;
import net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacerType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraTrunkPlacer {
   private static final DeferredRegister<TrunkPlacerType<?>> registry;
   public static final RegistryObject<TrunkPlacerType<PalmTrunkPlacer>> PALM_TRUNK_PLACER;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_122849_, "tensura");
      PALM_TRUNK_PLACER = registry.register("palm_trunk_placer", () -> {
         return new TrunkPlacerType(PalmTrunkPlacer.CODEC);
      });
   }
}
