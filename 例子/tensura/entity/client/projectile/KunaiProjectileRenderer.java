package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.inventory.InventoryMenu;

public class KunaiProjectileRenderer extends EntityRenderer<KunaiProjectile> {
   private final ItemRenderer itemRenderer;

   public KunaiProjectileRenderer(Context renderManager) {
      super(renderManager);
      this.itemRenderer = renderManager.m_174025_();
   }

   public ResourceLocation getTextureLocation(KunaiProjectile instance) {
      return InventoryMenu.f_39692_;
   }

   public void render(KunaiProjectile KunaiProjectile, float f, float g, PoseStack matrixStack, MultiBufferSource vertexConsumerProvider, int i) {
      matrixStack.m_85836_();
      matrixStack.m_85845_(Vector3f.f_122225_.m_122240_(Mth.m_14179_(g, KunaiProjectile.f_19859_, KunaiProjectile.m_146908_()) - 90.0F));
      matrixStack.m_85845_(Vector3f.f_122227_.m_122240_(Mth.m_14179_(g, KunaiProjectile.f_19860_, KunaiProjectile.m_146909_()) + 45.0F));
      matrixStack.m_85845_(Vector3f.f_122223_.m_122240_(180.0F));
      matrixStack.m_85837_(0.0D, -0.1D, 0.0D);
      matrixStack.m_85841_(0.5F, 0.5F, 0.5F);
      this.itemRenderer.m_174269_(KunaiProjectile.getSourceItem(), TransformType.GUI, i, OverlayTexture.f_118083_, matrixStack, vertexConsumerProvider, KunaiProjectile.m_19879_());
      matrixStack.m_85849_();
   }
}
