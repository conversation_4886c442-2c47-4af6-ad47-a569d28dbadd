package com.github.manasmods.tensura.entity.magic.field;

import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;

public class Hellfire extends AreaField {
   public Hellfire(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public Hellfire(Level level, Entity entity) {
      this((EntityType)TensuraEntityTypes.HELLFIRE.get(), level);
      this.m_5602_(entity);
   }

   public void applyEffect(LivingEntity target, boolean instant) {
      if (!this.isAlly(target)) {
         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_7500_()) {
               return;
            }
         }

         target.m_20254_(5);
         if (instant) {
            Entity owner = this.m_37282_();
            DamageSource source = owner == null ? DamageSource.f_19307_ : TensuraDamageSources.burn(owner);
            target.m_6469_(DamageSourceHelper.addSkillAndCost(source, this.getMpCost(), this.getSkill()), this.getDamage());
         }

      }
   }

   private boolean isAlly(LivingEntity target) {
      Entity owner = this.m_37282_();
      if (target == owner) {
         return true;
      } else {
         if (owner instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)owner;
            if (target.m_7307_(entity)) {
               return true;
            }

            if (owner instanceof IfritEntity) {
               IfritEntity ifrit = (IfritEntity)owner;
               return !ifrit.shouldAttack(target);
            }
         }

         return false;
      }
   }

   public boolean isInstant() {
      return true;
   }
}
