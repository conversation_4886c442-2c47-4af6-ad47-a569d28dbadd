package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.slime.SlimeRace;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class MagicSenseSkill extends Skill {
   public MagicSenseSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 50.0D;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (TensuraPlayerCapability.getRace(entity) instanceof SlimeRace) {
         return true;
      } else {
         return entity.m_21023_(MobEffects.f_216964_) || entity.m_21023_(MobEffects.f_19610_);
      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.isMastered(living);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isMastered(entity) ? false : instance.isToggled();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 5.0D;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity entity, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         if (SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.DANGER_SENSE.get())) {
            ManasSkill skill = (ManasSkill)ExtraSkills.SENSE_SOUNDWAVE.get();
            ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
            manipulation.setMastery(-100);
            if (storage.learnSkill(manipulation) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
            }

         }
      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      if (!storage.getSkill((ManasSkill)ExtraSkills.SENSE_HEAT_SOURCE.get()).isEmpty()) {
         if (!storage.getSkill((ManasSkill)ExtraSkills.SENSE_SOUNDWAVE.get()).isEmpty()) {
            ManasSkill skill = (ManasSkill)ExtraSkills.UNIVERSAL_PERCEPTION.get();
            ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
            manipulation.setMastery(-100);
            if (storage.learnSkill(manipulation) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 30 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         living.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 20, 1, false, false));
         return true;
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * 5.0D)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 200, 1, false, false, false));
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         int level = ((MobEffectInstance)Objects.requireNonNull(entity.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_();
         if (level == 1) {
            entity.m_21195_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get());
         }
      }

   }
}
