package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.DirewolfEntity;
import com.github.manasmods.tensura.entity.variant.DirewolfVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class DirewolfRenderer extends GeoEntityRenderer<DirewolfEntity> {
   public DirewolfRenderer(Context renderManager) {
      super(renderManager, new DirewolfModel());
      this.f_114477_ = 0.5F;
   }

   public RenderType getRenderType(<PERSON>rewolfEntity wolf, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      float size = wolf.getSize();
      if (wolf.m_6162_()) {
         size *= 0.5F;
      }

      stack.m_85841_(size, size, size);
      return RenderType.m_110473_(this.getTextureLocation(wolf));
   }

   public ResourceLocation getTextureLocation(DirewolfEntity instance) {
      return getLocation(instance);
   }

   public static ResourceLocation getLocation(DirewolfEntity entity) {
      DirewolfVariant variant = entity.getVariant();
      if (entity.isStar() && entity.m_8077_()) {
         if ("Guitar".equalsIgnoreCase(entity.m_7755_().getString())) {
            if (variant == DirewolfVariant.STAR_WOLF) {
               return new ResourceLocation("tensura", "textures/entity/direwolf/guitar_wolf.png");
            }

            if (variant == DirewolfVariant.TEMPEST_STAR_WOLF) {
               return new ResourceLocation("tensura", "textures/entity/direwolf/guitar_star_wolf.png");
            }
         } else if ("Momo".equalsIgnoreCase(entity.m_7755_().getString()) || "Memoires".equalsIgnoreCase(entity.m_7755_().getString())) {
            return new ResourceLocation("tensura", "textures/entity/direwolf/momo_wolf.png");
         }
      }

      if (entity.isStar() && variant.getBirthmarkName() != null) {
         return new ResourceLocation("tensura", "textures/entity/direwolf/" + variant.getBirthmarkName() + ".png");
      } else if (entity.isAlpha() && variant.getAlphaName() != null) {
         return new ResourceLocation("tensura", "textures/entity/direwolf/" + variant.getAlphaName() + ".png");
      } else {
         return new ResourceLocation("tensura", "textures/entity/direwolf/" + variant.getName() + ".png");
      }
   }
}
