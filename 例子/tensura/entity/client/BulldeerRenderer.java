package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BulldeerEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class BulldeerRenderer extends GeoEntityRenderer<BulldeerEntity> {
   public BulldeerRenderer(Context renderManager) {
      super(renderManager, new BulldeerModel());
      this.f_114477_ = 0.4F;
   }

   public ResourceLocation getTextureLocation(BulldeerEntity instance) {
      return instance.m_6162_() ? new ResourceLocation("tensura", "textures/entity/bulldeer/bulldeer_baby.png") : new ResourceLocation("tensura", "textures/entity/bulldeer/bulldeer.png");
   }

   public RenderType getRenderType(BulldeerEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      float scale = 0.75F;
      if (entity.m_6162_()) {
         scale /= 2.0F;
      }

      stack.m_85841_(scale, scale, scale);
      return super.getRenderType(entity, partialTicks, stack, renderTypeBuffer, vertexBuilder, packedLightIn, textureLocation);
   }
}
