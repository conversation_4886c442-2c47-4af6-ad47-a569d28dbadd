package com.github.manasmods.tensura.world.tree.leaves;

import com.github.manasmods.tensura.registry.biome.TensuraFoliagePlacers;
import com.mojang.datafixers.Products.P3;
import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import com.mojang.serialization.codecs.RecordCodecBuilder.Instance;
import com.mojang.serialization.codecs.RecordCodecBuilder.Mu;
import java.util.function.BiConsumer;
import net.minecraft.core.BlockPos;
import net.minecraft.util.RandomSource;
import net.minecraft.util.valueproviders.IntProvider;
import net.minecraft.world.level.LevelSimulatedReader;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacer;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacerType;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacer.FoliageAttachment;

public class PalmFoliagePlacer extends FoliagePlacer {
   public static final Codec<PalmFoliagePlacer> CODEC = RecordCodecBuilder.create((p_68427_) -> {
      return palmParts(p_68427_).apply(p_68427_, PalmFoliagePlacer::new);
   });
   protected final int height;

   protected static <P extends PalmFoliagePlacer> P3<Mu<P>, IntProvider, IntProvider, Integer> palmParts(Instance<P> p_68414_) {
      return m_68573_(p_68414_).and(Codec.intRange(0, 16).fieldOf("height").forGetter((placer) -> {
         return placer.height;
      }));
   }

   public PalmFoliagePlacer(IntProvider pRadius, IntProvider pOffset, int height) {
      super(pRadius, pOffset);
      this.height = height;
   }

   protected FoliagePlacerType<PalmFoliagePlacer> m_5897_() {
      return (FoliagePlacerType)TensuraFoliagePlacers.PALM_FOLIAGE.get();
   }

   public int m_214116_(RandomSource pRandom, int pHeight, TreeConfiguration pConfig) {
      return this.height;
   }

   protected boolean m_214203_(RandomSource pRandom, int pLocalX, int pLocalY, int pLocalZ, int pRange, boolean pLarge) {
      return false;
   }

   protected void m_213633_(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, TreeConfiguration pConfig, int pMaxFreeTreeHeight, FoliageAttachment pAttachment, int pFoliageHeight, int pFoliageRadius, int pOffset) {
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_());
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_122012_());
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_122029_());
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_122019_());
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_122024_());
      this.cycleAround(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_());
      BlockPos lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122013_(2));
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_());
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122030_(2));
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122029_());
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122029_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122020_(2));
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122019_());
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122019_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122025_(2));
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_());
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122013_(2).m_122030_(2));
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_().m_122029_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122030_(2).m_122020_(2));
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122029_().m_122019_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122025_(2).m_122020_(2));
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_().m_122019_().m_7495_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pAttachment.m_161451_().m_7495_().m_122025_(2).m_122013_(2));
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_().m_122012_().m_7495_());
   }

   private void cycleAround(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, TreeConfiguration pConfig, BlockPos pPos) {
      BlockPos lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, pPos);
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122029_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122019_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122019_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122024_());
      lastPos = placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_());
      placeAndContinue(pLevel, pBlockSetter, pRandom, pConfig, lastPos.m_122012_());
   }

   private static BlockPos placeAndContinue(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, TreeConfiguration pConfig, BlockPos pPos) {
      m_225622_(pLevel, pBlockSetter, pRandom, pConfig, pPos);
      return pPos;
   }
}
