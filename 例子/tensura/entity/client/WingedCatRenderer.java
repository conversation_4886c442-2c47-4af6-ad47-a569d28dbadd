package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.WingedCatEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class WingedCatRenderer extends GeoEntityRenderer<WingedCatEntity> {
   public WingedCatRenderer(Context renderManager) {
      super(renderManager, new WingedCatModel());
      this.f_114477_ = 0.9F;
   }

   public ResourceLocation getTextureLocation(WingedCatEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/winged_cat/winged_cat.png");
   }

   public RenderType getRenderType(WingedCatEntity cat, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      return RenderType.m_110473_(this.getTextureLocation(cat));
   }
}
