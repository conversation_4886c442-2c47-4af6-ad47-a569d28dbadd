package com.github.manasmods.tensura.entity.template;

import java.util.EnumSet;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class OrbitSwoopFLyingEntity extends FLyingTamableEntity {
   private static final EntityDataAccessor<BlockPos> ORBIT_POS;
   public Vec3 orbitOffset;
   public int behavior;
   protected static final int ORBIT = 0;
   protected static final int SWOOP = 2;

   public OrbitSwoopFLyingEntity(EntityType<? extends TamableAnimal> type, Level worldIn) {
      super(type, worldIn);
      this.orbitOffset = Vec3.f_82478_;
   }

   protected void switchNavigator(boolean wallCollide) {
      this.f_21342_ = new OrbitSwoopFLyingEntity.OrbitMoveControl(this, wallCollide);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(ORBIT_POS, BlockPos.f_121853_);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("AX", this.getOrbitPos().m_123341_());
      compound.m_128405_("AY", this.getOrbitPos().m_123342_());
      compound.m_128405_("AZ", this.getOrbitPos().m_123343_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128441_("AX")) {
         this.setOrbitPos(new BlockPos(compound.m_128451_("AX"), compound.m_128451_("AY"), compound.m_128451_("AZ")));
      }

   }

   public BlockPos getOrbitPos() {
      return (BlockPos)this.f_19804_.m_135370_(ORBIT_POS);
   }

   public void setOrbitPos(BlockPos pos) {
      this.f_19804_.m_135381_(ORBIT_POS, pos);
   }

   public void setWanderPos(BlockPos pPos) {
      super.setWanderPos(pPos);
      this.setOrbitPos(pPos.m_6630_(20));
   }

   public void m_21839_(boolean pOrderedToSit) {
      super.m_21839_(pOrderedToSit);
      if (pOrderedToSit) {
         this.setOrbitPos(this.m_20183_().m_6630_(20));
      }

   }

   static {
      ORBIT_POS = SynchedEntityData.m_135353_(OrbitSwoopFLyingEntity.class, EntityDataSerializers.f_135038_);
   }

   protected static class OrbitMoveControl extends MoveControl {
      private final OrbitSwoopFLyingEntity entity;
      private float speed = 0.1F;
      private final boolean wallCollide;

      public OrbitMoveControl(OrbitSwoopFLyingEntity entity, boolean wallCollide) {
         super(entity);
         this.entity = entity;
         this.wallCollide = wallCollide;
      }

      public void m_8126_() {
         if (!this.entity.m_21827_()) {
            double flyingSpeed = this.entity.m_21133_(Attributes.f_22280_);
            if (this.entity.m_21824_() && this.entity.shouldFollow()) {
               this.normalFlyingControl(flyingSpeed);
            } else {
               if (this.wallCollide && this.entity.f_19862_) {
                  this.entity.m_146922_(this.entity.m_146908_() + 180.0F);
                  this.speed = 0.1F;
               }

               float orbitX = (float)(this.entity.orbitOffset.f_82479_ - this.entity.m_20185_());
               float orbitY = (float)(this.entity.orbitOffset.f_82480_ - this.entity.m_20186_());
               float orbitZ = (float)(this.entity.orbitOffset.f_82481_ - this.entity.m_20189_());
               double horizontalDistance = (double)Mth.m_14116_(orbitX * orbitX + orbitZ * orbitZ);
               double verticalAdjustment = 1.0D - (double)Mth.m_14154_(orbitY * 0.7F) / horizontalDistance;
               orbitX = (float)((double)orbitX * verticalAdjustment);
               orbitZ = (float)((double)orbitZ * verticalAdjustment);
               horizontalDistance = (double)Mth.m_14116_(orbitX * orbitX + orbitZ * orbitZ);
               double distance = (double)Mth.m_14116_(orbitX * orbitX + orbitZ * orbitZ + orbitY * orbitY);
               float yaw = this.entity.m_146908_();
               float orbitYaw = (float)Mth.m_14136_((double)orbitZ, (double)orbitX);
               float startYaw = Mth.m_14177_(this.entity.m_146908_() + 90.0F);
               orbitYaw = Mth.m_14177_(orbitYaw * 57.295776F);
               this.entity.m_146922_(Mth.m_14148_(startYaw, orbitYaw, 10.0F) - 90.0F);
               this.entity.f_20883_ = this.entity.m_146908_();
               float finalPitch;
               float adjustedYaw;
               if (Mth.m_14145_(yaw, this.entity.m_146908_()) < 3.0F) {
                  finalPitch = 1.2F;
                  adjustedYaw = this.speed > finalPitch ? 10.0F : finalPitch / this.speed;
                  this.speed = Mth.m_14121_(this.speed, finalPitch, 0.005F * adjustedYaw);
               } else {
                  this.speed = Mth.m_14121_(this.speed, 0.4F, 0.05F);
               }

               finalPitch = (float)(-(Mth.m_14136_((double)(-orbitY), horizontalDistance) * 57.2957763671875D));
               this.entity.m_146926_(finalPitch);
               adjustedYaw = this.entity.m_146908_() + 90.0F;
               double finalX = ((double)this.speed + flyingSpeed) * (double)Mth.m_14089_(adjustedYaw * 0.017453292F) * Math.abs((double)orbitX / distance);
               double finalZ = ((double)this.speed + flyingSpeed) * (double)Mth.m_14031_(adjustedYaw * 0.017453292F) * Math.abs((double)orbitZ / distance);
               double finalY = ((double)this.speed + flyingSpeed) * (double)Mth.m_14031_(finalPitch * 0.017453292F) * Math.abs((double)orbitY / distance);
               Vec3 vector3d = this.entity.m_20184_();
               this.entity.m_20256_(vector3d.m_82549_((new Vec3(finalX, finalY, finalZ)).m_82546_(vector3d).m_82490_(0.2D)));
            }
         }
      }

      private void normalFlyingControl(double flyingSpeed) {
         if (this.f_24981_ == Operation.MOVE_TO) {
            Vec3 vector3d = new Vec3(this.f_24975_ - this.entity.m_20185_(), this.f_24976_ - this.entity.m_20186_(), this.f_24977_ - this.entity.m_20189_());
            double d0 = vector3d.m_82553_();
            if (d0 < this.entity.m_20191_().m_82309_()) {
               this.f_24981_ = Operation.WAIT;
               this.entity.m_20256_(this.entity.m_20184_().m_82490_(0.5D));
            } else {
               this.entity.m_20256_(this.entity.m_20184_().m_82549_(vector3d.m_82490_(this.f_24978_ * flyingSpeed * 0.05D / d0)));
               if (this.entity.m_5448_() == null) {
                  Vec3 vector3d1 = this.entity.m_20184_();
                  this.entity.m_146922_(-((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F);
               } else {
                  double d2 = this.entity.m_5448_().m_20185_() - this.entity.m_20185_();
                  double d1 = this.entity.m_5448_().m_20189_() - this.entity.m_20189_();
                  this.entity.m_146922_(-((float)Mth.m_14136_(d2, d1)) * 57.295776F);
               }

               this.entity.f_20883_ = this.entity.m_146908_();
            }
         } else if (this.f_24981_ == Operation.STRAFE) {
            this.f_24981_ = Operation.WAIT;
         }

      }
   }

   protected static class SwoopAttackGoal extends OrbitSwoopFLyingEntity.BaseOrbitMovementGoal {
      protected static final UUID BOOST = UUID.fromString("de6346bc-2674-4043-b99c-d817f607f848");
      protected final boolean canInterfere;
      protected final double speedBoost;

      public SwoopAttackGoal(OrbitSwoopFLyingEntity entity, boolean canInterfere, double speedBoost) {
         super(entity);
         this.canInterfere = canInterfere;
         this.speedBoost = speedBoost;
      }

      public boolean m_8036_() {
         if (this.entity.m_21824_() && this.entity.shouldFollow()) {
            return false;
         } else {
            return this.entity.m_5448_() != null && this.entity.behavior == 2;
         }
      }

      public boolean m_8045_() {
         LivingEntity livingentity = this.entity.m_5448_();
         if (livingentity == null) {
            return false;
         } else {
            return !livingentity.m_6084_() ? false : this.m_8036_();
         }
      }

      public void m_8056_() {
         AttributeModifier modifier = new AttributeModifier(BOOST, "Swoop Boost", this.speedBoost, net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation.ADDITION);
         AttributeInstance speed = this.entity.m_21051_(Attributes.f_22280_);
         if (speed != null) {
            speed.m_22118_(modifier);
         }

      }

      public void m_8041_() {
         this.entity.behavior = 0;
         AttributeInstance speed = this.entity.m_21051_(Attributes.f_22280_);
         if (speed != null) {
            speed.m_22120_(BOOST);
         }

      }

      public void m_8037_() {
         LivingEntity target = this.entity.m_5448_();
         if (target != null) {
            this.entity.orbitOffset = new Vec3(target.m_20185_(), target.m_20227_(0.5D), target.m_20189_());
            this.performAttack(target);
         }
      }

      protected void performAttack(LivingEntity target) {
         if (this.entity.m_20191_().m_82400_(0.2D).m_82381_(target.m_20191_())) {
            this.entity.m_7327_(target);
            this.entity.behavior = 0;
         } else if (this.canInterfere && this.entity.f_20916_ > 0) {
            this.entity.behavior = 0;
         }

      }
   }

   protected static class CircleFlightGoal extends OrbitSwoopFLyingEntity.BaseOrbitMovementGoal {
      private float angle;
      private float radius;
      private float height;
      private float direction;
      private final float baseRadius;

      public CircleFlightGoal(OrbitSwoopFLyingEntity entity, float baseRadius) {
         super(entity);
         this.baseRadius = baseRadius;
      }

      public boolean m_8036_() {
         if (this.entity.m_21824_() && this.entity.shouldFollow()) {
            return false;
         } else {
            return this.entity.m_5448_() == null || this.entity.behavior == 0;
         }
      }

      public void m_8056_() {
         this.radius = this.baseRadius + this.entity.f_19796_.m_188501_() * this.baseRadius;
         this.height = -4.0F + this.entity.f_19796_.m_188501_() * 6.0F;
         this.direction = this.entity.f_19796_.m_188499_() ? 1.0F : -1.0F;
         this.updateOffset();
      }

      public void m_8037_() {
         if (this.entity.f_19796_.m_188503_(350) == 0) {
            this.height = -4.0F + this.entity.f_19796_.m_188501_() * 6.0F;
         }

         if (this.entity.f_19796_.m_188503_(250) == 0) {
            --this.radius;
            if (this.radius < 8.0F) {
               this.radius = 16.0F;
               this.direction = -this.direction;
            }
         }

         if (this.entity.f_19796_.m_188503_(300) == 0) {
            this.angle = this.entity.f_19796_.m_188501_() * 2.0F * 3.1415927F;
            this.updateOffset();
         }

         if (this.isCloseToOffset()) {
            this.updateOffset();
         }

         if (this.entity.orbitOffset.f_82480_ < this.entity.m_20186_() && !this.entity.f_19853_.m_46859_(this.entity.m_20183_().m_6625_(1))) {
            this.height = Math.max(1.0F, this.height);
            this.updateOffset();
         }

         if (this.entity.orbitOffset.f_82480_ > this.entity.m_20186_() && !this.entity.f_19853_.m_46859_(this.entity.m_20183_().m_6630_(1))) {
            this.height = Math.min(-1.0F, this.height);
            this.updateOffset();
         }

      }

      private void updateOffset() {
         if (BlockPos.f_121853_.equals(this.entity.getOrbitPos())) {
            this.entity.setOrbitPos(this.entity.m_20183_());
         }

         this.angle += this.direction * 20.0F * 0.017453292F;
         this.entity.orbitOffset = Vec3.m_82528_(this.getBaseOrbitChange()).m_82520_((double)(this.radius * Mth.m_14089_(this.angle)), (double)(-4.0F + this.height), (double)(this.radius * Mth.m_14031_(this.angle)));
      }

      protected BlockPos getBaseOrbitChange() {
         return this.entity.getOrbitPos();
      }
   }

   protected abstract static class BaseOrbitMovementGoal extends Goal {
      protected OrbitSwoopFLyingEntity entity;

      public BaseOrbitMovementGoal(OrbitSwoopFLyingEntity entity) {
         this.entity = entity;
         this.m_7021_(EnumSet.of(Flag.MOVE));
      }

      protected boolean isCloseToOffset() {
         return this.entity.orbitOffset.m_82531_(this.entity.m_20185_(), this.entity.m_20186_(), this.entity.m_20189_()) < 4.0D;
      }

      public boolean m_183429_() {
         return true;
      }
   }
}
