package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;

public class ThoughtAccelerationSkill extends Skill {
   protected static final UUID ACCELERATION = UUID.fromString("46dc5eee-34e9-4a6c-ad3d-58048cb06c6f");

   public ThoughtAccelerationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 8000.0D;
   }

   public double learningCost() {
      return 50.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      onToggle(instance, entity, ACCELERATION, true);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      onToggle(instance, entity, ACCELERATION, false);
   }

   public static void onToggle(ManasSkillInstance instance, LivingEntity entity, UUID uuid, boolean on) {
      AttributeInstance speed;
      AttributeInstance attackSpeed;
      if (on) {
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
         speed = entity.m_21051_(Attributes.f_22279_);
         if (speed != null) {
            AttributeModifier speedModifier = new AttributeModifier(uuid, "Thought Acceleration", instance.isMastered(entity) ? 0.02D : 0.01D, Operation.ADDITION);
            if (!speed.m_22109_(speedModifier)) {
               speed.m_22125_(speedModifier);
            }
         }

         attackSpeed = entity.m_21051_(Attributes.f_22283_);
         if (attackSpeed != null) {
            AttributeModifier speedModifier = new AttributeModifier(uuid, "Thought Acceleration", instance.isMastered(entity) ? 0.4D : 0.2D, Operation.ADDITION);
            if (!attackSpeed.m_22109_(speedModifier)) {
               attackSpeed.m_22125_(speedModifier);
            }
         }
      } else {
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 0.5F, 0.5F);
         speed = entity.m_21051_(Attributes.f_22279_);
         if (speed != null) {
            speed.m_22127_(uuid);
         }

         attackSpeed = entity.m_21051_(Attributes.f_22283_);
         if (attackSpeed != null) {
            attackSpeed.m_22127_(uuid);
         }
      }

   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 10 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
