package com.github.manasmods.tensura.ability.magic.spiritual.darkness;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class DarknessCannonMagic extends SpiritualMagic {
   public DarknessCannonMagic() {
      super(MagicElemental.DARKNESS, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 80;
   }

   public int masteryCast() {
      return 40;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 15000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      instance.getOrCreateTag().m_128405_("BeamID", 0);
      instance.markDirty();
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         if (heldTicks % 100 == 0 && heldTicks > castTime) {
            this.addMasteryPoint(instance, entity);
         }

         if (heldTicks >= castTime) {
            double cost = this.magiculeCost(entity, instance);
            BeamProjectile.spawnLastingBeam((EntityType)TensuraEntityTypes.DARKNESS_CANNON.get(), 250.0F, 1.0F, 21, 20.0F, 0.0F, entity.m_146892_(), entity, instance, cost, cost, heldTicks);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 0.8F, 0.5F);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            this.addCastingParticle(instance, player, heldTicks);
         }

         return true;
      }
   }
}
