package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import java.util.Arrays;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

public class MagicBallRenderer extends EntityRenderer<TensuraProjectile> {
   public static final ModelLayerLocation MAGIC_BALL = new ModelLayerLocation(new ResourceLocation("tensura", "magic_ball"), "main");
   private final ModelPart bolt;

   public MagicBallRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(MAGIC_BALL);
      this.bolt = modelpart.m_171324_("Ball");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      partdefinition.m_171599_("Ball", CubeListBuilder.m_171558_().m_171514_(0, 32).m_171488_(-3.0F, 0.0F, -5.3333F, 6.0F, 6.0F, 6.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 0.0F, 0.0F));
      return LayerDefinition.m_171565_(meshdefinition, 48, 44);
   }

   public void render(TensuraProjectile entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !entity.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         float f = (float)entity.f_19797_ + partialTicks;
         poseStack.m_85836_();
         float swirlX = Mth.m_14089_(0.05F * f * 2.0F) * 90.0F;
         float swirlY = Mth.m_14031_(0.05F * f * 2.0F) * 90.0F;
         float swirlZ = Mth.m_14089_(0.05F * f * 2.0F + 5464.0F) * 90.0F;
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(swirlX * 0.45F));
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(swirlY * 0.45F));
         poseStack.m_85845_(Vector3f.f_122227_.m_122240_(swirlZ * 0.45F));
         float scale = entity.getVisualSize();
         poseStack.m_85841_(scale, scale, scale);
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(entity)));
         this.bolt.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(swirlZ));
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(swirlX));
         poseStack.m_85845_(Vector3f.f_122227_.m_122240_(swirlY));
         poseStack.m_85849_();
         super.m_7392_(entity, yaw, partialTicks, poseStack, bufferSource, light);
      }
   }

   public ResourceLocation getTextureLocation(TensuraProjectile instance) {
      ResourceLocation[] resourceLocations = instance.getTextureLocation();
      return resourceLocations == null ? new ResourceLocation("tensura", "textures/blank_texture.png") : (ResourceLocation)Arrays.stream(resourceLocations).toList().get(instance.f_19797_ % resourceLocations.length);
   }
}
