package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class InvisibleFireBoltProjectile extends FireBoltProjectile {
   public InvisibleFireBoltProjectile(EntityType<? extends InvisibleFireBoltProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public InvisibleFireBoltProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.INVISIBLE_FIRE_BOLT.get(), levelIn);
      this.m_5602_(shooter);
      this.invis = true;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_bolt/firebolt_5.png")};
   }

   public void m_8119_() {
      super.m_8119_();
      Entity var2 = this.m_37282_();
      if (var2 instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)var2;
         TensuraParticleHelper.spawnParticlesToOnePLayer(player, (ParticleOptions)TensuraParticles.RED_FIRE.get(), this.m_20185_(), this.m_20186_(), this.m_20189_(), 2, 0.0D, 0.0D, 0.0D, 0.0D, false);
      }
   }

   public void flyingParticles() {
   }
}
