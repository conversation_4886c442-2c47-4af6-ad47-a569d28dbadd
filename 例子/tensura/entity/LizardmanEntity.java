package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.controller.FlightMoveController;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.LizardmanVariant;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.EnumSet;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.Util;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomFlyingGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class LizardmanEntity extends HumanoidNPCEntity implements IAnimatable, IRanking, FlyingAnimal, IFollower {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> EVOLUTION_STATE;
   protected static final EntityDataAccessor<Boolean> FLYING;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public float prevFlyProgress;
   public float flyProgress;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;
   public boolean prevSwim = false;

   public LizardmanEntity(EntityType<? extends LizardmanEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21441_(BlockPathTypes.DANGER_FIRE, -1.0F);
      this.m_21441_(BlockPathTypes.WATER, -1.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 16.0F);
      this.m_21441_(BlockPathTypes.FENCE, -1.0F);
      this.switchNavigator(false);
      this.f_21364_ = 5;
      this.f_19793_ = 1.0F;
   }

   protected void switchNavigator(boolean onLand) {
      if (this.isDragonewt()) {
         if (!onLand && !this.m_5803_()) {
            this.f_21342_ = new FlightMoveController(this, 0.7F, true);
            this.f_21344_ = new StraightFlightNavigator(this, this.f_19853_);
            this.wasFlying = true;
         } else {
            this.f_21342_ = new TensuraTamableEntity.SleepMoveControl();
            this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
            this.wasFlying = false;
         }

      }
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 2.0D).m_22268_(Attributes.f_22276_, 24.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.20000000298023224D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 4.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 1.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 10.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false) {
         public boolean m_8036_() {
            return !LizardmanEntity.this.isDragonewt() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false) {
         public boolean m_8036_() {
            return LizardmanEntity.this.isDragonewt() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.2D, LizardmanEntity.class));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new LizardmanEntity.WalkGoal(this));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new WaterAvoidingRandomFlyingGoal(this, 1.2D) {
         public boolean m_8036_() {
            return !LizardmanEntity.this.isDragonewt() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{LizardmanEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(EVOLUTION_STATE, 0);
      this.f_19804_.m_135372_(FLYING, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("EvoState", this.getCurrentEvolutionState());
      compound.m_128379_("Flying", this.m_29443_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.setCurrentEvolutionState(compound.m_128451_("EvoState"));
      this.setFlying(compound.m_128471_("Flying"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int tick) {
      this.f_19804_.m_135381_(MISC_ANIMATION, tick);
   }

   public LizardmanVariant getVariant() {
      return LizardmanVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(LizardmanVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public void m_6136_(boolean pIsCharging) {
      super.m_6136_(pIsCharging);
      if (pIsCharging) {
         this.setMiscAnimation(3);
      }

   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      LizardmanEntity baby = (LizardmanEntity)((EntityType)TensuraEntityTypes.LIZARDMAN.get()).m_20615_(pLevel);
      if (baby == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            baby.m_21816_(uuid);
            baby.m_7105_(true);
         }

         int i = this.f_19796_.m_188503_(9);
         LizardmanVariant variant;
         if (i < 4) {
            variant = this.getVariant();
         } else if (i < 8 && pOtherParent instanceof LizardmanEntity) {
            LizardmanEntity lizardman = (LizardmanEntity)pOtherParent;
            variant = lizardman.getVariant();
         } else {
            variant = (LizardmanVariant)Util.m_214670_(LizardmanVariant.values(), this.f_19796_);
         }

         baby.setVariant(variant);
         if (this.isDragonewt() && ((LizardmanEntity)pOtherParent).isDragonewt()) {
            baby.evolve();
         }

         return baby;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_().m_41472_();
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return this.isDragonewt() ? false : super.m_142535_(pFallDistance, pMultiplier, pSource);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      if (this.m_5803_()) {
         return entitydimensions.m_20390_(1.0F, 0.5F);
      } else if (this.shouldSwim()) {
         return entitydimensions.m_20390_(1.0F, 0.25F);
      } else {
         return !this.m_21827_() && !this.m_21825_() ? entitydimensions : entitydimensions.m_20390_(1.0F, 0.75F);
      }
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public boolean canSleep() {
      return true;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean isPushedByFluid(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public double getFluidMotionScale(FluidType type) {
      return 1.0D;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   public boolean isDragonewt() {
      return this.getCurrentEvolutionState() == 1;
   }

   public int getCurrentEvolutionState() {
      return (Integer)this.f_19804_.m_135370_(EVOLUTION_STATE);
   }

   public void setCurrentEvolutionState(int state) {
      this.f_19804_.m_135381_(EVOLUTION_STATE, state);
   }

   public void evolve() {
      int current = this.getCurrentEvolutionState();
      if (current < this.getMaxEvolutionState()) {
         this.setCurrentEvolutionState(current + 1);
         this.gainSwimSpeed(this, 1.0D);
         SkillStorage storage = SkillAPI.getSkillsFrom(this);
         storage.learnSkill((ManasSkill)IntrinsicSkills.DRAGON_EYE.get());
         storage.learnSkill((ManasSkill)IntrinsicSkills.DRAGON_EAR.get());
         storage.learnSkill((ManasSkill)IntrinsicSkills.DRAGON_MODE.get());
         storage.learnSkill(this.f_19796_.m_188499_() ? (ManasSkill)IntrinsicSkills.FLAME_BREATH.get() : (ManasSkill)IntrinsicSkills.THUNDER_BREATH.get());
         ManasSkillInstance instance = new ManasSkillInstance((ManasSkill)ResistanceSkills.MAGIC_RESISTANCE.get());
         instance.setToggled(true);
         storage.learnSkill(instance);
         if (this.isDragonewt()) {
            this.switchNavigator(false);
         }
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (this.prevSwim != this.isInFluidType() && !this.m_20096_()) {
         this.m_6210_();
         this.prevSwim = this.isInFluidType() && !this.m_20096_();
      }

      if (this.isDragonewt()) {
         this.handleFlying();
      }

      this.miscAnimationHandler();
   }

   protected void handleFlying() {
      this.prevFlyProgress = this.flyProgress;
      this.targetingMovementHelper();
      if (this.m_29443_()) {
         if (this.flyProgress < 5.0F) {
            ++this.flyProgress;
         }
      } else if (this.flyProgress > 0.0F) {
         --this.flyProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_20159_() || this.m_27593_() || this.m_5803_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      return miscAnimation == 3 ? 25 : 7;
   }

   public void m_7023_(Vec3 vec3d) {
      if (this.isDragonewt() && this.m_20069_() && this.m_20184_().f_82480_ > 0.0D) {
         this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
      }

      super.m_7023_(vec3d);
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_) {
            this.setFlying(false);
         }

         if (this.m_29443_() && !this.isOverWater(this)) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (!this.m_6162_() && this.m_5957_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_27595_(player);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   protected void hurtShield(ItemStack stack, float pAmount) {
      super.hurtShield(stack, pAmount);
      this.setMiscAnimation(2);
   }

   protected boolean spearThrowAttack(LivingEntity pTarget, ItemStack weapon) {
      boolean success = super.spearThrowAttack(pTarget, weapon);
      if (success) {
         this.setMiscAnimation(4);
      }

      return success;
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.setVariant((LizardmanVariant)Util.m_214670_(LizardmanVariant.values(), this.f_19796_));
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public static boolean checkLizardSpawnRules(EntityType<LizardmanEntity> lizardman, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (!pLevel.m_8055_(pPos.m_7495_()).m_204336_(TensuraTags.Blocks.MOBS_SPAWNABLE_ON)) {
         return false;
      } else {
         return m_217057_(lizardman, pLevel, pSpawnType, pPos, pRandom) && pPos.m_123342_() < 70 && !pLevel.m_45527_(pPos);
      }
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.lizardmanSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      int i = pRandom.m_188503_(100);
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.IRON_SPEAR.get());
      if (i < 25) {
         if (i < 5) {
            stack = new ItemStack((ItemLike)TensuraToolItems.VORTEX_SPEAR.get());
         } else {
            stack = new ItemStack(Items.f_42713_);
         }
      }

      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      switch(pSlot) {
      case HEAD:
         if (pChance == 0) {
            return Items.f_42407_;
         } else {
            if (pChance == 1) {
               return (Item)TensuraArmorItems.MONSTER_LEATHER_D_HELMET.get();
            }

            return null;
         }
      case CHEST:
         if (pChance == 0) {
            return Items.f_42408_;
         } else if (pChance == 1) {
            return Items.f_42465_;
         } else if (pChance == 2) {
            return Items.f_42469_;
         } else {
            if (pChance == 3) {
               return (Item)TensuraArmorItems.MONSTER_LEATHER_D_CHESTPLATE.get();
            }

            return null;
         }
      case LEGS:
         if (pChance == 0) {
            return Items.f_42462_;
         } else if (pChance == 1) {
            return Items.f_42466_;
         } else if (pChance == 2) {
            return Items.f_42470_;
         } else {
            if (pChance == 3) {
               return (Item)TensuraArmorItems.MONSTER_LEATHER_D_LEGGINGS.get();
            }

            return null;
         }
      case FEET:
         if (pChance == 0) {
            return Items.f_42463_;
         } else if (pChance == 1) {
            return Items.f_42467_;
         } else if (pChance == 2) {
            return Items.f_42471_;
         } else {
            if (pChance == 3) {
               return (Item)TensuraArmorItems.MONSTER_LEATHER_D_LEGGINGS.get();
            }

            return null;
         }
      default:
         return null;
      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11799_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11804_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11802_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.sleep", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.shouldSwim()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.swim", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.sitting", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         if (event.isMoving()) {
            if (this.m_20096_() && this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.run", EDefaultLoopTypes.LOOP));
            } else if (this.m_20096_() || !this.isInFluidType()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.walk", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.attack", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.shield", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.crossbow", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4 || this.m_6117_() && this.isSpearType(this.m_21211_())) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.lizardman.spear", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(LizardmanEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(LizardmanEntity.class, EntityDataSerializers.f_135028_);
      EVOLUTION_STATE = SynchedEntityData.m_135353_(LizardmanEntity.class, EntityDataSerializers.f_135028_);
      FLYING = SynchedEntityData.m_135353_(LizardmanEntity.class, EntityDataSerializers.f_135035_);
   }

   public class WalkGoal extends Goal {
      protected final LizardmanEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(LizardmanEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!LizardmanEntity.this.isDragonewt()) {
            return false;
         } else if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = LizardmanEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = LizardmanEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.timeFlying < 200;
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (LizardmanEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (LizardmanEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (LizardmanEntity.this.isOverWater(this.entity)) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying >= 50 && !LizardmanEntity.this.isOverWater(this.entity) ? LizardmanEntity.this.getBlockGrounding(this.entity, vector3d) : LizardmanEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }
}
