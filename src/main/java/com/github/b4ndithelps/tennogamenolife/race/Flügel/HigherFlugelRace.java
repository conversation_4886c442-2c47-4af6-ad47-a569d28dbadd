package com.github.b4ndithelps.tennogamenolife.race.Flügel;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;

/**
 * 上级天翼是天翼族的进化形态
 * 拥有更强的飞行能力和魔素值
 */
public class HigherFlugelRace extends Race {

    // 种族基础属性
    private double baseHealth = 120.0;          // 基础生命值
    private double baseAttackDamage = 4.5;      // 基础攻击伤害
    private double baseAttackSpeed = 4.7;       // 基础攻击速度
    private double knockbackResistance = 0.4;   // 击退抗性
    private double jumpHeight = 1.8;            // 跳跃高度
    private double movementSpeed = 0.17;        // 移动速度
    private double sprintSpeed = 0.22;          // 冲刺速度
    private double auraMin = 1800.0;            // 最小灵气值
    private double auraMax = 2600.0;            // 最大灵气值
    private double startingMagiculeMin = 800.0;  // 最小初始魔素值
    private double startingMagiculeMax = 1200.0; // 最大初始魔素值

    private float playerSize = 2.0f;            // 玩家大小

    public HigherFlugelRace() {
        super(Difficulty.EASY);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(ExtraSkills.HEAVENLY_EYE.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        list.add(ExtraSkills.THOUGHT_ACCELERATION.get());
        list.add(ResistanceSkills.PARALYSIS_NULLIFICATION.get());
        list.add(ExtraSkills.WIND_MANIPULATION.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                // 关闭飞行
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                // 开启飞行
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    @Override
    public double getEvolutionPercentage(Player player) {
        double ep = Math.min(TensuraPlayerCapability.getBaseEP(player) * 50.0D / 150000.0D, 50.0D);
        double boss = Math.min(TensuraStats.getBossKilled(player) * 50 / 5, 50);
        return ep + boss;
    }

    @Override
    public List<Component> getRequirementsForRendering(Player player) {
        List<Component> list = new ArrayList<>();
        list.add(Component.translatable("tensura.evolution_menu.ep_requirement"));
        list.add(Component.translatable("tensura.evolution_menu.boss_kill_requirement"));
        return list;
    }

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.JIBRIL.get());
        return list;
    }

    @Nullable
    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.JIBRIL.get();
    }

    @Nullable
    @Override
    public Race getAwakeningEvolution(Player player) {
        return Tenreincarnationraces.JIBRIL.get();
    }

    @Nullable
    @Override
    public Race getHarvestFestivalEvolution(Player player) {
        return null;
    }

    @Override
    public List<Race> getPreviousEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.FLUGEL.get());
        return list;
    }
} 