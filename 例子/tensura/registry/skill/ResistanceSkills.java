package com.github.manasmods.tensura.registry.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.resist.AbnormalConditionNullification;
import com.github.manasmods.tensura.ability.skill.resist.AbnormalConditionResistance;
import com.github.manasmods.tensura.ability.skill.resist.ColdNullification;
import com.github.manasmods.tensura.ability.skill.resist.ColdResistance;
import com.github.manasmods.tensura.ability.skill.resist.CorrosionNullification;
import com.github.manasmods.tensura.ability.skill.resist.CorrosionResistance;
import com.github.manasmods.tensura.ability.skill.resist.DarknessAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.DarknessAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.EarthAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.EarthAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.ElectricityNullification;
import com.github.manasmods.tensura.ability.skill.resist.ElectricityResistance;
import com.github.manasmods.tensura.ability.skill.resist.FireAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.FireAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.GravityAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.GravityAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.HeatNullification;
import com.github.manasmods.tensura.ability.skill.resist.HeatResistance;
import com.github.manasmods.tensura.ability.skill.resist.HolyAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.HolyAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.LightAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.LightAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.MagicNullification;
import com.github.manasmods.tensura.ability.skill.resist.MagicResistance;
import com.github.manasmods.tensura.ability.skill.resist.NullificationSkill;
import com.github.manasmods.tensura.ability.skill.resist.PainResistanceSkill;
import com.github.manasmods.tensura.ability.skill.resist.ParalysisNullification;
import com.github.manasmods.tensura.ability.skill.resist.ParalysisResistanceSkill;
import com.github.manasmods.tensura.ability.skill.resist.PhysicalAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.PhysicalAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.PierceNullification;
import com.github.manasmods.tensura.ability.skill.resist.PierceResistance;
import com.github.manasmods.tensura.ability.skill.resist.PoisonNullification;
import com.github.manasmods.tensura.ability.skill.resist.PoisonResistance;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.ability.skill.resist.SpatialAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.SpatialAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.SpiritualAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.SpiritualAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.ThermalFluctuationNullification;
import com.github.manasmods.tensura.ability.skill.resist.ThermalFluctuationResistance;
import com.github.manasmods.tensura.ability.skill.resist.WaterAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.WaterAttackResistance;
import com.github.manasmods.tensura.ability.skill.resist.WindAttackNullification;
import com.github.manasmods.tensura.ability.skill.resist.WindAttackResistance;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class ResistanceSkills {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AbnormalConditionResistance> ABNORMAL_CONDITION_RESISTANCE;
   public static final RegistryObject<ColdResistance> COLD_RESISTANCE;
   public static final RegistryObject<CorrosionResistance> CORROSION_RESISTANCE;
   public static final RegistryObject<DarknessAttackResistance> DARKNESS_ATTACK_RESISTANCE;
   public static final RegistryObject<EarthAttackResistance> EARTH_ATTACK_RESISTANCE;
   public static final RegistryObject<ElectricityResistance> ELECTRICITY_RESISTANCE;
   public static final RegistryObject<FireAttackResistance> FLAME_ATTACK_RESISTANCE;
   public static final RegistryObject<GravityAttackResistance> GRAVITY_ATTACK_RESISTANCE;
   public static final RegistryObject<HeatResistance> HEAT_RESISTANCE;
   public static final RegistryObject<HolyAttackResistance> HOLY_ATTACK_RESISTANCE;
   public static final RegistryObject<LightAttackResistance> LIGHT_ATTACK_RESISTANCE;
   public static final RegistryObject<MagicResistance> MAGIC_RESISTANCE;
   public static final RegistryObject<PhysicalAttackResistance> PHYSICAL_ATTACK_RESISTANCE;
   public static final RegistryObject<ResistSkill> PAIN_RESISTANCE;
   public static final RegistryObject<ResistSkill> PARALYSIS_RESISTANCE;
   public static final RegistryObject<PierceResistance> PIERCE_RESISTANCE;
   public static final RegistryObject<PoisonResistance> POISON_RESISTANCE;
   public static final RegistryObject<SpatialAttackResistance> SPATIAL_ATTACK_RESISTANCE;
   public static final RegistryObject<SpiritualAttackResistance> SPIRITUAL_ATTACK_RESISTANCE;
   public static final RegistryObject<ThermalFluctuationResistance> THERMAL_FLUCTUATION_RESISTANCE;
   public static final RegistryObject<WaterAttackResistance> WATER_ATTACK_RESISTANCE;
   public static final RegistryObject<WindAttackResistance> WIND_ATTACK_RESISTANCE;
   public static final RegistryObject<AbnormalConditionNullification> ABNORMAL_CONDITION_NULLIFICATION;
   public static final RegistryObject<ColdNullification> COLD_NULLIFICATION;
   public static final RegistryObject<CorrosionNullification> CORROSION_NULLIFICATION;
   public static final RegistryObject<DarknessAttackNullification> DARKNESS_ATTACK_NULLIFICATION;
   public static final RegistryObject<EarthAttackNullification> EARTH_ATTACK_NULLIFICATION;
   public static final RegistryObject<ElectricityNullification> ELECTRICITY_NULLIFICATION;
   public static final RegistryObject<FireAttackNullification> FLAME_ATTACK_NULLIFICATION;
   public static final RegistryObject<GravityAttackNullification> GRAVITY_ATTACK_NULLIFICATION;
   public static final RegistryObject<HeatNullification> HEAT_NULLIFICATION;
   public static final RegistryObject<HolyAttackNullification> HOLY_ATTACK_NULLIFICATION;
   public static final RegistryObject<LightAttackNullification> LIGHT_ATTACK_NULLIFICATION;
   public static final RegistryObject<MagicNullification> MAGIC_NULLIFICATION;
   public static final RegistryObject<ResistSkill> PAIN_NULLIFICATION;
   public static final RegistryObject<ParalysisNullification> PARALYSIS_NULLIFICATION;
   public static final RegistryObject<PierceNullification> PIERCE_NULLIFICATION;
   public static final RegistryObject<PhysicalAttackNullification> PHYSICAL_ATTACK_NULLIFICATION;
   public static final RegistryObject<PoisonNullification> POISON_NULLIFICATION;
   public static final RegistryObject<SpatialAttackNullification> SPATIAL_ATTACK_NULLIFICATION;
   public static final RegistryObject<SpiritualAttackNullification> SPIRITUAL_ATTACK_NULLIFICATION;
   public static final RegistryObject<ThermalFluctuationNullification> THERMAL_FLUCTUATION_NULLIFICATION;
   public static final RegistryObject<WaterAttackNullification> WATER_ATTACK_NULLIFICATION;
   public static final RegistryObject<WindAttackNullification> WIND_ATTACK_NULLIFICATION;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      ABNORMAL_CONDITION_RESISTANCE = registry.register("abnormal_condition_resistance", AbnormalConditionResistance::new);
      COLD_RESISTANCE = registry.register("cold_resistance", ColdResistance::new);
      CORROSION_RESISTANCE = registry.register("corrosion_resistance", CorrosionResistance::new);
      DARKNESS_ATTACK_RESISTANCE = registry.register("darkness_attack_resistance", DarknessAttackResistance::new);
      EARTH_ATTACK_RESISTANCE = registry.register("earth_attack_resistance", EarthAttackResistance::new);
      ELECTRICITY_RESISTANCE = registry.register("electricity_resistance", ElectricityResistance::new);
      FLAME_ATTACK_RESISTANCE = registry.register("flame_attack_resistance", FireAttackResistance::new);
      GRAVITY_ATTACK_RESISTANCE = registry.register("gravity_attack_resistance", GravityAttackResistance::new);
      HEAT_RESISTANCE = registry.register("heat_resistance", HeatResistance::new);
      HOLY_ATTACK_RESISTANCE = registry.register("holy_attack_resistance", HolyAttackResistance::new);
      LIGHT_ATTACK_RESISTANCE = registry.register("light_attack_resistance", LightAttackResistance::new);
      MAGIC_RESISTANCE = registry.register("magic_resistance", MagicResistance::new);
      PHYSICAL_ATTACK_RESISTANCE = registry.register("physical_attack_resistance", PhysicalAttackResistance::new);
      PAIN_RESISTANCE = registry.register("pain_resistance", PainResistanceSkill::new);
      PARALYSIS_RESISTANCE = registry.register("paralysis_resistance", ParalysisResistanceSkill::new);
      PIERCE_RESISTANCE = registry.register("pierce_resistance", PierceResistance::new);
      POISON_RESISTANCE = registry.register("poison_resistance", PoisonResistance::new);
      SPATIAL_ATTACK_RESISTANCE = registry.register("spatial_attack_resistance", SpatialAttackResistance::new);
      SPIRITUAL_ATTACK_RESISTANCE = registry.register("spiritual_attack_resistance", SpiritualAttackResistance::new);
      THERMAL_FLUCTUATION_RESISTANCE = registry.register("thermal_fluctuation_resistance", ThermalFluctuationResistance::new);
      WATER_ATTACK_RESISTANCE = registry.register("water_attack_resistance", WaterAttackResistance::new);
      WIND_ATTACK_RESISTANCE = registry.register("wind_attack_resistance", WindAttackResistance::new);
      ABNORMAL_CONDITION_NULLIFICATION = registry.register("abnormal_condition_nullification", AbnormalConditionNullification::new);
      COLD_NULLIFICATION = registry.register("cold_nullification", ColdNullification::new);
      CORROSION_NULLIFICATION = registry.register("corrosion_nullification", CorrosionNullification::new);
      DARKNESS_ATTACK_NULLIFICATION = registry.register("darkness_attack_nullification", DarknessAttackNullification::new);
      EARTH_ATTACK_NULLIFICATION = registry.register("earth_attack_nullification", EarthAttackNullification::new);
      ELECTRICITY_NULLIFICATION = registry.register("electricity_nullification", ElectricityNullification::new);
      FLAME_ATTACK_NULLIFICATION = registry.register("flame_attack_nullification", FireAttackNullification::new);
      GRAVITY_ATTACK_NULLIFICATION = registry.register("gravity_attack_nullification", GravityAttackNullification::new);
      HEAT_NULLIFICATION = registry.register("heat_nullification", HeatNullification::new);
      HOLY_ATTACK_NULLIFICATION = registry.register("holy_attack_nullification", HolyAttackNullification::new);
      LIGHT_ATTACK_NULLIFICATION = registry.register("light_attack_nullification", LightAttackNullification::new);
      MAGIC_NULLIFICATION = registry.register("magic_nullification", MagicNullification::new);
      PAIN_NULLIFICATION = registry.register("pain_nullification", NullificationSkill::new);
      PARALYSIS_NULLIFICATION = registry.register("paralysis_nullification", ParalysisNullification::new);
      PIERCE_NULLIFICATION = registry.register("pierce_nullification", PierceNullification::new);
      PHYSICAL_ATTACK_NULLIFICATION = registry.register("physical_attack_nullification", PhysicalAttackNullification::new);
      POISON_NULLIFICATION = registry.register("poison_nullification", PoisonNullification::new);
      SPATIAL_ATTACK_NULLIFICATION = registry.register("spatial_attack_nullification", SpatialAttackNullification::new);
      SPIRITUAL_ATTACK_NULLIFICATION = registry.register("spiritual_attack_nullification", SpiritualAttackNullification::new);
      THERMAL_FLUCTUATION_NULLIFICATION = registry.register("thermal_fluctuation_nullification", ThermalFluctuationNullification::new);
      WATER_ATTACK_NULLIFICATION = registry.register("water_attack_nullification", WaterAttackNullification::new);
      WIND_ATTACK_NULLIFICATION = registry.register("wind_attack_nullification", WindAttackNullification::new);
   }
}
