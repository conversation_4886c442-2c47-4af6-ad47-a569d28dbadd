package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Optional;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class SniperGrenadeProjectile extends TensuraProjectile {
   public SniperGrenadeProjectile(EntityType<? extends SniperGrenadeProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(0.5F);
   }

   public SniperGrenadeProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.SNIPER_GRENADE.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(0.5F);
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("minecraft:textures/block/blast_furnace_side.png")};
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected boolean shouldGrief() {
      return false;
   }

   public void explosion(double x, double y, double z) {
      if (!(this.getExplosionRadius() <= 0.0F)) {
         super.explosion(x, y, z);
         this.m_146870_();
      }
   }

   protected void dealDamage(Entity target) {
      Entity var3 = this.m_37282_();
      if (var3 instanceof Player) {
         Player player = (Player)var3;
         target.m_6469_(DamageSourceHelper.addSkillAndCost(DamageSource.m_19344_(player), this.getMpCost(), this.getSkill()), 0.0F);
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123747_, x, y, z, 3, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      float radius = this.m_20205_();
      double x = this.m_20185_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;
      double y = this.m_20186_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;
      double z = this.m_20189_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;

      for(int j = 0; j < 10; ++j) {
         double newX = x + this.f_19796_.m_188583_() / 4.0D;
         double newY = y + this.f_19796_.m_188583_() / 4.0D;
         double newZ = z + this.f_19796_.m_188583_() / 4.0D;
         this.f_19853_.m_7106_(ParticleTypes.f_123762_, newX, newY, newZ, 0.0D, 0.0D, 0.0D);
      }

   }
}
