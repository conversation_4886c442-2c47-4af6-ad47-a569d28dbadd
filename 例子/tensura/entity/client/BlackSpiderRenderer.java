package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.entity.BlackSpiderEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class BlackSpiderRenderer extends GeoEntityRenderer<BlackSpiderEntity> {
   public BlackSpiderRenderer(Context renderManager) {
      super(renderManager, new BlackSpiderModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(BlackSpiderEntity instance) {
      return getSpiderTexture(instance);
   }

   public static ResourceLocation getSpiderTexture(BlackSpiderEntity instance) {
      if (instance.m_7770_() != null && instance.m_7770_().getString().equals("Kumoko")) {
         return new ResourceLocation("tensura", "textures/entity/black_spider/black_spider_kumoko.png");
      } else if ((Boolean)TensuraClientConfig.INSTANCE.displayConfig.arachnophobia.get()) {
         return new ResourceLocation("tensura", "textures/entity/black_spider/black_spider_safe.png");
      } else {
         return instance.isStriped() ? new ResourceLocation("tensura", "textures/entity/black_spider/black_spider_yellow.png") : new ResourceLocation("tensura", "textures/entity/black_spider/black_spider_black.png");
      }
   }

   public RenderType getRenderType(BlackSpiderEntity spider, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (spider.m_6162_()) {
         stack.m_85841_(0.2F, 0.2F, 0.2F);
      }

      return RenderType.m_110473_(this.getTextureLocation(spider));
   }
}
