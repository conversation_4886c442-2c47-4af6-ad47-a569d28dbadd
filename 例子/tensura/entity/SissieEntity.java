package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.RandomFluidSwimmingGoal;
import com.github.manasmods.tensura.api.entity.ai.SemiAquaticFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.entity.template.SwimmingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.TryFindWaterGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.monster.Guardian;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.Tags.Items;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class SissieEntity extends SwimmingTamableEntity implements IAnimatable, ITensuraMount, HasCustomInventoryScreen, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   public int miscAnimationTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public SissieEntity(EntityType<? extends SissieEntity> type, Level level) {
      super(type, level);
      this.initInventory();
      this.f_21364_ = 40;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 24.0D).m_22268_(Attributes.f_22276_, 80.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22278_, 0.4000000059604645D).m_22268_(Attributes.f_22288_, 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new TryFindWaterGoal(this));
      this.f_21345_.m_25352_(2, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(4, new RandomFluidSwimmingGoal(this, 1.0D, 8, (fluidState) -> {
         return fluidState.m_205070_(FluidTags.f_13131_);
      }));
      this.f_21345_.m_25352_(4, new SemiAquaticFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false, true));
      this.f_21345_.m_25352_(4, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(5, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(6, new SissieEntity.SissieJumpAttackGoal(this));
      this.f_21345_.m_25352_(6, new SissieEntity.SissieAttackGoal(this, 2.0D, true));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 64.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   private boolean shouldAttack(LivingEntity entity) {
      if (entity.m_20096_()) {
         if (entity.m_20186_() - this.m_20186_() > 3.0D) {
            return false;
         }

         if (this.m_20270_(entity) > 6.0F) {
            return false;
         }
      }

      if (entity instanceof Player) {
         return true;
      } else if (entity instanceof Villager) {
         return true;
      } else if (!(entity instanceof GiantCodEntity) && !(entity instanceof GiantSalmonEntity)) {
         if (entity instanceof Animal) {
            return !(entity instanceof SissieEntity);
         } else {
            return entity instanceof Guardian;
         }
      } else {
         return true;
      }
   }

   protected boolean isJumper() {
      return true;
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Saddled", this.isSaddled());
      compound.m_128379_("Chested", this.isChested());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSaddled(compound.m_128471_("Saddled"));
      this.setChested(compound.m_128471_("Chested"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean isPushedByFluid(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19310_ || super.m_6673_(source);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      SissieEntity fish = (SissieEntity)((EntityType)TensuraEntityTypes.SISSIE.get()).m_20615_(pLevel);
      if (fish == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            fish.m_21816_(uuid);
            fish.m_7105_(true);
         }

         return fish;
      }
   }

   public boolean shouldFollow() {
      return this.getMiscAnimation() == 2 ? false : super.shouldFollow();
   }

   public boolean shouldUseJumpAttack(LivingEntity target) {
      if (!this.isJumper()) {
         return false;
      } else if (this.getMiscAnimation() >= 3) {
         return false;
      } else if (this.jumpCooldown != 0) {
         return false;
      } else if (!target.m_20069_() && !(target.m_20202_() instanceof Boat)) {
         Vec3 eye = target.m_146892_();
         Vec3 below = eye.m_82520_(0.0D, -10.0D, 0.0D);
         return this.f_19853_.m_45547_(new ClipContext(eye, below, Block.COLLIDER, Fluid.NONE, this)).m_6662_().equals(Type.MISS);
      } else {
         return false;
      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
         this.m_5496_(SoundEvents.f_11865_, 1.0F, 1.0F);
      }

      return flag;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         LivingEntity target = this.m_5448_();
         if (this.getMiscAnimation() >= 3 && (target != null || this.getMiscAnimation() == 5)) {
            float f = (float)this.m_21133_(Attributes.f_22281_);
            if (this.getMiscAnimation() == 3) {
               f *= 1.5F;
            }

            if (target != null) {
               this.m_21566_().m_6849_(target.m_20185_(), target.m_20186_(), target.m_20189_(), this.m_21566_().m_24999_());
               this.m_21563_().m_148051_(target);
            }

            double radius = this.getMiscAnimation() == 4 ? 1.0D : 0.0D;
            AABB aabb = this.m_20191_().m_82383_(this.m_20252_(1.0F).m_82490_(this.m_6162_() ? 2.0D : 4.0D)).m_82400_(radius);
            List<LivingEntity> list = this.m_9236_().m_6443_(LivingEntity.class, aabb, (livingx) -> {
               return !livingx.m_7306_(this) && livingx.m_20202_() != this && livingx.m_6084_();
            });
            Iterator var7;
            LivingEntity living;
            if (this.getMiscAnimation() != 3 && this.getMiscAnimation() != 5) {
               if (this.getMiscAnimation() == 4) {
                  var7 = list.iterator();

                  while(var7.hasNext()) {
                     living = (LivingEntity)var7.next();
                     living.m_6469_(DamageSource.m_19370_(this), f);
                     if (living == target) {
                        this.setMiscAnimation(0);
                     }
                  }
               }
            } else {
               var7 = list.iterator();

               while(var7.hasNext()) {
                  living = (LivingEntity)var7.next();
                  living.m_6469_(DamageSource.m_19370_(this), f);
                  if (living == target) {
                     this.setMiscAnimation(0);
                  }
               }
            }
         }

         boolean shouldStop = (this.getMiscAnimation() == 3 || this.getMiscAnimation() == 4) && target == null;
         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation()) || shouldStop) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   protected void swimmingParticle() {
   }

   private int getAnimationTick(int miscAnimation) {
      if (miscAnimation == 5) {
         return 20;
      } else if (miscAnimation > 2) {
         return 600;
      } else {
         return miscAnimation == 2 ? 38 : 9;
      }
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 2) {
         this.setMiscAnimation(5);
      }
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (!this.m_21824_()) {
            return super.m_6071_(player, hand);
         } else {
            if (!this.m_6162_() && this.m_21830_(player)) {
               Item item = itemstack.m_41720_();
               if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN) && (itemstack.m_41613_() >= 2 || player.m_150110_().f_35937_)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(2);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }

               if (this.isSaddled() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            }

            if (!this.m_21830_(player) || !player.m_36341_() && (this.isSaddled() || this.isChested())) {
               if (!this.isSaddled() || this.getControllingPassenger() == null && !this.m_21830_(player)) {
                  if (this.isChested()) {
                     this.m_213583_(player);
                  }
               } else {
                  if (this.m_21830_(player)) {
                     this.m_21839_(false);
                     this.setWandering(false);
                  }

                  player.m_7998_(this, false);
               }
            } else {
               this.commanding(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }
   }

   public InteractionResult handleEating(Player pPlayer, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!pPlayer.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_27595_(pPlayer);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (this.m_6162_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_8035_();
            this.m_146740_(m_216967_(-i), true);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(5.0F);
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(54) {
         public boolean m_6542_(Player player) {
            return SissieEntity.this.m_6084_() && !SissieEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39962_, menu, inventory, SissieEntity.this.inventory, 6);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public double m_6048_() {
      return (double)this.m_6972_(Pose.STANDING).f_20378_ - 0.5D;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -1.0F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         float yaw;
         float f3;
         float f;
         switch(this.m_20197_().indexOf(passenger)) {
         case 1:
            yaw = this.m_146908_() * 0.017453292F;
            f3 = Mth.m_14031_((float)((double)yaw + Math.toRadians(145.0D)));
            f = -Mth.m_14089_((float)((double)yaw + Math.toRadians(145.0D)));
            passenger.m_6034_(this.m_20185_() + extraX * 1.25D + (double)f3 * 1.5D, yOffset, this.m_20189_() + extraZ * 1.25D + (double)f * 1.5D);
            break;
         case 2:
            yaw = this.m_146908_() * 0.017453292F;
            f3 = Mth.m_14031_((float)((double)yaw + Math.toRadians(-145.0D)));
            f = -Mth.m_14089_((float)((double)yaw + Math.toRadians(-145.0D)));
            passenger.m_6034_(this.m_20185_() + extraX * 1.25D + (double)f3 * 1.5D, yOffset, this.m_20189_() + extraZ * 1.25D + (double)f * 1.5D);
            break;
         default:
            passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
         }

      }
   }

   public boolean m_7310_(Entity other) {
      return this.m_20197_().size() < 3;
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (pJumpPower >= 50) {
         if (this.canJumpOutOfWater()) {
            this.setMiscAnimation(2);
         }

      }
   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 travelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(0.0F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && this.canJumpOutOfWater() && !this.isPlayerJumping() && this.isInFluidType()) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.2F;
            if (!this.m_6109_()) {
               if (controller instanceof Player) {
                  this.m_20256_(Vec3.f_82478_);
               }
            } else {
               this.m_7910_((float)this.m_21133_(Attributes.f_22279_));
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && controller.f_20899_) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.07D, 0.0D));
               } else if (this.isInFluidType() && TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                  this.descending(this, controller);
               }

               AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
               if (instance != null) {
                  instance.m_22100_(controller.m_20142_() ? 10.0D : 8.0D);
               }

               super.m_7023_(new Vec3((double)f, travelVector.f_82480_, (double)f1));
            }

            if (this.isInFluidType()) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
            if (instance != null && instance.m_22115_() != 1.0D) {
               instance.m_22100_(1.0D);
            }

            this.f_20887_ = 0.04F;
            super.m_7023_(travelVector);
         }
      }

   }

   public int m_5792_() {
      return 1;
   }

   public static boolean checkSissieSpawnRules(EntityType<SissieEntity> type, LevelAccessor level, MobSpawnType reason, BlockPos pos, RandomSource source) {
      return pos.m_123342_() > 30 && pos.m_123342_() < level.m_5736_() ? level.m_6425_(pos).m_205070_(FluidTags.f_13131_) : false;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.sissieSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (!this.isInFluidType()) {
         if (!this.f_19853_.m_8055_(this.m_20183_().m_7495_()).m_60767_().m_76334_() && (!this.f_19853_.m_8055_(this.m_20183_().m_6625_(2)).m_60767_().m_76334_() || !this.f_19853_.m_6425_(this.m_20183_().m_7495_()).m_76178_())) {
            if (this.getMiscAnimation() != 2 && this.f_19853_.m_6425_(this.m_20183_().m_7495_()).m_76178_() && this.f_19853_.m_6425_(this.m_20183_().m_6625_(2)).m_76178_() && this.f_19853_.m_6425_(this.m_20183_().m_6625_(3)).m_76178_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.fall", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.flop", EDefaultLoopTypes.LOOP));
         }
      } else if (this.getMiscAnimation() != 3 && this.getMiscAnimation() != 5) {
         if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.chase_thrash", EDefaultLoopTypes.LOOP));
         } else if (event.isMoving()) {
            if (this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.swim_fast", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.swim", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.idle", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.chase_bite", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.sissie.jump_attack", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(SissieEntity.class, EntityDataSerializers.f_135028_);
      SADDLED = SynchedEntityData.m_135353_(SissieEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(SissieEntity.class, EntityDataSerializers.f_135035_);
   }

   static class SissieJumpAttackGoal extends SwimmingTamableEntity.JumpAttackGoal {
      private final SissieEntity entity;

      public SissieJumpAttackGoal(SissieEntity entity) {
         super(entity);
         this.entity = entity;
      }

      public void m_8056_() {
         super.m_8056_();
         LivingEntity target = this.entity.m_5448_();
         if (target != null && !(this.entity.m_20275_(target.m_20185_(), this.entity.m_20186_(), target.m_20189_()) >= 150.0D)) {
            this.entity.setMiscAnimation(2);
         }
      }
   }

   static class SissieAttackGoal extends SwimmingTamableEntity.JumperMeleeAttackGoal {
      private SissieEntity entity;

      public SissieAttackGoal(SissieEntity entity, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(entity, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.entity = entity;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         if (this.m_25564_()) {
            int randomAttack = this.randomAttack(pDistToEnemySqr);
            if (randomAttack >= 3) {
               this.m_25563_();
               if (this.entity.getMiscAnimation() == 0) {
                  this.entity.setMiscAnimation(randomAttack);
               }
            } else if (pDistToEnemySqr <= this.m_6639_(pEnemy)) {
               this.m_25563_();
               if (this.entity.getMiscAnimation() == 0) {
                  this.entity.setMiscAnimation(randomAttack);
               }

               this.entity.m_7327_(pEnemy);
            }

         }
      }

      protected int randomAttack(double distance) {
         if (distance >= 144.0D || this.entity.f_19796_.m_188503_(9) == 3) {
            if (this.entity.f_19796_.m_188503_(6) == 3) {
               return 3;
            }

            if (this.entity.f_19796_.m_188503_(20) == 3) {
               return 4;
            }
         }

         return 1;
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return super.m_6639_(pAttackTarget) + 9.0D;
      }
   }
}
