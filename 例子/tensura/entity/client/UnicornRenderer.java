package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.UnicornEntity;
import com.github.manasmods.tensura.entity.client.layer.UnicornArmorLayer;
import com.github.manasmods.tensura.entity.client.layer.UnicornHornLayer;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;

public class UnicornRenderer<T extends UnicornEntity> extends <PERSON><PERSON><PERSON><PERSON><PERSON><T, UnicornModel<T>> {
   public UnicornRenderer(Context context) {
      super(context, new UnicornModel(context.m_174023_(UnicornModel.UNICORN)), 0.75F);
      this.m_115326_(new UnicornArmorLayer(this, context.m_174027_()));
      this.m_115326_(new UnicornHornLayer(this));
   }

   public ResourceLocation getTextureLocation(T entity) {
      return new ResourceLocation("tensura", "textures/entity/unicorn/unicorn.png");
   }

   protected void scale(T pLivingEntity, PoseStack pMatrixStack, float pPartialTickTime) {
      pMatrixStack.m_85841_(1.1F, 1.1F, 1.1F);
   }
}
