package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import java.util.List;
import java.util.Objects;
import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.monster.EnderMan;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;

public class InvisibleArrow extends AbstractArrow {
   public InvisibleArrow(EntityType<? extends InvisibleArrow> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public InvisibleArrow(Level pLevel, LivingEntity pShooter) {
      super((EntityType)TensuraEntityTypes.INVISIBLE_ARROW.get(), pShooter, pLevel);
   }

   public InvisibleArrow(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.INVISIBLE_ARROW.get(), pX, pY, pZ, pLevel);
   }

   public ItemStack m_7941_() {
      return new ItemStack((ItemLike)TensuraToolItems.INVISIBLE_ARROW.get());
   }

   protected void m_5790_(EntityHitResult pResult) {
      super.m_5790_(pResult);
      Entity entity = pResult.m_82443_();
      Entity owner = this.m_37282_();
      if (entity.m_6095_() == EntityType.f_20566_) {
         int i = Mth.m_14165_(Mth.m_14008_(this.m_20184_().m_82553_() * 2.0D, 0.0D, 2.147483647E9D));
         if (this.m_6060_()) {
            entity.m_20254_(5);
         }

         entity.m_6469_(DamageSource.f_19319_, (float)i);
         if (entity instanceof LivingEntity) {
            LivingEntity livingentity = (LivingEntity)entity;
            if (!this.f_19853_.f_46443_) {
               if (owner instanceof LivingEntity) {
                  LivingEntity livingOwner = (LivingEntity)owner;
                  if (owner instanceof Player) {
                     Player player = (Player)owner;
                     if (!player.m_7500_() && entity instanceof EnderMan) {
                        EnderMan enderMan = (EnderMan)entity;
                        enderMan.m_6710_(livingOwner);
                     }
                  }

                  livingOwner.m_21335_(entity);
                  EnchantmentHelper.m_44823_(livingentity, livingOwner);
                  EnchantmentHelper.m_44896_(livingOwner, livingentity);
               }

               if (owner instanceof ServerPlayer) {
                  ServerPlayer serverPlayer = (ServerPlayer)owner;
                  if (!entity.m_6084_() && this.m_36795_()) {
                     CriteriaTriggers.f_10556_.m_46871_(serverPlayer, List.of(entity));
                  }
               }
            }
         }

         this.m_5496_(this.m_7239_(), 1.0F, 1.2F / (this.f_19796_.m_188501_() * 0.2F + 0.9F));
         if (this.m_36796_() <= 0) {
            this.m_146870_();
         }
      }

   }

   protected float m_6882_() {
      return 0.5F;
   }

   public boolean m_20145_() {
      return true;
   }

   public boolean m_20177_(Player pPlayer) {
      if (pPlayer.m_5833_()) {
         return false;
      } else if (this.m_37282_() == pPlayer) {
         return false;
      } else if (pPlayer.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         return ((MobEffectInstance)Objects.requireNonNull(pPlayer.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_() < 3;
      } else {
         return this.m_20145_();
      }
   }

   public void m_6901_() {
      if (++this.f_36697_ >= (Integer)TensuraConfig.INSTANCE.entitiesConfig.spearDespawn.get()) {
         this.m_146870_();
      }

   }
}
