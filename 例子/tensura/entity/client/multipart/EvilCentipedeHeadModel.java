package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.EvilCentipedeEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class EvilCentipedeHeadModel extends AnimatedGeoModel<EvilCentipedeEntity> {
   public ResourceLocation getModelResource(EvilCentipedeEntity object) {
      return new ResourceLocation("tensura", "geo/evil_centipede_head.geo.json");
   }

   public ResourceLocation getTextureResource(EvilCentipedeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/evil_centipede/evil_centipede.png");
   }

   public ResourceLocation getAnimationResource(EvilCentipedeEntity entity) {
      return new ResourceLocation("tensura", "animations/multipart/evil_centipede_head.animation.json");
   }
}
