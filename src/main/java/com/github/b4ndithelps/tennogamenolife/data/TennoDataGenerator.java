package com.github.b4ndithelps.tennogamenolife.data;

import com.github.b4ndithelps.tennogamenolife.data.gen.TennoGearEPProvider;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.MOD)
public class TennoDataGenerator {
    @SubscribeEvent
    public static void gatherData(GatherDataEvent event) {
        var generator = event.getGenerator();
        var existingFileHelper = event.getExistingFileHelper();
        generator.addProvider(event.includeServer(), new TennoGearEPProvider(event));
    }
} 