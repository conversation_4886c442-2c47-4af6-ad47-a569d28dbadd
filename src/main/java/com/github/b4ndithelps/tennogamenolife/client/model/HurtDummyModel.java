package com.github.b4ndithelps.tennogamenolife.client.model;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.entity.HurtDummyEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.*;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

public class HurtDummyModel extends EntityModel<HurtDummyEntity> {
    // 模型层位置
    public static final ModelLayerLocation LAYER_LOCATION = new ModelLayerLocation(
            new ResourceLocation(Tenreincarnation.MODID, "hurt_dummy"), "main");
    
	private final ModelPart bone;
	private final ModelPart bone7;
	private final ModelPart bone6;
	private final ModelPart bone5;
	private final ModelPart bone4;
	private final ModelPart bone3;
	private final ModelPart bone2;

	public HurtDummyModel(ModelPart root) {
		this.bone = root.getChild("bone");
		this.bone7 = root.getChild("bone7");
		this.bone6 = this.bone7.getChild("bone6");
		this.bone5 = this.bone7.getChild("bone5");
		this.bone4 = this.bone7.getChild("bone4");
		this.bone3 = this.bone7.getChild("bone3");
		this.bone2 = this.bone7.getChild("bone2");
	}

	public static LayerDefinition createBodyLayer() {
		MeshDefinition meshdefinition = new MeshDefinition();
		PartDefinition partdefinition = meshdefinition.getRoot();

		PartDefinition bone = partdefinition.addOrReplaceChild("bone", CubeListBuilder.create().texOffs(48, 25).addBox(-1.0F, -1.0F, -1.0F, 2.0F, 16.0F, 2.0F, new CubeDeformation(0.0F)), PartPose.offset(0.0F, 9.0F, 0.0F));

		PartDefinition bone7 = partdefinition.addOrReplaceChild("bone7", CubeListBuilder.create(), PartPose.offset(0.0F, -3.0F, 0.0F));

		PartDefinition bone6 = bone7.addOrReplaceChild("bone6", CubeListBuilder.create().texOffs(48, 21).addBox(0.0F, -1.0F, -1.0F, 17.0F, 2.0F, 2.0F, new CubeDeformation(0.0F)), PartPose.offset(8.0F, -4.0F, 0.0F));

		PartDefinition bone5 = bone7.addOrReplaceChild("bone5", CubeListBuilder.create().texOffs(48, 17).addBox(-17.0F, -1.0F, -1.0F, 17.0F, 2.0F, 2.0F, new CubeDeformation(0.0F)), PartPose.offset(-7.0F, -4.0F, 0.0F));

		PartDefinition bone4 = bone7.addOrReplaceChild("bone4", CubeListBuilder.create().texOffs(0, 0).addBox(-9.0F, -1.0F, -8.0F, 19.0F, 1.0F, 16.0F, new CubeDeformation(0.0F))
		.texOffs(46, 44).addBox(-6.0F, -9.0F, -5.0F, 13.0F, 8.0F, 10.0F, new CubeDeformation(0.0F)), PartPose.offset(0.0F, -18.0F, 0.0F));

		PartDefinition bone3 = bone7.addOrReplaceChild("bone3", CubeListBuilder.create().texOffs(0, 44).addBox(-5.0F, -11.0F, -6.0F, 11.0F, 11.0F, 12.0F, new CubeDeformation(0.0F)), PartPose.offset(0.0F, -7.0F, 0.0F));

		PartDefinition bone2 = bone7.addOrReplaceChild("bone2", CubeListBuilder.create().texOffs(0, 17).addBox(-7.0F, -18.0F, -5.0F, 15.0F, 18.0F, 9.0F, new CubeDeformation(0.0F)), PartPose.offset(0.0F, 11.0F, 0.0F));

		return LayerDefinition.create(meshdefinition, 128, 128);
	}

    @Override
    public void setupAnim(HurtDummyEntity entity, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch) {
        // 重置模型部件的姿态
        this.bone7.xRot = 0.0F;
        this.bone7.yRot = 0.0F;
        this.bone7.zRot = 0.0F;
        
        // 如果实体正在播放动画，应用动画
        if (entity.isPlayingAnimation()) {
            // 获取动画进度 (0.0-1.0)
            float progress = entity.getAnimationProgress();
            
            // 手动应用动画 - 左右摇摆
            // 进度 0.0-0.25: 向左摇摆 (0 -> -10度)
            // 进度 0.25-0.5: 中间位置 (-10度 -> 0度)
            // 进度 0.5-0.75: 向右摇摆 (0 -> 10度)
            // 进度 0.75-1.0: 回到中间 (10度 -> 0度)
            
            if (progress < 0.25f) {
                // 向左摇摆
                this.bone7.yRot = Mth.lerp(progress * 4, 0.0F, (float) Math.toRadians(-10.0F));
            } else if (progress < 0.5f) {
                // 回中
                this.bone7.yRot = Mth.lerp((progress - 0.25f) * 4, (float) Math.toRadians(-10.0F), 0.0F);
            } else if (progress < 0.75f) {
                // 向右摇摆
                this.bone7.yRot = Mth.lerp((progress - 0.5f) * 4, 0.0F, (float) Math.toRadians(10.0F));
            } else {
                // 回中
                this.bone7.yRot = Mth.lerp((progress - 0.75f) * 4, (float) Math.toRadians(10.0F), 0.0F);
            }
        }
    }

    @Override
    public void renderToBuffer(PoseStack poseStack, VertexConsumer vertexConsumer, int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
        bone.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
        bone7.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
    }
} 