package com.github.manasmods.tensura.entity.magic.field;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import java.util.Iterator;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;
import net.minecraftforge.fluids.FluidType;

public class AreaField extends Projectile {
   private static final EntityDataAccessor<Integer> LIFE;
   private static final EntityDataAccessor<Integer> TICK_EACH_HIT;
   private static final EntityDataAccessor<Float> RADIUS;
   private static final EntityDataAccessor<Float> VISUAL_RADIUS;
   protected float damage = 0.0F;
   protected double mpCost = 0.0D;
   protected ManasSkillInstance skill = null;

   public AreaField(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19794_ = true;
   }

   protected void m_8097_() {
      this.m_20088_().m_135372_(LIFE, 600);
      this.m_20088_().m_135372_(TICK_EACH_HIT, 20);
      this.m_20088_().m_135372_(RADIUS, 5.0F);
      this.m_20088_().m_135372_(VISUAL_RADIUS, 0.0F);
   }

   protected void m_7378_(CompoundTag pCompound) {
      this.f_19797_ = pCompound.m_128451_("Age");
      this.setLife(pCompound.m_128451_("Life"));
      this.setTickEachHit(pCompound.m_128451_("TickEachHit"));
      this.setDamage(pCompound.m_128457_("Damage"));
      this.setMpCost(pCompound.m_128459_("MPCost"));
      this.setRadius(pCompound.m_128457_("Radius"));
      this.setVisualRadius(pCompound.m_128457_("VisualRadius"));
      if (this.skill != null) {
         pCompound.m_128365_("skill", this.skill.toNBT());
      }

      super.m_7378_(pCompound);
   }

   protected void m_7380_(CompoundTag pCompound) {
      pCompound.m_128405_("Age", this.f_19797_);
      pCompound.m_128405_("Life", this.getLife());
      pCompound.m_128405_("TickEachHit", this.getTickEachHit());
      pCompound.m_128350_("Damage", this.getDamage());
      pCompound.m_128347_("MPCost", this.getMpCost());
      pCompound.m_128350_("Radius", this.getRadius());
      pCompound.m_128350_("VisualRadius", this.getVisualRadius());
      if (pCompound.m_128441_("skill")) {
         Tag var3 = pCompound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

      super.m_7380_(pCompound);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (RADIUS.equals(pKey)) {
         this.m_6210_();
         if (this.getRadius() < 0.1F) {
            this.m_146870_();
         }
      }

      super.m_7350_(pKey);
   }

   public int getLife() {
      return (Integer)this.m_20088_().m_135370_(LIFE);
   }

   public void setLife(int life) {
      this.m_20088_().m_135381_(LIFE, life);
   }

   public float getRadius() {
      return (Float)this.m_20088_().m_135370_(RADIUS);
   }

   public void setRadius(float pRadius) {
      this.m_20088_().m_135381_(RADIUS, Mth.m_14036_(pRadius, 0.0F, 50.0F));
      this.m_6210_();
   }

   public float getVisualRadius() {
      return (Float)this.m_20088_().m_135370_(VISUAL_RADIUS);
   }

   public void setVisualRadius(float pRadius) {
      this.m_20088_().m_135381_(VISUAL_RADIUS, pRadius);
   }

   public int getTickEachHit() {
      return (Integer)this.m_20088_().m_135370_(TICK_EACH_HIT);
   }

   public void setTickEachHit(int tick) {
      this.m_20088_().m_135381_(TICK_EACH_HIT, tick);
   }

   public boolean isInstant() {
      return false;
   }

   protected boolean m_5603_(Entity pTarget) {
      return true;
   }

   public boolean isPushedByFluid(FluidType type) {
      return false;
   }

   public boolean m_6060_() {
      return false;
   }

   public void m_6210_() {
      double d0 = this.m_20185_();
      double d1 = this.m_20186_();
      double d2 = this.m_20189_();
      super.m_6210_();
      this.m_6034_(d0, d1, d2);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      return EntityDimensions.m_20395_(this.getRadius() * 2.0F, this.getRadius() * 2.0F);
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.f_19797_ > this.getLife()) {
         this.m_146870_();
      }

      if (this.getVisualRadius() != this.getRadius()) {
         this.setVisualRadius(Math.min(this.getVisualRadius() + 0.25F, this.getRadius()));
      }

      if (this.f_19797_ == 1 && this.isInstant() && !this.f_19853_.m_5776_()) {
         this.hitTarget(true);
      }

      if (this.m_9236_().m_5776_()) {
         this.ambientParticles();
      } else if (this.f_19797_ == 1 || this.f_19797_ % this.getTickEachHit() == 0) {
         this.hitTarget(false);
      }

   }

   protected void hitTarget(boolean instant) {
      if (!this.m_9236_().m_5776_()) {
         List<LivingEntity> targets = this.m_9236_().m_45976_(LivingEntity.class, this.m_20191_());
         Iterator var3 = targets.iterator();

         while(var3.hasNext()) {
            LivingEntity target = (LivingEntity)var3.next();
            if (this.m_5603_(target) && !(target.m_20275_(this.m_20185_(), this.m_20186_() + (double)this.getRadius(), this.m_20189_()) > (double)(this.getRadius() * this.getRadius()))) {
               this.applyEffect(target, instant);
            }
         }

      }
   }

   public void applyEffect(LivingEntity target, boolean instant) {
   }

   public void ambientParticles() {
   }

   public float getDamage() {
      return this.damage;
   }

   public void setDamage(float damage) {
      this.damage = damage;
   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   static {
      LIFE = SynchedEntityData.m_135353_(AreaField.class, EntityDataSerializers.f_135028_);
      TICK_EACH_HIT = SynchedEntityData.m_135353_(AreaField.class, EntityDataSerializers.f_135028_);
      RADIUS = SynchedEntityData.m_135353_(AreaField.class, EntityDataSerializers.f_135029_);
      VISUAL_RADIUS = SynchedEntityData.m_135353_(AreaField.class, EntityDataSerializers.f_135029_);
   }
}
