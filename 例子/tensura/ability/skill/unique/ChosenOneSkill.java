package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.HakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.HeroHakiSkill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class ChosenOneSkill extends Skill {
   private static final String HAKI = "896381ea-3f60-48cf-bc8e-49267cca6b9a";

   public ChosenOneSkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "896381ea-3f60-48cf-bc8e-49267cca6b9a", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 75000.0D;
   }

   public double learningCost() {
      return 2000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.chosen_one.haki");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.chosen_one.charisma");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 25.0D;
         break;
      case 2:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled() || this.isInSlot(entity);
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity living, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         if (living instanceof Player) {
            Player player = (Player)living;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               cap.setBlessed(true);
            });
            TensuraPlayerCapability.sync(player);
         }

      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.isToggled()) {
         entity.m_7292_(new MobEffectInstance(MobEffects.f_19595_, 240, 4, false, false, false));
      }

      if (this.isInSlot(entity)) {
         entity.m_7292_(new MobEffectInstance(MobEffects.f_19621_, 240, 4, false, false, false));
         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
            return living.m_6084_() && (living.m_7307_(entity) || living.m_7306_(entity));
         });
         if (list.isEmpty()) {
            return;
         }

         Iterator var4 = list.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.ALLY_BOOST.get(), 240, 1, false, false, false);
         }
      }

   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_(MobEffects.f_19595_);
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         HeroHakiSkill.activateHeroHaki(instance, entity, heldTicks);
         return true;
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         HakiSkill.changeEPUsed(instance, entity, delta);
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 1) {
         if (this.hasAttributeApplied(entity, Attributes.f_22279_, "896381ea-3f60-48cf-bc8e-49267cca6b9a")) {
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 2) {
         Level level = entity.m_9236_();
         List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(10.0D), (targetx) -> {
            return !targetx.m_7306_(entity) && targetx.m_6084_() && !targetx.m_7307_(entity);
         });
         if (!list.isEmpty()) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               this.addMasteryPoint(instance, entity);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 1.0F, 1.0F);
               Iterator var5 = list.iterator();

               while(true) {
                  LivingEntity target;
                  NeutralMob mob;
                  do {
                     Mob mob;
                     do {
                        do {
                           do {
                              do {
                                 if (!var5.hasNext()) {
                                    return;
                                 }

                                 target = (LivingEntity)var5.next();
                              } while(target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_CHARISMA));
                           } while(!CharmSkill.canMindControl(target, level));
                        } while(target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()));

                        if (!(target instanceof Mob)) {
                           break;
                        }

                        mob = (Mob)target;
                     } while(mob.m_5912_());

                     if (!(target instanceof NeutralMob)) {
                        break;
                     }

                     mob = (NeutralMob)target;
                  } while(mob.m_21660_());

                  if (SkillHelper.getSubordinateOwner(target) == null && !SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
                     int duration = SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get()) ? 1200 : 2400;
                     SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.MIND_CONTROL.get(), duration, 1, false, false, false, true);
                     if (target.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get())) {
                        TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                           if (!Objects.equals(cap.getTemporaryOwner(), entity.m_20148_())) {
                              cap.setTemporaryOwner(entity.m_20148_());
                              if (entity instanceof Player) {
                                 Player player = (Player)entity;
                                 if (target instanceof TamableAnimal) {
                                    TamableAnimal animal = (TamableAnimal)target;
                                    animal.m_21828_(player);
                                 } else if (target instanceof TensuraHorseEntity) {
                                    TensuraHorseEntity horse = (TensuraHorseEntity)target;
                                    horse.m_30637_(player);
                                 }
                              }

                              TensuraEPCapability.sync(target);
                              entity.m_21011_(InteractionHand.MAIN_HAND, true);
                              TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_);
                           }

                        });
                     }
                  }
               }
            }
         }
      }
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
      if (instance.isMastered(owner)) {
         if (e.getSource().m_7639_() != owner) {
            LivingEntity entity = e.getEntity();
            if (!entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
               if (!(entity instanceof Player) || entity.m_9236_().m_6106_().m_5466_()) {
                  List<ManasSkillInstance> list = List.copyOf(SkillAPI.getSkillsFrom(entity).getLearnedSkills());
                  if (!list.isEmpty()) {
                     Iterator var6 = list.iterator();

                     while(var6.hasNext()) {
                        ManasSkillInstance targetInstance = (ManasSkillInstance)var6.next();
                        if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this && !(targetInstance.getSkill() instanceof SpiritualMagic)) {
                           SkillPlunderEvent event = new SkillPlunderEvent(entity, owner, true, targetInstance.getSkill());
                           if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(owner, event.getSkill(), instance.getRemoveTime())) {
                              SkillAPI.getSkillsFrom(entity).forgetSkill(event.getSkill());
                              if (owner instanceof Player) {
                                 Player player = (Player)owner;
                                 player.m_5661_(Component.m_237110_("tensura.skill.acquire_fallen", new Object[]{event.getSkill().getName(), entity.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                                 player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                              }
                           }
                        }
                     }

                  }
               }
            }
         }
      }
   }
}
