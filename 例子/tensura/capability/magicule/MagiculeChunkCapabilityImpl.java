package com.github.manasmods.tensura.capability.magicule;

import com.github.manasmods.tensura.Tensura;
import com.github.manasmods.tensura.api.magicule.MagiculeEvent;
import com.github.manasmods.tensura.api.magicule.MagiculeModifier;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncMagiculePacket;
import com.mojang.datafixers.util.Pair;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import lombok.NonNull;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class MagiculeChunkCapabilityImpl implements MagiculeChunkCapability {
   static final Capability<MagiculeChunkCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<MagiculeChunkCapability>() {
   });
   private static final ResourceLocation IDENTIFIER = new ResourceLocation("tensura", "magicule");
   private long magiculeSeed = -1L;
   private double maxMagicule;
   private double regenerationRate;
   private double magiculePercentage;
   @Nullable
   private LevelChunk chunk = null;
   private boolean dirty = false;
   private int syncCounter;
   private Map<BlockPos, MagiculeChunkCapabilityImpl.MagiculeCacheEntry> magiculeCache = new HashMap();

   @SubscribeEvent
   static void attach(AttachCapabilitiesEvent<LevelChunk> e) {
      e.addCapability(IDENTIFIER, new MagiculeCapablilityProvider());
   }

   public static MagiculeChunkCapability get(LevelChunk chunk) {
      MagiculeChunkCapabilityImpl capability = (MagiculeChunkCapabilityImpl)chunk.getCapability(CAPABILITY).orElseThrow(() -> {
         return new IllegalStateException("Magicule capability not found!");
      });
      capability.initialize(chunk);
      return capability;
   }

   public MagiculeChunkCapabilityImpl() {
      this.maxMagicule = (Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagicule.get();
      this.regenerationRate = (Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagiculeRegeneration.get();
      this.syncCounter = (Integer)TensuraConfig.INSTANCE.magiculeConfig.magiculeSyncInterval.get();
   }

   private boolean isInitialized() {
      if (this.chunk == null) {
         return true;
      } else if (this.chunk.m_62953_().m_5776_()) {
         return true;
      } else {
         return this.magiculeSeed == (Long)TensuraConfig.INSTANCE.magiculeConfig.magiculeSeed.get();
      }
   }

   public void tick() {
      if (this.isInitialized()) {
         if (this.chunk != null) {
            this.regenerationTick();
            this.cacheTick();
            this.syncTick();
         }
      }
   }

   public double getMagicule() {
      return this.maxMagicule * this.magiculePercentage;
   }

   private void regenerationTick() {
      if (this.magiculePercentage != 1.0D) {
         MagiculeEvent.Regeneration event = new MagiculeEvent.Regeneration(this.chunk, this);
         if (!MinecraftForge.EVENT_BUS.post(event)) {
            this.magiculePercentage = 1.0D / this.maxMagicule * event.getNewMagicule();
            this.markDirty();
         }
      }
   }

   private void syncTick() {
      if (--this.syncCounter <= 0) {
         this.syncCounter = (Integer)TensuraConfig.INSTANCE.magiculeConfig.magiculeSyncInterval.get();
         if (this.dirty) {
            this.sync();
            this.dirty = false;
         }
      }
   }

   private void cacheTick() {
      this.magiculeCache.keySet().removeIf((pos) -> {
         MagiculeChunkCapabilityImpl.MagiculeCacheEntry entry = (MagiculeChunkCapabilityImpl.MagiculeCacheEntry)this.magiculeCache.get(pos);
         entry.tick();
         return entry.isOutdated();
      });
   }

   private void markDirty() {
      this.dirty = true;
      if (this.chunk != null) {
         this.chunk.m_8092_(true);
      }
   }

   private void initialize(@NonNull LevelChunk chunk) {
      if (chunk == null) {
         throw new NullPointerException("chunk is marked non-null but is null");
      } else {
         this.chunk = chunk;
         if (!this.isInitialized()) {
            this.resetBaseValues();
            MagiculeEvent.Initialization event = new MagiculeEvent.Initialization(chunk, this);
            MinecraftForge.EVENT_BUS.post(event);
            this.maxMagicule = event.getNewMaxMagicule();
            this.regenerationRate = event.getNewRegenerationRate();
            this.magiculePercentage = 1.0D;
            this.magiculeSeed = (Long)TensuraConfig.INSTANCE.magiculeConfig.magiculeSeed.get();
            this.markDirty();
         }
      }
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();
      tag.m_128356_("magiculeSeed", this.magiculeSeed);
      tag.m_128347_("maxMagicule", this.maxMagicule);
      tag.m_128347_("regenerationRate", this.regenerationRate);
      tag.m_128347_("magiculePercentage", this.magiculePercentage);
      return tag;
   }

   public void deserializeNBT(CompoundTag tag) {
      this.magiculeSeed = tag.m_128454_("magiculeSeed");
      this.maxMagicule = tag.m_128459_("maxMagicule");
      this.regenerationRate = tag.m_128459_("regenerationRate");
      this.magiculePercentage = tag.m_128459_("magiculePercentage");
   }

   @Nullable
   private MagiculeChunkCapabilityImpl.MagiculeCacheEntry getOrCreateCacheEntry(BlockPos pos) {
      return (MagiculeChunkCapabilityImpl.MagiculeCacheEntry)this.magiculeCache.computeIfAbsent(pos, (position) -> {
         if (this.chunk == null) {
            return null;
         } else {
            MagiculeEvent.RegisterModifier event = new MagiculeEvent.RegisterModifier(this.chunk, this, position);
            MinecraftForge.EVENT_BUS.post(event);
            return MagiculeChunkCapabilityImpl.MagiculeCacheEntry.of(event.getModifiers());
         }
      });
   }

   public double getMaxMagicule(BlockPos pos) {
      MagiculeChunkCapabilityImpl.MagiculeCacheEntry entry = this.getOrCreateCacheEntry(pos);
      return entry == null ? this.maxMagicule : entry.getMaxMagicule(this.maxMagicule);
   }

   public double getMagicule(BlockPos pos) {
      MagiculeChunkCapabilityImpl.MagiculeCacheEntry entry = this.getOrCreateCacheEntry(pos);
      return entry == null ? this.maxMagicule * this.magiculePercentage : entry.getMagicule(this.maxMagicule, this.magiculePercentage);
   }

   private void sync() {
      if (this.chunk != null) {
         if (!this.chunk.m_62953_().m_5776_()) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_CHUNK.with(() -> {
               return this.chunk;
            }), new SyncMagiculePacket(this.chunk.m_7697_(), this.serializeNBT()));
         }
      }
   }

   public void sync(@NonNull ServerPlayer player) {
      if (player == null) {
         throw new NullPointerException("player is marked non-null but is null");
      } else if (this.chunk != null) {
         if (!this.chunk.m_62953_().m_5776_()) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
               return player;
            }), new SyncMagiculePacket(this.chunk.m_7697_(), this.serializeNBT()));
         }
      }
   }

   public boolean consumeMagicule(BlockPos pos, double amount) {
      MagiculeChunkCapabilityImpl.MagiculeCacheEntry entry = this.getOrCreateCacheEntry(pos);
      if (entry == null) {
         return false;
      } else {
         MagiculeEvent.Consume event = new MagiculeEvent.Consume(this.chunk, this, pos, amount);
         if (MinecraftForge.EVENT_BUS.post(event)) {
            return false;
         } else {
            double magicule = entry.getMagicule(this.maxMagicule, this.magiculePercentage);
            if (magicule < event.getAmount()) {
               return false;
            } else {
               magicule -= event.getAmount();
               this.magiculePercentage = 1.0D / entry.getMaxMagicule(this.maxMagicule) * magicule;
               this.markDirty();
               return true;
            }
         }
      }
   }

   public void reinitialize() {
      if (this.chunk != null) {
         Tensura.getLogger().debug("Reinitializing magicule capability for chunk at {},{} in {}", this.chunk.m_7697_().f_45578_, this.chunk.m_7697_().f_45579_, this.chunk.m_62953_().m_46472_().m_135782_());
         this.magiculeSeed = -1L;
         this.resetBaseValues();
         this.initialize(this.chunk);
      }
   }

   public void resetBaseValues() {
      this.maxMagicule = (Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagicule.get();
      this.regenerationRate = (Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagiculeRegeneration.get();
      this.syncCounter = (Integer)TensuraConfig.INSTANCE.magiculeConfig.magiculeSyncInterval.get();
      this.magiculeCache.clear();
      this.dirty = false;
   }

   public double getMaxMagicule() {
      return this.maxMagicule;
   }

   public void setMaxMagicule(double maxMagicule) {
      this.maxMagicule = maxMagicule;
   }

   public double getRegenerationRate() {
      return this.regenerationRate;
   }

   public void setRegenerationRate(double regenerationRate) {
      this.regenerationRate = regenerationRate;
   }

   private static class MagiculeCacheEntry {
      private int remains;
      private final List<MagiculeModifier> modifiers;
      private Pair<Double, Double> maxValueCache;

      public void tick() {
         --this.remains;
      }

      public boolean isOutdated() {
         return this.remains <= 0;
      }

      public double getMaxMagicule(double maxMagicule) {
         if (this.maxValueCache != null && (Double)this.maxValueCache.getFirst() == maxMagicule) {
            return (Double)this.maxValueCache.getSecond();
         } else {
            double newMaxMagicule = maxMagicule;

            MagiculeModifier modifier;
            for(Iterator var5 = this.modifiers.iterator(); var5.hasNext(); newMaxMagicule = modifier.getMaxMagicule(newMaxMagicule)) {
               modifier = (MagiculeModifier)var5.next();
            }

            this.maxValueCache = Pair.of(maxMagicule, newMaxMagicule);
            return newMaxMagicule;
         }
      }

      public double getMagicule(double maxMagicule, double magiculePercentage) {
         return this.getMaxMagicule(maxMagicule) * magiculePercentage;
      }

      private MagiculeCacheEntry(List<MagiculeModifier> modifiers) {
         this.remains = (Integer)TensuraConfig.INSTANCE.magiculeConfig.modifierUpdateInterval.get();
         this.maxValueCache = null;
         this.modifiers = modifiers;
      }

      public static MagiculeChunkCapabilityImpl.MagiculeCacheEntry of(List<MagiculeModifier> modifiers) {
         return new MagiculeChunkCapabilityImpl.MagiculeCacheEntry(modifiers);
      }
   }
}
