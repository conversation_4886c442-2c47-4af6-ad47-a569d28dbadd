package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantBearEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class GiantBearRenderer extends GeoEntityRenderer<GiantBearEntity> {
   public GiantBearRenderer(Context renderManager) {
      super(renderManager, new GiantBearModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(GiantBearEntity instance) {
      return instance.m_6162_() ? new ResourceLocation("tensura", "textures/entity/bear/giant_bear_baby.png") : new ResourceLocation("tensura", "textures/entity/bear/giant_bear.png");
   }

   public RenderType getRenderType(GiantBearEntity bear, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      float scale = 0.75F;
      if (bear.m_6162_()) {
         scale /= 2.0F;
      }

      stack.m_85841_(scale, scale, scale);
      return RenderType.m_110473_(this.getTextureLocation(bear));
   }
}
