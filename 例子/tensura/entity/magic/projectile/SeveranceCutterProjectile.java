package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class SeveranceCutterProjectile extends SpaceCutProjectile {
   public SeveranceCutterProjectile(EntityType<? extends SeveranceCutterProjectile> type, Level level) {
      super(type, level);
      this.setSize(3.0F);
   }

   public SeveranceCutterProjectile(Level worldIn, LivingEntity shooter) {
      this((EntityType)TensuraEntityTypes.SEVERANCE_CUTTER.get(), worldIn);
      this.m_5602_(shooter);
      this.setSize(3.0F);
      this.invis = false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/dimension_cut/severence_blade.png")};
   }

   public boolean piercingBlock() {
      return true;
   }

   public boolean piercingEntity() {
      return true;
   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F)) {
         TensuraDamageSource damageSource = DamageSourceHelper.turnTensura(TensuraDamageSources.severanceBlade(this, this.m_37282_())).setMpCost(this.mpCost).setSkill(this.getSkill());
         if (this.getSkill().getSkill().equals(UniqueSkills.ABSOLUTE_SEVERANCE.get())) {
            damageSource = damageSource.setIgnoreResistance(1.0F);
         }

         target.m_6469_(damageSource, this.damage);
      }
   }
}
