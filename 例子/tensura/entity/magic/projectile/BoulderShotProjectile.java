package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.util.Optional;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;

public class BoulderShotProjectile extends TensuraProjectile {
   public BoulderShotProjectile(EntityType<? extends BoulderShotProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public BoulderShotProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.BOULDER_SHOT.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.earth_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/boulder_shot.png")};
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void hitEntity(Entity entity) {
      super.hitEntity(entity);
      if (entity instanceof LivingEntity) {
         LivingEntity pLivingEntity = (LivingEntity)entity;
         StoneShotProjectile.breakTargetArmor(pLivingEntity, 5);
      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_144135_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50079_.m_49966_()), x, y, z, 30, 0.5D, 0.5D, 0.5D, 0.15D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 2.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 2.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 2.0D;
         this.f_19853_.m_7106_(new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_50079_.m_49966_()), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
