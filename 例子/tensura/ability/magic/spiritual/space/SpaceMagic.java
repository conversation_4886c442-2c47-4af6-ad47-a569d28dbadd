package com.github.manasmods.tensura.ability.magic.spiritual.space;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.block.LightAirBlock;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class SpaceMagic extends SpiritualMagic {
   public SpaceMagic() {
      super(MagicElemental.SPACE, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 20;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else if (this.isInstant(instance, entity) && this.isOnAir(entity)) {
         this.solidSpace(instance, entity);
         return true;
      } else {
         return super.onHeld(instance, entity, heldTicks);
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      int castingTime = this.castingTime(instance, entity);
      if (castingTime != 0 || !this.isOnAir(entity)) {
         if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
            this.solidSpace(instance, entity);
         }
      }
   }

   private void solidSpace(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (TensuraGameRules.canSkillGrief(level)) {
         this.addMasteryPoint(instance, entity);
         if (this.isOnAir(entity)) {
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               BlockPos belowPos = new BlockPos(entity.m_20185_(), (double)(entity.m_146904_() - 1), entity.m_20189_());
               if (!level.m_8055_(belowPos).m_60713_((Block)TensuraBlocks.SOLID_SPACE.get())) {
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, belowPos);
                  if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                     BlockState lightAir = ((Block)TensuraBlocks.SOLID_SPACE.get()).m_49966_();
                     level.m_7731_(belowPos, lightAir, 11);
                     level.m_142346_(entity, GameEvent.f_157797_, belowPos);
                     level.m_186460_(belowPos, (Block)TensuraBlocks.SOLID_SPACE.get(), 1200);
                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, belowPos));
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_144048_, SoundSource.NEUTRAL, 1.0F, 1.0F);
                  }
               }
            }
         } else {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 4.0D);
            if (result.m_6662_() != Type.ENTITY) {
               BlockPos pos = result.m_82425_();
               BlockPos relative = pos.m_121945_(result.m_82434_());
               if (level.m_8055_(relative).m_60795_() || level.m_8055_(relative).m_60819_().m_192917_(Fluids.f_76193_)) {
                  if (SkillHelper.outOfMagicule(entity, instance)) {
                     return;
                  }

                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, relative);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  BlockState lightAir = ((Block)TensuraBlocks.SOLID_SPACE.get()).m_49966_();
                  if (level.m_8055_(relative).m_60819_().m_164512_(Fluids.f_76193_)) {
                     lightAir.m_61124_(LightAirBlock.WATERLOGGED, true);
                  }

                  level.m_7731_(relative, lightAir, 11);
                  level.m_142346_(entity, GameEvent.f_157797_, relative);
                  if (!instance.isMastered(entity)) {
                     level.m_186460_(relative, (Block)TensuraBlocks.SOLID_SPACE.get(), 1200);
                  }

                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, relative));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_144048_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               }
            }

         }
      }
   }

   private boolean isOnAir(LivingEntity entity) {
      if (!entity.m_6144_()) {
         return false;
      } else {
         boolean var10000;
         BlockPos belowPos;
         label28: {
            belowPos = new BlockPos(entity.m_20185_(), (double)(entity.m_146904_() - 1), entity.m_20189_());
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (player.m_150110_().f_35935_) {
                  var10000 = true;
                  break label28;
               }
            }

            var10000 = false;
         }

         boolean canFly = var10000;
         if (canFly) {
            return false;
         } else {
            return entity.f_19853_.m_8055_(belowPos).m_60795_() || entity.f_19853_.m_8055_(belowPos).m_60713_((Block)TensuraBlocks.SOLID_SPACE.get());
         }
      }
   }
}
