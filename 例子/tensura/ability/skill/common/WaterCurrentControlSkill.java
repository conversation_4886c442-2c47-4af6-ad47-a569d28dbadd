package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.ForgeMod;

public class WaterCurrentControlSkill extends Skill {
   protected static final UUID WCC_UUID = UUID.fromString("bdfb0968-3db1-11ee-be56-0242ac120002");

   public WaterCurrentControlSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 6000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)CommonSkills.HYDRAULIC_PROPULSION.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance swim = entity.m_21051_((Attribute)ForgeMod.SWIM_SPEED.get());
      if (swim != null) {
         AttributeModifier attributemodifier = new AttributeModifier(WCC_UUID, "Water Current Control", 3.0D, Operation.ADDITION);
         if (!swim.m_22109_(attributemodifier)) {
            swim.m_22125_(attributemodifier);
         }

         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11777_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance swim = entity.m_21051_((Attribute)ForgeMod.SWIM_SPEED.get());
      if (swim != null) {
         swim.m_22127_(WCC_UUID);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11775_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isToggled() ? false : entity.m_20072_();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
