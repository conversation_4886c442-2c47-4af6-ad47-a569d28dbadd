package com.github.manasmods.tensura.entity.client.misc;

import com.github.manasmods.tensura.block.CharybdisCoreBlock;
import com.github.manasmods.tensura.entity.projectile.PrimedCharybdisCoreEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.BlockRenderDispatcher;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.client.renderer.texture.TextureAtlas;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.SculkSensorPhase;
import org.jetbrains.annotations.NotNull;

public class PrimedCharybdisCoreRenderer extends EntityRenderer<PrimedCharybdisCoreEntity> {
   private final BlockRenderDispatcher blockRenderer;

   public PrimedCharybdisCoreRenderer(Context pContext) {
      super(pContext);
      this.f_114477_ = 0.5F;
      this.blockRenderer = pContext.m_234597_();
   }

   public void render(PrimedCharybdisCoreEntity pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      pMatrixStack.m_85836_();
      pMatrixStack.m_85837_(0.0D, 0.5D, 0.0D);
      int i = pEntity.getFuse();
      if ((float)i - pPartialTicks + 1.0F < 10.0F) {
         float f = 1.0F - ((float)i - pPartialTicks + 1.0F) / 10.0F;
         f = Mth.m_14036_(f, 0.0F, 1.0F);
         f *= f;
         f *= f;
         float f1 = 1.0F + f * 0.3F;
         pMatrixStack.m_85841_(f1, f1, f1);
      }

      pMatrixStack.m_85845_(Vector3f.f_122225_.m_122240_(-90.0F));
      pMatrixStack.m_85837_(-0.5D, -0.5D, 0.5D);
      pMatrixStack.m_85845_(Vector3f.f_122225_.m_122240_(90.0F));
      renderWhiteSolidBlock(this.blockRenderer, (BlockState)((Block)TensuraBlocks.CHARYBDIS_CORE.get()).m_49966_().m_61124_(CharybdisCoreBlock.MODE, SculkSensorPhase.COOLDOWN), pMatrixStack, pBuffer, pPackedLight, i / 5 % 2 == 0);
      pMatrixStack.m_85849_();
      super.m_7392_(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
   }

   @NotNull
   public ResourceLocation getTextureLocation(PrimedCharybdisCoreEntity pEntity) {
      return TextureAtlas.f_118259_;
   }

   public static void renderWhiteSolidBlock(BlockRenderDispatcher pBlockRenderDispatcher, BlockState pState, PoseStack pPoseStack, MultiBufferSource pBuffer, int pPackedLight, boolean pWhiteOverlay) {
      int i;
      if (pWhiteOverlay) {
         i = OverlayTexture.m_118093_(OverlayTexture.m_118088_(1.0F), 10);
      } else {
         i = OverlayTexture.f_118083_;
      }

      pBlockRenderDispatcher.m_110912_(pState, pPoseStack, pBuffer, pPackedLight, i);
   }
}
