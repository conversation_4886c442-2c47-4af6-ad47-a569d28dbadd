package com.github.manasmods.tensura.ability.skill;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;

public class Skill extends TensuraSkill {
   private final Skill.SkillType type;

   public Skill(Skill.SkillType type) {
      this.type = type;
   }

   @Nullable
   public ResourceLocation getSkillIcon() {
      ResourceLocation id = this.getRegistryName();
      if (id == null) {
         return new ResourceLocation("tensura", "textures/temp_textures/item/confused_rimuru.png");
      } else {
         String var10003 = this.getType().getNamespace();
         return new ResourceLocation("tensura", "textures/skill/" + var10003 + "/" + id.m_135815_().replace('/', '.') + ".png");
      }
   }

   @Nullable
   public MutableComponent getColoredName() {
      MutableComponent name = super.getColoredName();
      return name == null ? null : name.m_130940_(this.getType().getChatFormatting());
   }

   public int getMaxMastery() {
      short var10000;
      switch(this.getType()) {
      case EXTRA:
         var10000 = 500;
         break;
      case UNIQUE:
         var10000 = 1000;
         break;
      case ULTIMATE:
         var10000 = 2000;
         break;
      default:
         var10000 = 100;
      }

      return var10000;
   }

   public double getObtainingEpCost() {
      double var10000;
      switch(this.getType()) {
      case EXTRA:
         var10000 = 1000.0D;
         break;
      case UNIQUE:
         var10000 = 10000.0D;
         break;
      case ULTIMATE:
         var10000 = 100000.0D;
         break;
      default:
         var10000 = 100.0D;
      }

      return var10000;
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (!super.canInteractSkill(instance, entity)) {
         return false;
      } else {
         return !entity.m_21023_((MobEffect)TensuraMobEffects.ANTI_SKILL.get());
      }
   }

   public Skill.SkillType getType() {
      return this.type;
   }

   public static enum SkillType {
      RESISTANCE("resistance", ChatFormatting.DARK_AQUA),
      INTRINSIC("intrinsic", ChatFormatting.AQUA),
      COMMON("common", ChatFormatting.GREEN),
      EXTRA("extra", ChatFormatting.YELLOW),
      UNIQUE("unique", ChatFormatting.GOLD),
      ULTIMATE("ultimate", ChatFormatting.RED);

      private final String namespace;
      private final ChatFormatting chatFormatting;

      public MutableComponent getName() {
         return Component.m_237115_("tensura.skill.type." + this.namespace).m_130940_(this.chatFormatting);
      }

      public String getNamespace() {
         return this.namespace;
      }

      public ChatFormatting getChatFormatting() {
         return this.chatFormatting;
      }

      private SkillType(String namespace, ChatFormatting chatFormatting) {
         this.namespace = namespace;
         this.chatFormatting = chatFormatting;
      }

      // $FF: synthetic method
      private static Skill.SkillType[] $values() {
         return new Skill.SkillType[]{RESISTANCE, INTRINSIC, COMMON, EXTRA, UNIQUE, ULTIMATE};
      }
   }
}
