package com.github.manasmods.tensura.capability.effects;

import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.common.util.INBTSerializable;
import org.jetbrains.annotations.Nullable;

public interface ITensuraEffectsCapability extends INBTSerializable<CompoundTag> {
   boolean hasSyncedEffect(ResourceLocation var1);

   void addSyncedEffect(ResourceLocation var1, LivingEntity var2);

   void removeSyncedEffect(ResourceLocation var1, LivingEntity var2);

   void clearSyncedEffects(LivingEntity var1);

   @Nullable
   UUID getEffectSource(ResourceLocation var1);

   void setEffectSource(@Nullable UUID var1, ResourceLocation var2);

   void removeEffectSource(ResourceLocation var1);

   double getInfectionAge();

   float getLockedXRot();

   float getLockedYRot();

   void setInfectionAge(double var1);

   void setLockedXRot(float var1);

   void setLockedYRot(float var1);

   int getInsanityFOV();

   void setInsanityFOV(int var1);

   int getInsanityNightmare();

   void setInsanityNightmare(int var1);

   double getSeveranceAmount();

   void setSeveranceAmount(double var1);

   int getSeveranceRemoveSec();

   void setSeveranceRemoveSec(int var1);

   boolean isNoKnockBack();

   void setNoKnockBack(boolean var1);

   boolean isNoDyingAnimation();

   void setNoDyingAnimation(boolean var1);

   boolean isColossusStarted();

   void setColossusStarted(boolean var1);

   boolean isColossusPassed();

   void setColossusPassed(boolean var1);

   boolean isColossusWon();

   void setColossusWon(boolean var1);

   BlockPos getWarpPos();

   void setWarpPos(double var1, double var3, double var5);

   int getWarpPortalTime();

   void setWarpPortalTime(int var1);

   int getPresenceConceal();

   void setPresenceConceal(int var1);

   int getPresenceSense();

   void setPresenceSense(int var1);

   float getHeight();

   void setHeight(float var1);

   void setHeightUpdate(float var1);

   float getHeightUpdate();
}
