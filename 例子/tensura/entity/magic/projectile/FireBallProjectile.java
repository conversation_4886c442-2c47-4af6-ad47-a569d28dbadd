package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.block.HolyFireBlock;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BaseFireBlock;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.FireBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import org.jetbrains.annotations.NotNull;

public class FireBallProjectile extends FireBoltProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/fire_ball/fire_ball_5.png")};

   public FireBallProjectile(EntityType<? extends FireBallProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public FireBallProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.FIRE_BALL.get(), levelIn);
      this.m_5602_(shooter);
      this.setImpactParticleCount(8);
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   protected void m_8060_(@NotNull BlockHitResult pResult) {
      if (!this.f_19853_.f_46443_) {
         boolean skillGrief = this.shouldGrief();
         if (skillGrief) {
            this.placeFire(this);
            BlockPos blockpos = pResult.m_82425_().m_121945_(pResult.m_82434_());
            if (this.f_19853_.m_46859_(blockpos)) {
               SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), blockpos);
               if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                  return;
               }

               this.f_19853_.m_46597_(blockpos, BaseFireBlock.m_49245_(this.f_19853_, blockpos));
               MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), blockpos));
            }
         }
      }

      super.m_8060_(pResult);
   }

   protected void placeFire(Entity entity) {
      int yPos = Mth.m_14107_(entity.m_20186_()) - 1;
      int xPos = Mth.m_14107_(entity.m_20185_());
      int zPos = Mth.m_14107_(entity.m_20189_());
      BlockState fire = (BlockState)Blocks.f_50083_.m_49966_().m_61124_(FireBlock.f_53408_, 0);
      boolean placeFire = false;
      boolean removeBlock = false;

      for(int j = -2; j <= 2; ++j) {
         for(int k = -2; k <= 2; ++k) {
            for(int i = -1; i <= 3; ++i) {
               if (this.f_19853_.f_46441_.m_188500_() > 0.3D) {
                  int newYPos = yPos + i;
                  int newXPos = xPos + j;
                  int newZPos = zPos + k;
                  BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
                  BlockState blockState = this.f_19853_.m_8055_(blockpos);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), this.getSkill(), blockpos);
                  if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                     if (blockState.m_60767_().m_76336_() && blockState.m_60819_().m_76178_()) {
                        BlockPos blockPosDown = blockpos.m_7495_();
                        BlockState blockStateDown = this.f_19853_.m_8055_(blockPosDown);
                        if (blockStateDown.m_60783_(this.f_19853_, blockPosDown, Direction.UP)) {
                           removeBlock = this.f_19853_.m_7471_(blockpos, true) || removeBlock;
                        }
                     }

                     if (FireBlock.m_49255_(this.f_19853_, blockpos, Direction.UP)) {
                        placeFire = this.f_19853_.m_46597_(blockpos, fire) || placeFire;
                        this.f_19853_.m_186460_(blockpos, blockState.m_60734_(), HolyFireBlock.getFireTickDelay(this.f_19853_.f_46441_));
                     }

                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), this.getSkill(), blockpos));
                  }
               }
            }
         }
      }

      if (removeBlock && this.m_37282_() != null) {
         this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
      }

      if (placeFire && this.m_37282_() != null) {
         this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11892_);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20184_();
      double d0 = this.m_20185_() - vec3.f_82479_;
      double d1 = this.m_20186_() - vec3.f_82480_;
      double d2 = this.m_20189_() - vec3.f_82481_;

      for(int i = 0; i < 8; ++i) {
         Vec3 motion = this.vec3Random().m_82490_(0.10000000149011612D).m_82546_(this.m_20184_().m_82490_(0.10000000149011612D));
         Vec3 pos = this.vec3Random().m_82490_(0.30000001192092896D);
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.RED_FIRE.get(), d0 + pos.f_82479_, d1 + 0.5D + pos.f_82480_, d2 + pos.f_82481_, motion.f_82479_, motion.f_82480_, motion.f_82481_);
      }

   }
}
