package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LandfishEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class LandfishRenderer extends GeoEntityRenderer<LandfishEntity> {
   public LandfishRenderer(Context renderManager) {
      super(renderManager, new LandfishModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(LandfishEntity instance) {
      return instance.getVariant().getTextureLocation();
   }

   public RenderType getRenderType(LandfishEntity landfishEntity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (landfishEntity.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(landfishEntity));
   }
}
