package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SeveranceCutterProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class AbsoluteSeveranceSkill extends Skill {
   public AbsoluteSeveranceSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 15000.0D;
   }

   public double learningCost() {
      return 1000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.absolute_severance.coat");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.absolute_severance.projectile");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 1000.0D;
         break;
      case 2:
         var10000 = 500.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get());
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         switch(instance.getMode()) {
         case 1:
            if (entity.m_21023_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get())) {
               entity.m_21195_((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get());
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else {
               int severance = instance.isMastered(entity) ? 19 : 4;
               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get(), 1200, severance, false, false, false));
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
            break;
         case 2:
            SeveranceCutterProjectile spaceCutter = new SeveranceCutterProjectile(entity.m_9236_(), entity);
            spaceCutter.setSpeed(2.5F);
            spaceCutter.setDamage(this.isMastered(instance, entity) ? 300.0F : 50.0F);
            spaceCutter.setSize(this.isMastered(instance, entity) ? 8.0F : 5.0F);
            spaceCutter.setMpCost(this.magiculeCost(entity, instance));
            spaceCutter.setSkill(instance);
            spaceCutter.m_20242_(true);
            spaceCutter.setPosAndShoot(entity);
            spaceCutter.setPosDirection(entity, TensuraProjectile.PositionDirection.MIDDLE);
            entity.m_9236_().m_7967_(spaceCutter);
            instance.addMasteryPoint(entity);
            instance.setCoolDown(3);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_21011_(InteractionHand.OFF_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12520_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
