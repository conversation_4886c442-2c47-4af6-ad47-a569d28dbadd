package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.protocol.game.ClientboundSoundPacket;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.ThrowableProjectile;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingChangeTargetEvent;

public class ObserverSkill extends Skill {
   public ObserverSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.observer.trap");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.observer.monster");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.observer.presence");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (this.isInSlot(event.getEntity())) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_() && !damageSource.m_19387_()) {
               Entity var5 = damageSource.m_7640_();
               if (var5 instanceof LivingEntity) {
                  LivingEntity entity = (LivingEntity)var5;
                  if (!entity.m_217043_().m_188499_()) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     event.setCanceled(true);
                     if (SkillUtils.canNegateDodge(entity, damageSource)) {
                        event.setCanceled(false);
                     }

                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (this.isInSlot(entity)) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (event.getProjectile() instanceof ThrowableProjectile || event.getProjectile() instanceof AbstractArrow) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         switch(instance.getMode()) {
         case 1:
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.DANGER_DETECTION.get(), 5, 0, false, false));
            break;
         case 2:
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.DANGER_DETECTION.get(), 5, 1, false, false));
            break;
         case 3:
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 5, 0, false, false));
         }

         return true;
      }
   }

   public void onBeingTargeted(ManasSkillInstance instance, LivingEntity target, LivingChangeTargetEvent event) {
      if (!SkillUtils.noInteractiveMode(target)) {
         if (instance.isToggled()) {
            if (target instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)target;
               LivingEntity var6 = event.getEntity();
               if (var6 instanceof Mob) {
                  Mob mob = (Mob)var6;
                  if (mob.m_5448_() == null || !target.m_7306_(mob.m_5448_())) {
                     this.sendSound(player, mob);
                  }

               }
            }
         }
      }
   }

   private void sendSound(ServerPlayer user, LivingEntity target) {
      Vec3 eyeVec = user.m_146892_();
      Vec3 soundPos = eyeVec.m_82549_(target.m_146892_().m_82546_(eyeVec).m_82541_().m_82490_(5.0D));
      user.f_8906_.m_9829_(new ClientboundSoundPacket(SoundEvents.f_11699_, SoundSource.HOSTILE, soundPos.m_7096_(), eyeVec.m_7098_(), soundPos.m_7094_(), 1.0F, 1.0F, user.m_217043_().m_188505_()));
   }
}
