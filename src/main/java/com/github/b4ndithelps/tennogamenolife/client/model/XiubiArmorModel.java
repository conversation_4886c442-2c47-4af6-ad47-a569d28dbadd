package com.github.b4ndithelps.tennogamenolife.client.model;

import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import net.minecraft.client.animation.AnimationChannel;
import net.minecraft.client.animation.AnimationDefinition;
import net.minecraft.client.animation.Keyframe;
import net.minecraft.client.animation.KeyframeAnimations;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.*;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.ItemStack;

/**
 * 休比机铠装饰模型
 * 基于Blockbench导出的新机铠模型，包含更新的动画
 */
public class XiubiArmorModel extends EntityModel<XiubiEntity> {
    
    // 模型层位置
    public static final ModelLayerLocation LAYER_LOCATION = 
        new ModelLayerLocation(new ResourceLocation("tennogamenolife", "xiubi_armor"), "main");
    
    private final ModelPart bone4;
    private final ModelPart bone3;
    private final ModelPart bone2;
    private final ModelPart bone5;
    private final ModelPart bone7;
    private final ModelPart bone8;
    private final ModelPart youyi;
    private final ModelPart bone10;
    private final ModelPart zuoyi;
    private final ModelPart bone6;

    // 机铠动画定义 - 更新版本
    public static final AnimationDefinition IDLE_ANIMATION = AnimationDefinition.Builder.withLength(8.0F).looping()
        .addAnimation("zuoyi", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(2.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 21.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(6.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, -10.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(8.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("youyi", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(2.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, -21.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(6.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 10.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(8.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone10", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(2.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, -27.5F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(5.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(6.5F, KeyframeAnimations.degreeVec(0.0F, 0.0F, -12.5F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(8.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone6", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(2.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 27.5F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(5.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(6.5F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 12.5F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(8.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .build();

    public static final AnimationDefinition WEAPON_ANIMATION = AnimationDefinition.Builder.withLength(0.75F)
        .addAnimation("zuoyi", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.degreeVec(2.2985921868F, -1.4387113584F, 77.3023476933F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("zuoyi", new AnimationChannel(AnimationChannel.Targets.SCALE,
            new Keyframe(0.0F, KeyframeAnimations.scaleVec(1.0F, 1.0F, 1.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.scaleVec(0.6F, 0.8F, 1.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("youyi", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.degreeVec(0.8479548998F, -2.2455933803F, -81.9676102269F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("youyi", new AnimationChannel(AnimationChannel.Targets.SCALE,
            new Keyframe(0.0F, KeyframeAnimations.scaleVec(1.0F, 1.0F, 1.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.scaleVec(0.6F, 0.8F, 1.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone10", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.6667F, KeyframeAnimations.degreeVec(0.0F, 0.0F, -65.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.degreeVec(0.0F, 25.0F, -65.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone6", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.6667F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 65.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.75F, KeyframeAnimations.degreeVec(0.0F, -20.0F, 65.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .build();

    public static final AnimationDefinition CANCEL_WEAPON_ANIMATION = AnimationDefinition.Builder.withLength(0.5833F)
        .addAnimation("zuoyi", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(2.2985921868F, -1.4387113584F, 77.3023476933F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("zuoyi", new AnimationChannel(AnimationChannel.Targets.SCALE,
            new Keyframe(0.0F, KeyframeAnimations.scaleVec(0.6F, 0.8F, 1.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.scaleVec(1.0F, 1.0F, 1.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("youyi", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.8479548998F, -2.2455933803F, -81.9676102269F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("youyi", new AnimationChannel(AnimationChannel.Targets.SCALE,
            new Keyframe(0.0F, KeyframeAnimations.scaleVec(0.6F, 0.8F, 1.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.scaleVec(1.0F, 1.0F, 1.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone10", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 25.0F, -65.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone6", new AnimationChannel(AnimationChannel.Targets.ROTATION,
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, -20.0F, 65.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(0.5833F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .build();

    public XiubiArmorModel(ModelPart root) {
        this.bone4 = root.getChild("bone4");
        this.bone3 = this.bone4.getChild("bone3");
        this.bone2 = this.bone4.getChild("bone2");
        this.bone5 = this.bone4.getChild("bone5");
        this.bone7 = root.getChild("bone7");
        this.bone8 = root.getChild("bone8");
        this.youyi = root.getChild("youyi");
        this.bone10 = this.youyi.getChild("bone10");
        this.zuoyi = root.getChild("zuoyi");
        this.bone6 = this.zuoyi.getChild("bone6");
    }

    public static LayerDefinition createBodyLayer() {
        MeshDefinition meshdefinition = new MeshDefinition();
        PartDefinition partdefinition = meshdefinition.getRoot();

        PartDefinition bone4 = partdefinition.addOrReplaceChild("bone4",
            CubeListBuilder.create(), PartPose.offset(0.0F, 24.0F, 0.0F));

        PartDefinition bone3 = bone4.addOrReplaceChild("bone3",
            CubeListBuilder.create().texOffs(40, 19).addBox(-5.0F, -31.0F, -2.0F, 1.0F, 3.0F, 4.0F, new CubeDeformation(0.0F))
            .texOffs(48, 9).addBox(-5.3F, -30.6F, -1.5F, 1.0F, 2.0F, 3.0F, new CubeDeformation(0.0F)),
            PartPose.offset(0.0F, 0.0F, 0.0F));

        PartDefinition cube_r1 = bone3.addOrReplaceChild("cube_r1",
            CubeListBuilder.create().texOffs(10, 53).addBox(0.0F, -3.0F, 0.0F, 1.0F, 1.0F, 2.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -33.1F, -1.7F, -1.9722F, 0.0F, 0.0F));

        PartDefinition cube_r2 = bone3.addOrReplaceChild("cube_r2",
            CubeListBuilder.create().texOffs(42, 44).addBox(0.0F, -3.0F, -1.0F, 1.0F, 1.0F, 4.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -27.1F, -2.4F, -1.117F, 0.0F, 0.0F));

        PartDefinition cube_r3 = bone3.addOrReplaceChild("cube_r3",
            CubeListBuilder.create().texOffs(28, 49).addBox(0.0F, -4.0F, -2.0F, 1.0F, 4.0F, 2.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -35.6F, 1.2F, 2.6878F, 0.0F, 0.0F));

        PartDefinition cube_r4 = bone3.addOrReplaceChild("cube_r4",
            CubeListBuilder.create().texOffs(54, 25).addBox(0.0F, -5.0F, -1.0F, 1.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -39.4F, 3.1F, 2.6878F, 0.0F, 0.0F));

        PartDefinition cube_r5 = bone3.addOrReplaceChild("cube_r5",
            CubeListBuilder.create().texOffs(54, 22).addBox(0.0F, -4.0F, -1.0F, 1.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -36.7F, 5.8F, 2.6878F, 0.0F, 0.0F));

        PartDefinition cube_r6 = bone3.addOrReplaceChild("cube_r6",
            CubeListBuilder.create().texOffs(48, 35).addBox(0.0F, -4.0F, -2.0F, 1.0F, 4.0F, 2.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -33.9F, 4.4F, 2.6878F, 0.0F, 0.0F));

        PartDefinition cube_r7 = bone3.addOrReplaceChild("cube_r7",
            CubeListBuilder.create().texOffs(52, 44).addBox(0.0F, -3.0F, -2.0F, 1.0F, 1.0F, 2.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -29.0F, 5.8F, 0.6458F, 0.0F, 0.0F));

        PartDefinition cube_r8 = bone3.addOrReplaceChild("cube_r8",
            CubeListBuilder.create().texOffs(4, 51).addBox(0.0F, -3.0F, -2.0F, 1.0F, 1.0F, 2.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -27.6F, 3.6F, 0.192F, 0.0F, 0.0F));

        PartDefinition cube_r9 = bone3.addOrReplaceChild("cube_r9",
            CubeListBuilder.create().texOffs(32, 44).addBox(0.0F, -3.0F, -2.0F, 1.0F, 1.0F, 4.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -26.3F, -0.3F, -0.6109F, 0.0F, 0.0F));

        PartDefinition cube_r10 = bone3.addOrReplaceChild("cube_r10",
            CubeListBuilder.create().texOffs(48, 4).addBox(0.0F, -2.0F, -1.0F, 1.0F, 2.0F, 3.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.0F, -27.4F, -0.6F, 0.8901F, 0.0F, 0.0F));

        PartDefinition bone2 = bone4.addOrReplaceChild("bone2",
            CubeListBuilder.create(), PartPose.offset(0.0F, -3.0F, -6.0F));

        PartDefinition cube_r11 = bone2.addOrReplaceChild("cube_r11",
            CubeListBuilder.create().texOffs(4, 54).addBox(-2.0F, 0.0F, 0.0F, 1.0F, 4.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-4.6F, 0.6F, 11.9F, 0.9849F, 0.1616F, 0.0167F));

        PartDefinition cube_r12 = bone2.addOrReplaceChild("cube_r12",
            CubeListBuilder.create().texOffs(50, 49).addBox(1.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-7.1F, -1.7F, 11.8F, 0.2057F, -0.0397F, 0.1879F));

        PartDefinition cube_r13 = bone2.addOrReplaceChild("cube_r13",
            CubeListBuilder.create().texOffs(50, 19).addBox(1.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-5.7F, -6.2F, 10.9F, 0.1888F, -0.0913F, 0.4451F));

        PartDefinition cube_r14 = bone2.addOrReplaceChild("cube_r14",
            CubeListBuilder.create().texOffs(46, 49).addBox(1.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-3.2F, -9.8F, 9.1F, 0.553F, -0.3524F, 0.5099F));

        PartDefinition cube_r15 = bone2.addOrReplaceChild("cube_r15",
            CubeListBuilder.create().texOffs(22, 53).addBox(-2.0F, 0.0F, -1.0F, 2.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-1.0F, -12.3F, 9.0F, 0.0F, 0.0F, -0.829F));

        PartDefinition cube_r16 = bone2.addOrReplaceChild("cube_r16",
            CubeListBuilder.create().texOffs(38, 49).addBox(-2.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(5.7F, -6.2F, 10.9F, 0.1888F, 0.0913F, -0.4451F));

        PartDefinition cube_r17 = bone2.addOrReplaceChild("cube_r17",
            CubeListBuilder.create().texOffs(34, 49).addBox(-2.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(3.2F, -9.8F, 9.1F, 0.553F, 0.3524F, -0.5099F));

        PartDefinition cube_r18 = bone2.addOrReplaceChild("cube_r18",
            CubeListBuilder.create().texOffs(42, 49).addBox(-2.0F, -3.0F, 0.0F, 1.0F, 5.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(7.1F, -1.7F, 11.8F, 0.2057F, 0.0397F, -0.1879F));

        PartDefinition cube_r19 = bone2.addOrReplaceChild("cube_r19",
            CubeListBuilder.create().texOffs(0, 51).addBox(1.0F, 0.0F, 0.0F, 1.0F, 4.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(4.6F, 0.6F, 11.9F, 0.9849F, -0.1616F, -0.0167F));

        PartDefinition cube_r20 = bone2.addOrReplaceChild("cube_r20",
            CubeListBuilder.create().texOffs(16, 53).addBox(0.0F, 0.0F, -1.0F, 2.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(1.0F, -12.3F, 9.0F, 0.0F, 0.0F, 0.829F));

        PartDefinition cube_r21 = bone2.addOrReplaceChild("cube_r21",
            CubeListBuilder.create().texOffs(50, 32).addBox(0.0F, 0.0F, -1.0F, 2.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(1.0F, -13.3F, 9.0F, 0.0F, 0.0F, 1.5708F));

        PartDefinition bone5 = bone4.addOrReplaceChild("bone5",
            CubeListBuilder.create(), PartPose.offset(0.0F, 0.0F, 0.0F));

        PartDefinition cube_r22 = bone5.addOrReplaceChild("cube_r22",
            CubeListBuilder.create().texOffs(20, 48).addBox(-1.0F, -2.0F, -2.0F, 1.0F, 2.0F, 3.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(4.7F, -25.3F, -1.3F, 1.0976F, 0.3175F, -0.9523F));

        PartDefinition cube_r23 = bone5.addOrReplaceChild("cube_r23",
            CubeListBuilder.create().texOffs(12, 48).addBox(0.0F, -2.0F, -2.0F, 1.0F, 2.0F, 3.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(-4.7F, -25.3F, -1.3F, 1.0976F, -0.3175F, 0.9523F));

        PartDefinition bone7 = partdefinition.addOrReplaceChild("bone7",
            CubeListBuilder.create().texOffs(20, 16).addBox(1.6F, -21.9F, 1.4F, 3.0F, 3.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offset(-0.6F, 24.0F, 0.0F));

        PartDefinition bone8 = partdefinition.addOrReplaceChild("bone8",
            CubeListBuilder.create().texOffs(20, 16).mirror().addBox(-4.6F, -21.9F, 1.4F, 3.0F, 3.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offset(0.6F, 24.0F, 0.0F));

        PartDefinition youyi = partdefinition.addOrReplaceChild("youyi",
            CubeListBuilder.create().texOffs(40, 29).mirror().addBox(-7.3163F, 0.1432F, 3.0197F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-2.4F, 3.9F, 2.0F, 0.0171F, 0.121F, 0.3326F));

        // 添加youyi的所有子部件
        PartDefinition cube_r24 = youyi.addOrReplaceChild("cube_r24",
            CubeListBuilder.create().texOffs(40, 16).mirror().addBox(-5.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-11.8163F, 2.8432F, 3.0197F, 0.0F, 0.0F, -0.733F));

        PartDefinition cube_r25 = youyi.addOrReplaceChild("cube_r25",
            CubeListBuilder.create().texOffs(32, 32).mirror().addBox(-5.0F, -4.0F, 0.0F, 8.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-10.4163F, 4.2432F, 3.0197F, 0.0F, 0.0F, -0.733F));

        PartDefinition cube_r26 = youyi.addOrReplaceChild("cube_r26",
            CubeListBuilder.create().texOffs(48, 2).mirror().addBox(-3.0F, -3.0F, 0.0F, 5.0F, 1.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-13.0163F, 7.8432F, 3.0197F, 0.0F, 0.0F, -0.733F));

        PartDefinition cube_r27 = youyi.addOrReplaceChild("cube_r27",
            CubeListBuilder.create().texOffs(0, 48).mirror().addBox(-4.0F, -4.0F, 0.0F, 5.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-16.2163F, 6.8432F, 3.0197F, 0.0F, 0.0F, -0.733F));

        PartDefinition cube_r28 = youyi.addOrReplaceChild("cube_r28",
            CubeListBuilder.create().texOffs(54, 19).mirror().addBox(-5.0F, -2.0F, 0.0F, 2.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-7.5163F, 0.9432F, 3.0197F, 0.0F, 0.0F, 0.4363F));

        PartDefinition cube_r29 = youyi.addOrReplaceChild("cube_r29",
            CubeListBuilder.create().texOffs(54, 28).mirror().addBox(-5.0F, -2.0F, 0.0F, 1.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-8.6163F, 1.4432F, 3.0197F, 0.0F, 0.0F, 0.4363F));

        PartDefinition cube_r30 = youyi.addOrReplaceChild("cube_r30",
            CubeListBuilder.create().texOffs(32, 41).mirror().addBox(-5.0F, -2.0F, 0.0F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-8.0163F, 1.7432F, 3.0197F, 0.0F, 0.0F, 0.4363F));

        PartDefinition cube_r31 = youyi.addOrReplaceChild("cube_r31",
            CubeListBuilder.create().texOffs(40, 26).mirror().addBox(-5.0F, -2.0F, 0.0F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-0.0163F, 0.5432F, -0.9803F, -0.4644F, 1.0351F, -0.5862F));

        PartDefinition bone10 = youyi.addOrReplaceChild("bone10",
            CubeListBuilder.create(), PartPose.offset(-11.9163F, -1.5568F, 3.0197F));

        PartDefinition cube_r32 = bone10.addOrReplaceChild("cube_r32",
            CubeListBuilder.create().texOffs(46, 41).mirror().addBox(-4.0F, -4.0F, 0.0F, 5.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-7.0F, -1.0F, 0.0F, 0.0F, 0.0F, 0.0349F));

        PartDefinition cube_r33 = bone10.addOrReplaceChild("cube_r33",
            CubeListBuilder.create().texOffs(32, 38).mirror().addBox(-5.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-1.0F, -0.8F, 0.0F, 0.0F, 0.0F, 0.0349F));

        PartDefinition cube_r34 = bone10.addOrReplaceChild("cube_r34",
            CubeListBuilder.create().texOffs(32, 35).mirror().addBox(-5.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-1.1F, 1.2F, 0.0F, 0.0F, 0.0F, 0.0349F));

        PartDefinition cube_r35 = bone10.addOrReplaceChild("cube_r35",
            CubeListBuilder.create().texOffs(48, 0).mirror().addBox(-3.0F, -3.0F, 0.0F, 5.0F, 1.0F, 1.0F, new CubeDeformation(0.0F)).mirror(false),
            PartPose.offsetAndRotation(-1.1F, 2.2F, 0.0F, 0.0F, 0.0F, 0.0349F));

        PartDefinition zuoyi = partdefinition.addOrReplaceChild("zuoyi",
            CubeListBuilder.create().texOffs(40, 29).addBox(1.3849F, 0.0537F, 3.3152F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(2.4F, 4.0F, 1.7F, 0.0171F, -0.121F, -0.3326F));

        // 添加zuoyi的所有子部件
        PartDefinition cube_r36 = zuoyi.addOrReplaceChild("cube_r36",
            CubeListBuilder.create().texOffs(40, 16).addBox(-2.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(11.8849F, 2.7537F, 3.3152F, 0.0F, 0.0F, 0.733F));

        PartDefinition cube_r37 = zuoyi.addOrReplaceChild("cube_r37",
            CubeListBuilder.create().texOffs(32, 32).addBox(-3.0F, -4.0F, 0.0F, 8.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(10.4849F, 4.1537F, 3.3152F, 0.0F, 0.0F, 0.733F));

        PartDefinition cube_r38 = zuoyi.addOrReplaceChild("cube_r38",
            CubeListBuilder.create().texOffs(48, 2).addBox(-2.0F, -3.0F, 0.0F, 5.0F, 1.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(13.0849F, 7.7537F, 3.3152F, 0.0F, 0.0F, 0.733F));

        PartDefinition cube_r39 = zuoyi.addOrReplaceChild("cube_r39",
            CubeListBuilder.create().texOffs(0, 48).addBox(-1.0F, -4.0F, 0.0F, 5.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(16.2849F, 6.7537F, 3.3152F, 0.0F, 0.0F, 0.733F));

        PartDefinition cube_r40 = zuoyi.addOrReplaceChild("cube_r40",
            CubeListBuilder.create().texOffs(54, 19).addBox(3.0F, -2.0F, 0.0F, 2.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(7.5849F, 0.8537F, 3.3152F, 0.0F, 0.0F, -0.4363F));

        PartDefinition cube_r41 = zuoyi.addOrReplaceChild("cube_r41",
            CubeListBuilder.create().texOffs(54, 28).addBox(4.0F, -2.0F, 0.0F, 1.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(8.6849F, 1.3537F, 3.3152F, 0.0F, 0.0F, -0.4363F));

        PartDefinition cube_r42 = zuoyi.addOrReplaceChild("cube_r42",
            CubeListBuilder.create().texOffs(32, 41).addBox(-1.0F, -2.0F, 0.0F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(8.0849F, 1.6537F, 3.3152F, 0.0F, 0.0F, -0.4363F));

        PartDefinition cube_r43 = zuoyi.addOrReplaceChild("cube_r43",
            CubeListBuilder.create().texOffs(40, 26).addBox(-1.0F, -2.0F, 0.0F, 6.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(0.0849F, 0.4537F, -0.6848F, -0.4644F, -1.0351F, 0.5862F));

        PartDefinition bone6 = zuoyi.addOrReplaceChild("bone6",
            CubeListBuilder.create(), PartPose.offset(11.0849F, -1.4463F, 3.3152F));

        PartDefinition cube_r44 = bone6.addOrReplaceChild("cube_r44",
            CubeListBuilder.create().texOffs(48, 0).addBox(-2.0F, -3.0F, 0.0F, 5.0F, 1.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(2.0F, 2.0F, 0.0F, 0.0F, 0.0F, -0.0349F));

        PartDefinition cube_r45 = bone6.addOrReplaceChild("cube_r45",
            CubeListBuilder.create().texOffs(32, 38).addBox(-2.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(1.9F, -1.0F, 0.0F, 0.0F, 0.0F, -0.0349F));

        PartDefinition cube_r46 = bone6.addOrReplaceChild("cube_r46",
            CubeListBuilder.create().texOffs(32, 35).addBox(-2.0F, -4.0F, 0.0F, 7.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(2.0F, 1.0F, 0.0F, 0.0F, 0.0F, -0.0349F));

        PartDefinition cube_r47 = bone6.addOrReplaceChild("cube_r47",
            CubeListBuilder.create().texOffs(46, 41).addBox(-1.0F, -4.0F, 0.0F, 5.0F, 2.0F, 1.0F, new CubeDeformation(0.0F)),
            PartPose.offsetAndRotation(7.9F, -1.2F, 0.0F, 0.0F, 0.0F, -0.0349F));

        return LayerDefinition.create(meshdefinition, 64, 64);
    }

    @Override
    public void setupAnim(XiubiEntity entity, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch) {
        // 重置所有旋转和缩放
        this.resetTransforms();

        // 设置头部骨骼跟随头部旋转
        setupHeadRotation(netHeadYaw, headPitch);

        // 根据动画状态应用相应的动画
        XiubiEntity.ArmorAnimationState animState = entity.getArmorAnimationState();
        int animTimer = entity.getAnimationTimer();

        switch (animState) {
            case WEAPON:
                // 武器动画 - 最高优先级
                this.applyWeaponAnimation(animTimer);
                break;
            case CANCEL_WEAPON:
                // 取消武器动画 - 高优先级
                this.applyCancelWeaponAnimation(animTimer);
                break;
            case IDLE:
            default:
                // 待机动画 - 最低优先级
                this.applyIdleAnimation(animTimer);
                break;
        }
    }



    /**
     * 为玩家装饰设置动画（重载方法）
     */
    public void setupAnimForPlayer(float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch) {
        // 重置所有旋转和缩放
        this.resetTransforms();

        // 设置头部骨骼跟随头部旋转
        setupHeadRotation(netHeadYaw, headPitch);
    }

    /**
     * 设置头部骨骼跟随玩家头部旋转
     * 现在头部装饰通过PoseStack变换绑定，这里不需要额外的旋转
     * 但也不要重置，让PoseStack变换完全控制
     */
    private void setupHeadRotation(float netHeadYaw, float headPitch) {
        // 头部装饰现在通过渲染层的PoseStack变换绑定到玩家头部
        // 不需要在模型内部设置旋转，也不要重置旋转
        // 让PoseStack变换完全控制头部装饰的旋转

        // 注释掉重置代码，避免覆盖PoseStack变换
        // this.bone3.yRot = 0.0F;
        // this.bone3.xRot = 0.0F;
        // this.bone5.yRot = 0.0F;
        // this.bone5.xRot = 0.0F;
    }

    /**
     * 重置所有部件的变换
     */
    public void resetTransforms() {
        // 重置旋转
        this.zuoyi.xRot = 0.0F;
        this.zuoyi.yRot = 0.0F;
        this.zuoyi.zRot = 0.0F;
        this.youyi.xRot = 0.0F;
        this.youyi.yRot = 0.0F;
        this.youyi.zRot = 0.0F;
        this.bone10.xRot = 0.0F;
        this.bone10.yRot = 0.0F;
        this.bone10.zRot = 0.0F;
        this.bone6.xRot = 0.0F;
        this.bone6.yRot = 0.0F;
        this.bone6.zRot = 0.0F;

        // 重置缩放
        this.zuoyi.xScale = 1.0F;
        this.zuoyi.yScale = 1.0F;
        this.zuoyi.zScale = 1.0F;
        this.youyi.xScale = 1.0F;
        this.youyi.yScale = 1.0F;
        this.youyi.zScale = 1.0F;
    }

    /**
     * 应用待机动画
     */
    public void applyIdleAnimation(int timer) {
        float time = timer * 0.05F; // 控制动画速度
        float cycle = time % 8.0F; // 8秒循环

        // zuoyi动画
        if (cycle <= 2.0F) {
            this.zuoyi.zRot = (float) Math.toRadians(21.0F * (cycle / 2.0F));
        } else if (cycle <= 6.0F) {
            float progress = (cycle - 2.0F) / 4.0F;
            this.zuoyi.zRot = (float) Math.toRadians(21.0F - 31.0F * progress);
        } else {
            float progress = (cycle - 6.0F) / 2.0F;
            this.zuoyi.zRot = (float) Math.toRadians(-10.0F + 10.0F * progress);
        }

        // youyi动画 (镜像)
        if (cycle <= 2.0F) {
            this.youyi.zRot = (float) Math.toRadians(-21.0F * (cycle / 2.0F));
        } else if (cycle <= 6.0F) {
            float progress = (cycle - 2.0F) / 4.0F;
            this.youyi.zRot = (float) Math.toRadians(-21.0F + 31.0F * progress);
        } else {
            float progress = (cycle - 6.0F) / 2.0F;
            this.youyi.zRot = (float) Math.toRadians(10.0F - 10.0F * progress);
        }

        // bone10动画
        if (cycle <= 2.0F) {
            this.bone10.zRot = (float) Math.toRadians(-27.5F * (cycle / 2.0F));
        } else if (cycle <= 5.0F) {
            float progress = (cycle - 2.0F) / 3.0F;
            this.bone10.zRot = (float) Math.toRadians(-27.5F + 27.5F * progress);
        } else if (cycle <= 6.5F) {
            float progress = (cycle - 5.0F) / 1.5F;
            this.bone10.zRot = (float) Math.toRadians(-12.5F * progress);
        } else {
            float progress = (cycle - 6.5F) / 1.5F;
            this.bone10.zRot = (float) Math.toRadians(-12.5F + 12.5F * progress);
        }

        // bone6动画 (镜像)
        if (cycle <= 2.0F) {
            this.bone6.zRot = (float) Math.toRadians(27.5F * (cycle / 2.0F));
        } else if (cycle <= 5.0F) {
            float progress = (cycle - 2.0F) / 3.0F;
            this.bone6.zRot = (float) Math.toRadians(27.5F - 27.5F * progress);
        } else if (cycle <= 6.5F) {
            float progress = (cycle - 5.0F) / 1.5F;
            this.bone6.zRot = (float) Math.toRadians(12.5F * progress);
        } else {
            float progress = (cycle - 6.5F) / 1.5F;
            this.bone6.zRot = (float) Math.toRadians(12.5F - 12.5F * progress);
        }
    }

    /**
     * 应用武器动画 - 更新版本
     */
    public void applyWeaponAnimation(int timer) {
        float time = timer * 0.05F; // 减慢动画速度（从0.05F改为0.02F）

        if (time <= 0.75F) {
            float progress = time / 0.75F;
            // 旋转动画 - 使用精确的角度
            this.zuoyi.xRot = (float) Math.toRadians(2.2985921868F * progress);
            this.zuoyi.yRot = (float) Math.toRadians(-1.4387113584F * progress);
            this.zuoyi.zRot = (float) Math.toRadians(77.3023476933F * progress);

            this.youyi.xRot = (float) Math.toRadians(0.8479548998F * progress);
            this.youyi.yRot = (float) Math.toRadians(-2.2455933803F * progress);
            this.youyi.zRot = (float) Math.toRadians(-81.9676102269F * progress);

            // 缩放动画
            this.zuoyi.xScale = 1.0F - 0.4F * progress; // 1.0 -> 0.6
            this.zuoyi.yScale = 1.0F - 0.2F * progress; // 1.0 -> 0.8
            this.youyi.xScale = 1.0F - 0.4F * progress;
            this.youyi.yScale = 1.0F - 0.2F * progress;

            // bone10和bone6的动画
            if (progress <= 0.6667F / 0.75F) { // 约0.889
                float boneProgress = progress / (0.6667F / 0.75F);
                this.bone10.zRot = (float) Math.toRadians(-65.0F * boneProgress);
                this.bone6.zRot = (float) Math.toRadians(65.0F * boneProgress);
            } else {
                this.bone10.zRot = (float) Math.toRadians(-65.0F);
                this.bone10.yRot = (float) Math.toRadians(25.0F);
                this.bone6.zRot = (float) Math.toRadians(65.0F);
                this.bone6.yRot = (float) Math.toRadians(-20.0F);
            }
        } else {
            // 保持最终状态
            this.zuoyi.xRot = (float) Math.toRadians(2.2985921868F);
            this.zuoyi.yRot = (float) Math.toRadians(-1.4387113584F);
            this.zuoyi.zRot = (float) Math.toRadians(77.3023476933F);

            this.youyi.xRot = (float) Math.toRadians(0.8479548998F);
            this.youyi.yRot = (float) Math.toRadians(-2.2455933803F);
            this.youyi.zRot = (float) Math.toRadians(-81.9676102269F);

            this.zuoyi.xScale = 0.6F;
            this.zuoyi.yScale = 0.8F;
            this.youyi.xScale = 0.6F;
            this.youyi.yScale = 0.8F;

            this.bone10.zRot = (float) Math.toRadians(-65.0F);
            this.bone10.yRot = (float) Math.toRadians(25.0F);
            this.bone6.zRot = (float) Math.toRadians(65.0F);
            this.bone6.yRot = (float) Math.toRadians(-20.0F);
        }
    }

    /**
     * 应用取消武器动画 - 更新版本
     */
    public void applyCancelWeaponAnimation(int timer) {
        float time = timer * 0.05F;
        float progress = Math.min(time / 0.5833F, 1.0F);

        // 从武器状态回到初始状态 - 使用精确的角度
        this.zuoyi.xRot = (float) Math.toRadians(2.2985921868F * (1.0F - progress));
        this.zuoyi.yRot = (float) Math.toRadians(-1.4387113584F * (1.0F - progress));
        this.zuoyi.zRot = (float) Math.toRadians(77.3023476933F * (1.0F - progress));

        this.youyi.xRot = (float) Math.toRadians(0.8479548998F * (1.0F - progress));
        this.youyi.yRot = (float) Math.toRadians(-2.2455933803F * (1.0F - progress));
        this.youyi.zRot = (float) Math.toRadians(-81.9676102269F * (1.0F - progress));

        // 缩放回到正常
        this.zuoyi.xScale = 0.6F + 0.4F * progress; // 0.6 -> 1.0
        this.zuoyi.yScale = 0.8F + 0.2F * progress; // 0.8 -> 1.0
        this.youyi.xScale = 0.6F + 0.4F * progress;
        this.youyi.yScale = 0.8F + 0.2F * progress;

        this.bone10.zRot = (float) Math.toRadians(-65.0F * (1.0F - progress));
        this.bone10.yRot = (float) Math.toRadians(25.0F * (1.0F - progress));
        this.bone6.zRot = (float) Math.toRadians(65.0F * (1.0F - progress));
        this.bone6.yRot = (float) Math.toRadians(-20.0F * (1.0F - progress));
    }

    @Override
    public void renderToBuffer(com.mojang.blaze3d.vertex.PoseStack poseStack, com.mojang.blaze3d.vertex.VertexConsumer vertexConsumer,
                              int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
        bone4.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
        bone7.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
        bone8.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
        youyi.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
        zuoyi.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
    }

}
