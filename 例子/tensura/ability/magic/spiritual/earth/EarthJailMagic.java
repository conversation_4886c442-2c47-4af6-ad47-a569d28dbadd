package com.github.manasmods.tensura.ability.magic.spiritual.earth;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.block.Blocks;

public class EarthJailMagic extends SpiritualMagic {
   public EarthJailMagic() {
      super(MagicElemental.EARTH, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 140;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            int radius = instance.isMastered(entity) ? 30 : 20;
            LivingEntity target = SkillHelper.getTargetingEntity(entity, (double)radius, false, true);
            if (target != null) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12054_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()));
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.BOG_EFFECT.get());
               DamageSource damageSource = TensuraDamageSources.elementalAttack("tensura.earth_attack", entity, true);
               if (target.m_6469_(damageSource, 50.0F)) {
                  if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.EARTH_ATTACK_NULLIFICATION.get())) {
                     return;
                  }

                  this.addMasteryPoint(instance, entity);
                  instance.setCoolDown(instance.isMastered(entity) ? 20 : 10);
                  instance.getOrCreateTag().m_128405_("HeldTicks", 0);
                  instance.markDirty();
                  int slowness = 5;
                  if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.EARTH_ATTACK_RESISTANCE.get())) {
                     slowness = 2;
                  }

                  target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 1200, slowness, false, false, false));
                  if (instance.isMastered(entity)) {
                     target.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 1200, 1, false, false, false));
                  }

                  target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.BURDEN.get(), 1200, 1, false, false, false));
                  target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 1200, 1, false, false, false));
               }

            }
         }
      }
   }
}
