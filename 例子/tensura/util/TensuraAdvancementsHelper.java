package com.github.manasmods.tensura.util;

import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.advancements.Advancement;
import net.minecraft.advancements.AdvancementProgress;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.PlayerAdvancements;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class TensuraAdvancementsHelper {
   @Nullable
   public static Advancement getAdvancement(Player player, ResourceLocation advancement) {
      return getAdvancement(player.f_19853_, advancement);
   }

   @Nullable
   public static Advancement getAdvancement(Level level, ResourceLocation advancement) {
      return level.m_7654_().m_129889_().m_136041_(advancement);
   }

   public static void grant(ServerPlayer serverPlayer, ResourceLocation resourceLocation) {
      Advancement advancement = getAdvancement((Player)serverPlayer, resourceLocation);
      PlayerAdvancements playerAdvancements = serverPlayer.m_8960_();
      AdvancementProgress advancementProgress = playerAdvancements.m_135996_(advancement);
      if (!advancementProgress.m_8193_()) {
         Iterator var5 = advancementProgress.m_8219_().iterator();

         while(var5.hasNext()) {
            String key = (String)var5.next();
            playerAdvancements.m_135988_(advancement, key);
         }

      }
   }

   public static void grantAllTensuraAdvancements(ServerPlayer serverPlayer) {
      Iterator var1 = TensuraAdvancementsHelper.Advancements.ALL_ADVANCEMENTS.iterator();

      while(var1.hasNext()) {
         ResourceLocation advancement = (ResourceLocation)var1.next();
         grant(serverPlayer, advancement);
      }

   }

   public static void revoke(ServerPlayer serverPlayer, ResourceLocation resourceLocation) {
      Advancement advancement = getAdvancement((Player)serverPlayer, resourceLocation);
      PlayerAdvancements playerAdvancements = serverPlayer.m_8960_();
      AdvancementProgress advancementProgress = playerAdvancements.m_135996_(advancement);
      if (advancementProgress.m_8193_()) {
         Iterator var5 = advancementProgress.m_8220_().iterator();

         while(var5.hasNext()) {
            String key = (String)var5.next();
            playerAdvancements.m_135998_(advancement, key);
         }

      }
   }

   public static void revokeAllTensuraAdvancements(ServerPlayer serverPlayer) {
      Collection<Advancement> advancements = serverPlayer.m_20194_().m_129889_().m_136028_();
      Iterator var2 = advancements.iterator();

      while(var2.hasNext()) {
         Advancement advancement = (Advancement)var2.next();
         revoke(serverPlayer, advancement.m_138327_());
      }

   }

   public static class Advancements {
      public static final ResourceLocation REINCARNATED = new ResourceLocation("tensura", "reincarnated");
      public static final ResourceLocation TAMED_A_SLIME = new ResourceLocation("tensura", "tamed_a_slime");
      public static final ResourceLocation GET_BUCKETED = new ResourceLocation("tensura", "get_bucketed");
      public static final ResourceLocation TRAITOR = new ResourceLocation("tensura", "slime_traitor");
      public static final ResourceLocation GROW_A_SLIME = new ResourceLocation("tensura", "grow_a_slime");
      public static final ResourceLocation KING_SLIME = new ResourceLocation("tensura", "king_slime");
      public static final ResourceLocation SLIME_ARMY = new ResourceLocation("tensura", "slime_army");
      public static final ResourceLocation D_RANK = new ResourceLocation("tensura", "d_rank");
      public static final ResourceLocation C_RANK = new ResourceLocation("tensura", "c_rank");
      public static final ResourceLocation B_RANK = new ResourceLocation("tensura", "b_rank");
      public static final ResourceLocation A_RANK = new ResourceLocation("tensura", "a_rank");
      public static final ResourceLocation SA_RANK = new ResourceLocation("tensura", "sa_rank");
      public static final ResourceLocation S_RANK = new ResourceLocation("tensura", "s_rank");
      public static final ResourceLocation SS_RANK = new ResourceLocation("tensura", "ss_rank");
      public static final ResourceLocation GROWTH_SPURT = new ResourceLocation("tensura", "grow_spurt");
      public static final ResourceLocation INFAMY_FAMOUS = new ResourceLocation("tensura", "infamy_famous");
      public static final ResourceLocation HIGHER_FORM = new ResourceLocation("tensura", "higher_form");
      public static final ResourceLocation HEAR_ME_DIREWOLVES = new ResourceLocation("tensura", "hear_me_direwolves");
      public static final ResourceLocation NAME_A_MOB = new ResourceLocation("tensura", "name_a_mob");
      public static final ResourceLocation RULER_OF_MONSTERS = new ResourceLocation("tensura", "ruler_of_monsters");
      public static final ResourceLocation NANODA = new ResourceLocation("tensura", "nanoda");
      public static final ResourceLocation REWIND_TIME = new ResourceLocation("tensura", "rewind_time");
      public static final ResourceLocation OBTAIN_HIHIIROKANE_HOE = new ResourceLocation("tensura", "obtain_hihiirokane_hoe");
      public static final ResourceLocation GETCHA_LEATHERS = new ResourceLocation("tensura", "getcha_leathers");
      public static final ResourceLocation GOLD_RUSH = new ResourceLocation("tensura", "gold_rush");
      public static final ResourceLocation ACQUIRE_SILVERWARE = new ResourceLocation("tensura", "acquire_silverware");
      public static final ResourceLocation GETCHA_BETTER_LEATHERS = new ResourceLocation("tensura", "getcha_better_leathers");
      public static final ResourceLocation BELIEVE_T0_FLY = new ResourceLocation("tensura", "believe_to_fly");
      public static final ResourceLocation RIPOFF_ELYTRA = new ResourceLocation("tensura", "ripoff_elytra");
      public static final ResourceLocation MAGIC_ORE = new ResourceLocation("tensura", "magic_ore");
      public static final ResourceLocation LOW_MAGISTEEL = new ResourceLocation("tensura", "low_magisteel");
      public static final ResourceLocation HIGH_MAGISTEEL = new ResourceLocation("tensura", "high_magisteel");
      public static final ResourceLocation MITHRIL = new ResourceLocation("tensura", "mithril");
      public static final ResourceLocation ORICHALCUM = new ResourceLocation("tensura", "orichalcum");
      public static final ResourceLocation PURE_MAGISTEEL = new ResourceLocation("tensura", "pure_magisteel");
      public static final ResourceLocation ADAMANTITE = new ResourceLocation("tensura", "adamantite");
      public static final ResourceLocation HIHIIROKANE = new ResourceLocation("tensura", "hihiirokane");
      public static final ResourceLocation VIGILANT = new ResourceLocation("tensura", "vigilant");
      public static final ResourceLocation SHELL_LIZARD = new ResourceLocation("tensura", "shell_lizard");
      public static final ResourceLocation HISS_TORY = new ResourceLocation("tensura", "hiss_tory");
      public static final ResourceLocation GOODNIGHT_SPIDER = new ResourceLocation("tensura", "goodnight_spider");
      public static final ResourceLocation ARACHNOPHOBIC = new ResourceLocation("tensura", "arachnophobic");
      public static final ResourceLocation EAT_OR_BE_EATEN = new ResourceLocation("tensura", "eat_or_be_eaten");
      public static final ResourceLocation CONQUEROR_OF_FLAMES = new ResourceLocation("tensura", "conqueror_of_flames");
      public static final ResourceLocation RULER_OF_THE_SKIES = new ResourceLocation("tensura", "ruler_of_the_skies");
      public static final ResourceLocation GREAT_SAINT_OF_THE_WEST = new ResourceLocation("tensura", "great_saint_of_the_west");
      public static final ResourceLocation LABYRINTH = new ResourceLocation("tensura", "labyrinth");
      public static final ResourceLocation JUST_A_TEST = new ResourceLocation("tensura", "just_a_test");
      public static final ResourceLocation SPIRIT_PROTECTOR = new ResourceLocation("tensura", "spirit_protector");
      public static final ResourceLocation ELEMENTALIST = new ResourceLocation("tensura", "elementalist");
      public static final ResourceLocation BLESSED_ONE = new ResourceLocation("tensura", "blessed_one");
      public static final ResourceLocation INFINITY_CORES = new ResourceLocation("tensura", "infinity_cores");
      public static final ResourceLocation START_SMITHING = new ResourceLocation("tensura", "start_smithing");
      public static final ResourceLocation BECOME_NINJA = new ResourceLocation("tensura", "become_ninja");
      public static final ResourceLocation UNHEALABLE_WOUND = new ResourceLocation("tensura", "unhealable_wound");
      public static final ResourceLocation A_BIT_COLD = new ResourceLocation("tensura", "a_bit_cold");
      public static final ResourceLocation MASTER_SMITH = new ResourceLocation("tensura", "master_smith");
      public static final ResourceLocation NO_NO_SQUARE = new ResourceLocation("tensura", "no_no_square");
      public static final ResourceLocation BETTER_SMELTER = new ResourceLocation("tensura", "better_smelter");
      public static final ResourceLocation PIERROT_MASK = new ResourceLocation("tensura", "pierrot_mask");
      public static final ResourceLocation TOO_STRONG = new ResourceLocation("tensura", "too_strong");
      public static final ResourceLocation MAGIC_SEEDY_PLACE = new ResourceLocation("tensura", "magic_seedy_place");
      public static final ResourceLocation HIPOKUTE_FLOWER = new ResourceLocation("tensura", "hipokute_flower");
      public static final ResourceLocation GOOD_AS_NEW = new ResourceLocation("tensura", "good_as_new");
      public static final ResourceLocation FAST_LEARNER = new ResourceLocation("tensura", "fast_learner");
      public static final ResourceLocation MASTER_SKILL = new ResourceLocation("tensura", "master_skill");
      public static final ResourceLocation MASTER_UNIQUE_SKILL = new ResourceLocation("tensura", "master_unique_skill");
      public static final ResourceLocation FORBIDDEN_MANUAL = new ResourceLocation("tensura", "forbidden_manual");
      public static final ResourceLocation MONSTER_RIDER = new ResourceLocation("tensura", "monster_rider");
      public static final ResourceLocation CHOO_CHOO = new ResourceLocation("tensura", "choo_choo");
      public static final ResourceLocation GOOD_BOY = new ResourceLocation("tensura", "good_boy");
      public static final List<ResourceLocation> ALL_ADVANCEMENTS;

      static {
         ALL_ADVANCEMENTS = Arrays.asList(REINCARNATED, GROWTH_SPURT, HIGHER_FORM, TAMED_A_SLIME, GET_BUCKETED, TRAITOR, GROW_A_SLIME, KING_SLIME, SLIME_ARMY, NAME_A_MOB, HEAR_ME_DIREWOLVES, RULER_OF_MONSTERS, OBTAIN_HIHIIROKANE_HOE, EAT_OR_BE_EATEN, CONQUEROR_OF_FLAMES, RULER_OF_THE_SKIES, GREAT_SAINT_OF_THE_WEST, GETCHA_LEATHERS, GOLD_RUSH, ACQUIRE_SILVERWARE, GETCHA_BETTER_LEATHERS, BELIEVE_T0_FLY, MAGIC_ORE, LOW_MAGISTEEL, HIGH_MAGISTEEL, MITHRIL, ORICHALCUM, PURE_MAGISTEEL, ADAMANTITE, HIHIIROKANE, VIGILANT, SHELL_LIZARD, HISS_TORY, GOODNIGHT_SPIDER, ARACHNOPHOBIC, LABYRINTH, JUST_A_TEST, SPIRIT_PROTECTOR, ELEMENTALIST, BLESSED_ONE, INFINITY_CORES, START_SMITHING, UNHEALABLE_WOUND, A_BIT_COLD, MASTER_SMITH, NO_NO_SQUARE, BETTER_SMELTER, PIERROT_MASK, TOO_STRONG, REWIND_TIME, MAGIC_SEEDY_PLACE, HIPOKUTE_FLOWER, GOOD_AS_NEW, FAST_LEARNER, MASTER_SKILL, MASTER_UNIQUE_SKILL, FORBIDDEN_MANUAL, MONSTER_RIDER, CHOO_CHOO, GOOD_BOY);
      }
   }
}
