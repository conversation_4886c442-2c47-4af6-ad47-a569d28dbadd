package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.List;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;

public class DrainSkill extends Skill {
   public DrainSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      LivingEntity target = SkillHelper.getTargetingEntity(entity, 3.0D, false);
      if (target != null && target.m_6084_()) {
         if (RaceHelper.hasNoBlood(target)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            }

         } else {
            if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.bloodDrain(entity), entity, instance), 6.0F)) {
               entity.m_5634_(6.0F);
               this.addMasteryPoint(instance, entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, DustParticleOptions.f_123656_);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11911_, SoundSource.PLAYERS, 1.0F, 1.0F);
               List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter((skillInstance) -> {
                  return this.isDrainable(skillInstance.getSkill());
               }).toList();
               ManasSkill skill = ((ManasSkillInstance)collection.get(target.m_217043_().m_188503_(collection.size()))).getSkill();
               if (!SkillUtils.fullyHasSkill(entity, skill)) {
                  SkillPlunderEvent event = new SkillPlunderEvent(target, entity, false, skill);
                  if (MinecraftForge.EVENT_BUS.post(event)) {
                     return;
                  }

                  if (learnTemporarySkill(entity, new TensuraSkillInstance(event.getSkill()))) {
                     instance.setCoolDown(80);
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        player.m_5661_(Component.m_237110_("tensura.skill.temporary.success_drain", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), false);
                     }

                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               } else if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237110_("tensura.skill.temporary.already_have", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }
            }

         }
      }
   }

   public static boolean learnTemporarySkill(LivingEntity entity, ManasSkillInstance skill) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> optional = storage.getSkill(skill.getSkill());
      if (optional.isEmpty()) {
         skill.setRemoveTime(80);
         return storage.learnSkill(skill);
      } else {
         ManasSkillInstance instance = (ManasSkillInstance)optional.get();
         if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
            return false;
         } else {
            ManasSkillInstance clone = instance.clone();
            clone.getOrCreateTag().m_128405_("OldRemoval", clone.getRemoveTime());
            clone.getOrCreateTag().m_128405_("OldMastery", clone.getMastery());
            clone.setRemoveTime(80);
            clone.setMastery(0);
            clone.markDirty();
            UnlockSkillEvent event = new UnlockSkillEvent(clone, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               return false;
            } else {
               instance.deserialize(event.getSkillInstance().toNBT());
               instance.markDirty();
               storage.syncChanges();
               return true;
            }
         }
      }
   }

   public boolean isDrainable(ManasSkill manasSkill) {
      if (!(manasSkill instanceof Skill)) {
         return false;
      } else {
         Skill skill = (Skill)manasSkill;
         return skill.getType().equals(Skill.SkillType.INTRINSIC) || skill.getType().equals(Skill.SkillType.COMMON) || skill.getType().equals(Skill.SkillType.EXTRA);
      }
   }
}
