package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import java.util.function.Predicate;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class ChefSkill extends Skill {
   public ChefSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double learningCost() {
      return 1000.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      LivingEntity living = SkillHelper.getTargetingEntity(entity, 3.0D, false);
      boolean success;
      Predicate predicate;
      int cost;
      float lackedHealth;
      double lackedMagicule;
      if (living != null && entity.m_6144_()) {
         success = TensuraEffectsCapability.getSeverance(living) > 0.0D;
         TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
            cap.setSeveranceAmount(0.0D);
         });
         predicate = (effect) -> {
            return effect.m_19483_() == MobEffectCategory.HARMFUL;
         };
         success = success || SkillHelper.removePredicateEffect(living, predicate, this.magiculeCost(entity, instance));
         cost = instance.isMastered(entity) ? 40 : 60;
         lackedHealth = living.m_21233_() - living.m_21223_();
         lackedMagicule = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedHealth * (float)cost)));
         if (lackedMagicule > 0.0D) {
            lackedHealth = (float)((double)lackedHealth - lackedMagicule / (double)cost);
         }

         living.m_5634_(lackedHealth);
         success = success || lackedHealth > 0.0F;
         if (success) {
            TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123749_, 2.0D);
         }
      } else {
         success = TensuraEffectsCapability.getSeverance(entity) > 0.0D;
         TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
            cap.setSeveranceAmount(0.0D);
         });
         predicate = (effect) -> {
            return effect.m_19483_() == MobEffectCategory.HARMFUL;
         };
         success = success || SkillHelper.removePredicateEffect(entity, predicate, this.magiculeCost(entity, instance));
         cost = instance.isMastered(entity) ? 40 : 60;
         lackedHealth = entity.m_21233_() - entity.m_21223_();
         lackedMagicule = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedHealth * (float)cost)));
         if (lackedMagicule > 0.0D) {
            lackedHealth = (float)((double)lackedHealth - lackedMagicule / (double)cost);
         }

         entity.m_5634_(lackedHealth);
         success = success || lackedHealth > 0.0F;
         if (success) {
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 2.0D);
         }
      }

      if (success) {
         this.addMasteryPoint(instance, entity);
         instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11937_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
