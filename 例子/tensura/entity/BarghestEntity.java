package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.UUID;
import java.util.function.Predicate;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class BarghestEntity extends TensuraTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   public int miscAnimationTicks = 0;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public BarghestEntity(EntityType<? extends BarghestEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 20;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22276_, 40.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.20000000298023224D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new BarghestEntity.BarghestAttackGoal());
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.0D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.1D));
      this.f_21345_.m_25352_(6, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(7, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(8, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(7, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean canSleep() {
      return true;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.barghestSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      BarghestEntity barghest = (BarghestEntity)((EntityType)TensuraEntityTypes.BARGHEST.get()).m_20615_(pLevel);
      if (barghest == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            barghest.m_21816_(uuid);
            barghest.m_7105_(true);
         }

         return barghest;
      }
   }

   protected float m_6118_() {
      return 0.5F * this.m_20098_();
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19309_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 5.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 5.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity)) {
         if (pEntity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)pEntity;
            if (this.f_19796_.m_188503_(5) == 1) {
               living.m_20254_(3);
            }
         }

         return true;
      } else {
         return false;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 15) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(3.0F);
            this.setMiscAnimation(1);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.setMiscAnimation(1);
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   protected SoundEvent m_7515_() {
      if (this.m_5448_() != null) {
         return SoundEvents.f_12619_;
      } else if (this.f_19796_.m_188503_(3) != 0) {
         return SoundEvents.f_12617_;
      } else {
         return this.m_21824_() && this.m_21223_() < 10.0F ? SoundEvents.f_12625_ : SoundEvents.f_12622_;
      }
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12621_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12618_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected boolean isInjured() {
      return (double)this.m_21223_() < (double)this.m_21233_() / 4.0D;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      String name;
      if (this.m_5803_()) {
         name = "animation.barghest.sleep";
      } else if (this.m_21825_()) {
         name = this.isInjured() ? "animation.barghest.sit_injured" : "animation.barghest.sit";
      } else if (!this.m_20069_() && !this.m_20077_()) {
         if (event.isMoving()) {
            if (this.m_21660_()) {
               name = "animation.barghest.run";
            } else {
               name = this.isInjured() ? "animation.barghest.walk_injured" : "animation.barghest.walk";
            }
         } else {
            name = this.isInjured() ? "animation.barghest.idle_injured" : "animation.barghest.idle";
         }
      } else {
         name = "animation.barghest.swim";
      }

      event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.LOOP));
      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         String name = null;
         if (!this.m_5803_()) {
            if (this.getMiscAnimation() == 1) {
               name = "animation.barghest.eat";
            } else if (this.getMiscAnimation() == 2) {
               name = "animation.barghest.bite";
            }
         }

         if (name != null) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(BarghestEntity.class, EntityDataSerializers.f_135028_);
   }

   class BarghestAttackGoal extends MeleeAttackGoal {
      public BarghestAttackGoal() {
         super(BarghestEntity.this, 2.0D, true);
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (pDistToEnemySqr <= d0 && this.m_25565_() <= 0) {
            this.m_25563_();
            BarghestEntity.this.setMiscAnimation(2);
            this.f_25540_.m_7327_(pEnemy);
         }

      }

      public void m_8056_() {
         BarghestEntity.this.setSleeping(Boolean.FALSE);
         super.m_8056_();
      }
   }
}
