package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Objects;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class OneEyedOwlEntity extends FLyingTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> ATTACK_TICK;
   public float prevAttackProgress;
   public float attackProgress;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public OneEyedOwlEntity(EntityType<? extends TamableAnimal> type, Level worldIn) {
      super(type, worldIn);
      this.f_21364_ = 5;
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 10.0D).m_22268_(Attributes.f_22281_, 1.0D).m_22268_(Attributes.f_22278_, 0.009999999776482582D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22279_, 0.15000000596046448D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new OneEyedOwlEntity.OwlFlightAttackGoal(this));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(4, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(6, new EatBlockGoal(this));
      this.f_21345_.m_25352_(7, new FLyingTamableEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(4, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(ATTACK_TICK, 0);
   }

   public float m_20236_(Pose pose) {
      return this.m_20206_() * 0.8F;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || super.m_6673_(source);
   }

   public void m_8119_() {
      super.m_8119_();
      this.prevAttackProgress = this.attackProgress;
      if ((Integer)this.f_19804_.m_135370_(ATTACK_TICK) > 0) {
         this.f_19804_.m_135381_(ATTACK_TICK, (Integer)this.f_19804_.m_135370_(ATTACK_TICK) - 1);
         if (this.attackProgress < 5.0F) {
            ++this.attackProgress;
         }
      } else if (this.attackProgress > 0.0F) {
         --this.attackProgress;
      }

   }

   protected SoundEvent m_7515_() {
      return (SoundEvent)TensuraSoundEvents.OWL_AMBIENT.get();
   }

   protected SoundEvent m_7975_(DamageSource damageSourceIn) {
      return (SoundEvent)TensuraSoundEvents.BIRD_HURT.get();
   }

   protected SoundEvent m_5592_() {
      return (SoundEvent)TensuraSoundEvents.BIRD_DEATH.get();
   }

   public AgeableMob m_142606_(ServerLevel world, AgeableMob entity) {
      OneEyedOwlEntity owl = (OneEyedOwlEntity)((EntityType)TensuraEntityTypes.ONE_EYED_OWL.get()).m_20615_(world);
      if (owl == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            owl.m_21816_(uuid);
            owl.m_7105_(true);
         }

         return owl;
      }
   }

   public boolean m_6898_(ItemStack stack) {
      return stack.m_204117_(TensuraTags.Items.FISHES);
   }

   public boolean isTamingFood(ItemStack stack) {
      return stack.m_204117_(TensuraTags.Items.OWL_TAMING_FOOD);
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         FoodProperties food = itemstack.getFoodProperties((LivingEntity)null);
         if (food != null) {
            this.m_5634_((float)food.m_38744_());
         } else {
            this.m_5634_(3.0F);
         }

         return InteractionResult.SUCCESS;
      } else if (this.f_19853_.f_46443_) {
         boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack);
         return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
      } else if (this.m_21824_()) {
         if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
            this.commanding(player);
            return InteractionResult.SUCCESS;
         } else {
            return InteractionResult.PASS;
         }
      } else if (this.isTamingFood(itemstack)) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         if (this.f_19796_.m_188503_(3) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
            this.m_21828_(player);
            this.f_21344_.m_26573_();
            this.m_6710_((LivingEntity)null);
            this.m_21839_(true);
            this.f_19853_.m_7605_(this, (byte)7);
         } else {
            this.f_19853_.m_7605_(this, (byte)6);
         }

         return InteractionResult.SUCCESS;
      } else {
         return super.m_6071_(player, hand);
      }
   }

   public static boolean checkOwlSpawnRules(EntityType<OneEyedOwlEntity> pOwl, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184232_) && m_186209_(pLevel, pPos);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.oneEyedOwlSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (event.isMoving()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.one_eyed_owl.flying", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         return PlayState.CONTINUE;
      }
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      ATTACK_TICK = SynchedEntityData.m_135353_(OneEyedOwlEntity.class, EntityDataSerializers.f_135028_);
   }

   static class OwlFlightAttackGoal extends Goal {
      private final OneEyedOwlEntity owl;
      float circlingTime = 0.0F;
      float circleDistance = 1.0F;
      float yLevel = 2.0F;
      boolean clockwise = false;
      private int maxCircleTime;

      public OwlFlightAttackGoal(OneEyedOwlEntity crow) {
         this.owl = crow;
      }

      public boolean m_8036_() {
         return this.owl.m_5448_() != null && !this.owl.m_21827_();
      }

      public void m_8056_() {
         this.clockwise = this.owl.m_217043_().m_188499_();
         this.yLevel = (float)this.owl.m_217043_().m_188503_(2);
         this.circlingTime = 0.0F;
         this.maxCircleTime = this.owl.m_217043_().m_188503_(50);
         this.circleDistance = this.owl.m_217043_().m_188501_() * 3.0F;
      }

      public void m_8041_() {
         this.m_8056_();
         if (this.owl.m_20096_()) {
            this.owl.setFlying(false);
         }

      }

      public void m_8037_() {
         if (this.owl.m_29443_()) {
            ++this.circlingTime;
         }

         LivingEntity target = this.owl.m_5448_();
         if (target != null) {
            if (this.circlingTime > (float)this.maxCircleTime) {
               this.owl.m_21566_().m_6849_(target.m_20185_(), target.m_20186_() + (double)(target.m_20192_() / 2.0F), target.m_20189_(), 1.2999999523162842D);
               if (this.owl.m_20270_(target) < 2.0F) {
                  target.m_6469_(DamageSource.m_19370_(this.owl), (float)((AttributeInstance)Objects.requireNonNull(this.owl.m_21051_(Attributes.f_22281_))).m_22135_());
                  this.m_8041_();
               }
            } else {
               Vec3 circlePos = this.getCirclePos(target.m_20182_());
               if (circlePos == null) {
                  circlePos = target.m_20182_();
               }

               this.owl.setFlying(true);
               this.owl.m_21566_().m_6849_(circlePos.m_7096_(), circlePos.m_7098_() + (double)target.m_20192_() + 0.20000000298023224D, circlePos.m_7094_(), 1.0D);
            }

         }
      }

      @Nullable
      public Vec3 getCirclePos(Vec3 target) {
         float angle = 0.13962634F * (this.clockwise ? -this.circlingTime : this.circlingTime);
         double extraX = (double)(this.circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(this.circleDistance * Mth.m_14089_(angle));
         Vec3 pos = new Vec3(target.m_7096_() + extraX, target.m_7098_() + (double)this.yLevel, target.m_7094_() + extraZ);
         return this.owl.f_19853_.m_46859_(new BlockPos(pos)) ? pos : null;
      }
   }
}
