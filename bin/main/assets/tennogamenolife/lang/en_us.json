{"tennogamenolife.skill.magic_beads": "Magic Beads", "tennogamenolife.skill.magic_beads.description": "Extract and compress your own magicule into a bead", "item.tennogamenolife.mag": "Chaos Magic Orb", "item.tennogamenolife.mag.desc": "Right-clicking on a creature will increase its EP by 10,000, and it is made from your own magical essence!", "tennogamenolife.race.machine": "Machine", "tennogamenolife.race.machine.note": "A race created by the God of Wisdom, possessing powerful analytical and combat capabilities.", "tennogamenolife.race.befehler": "Commander", "tennogamenolife.race.befehler.notes": "Evolution form of Ekusu Makina Race with higher defense, magicule values and command ability", "tennogamenolife.race.kampf": "Combat", "tennogamenolife.race.kampf.notes": "Evolution form of Ekusu Makina Race focused on combat ability with higher attack and defense", "tennogamenolife.race.zeichen": "Design", "tennogamenolife.race.zeichen.notes": "Evolution form of Ekusu Makina Race focused on creation and design ability with higher magicule values", "tennogamenolife.race.pruefer": "<PERSON><PERSON><PERSON>", "tennogamenolife.race.pruefer.notes": "Evolution form of Ekusu Makina Race focused on analysis and detection ability with higher magicule values", "tennogamenolife.race.seher": "Observer", "tennogamenolife.race.seher.notes": "Evolution form of Ekusu Makina Race focused on observation and perception ability with higher magicule values", "tennogamenolife.race.hubie_dora": "<PERSON><PERSON>", "tennogamenolife.race.hubie_dora.notes": "Evolution form of Analyzer focused on advanced analysis and data processing ability with higher magicule values", "tennogamenolife.race.einzeig": "Einzeig", "tennogamenolife.race.einzeig.notes": "Evolution form of Commander with higher defense, magicule values and spatial manipulation ability", "tennogamenolife.race.emir_eins": "<PERSON><PERSON>", "tennogamenolife.race.emir_eins.notes": "Evolution form of Commander with higher defense, magicule values and spatial manipulation ability", "tennogamenolife.race.horou": "§6§<PERSON><PERSON><PERSON><PERSON>, <PERSON> of Doubt", "tennogamenolife.race.horou.notes": "The primordial deity who created the Ekusu Makina race. As the creator of the world, possessing the ability to control space, time and elements, making them nearly invincible in battle.", "tennogamenolife.race.horou.description": "<PERSON><PERSON> is the primordial deity with unparalleled power and magicule values. Due to loneliness, <PERSON><PERSON> created the Ekusu Makina race, and eventually, the energy converged to return to its source, reaching divine existence.", "item.tennogamenolife.mechanical_core": "§5Mechanical Core", "item.tennogamenolife.mechanical_core.desc": "§6§lAncient Relic", "tensura.evolution_menu.mechanical_core_requirement": "Consume 8 Mechanical Cores", "tennogamenolife.skill.barrier": "§lUmweg", "tennogamenolife.skill.barrier.description": "Stops creatures from reaching you and makes them stand still!", "tensura.evolution_menu.ep_requirement_specific": "Have at least 1 million EP", "tensura.evolution_menu.dragon_essence_requirement": "Consume 10 Dragon Essences", "tennogamenolife.race.flugel": "<PERSON><PERSON><PERSON><PERSON>", "tennogamenolife.race.flugel.note": "One of the sixteen races created by the God of War, possessing angelic wings and representing death.", "tennogamenolife.race.higher_flugel": "Higher Flügel", "tennogamenolife.race.higher_flugel.notes": "Evolution form of <PERSON><PERSON><PERSON><PERSON> with enhanced flight capabilities and magicule control, possessing sharper battle perception.", "tennogamenolife.skill.tengeki": "§6§lFlügel:§4Tengeki", "tennogamenolife.skill.tengeki.description": "It is a magic used by the Flügel during the Great War, their only named move, and also their most powerful attack method.", "skill.tengeki.forbidden_dimension": "§cThis skill cannot be used in the Labyrinth dimension!", "tennogamenolife.charging": "Charging: %d seconds", "tennogamenolife.charging_complete": "Power Increasing: %d seconds", "tennogamenolife.charging_max": "Ultimate Power Charged", "death.attack.magicule_explosion": "[%s] died from magical overload", "death.attack.magicule_explosion.player": "[%s] died from magical overload", "effect.tennogamenolife.weaken": "juvenile state", "tennogamenolife.race.jibril": "Jibril", "tennogamenolife.race.raphael": "<PERSON>", "tennogamenolife.race.azril": "Azril", "tennogamenolife.race.puraiya": "§6Puraiya", "tennogamenolife.skill.reisen": "§6§lLösen:§4Rēsen", "tennogamenolife.skill.reisen.description": "Copy the ultimate skill of the target for a short duration", "tennogamenolife.skill.reisen.success_copy": "Successfully copied %s skill!", "tennogamenolife.skill.reisen.success_copy_duration": "Successfully copied %s's ultimate skill for %d seconds!", "tennogamenolife.skill.reisen.cannot_copy": "<PERSON><PERSON> failed, please try again", "tennogamenolife.skill.reisen.no_ultimate_skill": "Target has no ultimate skill to copy!", "tennogamenolife.skill.reisen.only_player_skill": "Can only copy player ultimate skills!", "tennogamenolife.skill.reisen.no_target": "No target found!", "race.tennogamenolife.arutoshu": "<PERSON><PERSON><PERSON><PERSON>", "block.tennogamenolife.chess_block": "§6§lChess Display", "tennogamenolife.race.dwarf": "<PERSON><PERSON><PERSON>", "tennogamenolife.race.dwarf.notes": "Although they cannot use magic directly, they are good at making magic machines", "tennogamenolife.race.nii_tiruvirugu": "<PERSON><PERSON>", "item.tennogamenolife.star_essence": "Star Essence", "tensura.evolution_menu.star_essence_requirement": "Requires consuming 1 Star Essence", "tennogamenolife.race.veigu_dorauvuniru": "<PERSON><PERSON><PERSON>", "tennogamenolife.race.roni_dorauvuniru": "<PERSON><PERSON>", "tennogamenolife.race.ocain": "Ocain", "item.tennogamenolife.sickle": "§6<PERSON><PERSON><PERSON><PERSON>'s scythe", "tooltip.tensura.long_sword.tooltip": "§4§lIs this the legendary weapon of Death?", "tennogamenolife.race.fantasuma": "Fantasuma", "tennogamenolife.race.fantasuma.notes": "As the second among the sixteen races, they are the conscious changes in the world.", "tennogamenolife.race.dragonia": "Dragonia", "tennogamenolife.race.dragonia.notes": "A powerful race with dragon blood, possessing exceptional strength and magic resistance.", "tennogamenolife.race.lesser_dragonia": "Lesser Dragonia", "tennogamenolife.race.lesser_dragonia.notes": "An evolved form of Dragonia with enhanced strength, magic resistance, and higher magicule values.", "tennogamenolife.race.middle_dragonia": "Middle Dragonia", "tennogamenolife.race.middle_dragonia.notes": "An evolved form of Lesser Dragonia with higher health and magicule values, a more powerful dragon race.", "tennogamenolife.race.higher_dragonia": "Higher Dragonia", "tennogamenolife.race.higher_dragonia.notes": "An evolved form of Middle Dragonia with extremely high health and attack power, a top-tier existence among dragon races.", "tennogamenolife.race.aranreivu": "Aranreivu", "tennogamenolife.race.aranreivu.notes": "The final evolution of Higher Dragonia, a divine dragon existence with astonishing power and vitality.", "tennogamenolife.race.hartyleif": "<PERSON><PERSON><PERSON>", "tennogamenolife.race.hartyleif.notes": "Another evolution form of Higher Dragonia, possessing extreme destructive power and dominance, the supreme existence among dragon races.", "tennogamenolife.race.reginreivu": "Reginreivu", "tennogamenolife.race.reginreivu.notes": "The wisdom evolution form of Higher Dragonia, possessing extraordinary intelligence and foresight, the sage existence among dragon races.", "message.tennogamenolife.damage_dealt": "§8Damage Dealt: §c%s", "entity.tennogamenolife.hurt_dummy": "§dTest Dummy", "item.tennogamenolife.hurt_dummy": "§dTest Dummy", "tennogamenolife.skill.chain_mining": "<PERSON><PERSON>", "tennogamenolife.skill.chain_mining.description": "Be a groundhog, mine all the ores in the world.", "skill.tennogamenolife.chain_mining.enabled": "Ore Breaker enabled", "skill.tennogamenolife.chain_mining.disabled": "<PERSON><PERSON> disabled", "skill.tennogamenolife.chain_mining.no_magicule": "Not enough magicule for ore breaking", "skill.tennogamenolife.chain_mining.mastered": "Ore Breaker skill mastered! Maximum chain count increased to 24 blocks.", "skill.tennogamenolife.chain_mining.mode.chain": "<PERSON><PERSON>[<PERSON><PERSON><PERSON>]", "skill.tennogamenolife.chain_mining.mode.horizontal": "Horizontal Mining", "skill.tennogamenolife.chain_mining.mode.vertical": "Vertical Mining", "skill.tennogamenolife.chain_mining.mode.switch": "Switched to %s mode", "skill.tennogamenolife.chain_mining.mode.locked": "Skill not mastered, cannot switch modes", "tennogamenolife.skill.chain_felling": "<PERSON>", "tennogamenolife.skill.chain_felling.description": "Chop trees, chop trees. Want to get rich? Start by chopping trees.", "skill.tennogamenolife.chain_felling.enabled": "Tree Feller enabled", "skill.tennogamenolife.chain_felling.disabled": "<PERSON> disabled", "skill.tennogamenolife.chain_felling.no_magicule": "Not enough magicule for tree felling", "skill.tennogamenolife.chain_felling.mastered": "Tree Feller skill mastered! Maximum chain count increased to 64 blocks.", "tennogamenolife.skill.chain_shovel": "Earth Digger", "tennogamenolife.skill.chain_shovel.description": "Dig out a city by leveling all obstacles with a shovel.", "skill.tennogamenolife.chain_shovel.enabled": "Earth Digger enabled", "skill.tennogamenolife.chain_shovel.disabled": "Earth Digger disabled", "skill.tennogamenolife.chain_shovel.no_magicule": "Not enough magicule for earth digging", "skill.tennogamenolife.chain_shovel.mastered": "Earth Digger skill mastered! Maximum chain count increased to 24 blocks.", "skill.tennogamenolife.chain_shovel.mode.locked": "Skill not mastered, cannot switch modes", "skill.tennogamenolife.chain_shovel.mode.switch": "Switched to %s mode", "skill.tennogamenolife.chain_shovel.mode.chain": "Chain Digging [Default]", "skill.tennogamenolife.chain_shovel.mode.horizontal": "Horizontal Digging", "skill.tennogamenolife.chain_shovel.mode.vertical": "Vertical Digging", "item.tennogamenolife.chain_mining_skill_book": "Chain Mining Skill Book", "item.tennogamenolife.chain_mining_skill_book.tooltip": "Right-click to learn Chain Mining skill", "item.tennogamenolife.chain_mining_skill_book.tooltip2": "Mine multiple blocks of the same type at once", "item.tennogamenolife.chain_mining_skill_book.learned": "Successfully learned Chain Mining skill!", "item.tennogamenolife.chain_mining_skill_book.already_learned": "You already know the Chain Mining skill!", "item.tennogamenolife.chain_felling_skill_book": "<PERSON> Feller Skill Book", "item.tennogamenolife.chain_felling_skill_book.tooltip": "Right-click to learn Tree Feller skill", "item.tennogamenolife.chain_felling_skill_book.tooltip2": "Chop down entire trees at once", "item.tennogamenolife.chain_felling_skill_book.learned": "Successfully learned Tree Feller skill!", "item.tennogamenolife.chain_felling_skill_book.already_learned": "You already know the Tree Feller skill!", "item.tennogamenolife.chain_shovel_skill_book": "Earth Digger Skill Book", "item.tennogamenolife.chain_shovel_skill_book.tooltip": "Right-click to learn Earth Digger skill", "item.tennogamenolife.chain_shovel_skill_book.tooltip2": "Dig multiple dirt blocks at once", "item.tennogamenolife.chain_shovel_skill_book.learned": "Successfully learned Earth Digger skill!", "item.tennogamenolife.chain_shovel_skill_book.already_learned": "You already know the Earth Digger skill!"}