package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.Optional;
import java.util.function.Supplier;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestFalsifierItemPacket {
   private final int slot;
   private final ItemStack item;

   public RequestFalsifierItemPacket(FriendlyByteBuf buf) {
      this.slot = buf.readInt();
      this.item = buf.m_130267_();
   }

   public RequestFalsifierItemPacket(ItemStack item, EquipmentSlot slot) {
      this.slot = slot.m_20750_();
      this.item = item;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.slot);
      buf.m_130055_(this.item);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            SkillStorage storage = SkillAPI.getSkillsFrom(player);
            Optional<ManasSkillInstance> optional = storage.getSkill((ManasSkill)UniqueSkills.FALSIFIER.get());
            if (!optional.isEmpty()) {
               ManasSkillInstance instance = (ManasSkillInstance)optional.get();
               CompoundTag tag = instance.getOrCreateTag();
               EquipmentSlot equipmentSlot = this.getEquipmentSlot();
               tag.m_128365_(equipmentSlot.m_20751_(), this.item.serializeNBT());
               instance.markDirty();
               storage.syncChanges();
            }
         }
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private EquipmentSlot getEquipmentSlot() {
      if (this.slot == 0) {
         return EquipmentSlot.MAINHAND;
      } else {
         return this.slot == 5 ? EquipmentSlot.OFFHAND : EquipmentSlot.m_20744_(Type.ARMOR, this.slot - 1);
      }
   }

   public static ItemStack getFalsifierItem(LivingEntity pLivingEntity, EquipmentSlot slot) {
      ItemStack stack = getFalsifierItem(pLivingEntity, pLivingEntity.m_6844_(slot), slot);
      return stack.m_41783_() != null && stack.m_41783_().m_128471_("Empty") ? getEmptyStack() : stack;
   }

   public static ItemStack getFalsifierItem(LivingEntity pLivingEntity, ItemStack original, EquipmentSlot slot) {
      SkillStorage storage = SkillAPI.getSkillsFrom(pLivingEntity);
      Optional<ManasSkillInstance> optional = storage.getSkill((ManasSkill)UniqueSkills.FALSIFIER.get());
      if (optional.isEmpty()) {
         return original;
      } else {
         CompoundTag tag = ((ManasSkillInstance)optional.get()).getTag();
         if (tag != null && tag.m_128441_(slot.m_20751_())) {
            ItemStack itemStack = ItemStack.m_41712_(tag.m_128469_(slot.m_20751_()));
            if (itemStack.m_41619_()) {
               return original;
            } else if (itemStack.m_41783_() != null && itemStack.m_41783_().m_128471_("Empty")) {
               ItemStack stack = new ItemStack(Items.f_41852_);
               stack.m_41784_().m_128379_("Empty", true);
               return stack;
            } else {
               return itemStack;
            }
         } else {
            return original;
         }
      }
   }

   public static ItemStack getEmptyStack() {
      ItemStack stack = new ItemStack(Items.f_42127_);
      stack.m_41784_().m_128379_("Empty", true);
      return stack;
   }
}
