package com.github.manasmods.tensura.entity.magic.spike;

import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import org.jetbrains.annotations.NotNull;

public class EarthSpikeEntity extends SpikeEntity {
   public EarthSpikeEntity(EntityType<? extends EarthSpikeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public EarthSpikeEntity(Level pLevel, LivingEntity pOwner) {
      this((EntityType)TensuraEntityTypes.EARTH_SPIKE.get(), pLevel);
      this.setOwner(pOwner);
   }

   public boolean m_7337_(@NotNull Entity entity) {
      return entity instanceof IElementalSpirit ? false : super.m_7337_(entity);
   }

   public void applyEffect(LivingEntity target) {
      if (this.getTickCount() <= this.getExtendingTick()) {
         DamageSource damageSource = TensuraDamageSources.indirectElementalAttack("tensura.earth_attack", this, this.getOwner(), true);
         if (target.m_6469_(damageSource, this.getDamage())) {
            target.m_20184_().m_82520_(0.0D, 1.0D, 0.0D);
         }

      }
   }

   public void onBreak() {
      ParticleOptions particle = new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, particle);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, particle);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_144135_, SoundSource.PLAYERS, 1.0F, 1.0F);
      this.m_146870_();
   }
}
