package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.entity.variant.MothVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import javax.annotation.Nullable;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.geo.render.built.GeoModel;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class HellMothRenderer extends GeoEntityRenderer<HellMothEntity> {
   public HellMothRenderer(Context renderManager) {
      super(renderManager, new HellMothModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(HellMothEntity instance) {
      return instance.m_7770_() != null && instance.m_7770_().getString().equals("Mothra") ? new ResourceLocation("tensura", "textures/entity/hell_moth_line/mothra.png") : (ResourceLocation)MothVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public RenderType getRenderType(HellMothEntity moth, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (moth.m_6162_()) {
         float size = 0.25F * (float)moth.getBabySize();
         stack.m_85841_(size, size, size);
      }

      return RenderType.m_110473_(this.getTextureLocation(moth));
   }

   public void render(GeoModel model, HellMothEntity moth, float partialTick, RenderType type, PoseStack poseStack, @Nullable MultiBufferSource bufferSource, @Nullable VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
      float newRed = red;
      float newBlue = blue;
      float newGreen = green;
      if (moth.isWet) {
         newRed = red * 0.5F;
         newBlue = blue * 0.5F;
         newGreen = green * 0.5F;
      }

      super.render(model, moth, partialTick, type, poseStack, bufferSource, buffer, packedLight, packedOverlay, newRed, newGreen, newBlue, alpha);
   }
}
