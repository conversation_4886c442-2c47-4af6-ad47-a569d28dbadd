package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;

public class ElephantStampedeArt extends Battewill {
   public double learningCost() {
      return 1000.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks > 0 && heldTicks % 20 == 0) {
         if (SkillHelper.outOfAura(entity, instance)) {
            return false;
         }

         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         if (heldTicks % 40 == 0) {
            instance.addMasteryPoint(entity);
         }

         this.spawnAuraBullets(instance, entity, entity.m_146892_().m_82520_(0.0D, 6.0D, 0.0D));
      }

      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175830_, 1.0D);
      return true;
   }

   private void spawnAuraBullets(ManasSkillInstance instance, LivingEntity entity, Vec3 pos) {
      for(int i = 0; i < 8; ++i) {
         Vec3 arrowPos = entity.m_146892_().m_82549_((new Vec3(0.0D, 1.0D, 0.0D)).m_82535_(((float)(45 * i) - 22.5F) * 0.017453292F).m_82496_(1.5707964F));
         AuraBulletProjectile bullet = new AuraBulletProjectile(entity.m_9236_(), entity);
         bullet.setSpeed(0.75F);
         bullet.m_146884_(arrowPos);
         bullet.shootFromRot(pos.m_82546_(arrowPos).m_82541_());
         bullet.setDamage(instance.isMastered(entity) ? 50.0F : 25.0F);
         bullet.setExplosionRadius(4.0F);
         bullet.setSize(0.5F);
         bullet.setColor(AuraBulletProjectile.AuraColor.PURPLE);
         bullet.setSkill(instance);
         bullet.setApCost(this.magiculeCost(entity, instance));
         entity.m_9236_().m_7967_(bullet);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
