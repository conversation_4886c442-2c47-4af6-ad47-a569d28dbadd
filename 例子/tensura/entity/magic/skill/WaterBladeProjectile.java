package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class WaterBladeProjectile extends TensuraProjectile {
   public WaterBladeProjectile(EntityType<? extends WaterBladeProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public WaterBladeProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.WATER_BLADE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.water_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_6.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_7.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_8.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_9.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_10.png"), new ResourceLocation("tensura", "textures/entity/projectiles/water_blade/water_blade_11.png")};
   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F)) {
         TensuraDamageSource source = DamageSourceHelper.turnTensura(TensuraDamageSources.waterBlade(this, this.m_37282_())).setSkill(this.getSkill()).setMpCost(this.getMpCost());
         if (this.isSpiritAttack()) {
            target.m_6469_(source.setNotTensuraMagic(), this.getDamage());
         } else {
            target.m_6469_(source, this.getDamage());
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11807_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), x, y, z, 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_BUBBLE.get(), x, y, z, 25, 0.08D, 0.08D, 0.08D, 0.15D, false);
   }

   public void flyingParticles() {
   }
}
