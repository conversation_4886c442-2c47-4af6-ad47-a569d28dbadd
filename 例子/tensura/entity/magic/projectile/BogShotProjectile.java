package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class BogShotProjectile extends StoneShotProjectile {
   public BogShotProjectile(EntityType<? extends BogShotProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(2.0F);
   }

   public BogShotProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.BOG_SHOT.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(2.0F);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/bog_shot.png")};
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_215708_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.BOG_BUBBLE.get(), x, y, z, 35, 0.08D, 0.08D, 0.08D, 0.1D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.BOG_EFFECT.get(), x, y, z, 10, 0.08D, 0.08D, 0.08D, 0.1D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.BOG_BUBBLE.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);
      Vec3 random = this.vec3Random().m_82490_(0.009999999776482582D);
      this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.BOG_EFFECT.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, random.f_82479_, random.f_82480_, random.f_82481_);
   }
}
