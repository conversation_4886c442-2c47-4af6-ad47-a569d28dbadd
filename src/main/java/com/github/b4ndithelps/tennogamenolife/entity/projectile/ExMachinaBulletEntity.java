package com.github.b4ndithelps.tennogamenolife.entity.projectile;

import com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.damagesource.IndirectEntityDamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.NetworkHooks;

public class ExMachinaBulletEntity extends Projectile {
    
    private static final double MAX_NO_GRAVITY_DISTANCE = 70.0D;
    private static final float DAMAGE = 15.0F;
    
    private Vec3 startPos;
    private int life = 0;
    private static final int MAX_LIFE = 200;

    public ExMachinaBulletEntity(EntityType<? extends ExMachinaBulletEntity> entityType, Level level) {
        super(entityType, level);
    }

    public ExMachinaBulletEntity(Level level, LivingEntity shooter) {
        super(TenreincarnationEntities.EX_MACHINA_BULLET.get(), level);
        this.setOwner(shooter);
        this.startPos = shooter.position();
        this.setPos(shooter.getX(), shooter.getEyeY() - 0.1D, shooter.getZ());
        this.setInvisible(true);
    }

    @Override
    protected void defineSynchedData() {

    }

    @Override
    public void tick() {
        super.tick();

        this.life++;

        if (this.life > MAX_LIFE) {
            this.discard();
            return;
        }

        HitResult hitResult = ProjectileUtil.getHitResult(this, this::canHitEntity);
        if (hitResult.getType() != HitResult.Type.MISS) {
            this.onHit(hitResult);
            return;
        }

        double distanceFromStart = this.startPos != null ? this.position().distanceTo(this.startPos) : 0.0D;

        if (distanceFromStart > MAX_NO_GRAVITY_DISTANCE) {
            this.setNoGravity(false);
            Vec3 deltaMovement = this.getDeltaMovement();
            this.setDeltaMovement(deltaMovement.x, deltaMovement.y - 0.05D, deltaMovement.z);
        } else {
            this.setNoGravity(true);
        }

        Vec3 deltaMovement = this.getDeltaMovement();
        this.setPos(this.getX() + deltaMovement.x, this.getY() + deltaMovement.y, this.getZ() + deltaMovement.z);

        if (this.level.isClientSide) {
            this.level.addParticle(ParticleTypes.SOUL_FIRE_FLAME,
                this.getX(), this.getY(), this.getZ(),
                0.0D, 0.0D, 0.0D);
            for (int i = 0; i < 5; i++) {
                this.level.addParticle(ParticleTypes.ELECTRIC_SPARK,
                    this.getX() + (this.random.nextDouble() - 0.5D) * 0.3D,
                    this.getY() + (this.random.nextDouble() - 0.5D) * 0.3D,
                    this.getZ() + (this.random.nextDouble() - 0.5D) * 0.3D,
                    -deltaMovement.x * 0.2D,
                    -deltaMovement.y * 0.2D,
                    -deltaMovement.z * 0.2D);
            }
            for (int i = 0; i < 2; i++) {
                this.level.addParticle(ParticleTypes.WARPED_SPORE,
                    this.getX() + (this.random.nextDouble() - 0.5D) * 0.1D,
                    this.getY() + (this.random.nextDouble() - 0.5D) * 0.1D,
                    this.getZ() + (this.random.nextDouble() - 0.5D) * 0.1D,
                    -deltaMovement.x * 0.05D,
                    -deltaMovement.y * 0.05D,
                    -deltaMovement.z * 0.05D);
            }
        }
    }

    @Override
    protected void onHitEntity(EntityHitResult result) {
        Entity target = result.getEntity();
        Entity owner = this.getOwner();
        if (target == owner) {
            return;
        }
        DamageSource damageSource = owner instanceof LivingEntity ?
            new IndirectEntityDamageSource("tennogamenolife.ex_machina_bullet", this, (LivingEntity)owner).setProjectile() :
            DamageSource.MAGIC;
            
        target.hurt(damageSource, DAMAGE);
        this.discard();
    }

    @Override
    protected void onHit(HitResult result) {
        super.onHit(result);
        if (!this.level.isClientSide) {
            this.discard();
        }
    }

    @Override
    public boolean isPickable() {
        return false;
    }

    @Override
    public boolean hurt(DamageSource damageSource, float amount) {
        return false;
    }

    @Override
    public Packet<ClientGamePacketListener> getAddEntityPacket() {
        return (Packet<ClientGamePacketListener>) NetworkHooks.getEntitySpawningPacket(this);
    }
}
