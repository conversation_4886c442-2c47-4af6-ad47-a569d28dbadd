package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.EvilCentipedeBody;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class EvilCentipedeBodyModel extends AnimatedGeoModel<EvilCentipedeBody> {
   public ResourceLocation getModelResource(EvilCentipedeBody object) {
      return object.isEndSegment() ? new ResourceLocation("tensura", "geo/evil_centipede_tail.geo.json") : new ResourceLocation("tensura", "geo/evil_centipede_body.geo.json");
   }

   public ResourceLocation getTextureResource(EvilCentipedeBody instance) {
      return new ResourceLocation("tensura", "textures/entity/evil_centipede/evil_centipede.png");
   }

   public ResourceLocation getAnimationResource(EvilCentipedeBody entity) {
      return entity.isEndSegment() ? new ResourceLocation("tensura", "animations/multipart/evil_centipede_tail.animation.json") : new ResourceLocation("tensura", "animations/multipart/evil_centipede_body.animation.json");
   }

   public void setCustomAnimations(EvilCentipedeBody entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (entity.isChested() == chest.isHidden()) {
         chest.setHidden(!entity.isChested());
      }

   }
}
