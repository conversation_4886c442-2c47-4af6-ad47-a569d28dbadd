package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BeastGnomeEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class BeastGnomeModel extends AnimatedGeoModel<BeastGnomeEntity> {
   public ResourceLocation getModelResource(BeastGnomeEntity object) {
      return new ResourceLocation("tensura", "geo/beast_gnome.geo.json");
   }

   public ResourceLocation getTextureResource(BeastGnomeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/beast_gnome/beast_gnome.png");
   }

   public ResourceLocation getAnimationResource(BeastGnomeEntity moth) {
      return new ResourceLocation("tensura", "animations/beast_gnome.animation.json");
   }

   public void setCustomAnimations(BeastGnomeEntity gnome, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(gnome, instanceId, customPredicate);
      if (!gnome.m_5803_()) {
         if (gnome.getMiscAnimation() != 4 && gnome.getMiscAnimation() != 2 && gnome.getMiscAnimation() != 1) {
            EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
            IBone head = this.getAnimationProcessor().getBone("RotatingHead");
            if (head != null) {
               float pitch = gnome.m_21825_() ? extraData.headPitch - 5.0F : extraData.headPitch;
               head.setRotationX(pitch * 0.017453292F);
               head.setRotationY(extraData.netHeadYaw * 0.017453292F);
            }

         }
      }
   }
}
