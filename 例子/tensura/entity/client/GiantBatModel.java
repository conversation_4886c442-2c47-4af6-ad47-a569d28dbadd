package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantBatEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class GiantBatModel extends AnimatedGeoModel<GiantBatEntity> {
   public ResourceLocation getModelResource(GiantBatEntity object) {
      return new ResourceLocation("tensura", "geo/giant_bat.geo.json");
   }

   public ResourceLocation getTextureResource(GiantBatEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/giant_bat/giant_bat.png");
   }

   public ResourceLocation getAnimationResource(GiantBatEntity bat) {
      return new ResourceLocation("tensura", "animations/giant_bat.animation.json");
   }

   public void setCustomAnimations(GiantBatEntity bat, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bat, instanceId, customPredicate);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (bat.isChested() == chest.isHidden()) {
         chest.setHidden(!bat.isChested());
      }

      if (bat.isHanging() || bat.m_21825_()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            float pitch = bat.m_21825_() ? extraData.headPitch + 5.0F : extraData.headPitch;
            head.setRotationX(pitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }
      }

   }
}
