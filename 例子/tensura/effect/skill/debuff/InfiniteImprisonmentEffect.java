package com.github.manasmods.tensura.effect.skill.debuff;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import java.util.Collections;
import java.util.List;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ForgeMod;

public class InfiniteImprisonmentEffect extends TensuraMobEffect {
   protected static final String INFINITE = "739435c6-547b-11ee-8c99-0242ac120002";

   public InfiniteImprisonmentEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22279_, "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_(Attributes.f_22281_, "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_(Attributes.f_22283_, "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_(Attributes.f_22288_, "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)ForgeMod.REACH_DISTANCE.get(), "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)ManasCoreAttributes.JUMP_POWER.get(), "739435c6-547b-11ee-8c99-0242ac120002", -1.0D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      double MP = (double)(500 * (pAmplifier + 1));
      if (pLivingEntity instanceof Player) {
         Player player = (Player)pLivingEntity;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setMagicule(cap.getMagicule() - MP);
         });
         TensuraPlayerCapability.sync(player);
         if (player.m_150110_().f_35935_) {
            player.m_150110_().f_35935_ = false;
            player.m_6885_();
         }
      } else {
         SkillHelper.reduceEP(pLivingEntity, TensuraEffectsCapability.getEffectSource(pLivingEntity, this), MP, false);
      }

   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 10 == 0;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
