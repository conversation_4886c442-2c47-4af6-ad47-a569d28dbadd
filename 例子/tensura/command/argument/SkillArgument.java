package com.github.manasmods.tensura.command.argument;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.DynamicCommandExceptionType;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;

public class SkillArgument implements ArgumentType<ManasSkill> {
   private static final DynamicCommandExceptionType ERROR_INVALID_VALUE = new DynamicCommandExceptionType((o) -> {
      return Component.m_237115_("tensura.argument.skill.invalid");
   });

   public ManasSkill parse(StringReader reader) throws CommandSyntaxException {
      String remaining = reader.getRemaining();
      String registryName = remaining.contains(" ") ? remaining.split(" ")[0] : remaining;
      reader.setCursor(reader.getString().indexOf(registryName) + registryName.length());
      return (ManasSkill)SkillAPI.getSkillRegistry().getValues().stream().filter((skill) -> {
         return SkillAPI.getSkillRegistry().getKey(skill).toString().equalsIgnoreCase(registryName);
      }).findFirst().orElseThrow(() -> {
         return ERROR_INVALID_VALUE.create(registryName);
      });
   }

   public static SkillArgument skill() {
      return new SkillArgument();
   }

   public static ManasSkill getSkill(CommandContext<CommandSourceStack> context, String name) {
      return (ManasSkill)context.getArgument(name, ManasSkill.class);
   }

   public Collection<String> getExamples() {
      return (Collection)Stream.of(IntrinsicSkills.ABSORB_DISSOLVE.getId(), IntrinsicSkills.BODY_ARMOR.getId()).map(ResourceLocation::toString).collect(Collectors.toList());
   }

   public <S> CompletableFuture<Suggestions> listSuggestions(CommandContext<S> context, SuggestionsBuilder builder) {
      return context.getSource() instanceof SharedSuggestionProvider ? SharedSuggestionProvider.m_82957_(SkillAPI.getSkillRegistry().getValues().stream().filter((manasSkill) -> {
         return manasSkill instanceof TensuraSkill;
      }).map((skill) -> {
         return SkillAPI.getSkillRegistry().getKey(skill);
      }), builder) : Suggestions.empty();
   }

   public static SuggestionProvider<CommandSourceStack> getObtainedSkillSuggestions() {
      return (context, builder) -> {
         return SharedSuggestionProvider.m_82957_(SkillAPI.getSkillRegistry().getValues().stream().filter((manasSkill) -> {
            Player player = ((CommandSourceStack)context.getSource()).m_230896_();
            return player == null ? true : SkillUtils.hasSkill(player, manasSkill);
         }).map((skill) -> {
            return SkillAPI.getSkillRegistry().getKey(skill);
         }), builder);
      };
   }

   public static SuggestionProvider<CommandSourceStack> getObtainedSpatialStorageSuggestions() {
      return (context, builder) -> {
         return SharedSuggestionProvider.m_82957_(SkillAPI.getSkillRegistry().getValues().stream().filter((manasSkill) -> {
            if (!(manasSkill instanceof ISpatialStorage)) {
               return false;
            } else {
               Player player = ((CommandSourceStack)context.getSource()).m_230896_();
               return player == null ? true : SkillUtils.hasSkill(player, manasSkill);
            }
         }).map((skill) -> {
            return SkillAPI.getSkillRegistry().getKey(skill);
         }), builder);
      };
   }
}
