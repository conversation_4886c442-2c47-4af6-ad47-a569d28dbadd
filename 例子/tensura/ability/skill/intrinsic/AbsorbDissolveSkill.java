package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.slime.SlimeRace;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;

public class AbsorbDissolveSkill extends Skill {
   public AbsorbDissolveSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      ItemStack itemStack = entity.m_21205_();
      AbsorbDissolveSkill.Dissolving[] var4 = AbsorbDissolveSkill.Dissolving.values();
      int var5 = var4.length;

      for(int var6 = 0; var6 < var5; ++var6) {
         AbsorbDissolveSkill.Dissolving dissolving = var4[var6];
         if (dissolving.getItem().equals(itemStack.m_41720_())) {
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               if (itemStack.m_41720_().equals(TensuraMaterialItems.SLIME_IN_A_BUCKET.get())) {
                  TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.TRAITOR);
               }

               serverPlayer.m_36246_(Stats.f_12982_.m_12902_(itemStack.m_41720_()));
            }

            if (entity instanceof Player) {
               Player player = (Player)entity;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setMagicule(cap.getMagicule() + (double)dissolving.getMagicule());
                  if (cap.getMagicule() > player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get())) {
                     cap.setMagicule(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()));
                  }

               });
               TensuraPlayerCapability.sync(player);
            }

            if (dissolving.getHeal() > 0.0F) {
               entity.m_5634_(dissolving.getHeal());
            }

            if (itemStack.m_41720_().equals(TensuraMobDropItems.SLIME_CORE.get()) && TensuraPlayerCapability.getRace(entity) instanceof SlimeRace) {
               TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
                  if (cap.getHeight() == 0.0F) {
                     cap.setHeight(1.0F);
                  }

                  if (entity.m_6144_()) {
                     if (cap.getHeight() <= 1.0F) {
                        return;
                     }

                     cap.setHeight(cap.getHeight() - 0.2F);
                     TensuraEffectsCapability.sync(entity);
                  } else {
                     if (cap.getHeight() >= 3.0F) {
                        return;
                     }

                     cap.setHeight(cap.getHeight() + 0.2F);
                     TensuraEffectsCapability.sync(entity);
                  }

               });
            }

            itemStack.m_41774_(1);
            this.addMasteryPoint(instance, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12321_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }

   }

   public static enum Dissolving {
      ADAMANTITE_INGOT((Item)TensuraMaterialItems.ADAMANTITE_INGOT.get(), 100000, 0.0F),
      ARMORSAURUS_SCALE((Item)TensuraMobDropItems.ARMOURSAURUS_SCALE.get(), 50, 0.0F),
      ARMORSAURUS_SHELL((Item)TensuraMobDropItems.ARMOURSAURUS_SHELL.get(), 150, 0.0F),
      CHARYBDIS_SCALE((Item)TensuraMobDropItems.CHARYBDIS_SCALE.get(), 300, 0.0F),
      GEHENNA_SILK((Item)TensuraMobDropItems.GEHENNA_MOTH_SILK.get(), 50, 0.0F),
      ANT_CARAPACE((Item)TensuraMobDropItems.GIANT_ANT_CARAPACE.get(), 120, 0.0F),
      ANT_LEG((Item)TensuraConsumableItems.GIANT_ANT_LEG.get(), 60, 5.0F),
      BAT_WING((Item)TensuraMobDropItems.GIANT_BAT_WING.get(), 400, 2.0F),
      BEAST_HORN((Item)TensuraMobDropItems.BEAST_HORN.get(), 200, 2.0F),
      BLADE_TIGER_TAIL((Item)TensuraMobDropItems.BLADE_TIGER_TAIL.get(), 500, 2.0F),
      HELL_SILK((Item)TensuraMobDropItems.HELL_MOTH_SILK.get(), 10, 0.0F),
      HIHIIROKANE_INGOT((Item)TensuraMaterialItems.HIHIIROKANE_INGOT.get(), 400000, 0.0F),
      HMS_INGOT((Item)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get(), 5000, 0.0F),
      HIGH_CRYSTAL((Item)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), 5000, 0.0F),
      INSECTAR_CARAPACE((Item)TensuraMobDropItems.INSECTAR_CARAPACE.get(), 1200, 0.0F),
      INVISIBLE_FEATHER((Item)TensuraMobDropItems.INVISIBLE_FEATHER.get(), 10, 0.0F),
      SPIDER_CARAPACE((Item)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get(), 300, 0.0F),
      SPIDER_LEG((Item)TensuraConsumableItems.KNIGHT_SPIDER_LEG.get(), 100, 5.0F),
      LMS_INGOT((Item)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get(), 1000, 0.0F),
      LOW_CRYSTAL((Item)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get(), 1000, 0.0F),
      MAGIC_ORE((Item)TensuraMaterialItems.MAGIC_ORE.get(), 5000, 0.0F),
      MAGIC_STONE((Item)TensuraMaterialItems.MAGIC_STONE.get(), 1000, 0.0F),
      MEGALODON_MEAT((Item)TensuraConsumableItems.RAW_MEGALODON_MEAT.get(), 100, 5.0F),
      MITHRIL_INGOT((Item)TensuraMaterialItems.MITHRIL_INGOT.get(), 7500, 0.0F),
      LEATHER_A((Item)TensuraMobDropItems.MONSTER_LEATHER_A.get(), 1000, 0.0F),
      LEATHER_B((Item)TensuraMobDropItems.MONSTER_LEATHER_B.get(), 600, 0.0F),
      LEATHER_C((Item)TensuraMobDropItems.MONSTER_LEATHER_C.get(), 300, 0.0F),
      LEATHER_D((Item)TensuraMobDropItems.MONSTER_LEATHER_D.get(), 100, 0.0F),
      LEATHER_SA((Item)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A.get(), 10000, 0.0F),
      MID_CRYSTAL((Item)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), 2500, 0.0F),
      ORICHALCUM_INGOT((Item)TensuraMaterialItems.ORICHALCUM_INGOT.get(), 7500, 0.0F),
      PEACOCK_FEATHER((Item)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get(), 10, 0.0F),
      PMS_INGOT((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), 10000, 0.0F),
      SERPENT_SCALE((Item)TensuraMobDropItems.SERPENT_SCALE.get(), 100, 0.0F),
      SISSIE_FIN((Item)TensuraConsumableItems.SISSIE_FIN.get(), 3000, 5.0F),
      SPIDER_FANG((Item)TensuraMobDropItems.SPIDER_FANG.get(), 600, 0.0F),
      STEEL_THREAD((Item)TensuraMobDropItems.STEEL_THREAD.get(), 10, 0.0F),
      STICKY_THREAD((Item)TensuraMobDropItems.STICKY_THREAD.get(), 10, 0.0F),
      SLIME_BUCKET((Item)TensuraMaterialItems.SLIME_IN_A_BUCKET.get(), 0, 0.0F),
      SLIME_CORE((Item)TensuraMobDropItems.SLIME_CORE.get(), 50000, 0.0F),
      UNICORN_HORN((Item)TensuraMobDropItems.UNICORN_HORN.get(), 600, 10.0F),
      DEMON_ESSENCE((Item)TensuraMobDropItems.DRAGON_ESSENCE.get(), 3000, 5.0F),
      DRAGON_ESSENCE((Item)TensuraMobDropItems.DRAGON_ESSENCE.get(), 5000, 6.0F),
      ELEMENTAL_ESSENCE((Item)TensuraMobDropItems.DRAGON_ESSENCE.get(), 2000, 4.0F),
      ROYAL_BLOOD((Item)TensuraMobDropItems.ROYAL_BLOOD.get(), 1000, 3.0F),
      ZANE_BLOOD((Item)TensuraMobDropItems.ZANE_BLOOD.get(), 5000, 6.0F);

      private final Item item;
      private final int magicule;
      private final float heal;

      public Item getItem() {
         return this.item;
      }

      public int getMagicule() {
         return this.magicule;
      }

      public float getHeal() {
         return this.heal;
      }

      private Dissolving(Item item, int magicule, float heal) {
         this.item = item;
         this.magicule = magicule;
         this.heal = heal;
      }

      // $FF: synthetic method
      private static AbsorbDissolveSkill.Dissolving[] $values() {
         return new AbsorbDissolveSkill.Dissolving[]{ADAMANTITE_INGOT, ARMORSAURUS_SCALE, ARMORSAURUS_SHELL, CHARYBDIS_SCALE, GEHENNA_SILK, ANT_CARAPACE, ANT_LEG, BAT_WING, BEAST_HORN, BLADE_TIGER_TAIL, HELL_SILK, HIHIIROKANE_INGOT, HMS_INGOT, HIGH_CRYSTAL, INSECTAR_CARAPACE, INVISIBLE_FEATHER, SPIDER_CARAPACE, SPIDER_LEG, LMS_INGOT, LOW_CRYSTAL, MAGIC_ORE, MAGIC_STONE, MEGALODON_MEAT, MITHRIL_INGOT, LEATHER_A, LEATHER_B, LEATHER_C, LEATHER_D, LEATHER_SA, MID_CRYSTAL, ORICHALCUM_INGOT, PEACOCK_FEATHER, PMS_INGOT, SERPENT_SCALE, SISSIE_FIN, SPIDER_FANG, STEEL_THREAD, STICKY_THREAD, SLIME_BUCKET, SLIME_CORE, UNICORN_HORN, DEMON_ESSENCE, DRAGON_ESSENCE, ELEMENTAL_ESSENCE, ROYAL_BLOOD, ZANE_BLOOD};
      }
   }
}
