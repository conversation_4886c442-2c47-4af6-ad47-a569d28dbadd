package com.github.b4ndithelps.tennogamenolife.client.model;

import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import net.minecraft.client.animation.AnimationChannel;
import net.minecraft.client.animation.AnimationDefinition;
import net.minecraft.client.animation.Keyframe;
import net.minecraft.client.animation.KeyframeAnimations;
import net.minecraft.client.model.EntityModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.*;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

/**
 * 休比光环模型
 * 基于Blockbench导出的光环模型，包含动画
 */
public class XiubiHaloModel extends EntityModel<XiubiEntity> {
    
    // 模型层位置
    public static final ModelLayerLocation LAYER_LOCATION = 
        new ModelLayerLocation(new ResourceLocation("tennogamenolife", "xiubi_halo"), "main");
    
    private final ModelPart bone4;
    private final ModelPart bone3;
    private final ModelPart bone;
    private final ModelPart bone2;

    // 光环动画定义
    public static final AnimationDefinition HALO_ANIMATION = AnimationDefinition.Builder.withLength(5.0F).looping()
        .addAnimation("bone", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(5.0F, KeyframeAnimations.degreeVec(360.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone2", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(3.75F, KeyframeAnimations.degreeVec(540.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(5.0F, KeyframeAnimations.degreeVec(897.5F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .addAnimation("bone3", new AnimationChannel(AnimationChannel.Targets.ROTATION, 
            new Keyframe(0.0F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(2.0F, KeyframeAnimations.degreeVec(-90.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(3.5F, KeyframeAnimations.degreeVec(0.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR),
            new Keyframe(5.0F, KeyframeAnimations.degreeVec(360.0F, 0.0F, 0.0F), AnimationChannel.Interpolations.LINEAR)
        ))
        .build();

    public XiubiHaloModel(ModelPart root) {
        this.bone4 = root.getChild("bone4");
        this.bone3 = this.bone4.getChild("bone3");
        this.bone = this.bone4.getChild("bone");
        this.bone2 = this.bone4.getChild("bone2");
    }

    public static LayerDefinition createBodyLayer() {
        MeshDefinition meshdefinition = new MeshDefinition();
        PartDefinition partdefinition = meshdefinition.getRoot();

        PartDefinition bone4 = partdefinition.addOrReplaceChild("bone4", 
            CubeListBuilder.create(), PartPose.offsetAndRotation(0.7F, 19.8F, 1.0F, 0.0F, 3.1416F, 0.0F));

        PartDefinition bone3 = bone4.addOrReplaceChild("bone3", 
            CubeListBuilder.create().texOffs(0, 17).addBox(0.5307F, -2.6955F, -3.25F, 0.0F, 6.0F, 6.0F, new CubeDeformation(0.0F)), 
            PartPose.offsetAndRotation(9.5F, -28.7F, 1.55F, 0.0F, 0.0F, -0.3927F));

        PartDefinition bone = bone4.addOrReplaceChild("bone", 
            CubeListBuilder.create().texOffs(0, 0).addBox(0.2437F, -4.0026F, -4.25F, 0.0F, 8.0F, 9.0F, new CubeDeformation(0.0F)), 
            PartPose.offsetAndRotation(7.0F, -27.75F, 1.25F, 0.0F, 0.0F, -0.3927F));

        PartDefinition bone2 = bone4.addOrReplaceChild("bone2", 
            CubeListBuilder.create().texOffs(12, 17).addBox(0.2833F, -2.445F, -2.5F, 0.0F, 5.0F, 5.0F, new CubeDeformation(0.0F)), 
            PartPose.offsetAndRotation(7.75F, -27.75F, 1.2F, 0.0F, 0.0F, -0.3927F));

        return LayerDefinition.create(meshdefinition, 32, 32);
    }

    @Override
    public void setupAnim(XiubiEntity entity, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch) {
        // 应用光环动画 - 手动实现旋转
        float time = ageInTicks * 0.02F; // 控制动画速度

        // bone: 5秒一圈 (360度)
        this.bone.xRot = (time % 5.0F) * 2.0F * (float)Math.PI / 5.0F;

        // bone2: 更快的旋转
        this.bone2.xRot = (time % 3.75F) * 2.4F * (float)Math.PI / 3.75F;

        // bone3: 复杂的摆动
        float bone3Time = time % 5.0F;
        if (bone3Time < 2.0F) {
            this.bone3.xRot = -((float)Math.PI / 2.0F) * (bone3Time / 2.0F);
        } else if (bone3Time < 3.5F) {
            this.bone3.xRot = -((float)Math.PI / 2.0F) * (1.0F - (bone3Time - 2.0F) / 1.5F);
        } else {
            this.bone3.xRot = 2.0F * (float)Math.PI * (bone3Time - 3.5F) / 1.5F;
        }
    }

    @Override
    public void renderToBuffer(com.mojang.blaze3d.vertex.PoseStack poseStack, com.mojang.blaze3d.vertex.VertexConsumer vertexConsumer, 
                              int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
        bone4.render(poseStack, vertexConsumer, packedLight, packedOverlay, red, green, blue, alpha);
    }
}
