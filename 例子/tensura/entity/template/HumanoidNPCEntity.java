package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearProjectile;
import com.github.manasmods.tensura.event.EnergyRegenerateTickEvent;
import com.github.manasmods.tensura.item.custom.KunaiItem;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.item.templates.SimpleBowItem;
import com.github.manasmods.tensura.item.templates.SimpleCrossbowItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleSpearItem;
import com.github.manasmods.tensura.menu.HumanoidNPCMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundNPCScreenOpenPacket;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Objects;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ItemParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RangedAttackGoal;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.monster.CrossbowAttackMob;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.entity.projectile.ThrownTrident;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.ProjectileWeaponItem;
import net.minecraft.world.item.TridentItem;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.ToolActions;
import net.minecraftforge.event.entity.player.PlayerContainerEvent.Open;
import net.minecraftforge.network.PacketDistributor;

public class HumanoidNPCEntity extends TensuraTamableEntity implements HasCustomInventoryScreen, CrossbowAttackMob {
   private static final EntityDataAccessor<Boolean> CHARGING_CROSSBOW;
   public SimpleContainer inventory;

   public HumanoidNPCEntity(EntityType<? extends HumanoidNPCEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21553_(true);
      this.initInventory();
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(CHARGING_CROSSBOW, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("ChargingCrossbow", this.isChargingCrossbow());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(CHARGING_CROSSBOW, compound.m_128471_("ChargingCrossbow"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   protected boolean m_8028_() {
      return false;
   }

   protected boolean shouldHeal() {
      if (this.m_5803_()) {
         return false;
      } else {
         double maxHP = (double)this.m_21233_() - TensuraEffectsCapability.getSeverance(this);
         if (this.m_21660_() && maxHP > (double)(this.m_21233_() / 2.0F)) {
            return this.m_21223_() <= this.m_21233_() / 2.0F;
         } else {
            return (double)this.m_21223_() < maxHP && !this.shouldSwim();
         }
      }
   }

   public boolean shouldSwim() {
      return this.isInFluidType() && !this.m_20096_() && !this.m_21660_();
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(18) {
         public boolean m_6542_(Player player) {
            return HumanoidNPCEntity.this.m_6084_() && !HumanoidNPCEntity.this.f_19817_;
         }

         public void m_19189_(ItemStack pStack) {
            for(int i = 0; i < this.m_6643_(); ++i) {
               ItemStack itemstack = this.m_8020_(i);
               if (itemstack.m_41619_() && HumanoidNPCMenu.canPlaceInArmorSlot(i, pStack, HumanoidNPCEntity.this)) {
                  this.m_6836_(i, pStack.m_41777_());
                  pStack.m_41764_(0);
                  return;
               }
            }

         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.inventory != null && this.m_21824_()) {
         if (this.m_21830_(pPlayer)) {
            if (pPlayer instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)pPlayer;
               serverPlayer.m_6915_();
               serverPlayer.m_9217_();
               double EP = TensuraEPCapability.getEP(this);
               TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
                  return serverPlayer;
               }), new ClientboundNPCScreenOpenPacket(serverPlayer.f_8940_, this.inventory.m_6643_(), this.m_19879_(), EP));
               serverPlayer.f_36096_ = new HumanoidNPCMenu(serverPlayer.f_8940_, serverPlayer.m_150109_(), this.inventory, this, EP);
               serverPlayer.m_143399_(serverPlayer.f_36096_);
               MinecraftForge.EVENT_BUS.post(new Open(serverPlayer, serverPlayer.f_36096_));
            }
         }
      }
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.m_21824_()) {
         if (!this.f_19853_.f_46443_) {
            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
      }
   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (this.m_21824_() && !this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!this.f_19853_.f_46443_) {
            if (this.m_21830_(player)) {
               if (player.m_36341_()) {
                  this.commanding(player);
               } else {
                  this.m_213583_(player);
               }

               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            } else {
               return super.m_6071_(player, hand);
            }
         } else {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         }
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_() && this.f_19797_ % 10 == 0) {
         MinecraftForge.EVENT_BUS.post(new EnergyRegenerateTickEvent(this));
      }

   }

   public boolean canEquipSlots(EquipmentSlot slot) {
      return true;
   }

   public boolean canEquipSlots(EquipmentSlot slot, ItemStack stack) {
      if (!slot.equals(EquipmentSlot.OFFHAND)) {
         return this.canEquipSlots(slot);
      } else {
         ItemStack currentStack = this.m_6844_(slot);
         return !this.m_6898_(currentStack) || this.m_21211_() != currentStack;
      }
   }

   public boolean m_7243_(ItemStack pStack) {
      if (!this.m_6084_()) {
         return false;
      } else {
         EquipmentSlot[] var2 = EquipmentSlot.values();
         int var3 = var2.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            EquipmentSlot slot = var2[var4];
            if (this.canEquipSlots(slot, pStack) && HumanoidNPCMenu.canPlaceInArmorSlot(slot, pStack, this) && this.m_7808_(pStack, this.m_6844_(slot))) {
               return true;
            }
         }

         return this.inventory.m_216874_((stack) -> {
            return stack.m_41720_() == pStack.m_41720_();
         });
      }
   }

   protected void m_7581_(ItemEntity pItemEntity) {
      ItemStack itemstack = pItemEntity.m_32055_();
      if (this.m_21540_(itemstack)) {
         this.m_21053_(pItemEntity);
         this.m_7938_(pItemEntity, itemstack.m_41613_());
         pItemEntity.m_146870_();
      } else if (this.inventory.m_19183_(itemstack)) {
         this.inventory.m_19173_(itemstack);
         this.inventory.m_6596_();
         EquipmentSlot[] var3 = EquipmentSlot.values();
         int var4 = var3.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            EquipmentSlot slot = var3[var5];
            this.m_8061_(slot, this.inventory.m_8020_(HumanoidNPCMenu.getEquipmentSlotId(slot)));
         }

         this.m_21053_(pItemEntity);
         this.m_7938_(pItemEntity, itemstack.m_41613_());
         pItemEntity.m_146870_();
      }

   }

   public boolean m_21540_(ItemStack pStack) {
      EquipmentSlot slot = HumanoidNPCMenu.getEquipmentSlotForStack(pStack);
      ItemStack stack = this.inventory.m_8020_(HumanoidNPCMenu.getEquipmentSlotId(slot));
      if (this.m_7808_(pStack, stack) && this.m_7252_(pStack)) {
         double d0 = this.m_21824_() && this.inventory.m_8020_(HumanoidNPCMenu.getEquipmentSlotId(slot)) == stack ? 2.0D : (double)this.m_21519_(slot);
         if (!stack.m_41619_() && (double)Math.max(this.f_19796_.m_188501_() - 0.1F, 0.0F) < d0) {
            this.m_19983_(stack);
         }

         this.m_21468_(slot, pStack);
         this.inventory.m_6836_(HumanoidNPCMenu.getEquipmentSlotId(slot), pStack);
         this.inventory.m_6596_();
         return true;
      } else {
         return false;
      }
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      return this.m_21824_() ? 0.0F : super.m_21519_(pSlot);
   }

   protected void m_142642_(DamageSource pDamageSource, float pDamageAmount) {
      this.hurtArmor(pDamageSource, pDamageAmount, Inventory.f_150069_);
   }

   protected void m_6472_(DamageSource pDamageSource, float pDamage) {
      this.hurtArmor(pDamageSource, pDamage, Inventory.f_150068_);
   }

   public void hurtArmor(DamageSource pSource, float pDamage, int[] slots) {
      if (!(pDamage <= 0.0F)) {
         pDamage /= 4.0F;
         if (pDamage < 1.0F) {
            pDamage = 1.0F;
         }

         int[] var4 = slots;
         int var5 = slots.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            int i = var4[var6];
            ItemStack itemstack = this.inventory.m_8020_(i);
            if ((!pSource.m_19384_() || !itemstack.m_41720_().m_41475_()) && itemstack.m_41720_() instanceof ArmorItem) {
               itemstack.m_41622_((int)pDamage, this, (entity) -> {
               });
            }
         }

      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && pEntity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pEntity;
         ItemStack itemStack = this.inventory.m_8020_(4);
         itemStack.m_41720_().m_7579_(itemStack, living, this);
         this.inventory.m_6596_();
      }

      return flag;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         Entity var4 = pSource.m_7640_();
         if (var4 instanceof LivingEntity) {
            LivingEntity attacker = (LivingEntity)var4;
            if (DamageSourceHelper.isPhysicalAttack(pSource)) {
               ItemStack stack = this.m_6844_(EquipmentSlot.OFFHAND);
               if (stack.canPerformAction(ToolActions.SHIELD_BLOCK) && (double)this.f_19796_.m_188501_() <= 0.3D) {
                  this.hurtShield(this.inventory.m_8020_(5), pAmount);
                  this.inventory.m_6596_();
                  return false;
               }
            }
         }

         return super.m_6469_(pSource, pAmount);
      }
   }

   protected void hurtShield(ItemStack stack, float pAmount) {
      if (pAmount >= 3.0F) {
         stack.m_41622_(1 + Mth.m_14143_(pAmount), this, (living) -> {
            living.m_21166_(EquipmentSlot.OFFHAND);
         });
      }

      this.m_5496_(SoundEvents.f_12346_, 1.0F, 1.0F);
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      return m_21412_(pSlot, pChance);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      if (!(pRandom.m_188501_() >= 0.2F)) {
         int i = pRandom.m_188503_(2);
         if (pRandom.m_188501_() < 0.095F) {
            ++i;
         }

         if (pRandom.m_188501_() < 0.095F) {
            ++i;
         }

         if (pRandom.m_188501_() < 0.095F) {
            ++i;
         }

         boolean flag = true;
         EquipmentSlot[] var5 = EquipmentSlot.values();
         int var6 = var5.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            EquipmentSlot equipmentslot = var5[var7];
            if (equipmentslot.m_20743_() == Type.ARMOR) {
               ItemStack itemstack = this.m_6844_(equipmentslot);
               if (!flag && (double)pRandom.m_188501_() < 0.25D) {
                  break;
               }

               flag = false;
               if (itemstack.m_41619_()) {
                  Item item = this.getEquipmentForArmorSlot(equipmentslot, i);
                  if (item != null) {
                     ItemStack stack = new ItemStack(item);
                     this.m_8061_(equipmentslot, stack);
                     this.inventory.m_6836_(3 - equipmentslot.m_20749_(), stack);
                     this.inventory.m_6596_();
                  }
               }
            }
         }

      }
   }

   public boolean usingRangedWeapon() {
      ItemStack weapon = this.m_21205_();
      LivingEntity target = this.m_5448_();
      if (target == null) {
         return false;
      } else if (this.isSpearType(weapon)) {
         if (weapon.getEnchantmentLevel(Enchantments.f_44957_) > 0) {
            return false;
         } else {
            return (double)target.m_20270_(target) > this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 3.0D;
         }
      } else {
         return weapon.m_41720_() instanceof ProjectileWeaponItem;
      }
   }

   protected boolean isSpearType(ItemStack weapon) {
      return weapon.m_41720_() instanceof KunaiItem || weapon.m_41720_() instanceof SimpleSpearItem || weapon.m_41720_() instanceof TridentItem;
   }

   public void setMiscAnimation(int tick) {
   }

   public boolean isChargingCrossbow() {
      return (Boolean)this.f_19804_.m_135370_(CHARGING_CROSSBOW);
   }

   public void m_6136_(boolean pIsCharging) {
      this.f_19804_.m_135381_(CHARGING_CROSSBOW, pIsCharging);
   }

   public void m_5847_() {
      this.f_20891_ = 0;
   }

   public ItemStack m_6298_(ItemStack pShootable) {
      Item var3 = pShootable.m_41720_();
      if (var3 instanceof ProjectileWeaponItem) {
         ProjectileWeaponItem weaponItem = (ProjectileWeaponItem)var3;
         Predicate predicate = weaponItem.m_6442_();
         ItemStack itemstack = ProjectileWeaponItem.m_43010_(this, predicate);
         if (!itemstack.m_41619_()) {
            return ForgeHooks.getProjectile(this, pShootable, itemstack);
         } else {
            predicate = weaponItem.m_6437_();

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               ItemStack stack = this.inventory.m_8020_(i);
               if (predicate.test(stack)) {
                  return ForgeHooks.getProjectile(this, pShootable, stack);
               }
            }

            if (!this.m_21824_()) {
               return ForgeHooks.getProjectile(this, pShootable, new ItemStack(Items.f_42412_));
            } else {
               return ForgeHooks.getProjectile(this, pShootable, ItemStack.f_41583_);
            }
         }
      } else {
         return ItemStack.f_41583_;
      }
   }

   public void m_6504_(LivingEntity pTarget, float pDistanceFactor) {
      ItemStack weapon = this.m_21205_();
      Item var5 = weapon.m_41720_();
      if (var5 instanceof BowItem) {
         BowItem bow = (BowItem)var5;
         this.performBowAttack(weapon, bow, pTarget, pDistanceFactor);
      } else if (weapon.m_41720_() instanceof CrossbowItem) {
         this.m_32336_(this, 1.6F);
      } else {
         this.spearThrowAttack(pTarget, weapon);
      }

   }

   protected void performBowAttack(ItemStack weapon, BowItem bow, LivingEntity pTarget, float distance) {
      ItemStack itemstack = this.m_6298_(weapon);
      if (!itemstack.m_41619_()) {
         AbstractArrow arrow = ProjectileUtil.m_37300_(this, itemstack, distance);
         arrow = bow.customArrow(arrow);
         double d0 = pTarget.m_20185_() - this.m_20185_();
         double d1 = pTarget.m_20227_(0.3333333333333333D) - arrow.m_20186_();
         double d2 = pTarget.m_20189_() - this.m_20189_();
         double d3 = Math.sqrt(d0 * d0 + d2 * d2);
         if (bow instanceof SimpleBowItem) {
            SimpleBowItem tensuraBow = (SimpleBowItem)bow;
            arrow.m_36781_(tensuraBow.getBaseDamage());
         }

         arrow.m_6686_(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, (float)(14 - this.f_19853_.m_46791_().m_19028_() * 4));
         this.m_5496_(SoundEvents.f_12382_, 1.0F, 1.0F / (this.m_217043_().m_188501_() * 0.4F + 0.8F));
         this.f_19853_.m_7967_(arrow);
         if (weapon.getEnchantmentLevel(Enchantments.f_44952_) <= 0) {
            itemstack.m_41774_(1);
         }

         this.inventory.m_8020_(4).m_41622_(1, this, (living) -> {
            living.m_21166_(EquipmentSlot.MAINHAND);
         });
         this.inventory.m_6596_();
      }
   }

   protected boolean spearThrowAttack(LivingEntity pTarget, ItemStack weapon) {
      ItemStack copy;
      double d0;
      double d1;
      double d2;
      double d3;
      if (weapon.m_41720_() instanceof TridentItem) {
         copy = weapon.m_41777_();
         EngravingEnchantment.engrave(copy, Enchantments.f_44955_, 0);
         ThrownTrident trident = new ThrownTrident(this.f_19853_, this, copy);
         d0 = pTarget.m_20185_() - this.m_20185_();
         d1 = pTarget.m_20227_(0.3333333333333333D) - trident.m_20186_();
         d2 = pTarget.m_20189_() - this.m_20189_();
         d3 = Math.sqrt(d0 * d0 + d2 * d2);
         trident.m_6686_(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, (float)(14 - this.f_19853_.m_46791_().m_19028_() * 4));
         this.m_5496_(SoundEvents.f_11821_, 1.0F, 1.0F / (this.m_217043_().m_188501_() * 0.4F + 0.8F));
         this.f_19853_.m_7967_(trident);
         return true;
      } else if (weapon.m_41720_() instanceof SimpleSpearItem) {
         copy = weapon.m_41777_();
         EngravingEnchantment.engrave(copy, Enchantments.f_44955_, 0);
         SpearProjectile spear = new SpearProjectile(this.f_19853_, this, copy, true);
         d0 = pTarget.m_20185_() - this.m_20185_();
         d1 = pTarget.m_20227_(0.3333333333333333D) - spear.m_20186_();
         d2 = pTarget.m_20189_() - this.m_20189_();
         d3 = Math.sqrt(d0 * d0 + d2 * d2);
         spear.m_6686_(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, (float)(14 - this.f_19853_.m_46791_().m_19028_() * 4));
         this.m_5496_(SoundEvents.f_11821_, 1.0F, 1.0F / (this.m_217043_().m_188501_() * 0.4F + 0.8F));
         this.f_19853_.m_7967_(spear);
         return true;
      } else if (weapon.m_41720_() instanceof KunaiItem) {
         copy = weapon.m_41777_();
         EngravingEnchantment.engrave(copy, Enchantments.f_44955_, 0);
         KunaiProjectile kunai = new KunaiProjectile(this.f_19853_, this, copy, true);
         d0 = pTarget.m_20185_() - this.m_20185_();
         d1 = pTarget.m_20227_(0.3333333333333333D) - kunai.m_20186_();
         d2 = pTarget.m_20189_() - this.m_20189_();
         d3 = Math.sqrt(d0 * d0 + d2 * d2);
         kunai.m_6686_(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, (float)(14 - this.f_19853_.m_46791_().m_19028_() * 4));
         this.m_5496_(SoundEvents.f_11821_, 1.0F, 1.0F / (this.m_217043_().m_188501_() * 0.4F + 0.8F));
         this.f_19853_.m_7967_(kunai);
         return true;
      } else {
         return false;
      }
   }

   public void m_5811_(LivingEntity pTarget, ItemStack pCrossbowStack, Projectile pProjectile, float pProjectileAngle) {
      Item var6 = pCrossbowStack.m_41720_();
      if (var6 instanceof CrossbowItem) {
         CrossbowItem item = (CrossbowItem)var6;
         float power = CrossbowItem.m_40871_(pCrossbowStack, Items.f_42688_) ? 1.6F : 3.15F;
         if (item instanceof SimpleCrossbowItem) {
            SimpleCrossbowItem crossbow = (SimpleCrossbowItem)item;
            power = crossbow.m_40945_(pCrossbowStack);
         }

         this.m_32322_(this, pTarget, pProjectile, pProjectileAngle, power);
      }

   }

   static {
      CHARGING_CROSSBOW = SynchedEntityData.m_135353_(HumanoidNPCEntity.class, EntityDataSerializers.f_135035_);
   }

   public static class SpearTypeAttackGoal extends RangedAttackGoal {
      private final HumanoidNPCEntity npc;

      public SpearTypeAttackGoal(HumanoidNPCEntity pRangedAttackMob, double pSpeedModifier, int pAttackInterval, float pAttackRadius) {
         super(pRangedAttackMob, pSpeedModifier, pAttackInterval, pAttackRadius);
         this.npc = pRangedAttackMob;
      }

      public boolean m_8036_() {
         ItemStack weapon = this.npc.m_21205_();
         if (!super.m_8036_()) {
            return false;
         } else {
            LivingEntity target = this.npc.m_5448_();
            if (target == null) {
               return false;
            } else if (this.npc.isSpearType(weapon)) {
               if (weapon.getEnchantmentLevel(Enchantments.f_44957_) > 0) {
                  return false;
               } else {
                  return !this.closeEnoughForMelee(target);
               }
            } else {
               return false;
            }
         }
      }

      protected boolean closeEnoughForMelee(LivingEntity target) {
         return (double)target.m_20270_(this.npc) <= this.getAttackReachSqr(target);
      }

      public void m_8056_() {
         super.m_8056_();
         this.npc.m_21561_(true);
         this.npc.m_6672_(InteractionHand.MAIN_HAND);
      }

      public void m_8041_() {
         super.m_8041_();
         this.npc.m_5810_();
         this.npc.m_21561_(false);
         ItemStack weapon = this.npc.inventory.m_8020_(4);
         weapon.m_41622_(3, this.npc, (living) -> {
            living.m_21166_(EquipmentSlot.MAINHAND);
         });
         this.npc.inventory.m_6596_();
      }

      protected double getAttackReachSqr(LivingEntity pAttackTarget) {
         double attackRange = this.npc.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
         return (double)(this.npc.m_20205_() * 2.0F * this.npc.m_20205_() * 2.0F + pAttackTarget.m_20205_()) + attackRange * attackRange;
      }
   }

   public class NPCMeleeAttackGoal extends MeleeAttackGoal {
      public NPCMeleeAttackGoal(PathfinderMob pMob, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(pMob, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
      }

      public boolean m_8036_() {
         if (HumanoidNPCEntity.this.m_21827_()) {
            return false;
         } else {
            return HumanoidNPCEntity.this.usingRangedWeapon() ? false : super.m_8036_();
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         double attackRange = HumanoidNPCEntity.this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
         return super.m_6639_(pAttackTarget) + attackRange * attackRange;
      }
   }

   public static class EatingItemGoal extends Goal {
      private final HumanoidNPCEntity mob;
      private final Predicate<? super ItemStack> canEatItem;
      private final Predicate<? super HumanoidNPCEntity> canUseSelector;
      @Nullable
      private final SoundEvent finishUsingSound;
      private final float healAmount;
      private int chosenSlot;
      private int eatingTick;

      public EatingItemGoal(HumanoidNPCEntity pMob, Predicate<? super ItemStack> pCanEat, @Nullable SoundEvent pFinishUsingSound, Predicate<? super HumanoidNPCEntity> pCanUseSelector, float healAmount) {
         this.eatingTick = 0;
         this.mob = pMob;
         this.canEatItem = pCanEat;
         this.finishUsingSound = pFinishUsingSound;
         this.canUseSelector = pCanUseSelector;
         this.healAmount = healAmount;
      }

      public EatingItemGoal(HumanoidNPCEntity pMob, Predicate<? super HumanoidNPCEntity> pCanUseSelector, float healAmount) {
         Objects.requireNonNull(pMob);
         this(pMob, pMob::m_6898_, SoundEvents.f_11912_, pCanUseSelector, healAmount);
      }

      public boolean m_6767_() {
         return false;
      }

      public boolean m_8036_() {
         if (!this.mob.m_6084_()) {
            return false;
         } else if (!this.canUseSelector.test(this.mob)) {
            return false;
         } else {
            for(int slot = 0; slot < this.mob.inventory.m_6643_(); ++slot) {
               if (this.canEatItem.test(this.mob.inventory.m_8020_(slot))) {
                  this.chosenSlot = slot;
                  return true;
               }
            }

            return false;
         }
      }

      public boolean m_8045_() {
         if (this.mob.m_6117_()) {
            return true;
         } else if (this.mob.inventory.m_8020_(5).getFoodProperties(this.mob) == null) {
            return this.eatingTick-- > 0;
         } else {
            return false;
         }
      }

      public void m_8056_() {
         if (this.chosenSlot != 5) {
            ItemStack oldHand = this.mob.inventory.m_8020_(5).m_41777_();
            ItemStack chosenStack = this.mob.inventory.m_8020_(this.chosenSlot);
            this.mob.inventory.m_6836_(5, chosenStack);
            this.mob.m_8061_(EquipmentSlot.OFFHAND, chosenStack);
            this.mob.inventory.m_6836_(this.chosenSlot, oldHand);
            this.mob.inventory.m_6596_();
         }

         if (this.mob.m_5803_()) {
            this.mob.setSleeping(false);
         }

         this.mob.m_6672_(InteractionHand.OFF_HAND);
         if (this.mob.inventory.m_8020_(5).getFoodProperties(this.mob) == null) {
            this.eatingTick = 20;
         }

      }

      public void m_8037_() {
         if (this.eatingTick > 0) {
            ItemStack stack = this.mob.inventory.m_8020_(5);
            if (stack.getFoodProperties(this.mob) == null) {
               this.spawnItemParticles(stack, 5);
               this.mob.m_5496_(this.mob.m_7866_(stack), 0.5F + 0.5F * (float)this.mob.f_19796_.m_188503_(2), (this.mob.f_19796_.m_188501_() - this.mob.f_19796_.m_188501_()) * 0.2F + 1.0F);
            }
         }
      }

      public void m_8041_() {
         if (this.eatingTick <= 0) {
            ItemStack eaten;
            if (this.chosenSlot != 5) {
               eaten = this.mob.inventory.m_8020_(5).m_41777_();
               Item var3 = eaten.m_41720_();
               if (var3 instanceof HealingPotionItem) {
                  HealingPotionItem potion = (HealingPotionItem)var3;
                  potion.healEntity(this.mob, 1.0F);
                  eaten.m_41774_(1);
               }

               ItemStack chosenStack = this.mob.inventory.m_8020_(this.chosenSlot);
               this.mob.inventory.m_6836_(5, chosenStack);
               this.mob.m_8061_(EquipmentSlot.OFFHAND, chosenStack);
               this.mob.inventory.m_6836_(this.chosenSlot, eaten);
            }

            if (this.mob.inventory.m_8020_(this.chosenSlot).getFoodProperties(this.mob) == null) {
               eaten = this.mob.inventory.m_8020_(this.chosenSlot).m_41777_();
               this.spawnItemParticles(eaten, 16);
               eaten.m_41774_(1);
               this.mob.inventory.m_6836_(this.chosenSlot, eaten);
            }

            this.mob.inventory.m_6596_();
            this.mob.m_5634_(this.healAmount);
            if (this.finishUsingSound != null) {
               this.mob.m_5496_(this.finishUsingSound, 1.0F, this.mob.m_217043_().m_188501_() * 0.2F + 0.9F);
            }

         }
      }

      private void spawnItemParticles(ItemStack pStack, int pAmount) {
         for(int i = 0; i < pAmount; ++i) {
            Vec3 vec3 = new Vec3(((double)this.mob.f_19796_.m_188501_() - 0.5D) * 0.1D, Math.random() * 0.1D + 0.1D, 0.0D);
            vec3 = vec3.m_82496_(-this.mob.m_146909_() * 0.017453292F);
            vec3 = vec3.m_82524_(-this.mob.m_146908_() * 0.017453292F);
            double d0 = (double)(-this.mob.f_19796_.m_188501_()) * 0.6D - 0.3D;
            Vec3 vec31 = new Vec3(((double)this.mob.f_19796_.m_188501_() - 0.5D) * 0.3D, d0, 0.6D);
            vec31 = vec31.m_82496_(-this.mob.m_146909_() * 0.017453292F);
            vec31 = vec31.m_82524_(-this.mob.m_146908_() * 0.017453292F);
            vec31 = vec31.m_82520_(this.mob.m_20185_(), this.mob.m_20188_(), this.mob.m_20189_());
            ((ServerLevel)this.mob.f_19853_).m_8767_(new ItemParticleOption(ParticleTypes.f_123752_, pStack), vec31.f_82479_, vec31.f_82480_, vec31.f_82481_, 1, vec3.f_82479_, vec3.f_82480_ + 0.05D, vec3.f_82481_, 0.0D);
         }

      }
   }
}
