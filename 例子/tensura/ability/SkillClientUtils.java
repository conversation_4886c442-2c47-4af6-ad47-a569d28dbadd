package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.monster.Enemy;
import net.minecraft.world.entity.player.Player;

public class SkillClientUtils {
   public static boolean isSkillHeldClient(LivingEntity entity, ManasSkill skill) {
      if (!SkillUtils.hasSkill(entity, skill)) {
         return false;
      } else if (TensuraSkillCapability.isSkillInSlot(entity, skill, 0)) {
         return TensuraKeybinds.ACTIVATE_SLOT_1.m_90857_();
      } else if (TensuraSkillCapability.isSkillInSlot(entity, skill, 1)) {
         return TensuraKeybinds.ACTIVATE_SLOT_2.m_90857_();
      } else {
         return TensuraSkillCapability.isSkillInSlot(entity, skill, 2) ? TensuraKeybinds.ACTIVATE_SLOT_3.m_90857_() : false;
      }
   }

   public static double zoomValue(LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      double zoom = 0.0D;
      TensuraSkill dragonEye = (TensuraSkill)IntrinsicSkills.DRAGON_EYE.get();
      if (storage.getSkill(dragonEye).isPresent() && isSkillHeldClient(entity, dragonEye)) {
         zoom += ((ManasSkillInstance)storage.getSkill(dragonEye).get()).getOrCreateTag().m_128459_("range");
      }

      TensuraSkill farSight = (TensuraSkill)CommonSkills.FARSIGHT.get();
      if (storage.getSkill(farSight).isPresent() && isSkillHeldClient(entity, farSight)) {
         zoom += ((ManasSkillInstance)storage.getSkill(farSight).get()).getOrCreateTag().m_128459_("range");
      }

      return zoom;
   }

   public static int getGlowColor(Player player, Entity entity) {
      if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.CAN_STAY_INVISIBLE) && entity.m_20177_(player)) {
         return 0;
      } else {
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            if (living.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get())) {
               return 0;
            }
         }

         ChatFormatting chatFormatting = ChatFormatting.RESET;
         double distance = (double)player.m_20270_(entity);
         MobEffectInstance allSeeing = player.m_21124_((MobEffect)TensuraMobEffects.ALL_SEEING.get());
         if (allSeeing != null) {
            distance -= (double)(10 * (allSeeing.m_19564_() + 1));
         }

         MobEffectInstance presenceSense = player.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get());
         if (presenceSense != null && distance <= 30.0D) {
            if (presenceSense.m_19564_() >= 3) {
               chatFormatting = ChatFormatting.DARK_BLUE;
            } else if (entity instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)entity;
               if (living.m_6084_()) {
                  if (presenceSense.m_19564_() == 2) {
                     chatFormatting = ChatFormatting.BLUE;
                  } else if (presenceSense.m_19564_() == 1) {
                     chatFormatting = ChatFormatting.AQUA;
                  } else {
                     chatFormatting = ChatFormatting.DARK_AQUA;
                  }
               }
            }
         }

         if (player.m_21124_((MobEffect)TensuraMobEffects.HEAT_SENSE.get()) != null && distance <= 20.0D && !player.m_9236_().m_6042_().f_63857_()) {
            if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.COLD_SOURCE)) {
               chatFormatting = ChatFormatting.DARK_AQUA;
            } else if (entity instanceof LivingEntity && !entity.m_6095_().m_204039_(TensuraTags.EntityTypes.COLD_BLOODED)) {
               chatFormatting = ChatFormatting.RED;
            }
         }

         MobEffectInstance dangerDetection = player.m_21124_((MobEffect)TensuraMobEffects.DANGER_DETECTION.get());
         if (dangerDetection != null && dangerDetection.m_19564_() >= 1 && distance <= 50.0D) {
            if (entity instanceof NeutralMob) {
               chatFormatting = ChatFormatting.YELLOW;
            } else if (entity instanceof Enemy) {
               chatFormatting = ChatFormatting.DARK_RED;
            }
         }

         MobEffectInstance recon = player.m_21124_((MobEffect)TensuraMobEffects.REAPER_RECON.get());
         if (recon != null && distance <= (double)(30 + 20 * recon.m_19564_())) {
            chatFormatting = ChatFormatting.GRAY;
         }

         MobEffectInstance auditorySense = player.m_21124_((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get());
         if (auditorySense != null && distance <= (double)(20 + 20 * auditorySense.m_19564_()) && !SkillUtils.canBlockSoundDetect(entity) && !entity.m_20067_()) {
            chatFormatting = ChatFormatting.GREEN;
         }

         return chatFormatting.m_126665_() != null ? chatFormatting.m_126665_() : 0;
      }
   }

   public static boolean shouldCancelFireOverlay(LivingEntity living) {
      if (MobEffectHelper.hasTrueInvisibility(living)) {
         return true;
      } else if (SkillUtils.isSkillToggled(living, (ManasSkill)ResistanceSkills.FLAME_ATTACK_NULLIFICATION.get())) {
         return true;
      } else {
         return SkillUtils.isSkillToggled(living, (ManasSkill)ResistanceSkills.HEAT_NULLIFICATION.get()) ? true : SkillUtils.isSkillToggled(living, (ManasSkill)ResistanceSkills.THERMAL_FLUCTUATION_NULLIFICATION.get());
      }
   }
}
