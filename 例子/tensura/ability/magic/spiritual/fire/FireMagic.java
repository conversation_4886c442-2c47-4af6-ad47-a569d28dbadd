package com.github.manasmods.tensura.ability.magic.spiritual.fire;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.world.TensuraGameRules;
import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.BaseFireBlock;
import net.minecraft.world.level.block.CampfireBlock;
import net.minecraft.world.level.block.CandleBlock;
import net.minecraft.world.level.block.CandleCakeBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class FireMagic extends SpiritualMagic {
   public FireMagic() {
      super(MagicElemental.FLAME, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         Level level = entity.m_9236_();
         if (TensuraGameRules.canSkillGrief(level)) {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 6.0D);
            if (result.m_6662_() == Type.BLOCK) {
               BlockPos pos = result.m_82425_();
               BlockState state = level.m_8055_(pos);
               if (!CampfireBlock.m_51321_(state) && !CandleBlock.m_152845_(state) && !CandleCakeBlock.m_152910_(state)) {
                  BlockPos relative = pos.m_121945_(result.m_82434_());
                  if (BaseFireBlock.m_49255_(level, relative, entity.m_6350_())) {
                     if (SkillHelper.outOfMagicule(entity, instance)) {
                        return;
                     }

                     this.addMasteryPoint(instance, entity);
                     SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, relative);
                     if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                        return;
                     }

                     BlockState relativeState = BaseFireBlock.m_49245_(level, relative);
                     level.m_7731_(relative, relativeState, 11);
                     level.m_142346_(entity, GameEvent.f_157797_, pos);
                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, relative));
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 1.0F, 1.0F);
                  }
               } else {
                  if (SkillHelper.outOfMagicule(entity, instance)) {
                     return;
                  }

                  this.addMasteryPoint(instance, entity);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  level.m_7731_(pos, (BlockState)state.m_61124_(BlockStateProperties.f_61443_, Boolean.TRUE), 11);
                  level.m_142346_(entity, GameEvent.f_157792_, pos);
                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               }
            }

         }
      }
   }
}
