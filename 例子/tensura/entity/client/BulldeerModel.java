package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BulldeerEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class BulldeerModel extends AnimatedGeoModel<BulldeerEntity> {
   public ResourceLocation getModelResource(BulldeerEntity object) {
      return new ResourceLocation("tensura", "geo/bulldeer.geo.json");
   }

   public ResourceLocation getTextureResource(BulldeerEntity object) {
      return object.m_6162_() ? new ResourceLocation("tensura", "textures/entity/bulldeer/bulldeer_baby.png") : new ResourceLocation("tensura", "textures/entity/bulldeer/bulldeer.png");
   }

   public ResourceLocation getAnimationResource(BulldeerEntity entity) {
      return new ResourceLocation("tensura", "animations/bulldeer.animation.json");
   }

   public void setCustomAnimations(BulldeerEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      if (entity.getMiscAnimation() == 0) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
