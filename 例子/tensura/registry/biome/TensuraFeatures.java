package com.github.manasmods.tensura.registry.biome;

import com.github.manasmods.tensura.world.features.FloatingDebrisFeature;
import com.github.manasmods.tensura.world.features.HellBlockBlobFeature;
import com.github.manasmods.tensura.world.features.RockySpikeFeature;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraFeatures {
   private static final DeferredRegister<Feature<?>> registry;
   public static final RegistryObject<RockySpikeFeature> ROCKY_SPIKE;
   public static final RegistryObject<HellBlockBlobFeature> HELL_BLOCK_BLOB;
   public static final RegistryObject<FloatingDebrisFeature> FLOATING_DEBRIS;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.FEATURES, "tensura");
      ROCKY_SPIKE = registry.register("rocky_spike", RockySpikeFeature::new);
      HELL_BLOCK_BLOB = registry.register("hell_block_blob", HellBlockBlobFeature::new);
      FLOATING_DEBRIS = registry.register("floating_debris", FloatingDebrisFeature::new);
   }
}
