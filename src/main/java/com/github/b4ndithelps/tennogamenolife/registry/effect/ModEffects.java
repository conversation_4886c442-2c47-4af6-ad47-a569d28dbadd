package com.github.b4ndithelps.tennogamenolife.registry.effect;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.effect.WeakenEffect;
import net.minecraft.world.effect.MobEffect;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModEffects {
    public static final DeferredRegister<MobEffect> MOB_EFFECTS = 
        DeferredRegister.create(ForgeRegistries.MOB_EFFECTS, Tenreincarnation.MODID);

    public static final RegistryObject<MobEffect> WEAKEN = MOB_EFFECTS.register("weaken",
        WeakenEffect::new);
} 