package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;

public class SeerSkill extends Skill {
   protected static final UUID SEER = UUID.fromString("5aa90ead-4edc-4a45-b93f-2293ee00a577");

   public SeerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         boolean futureVision = entity.m_21023_((MobEffect)TensuraMobEffects.FUTURE_VISION.get());
         if (instance.isToggled() || futureVision) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (futureVision || !(entity.m_217043_().m_188501_() > (instance.isMastered(entity) ? 0.7F : 0.5F))) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     event.setCanceled(true);
                     if (SkillUtils.canNegateDodge(entity, damageSource)) {
                        event.setCanceled(false);
                     }

                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      boolean futureVision = entity.m_21023_((MobEffect)TensuraMobEffects.FUTURE_VISION.get());
      if (instance.isToggled() || futureVision) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (futureVision || !(entity.m_217043_().m_188501_() > (instance.isMastered(entity) ? 0.7F : 0.5F))) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      LivingEntity entity = event.getEntity();
      if (instance.isToggled()) {
         DamageSource damageSource = event.getSource();
         if (!damageSource.m_19378_()) {
            if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
               float multiplier = instance.isMastered(entity) ? 0.5F : 0.7F;
               event.setAmount(event.getAmount() * multiplier);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12346_, SoundSource.PLAYERS, 2.0F, 1.0F);
            }
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance chance = entity.m_21051_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get());
      if (chance != null) {
         int amount = instance.isMastered(entity) ? 50 : 33;
         AttributeModifier attributemodifier = new AttributeModifier(SEER, "Precognition", (double)amount, Operation.ADDITION);
         if (!chance.m_22109_(attributemodifier)) {
            chance.m_22125_(attributemodifier);
         }

      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance chance = entity.m_21051_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get());
      if (chance != null) {
         chance.m_22127_(SEER);
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.FUTURE_VISION.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.FUTURE_VISION.get());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
      } else {
         int duration = instance.isMastered(entity) ? 400 : 200;
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FUTURE_VISION.get(), duration, 0, false, false, false));
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_21023_((MobEffect)TensuraMobEffects.FUTURE_VISION.get());
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
