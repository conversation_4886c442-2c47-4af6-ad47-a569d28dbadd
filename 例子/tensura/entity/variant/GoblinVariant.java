package com.github.manasmods.tensura.entity.variant;

import com.github.manasmods.tensura.entity.GoblinEntity;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public class GoblinVariant {
   public static enum Bottom {
      SHORTS(0, "shorts_white"),
      PANTS(1, "pants_white"),
      PANTS_TORN_RIGHT(2, "pants_torn_right"),
      PANTS_TORN_LEFT(3, "pants_torn_left");

      private static final GoblinVariant.Bottom[] BY_ID = (GoblinVariant.Bottom[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Bottom::getId)).toArray((x$0) -> {
         return new GoblinVariant.Bottom[x$0];
      });
      private final int id;
      private final String location;

      private Bottom(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         return new ResourceLocation("tensura", "textures/entity/goblin/unisex/bottom/legs_" + entity.getBottom().getLocation() + ".png");
      }

      public static GoblinVariant.Bottom byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Bottom)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Bottom[] $values() {
         return new GoblinVariant.Bottom[]{SHORTS, PANTS, PANTS_TORN_RIGHT, PANTS_TORN_LEFT};
      }
   }

   public static enum Top {
      T_SHIRT(0, "tshirt_white"),
      VEST(1, "vest_white"),
      V_BROKEN_RIGHT(2, "vest_broken_right_white"),
      V_BROKEN_LEFT(3, "vest_broken_left_white");

      private static final GoblinVariant.Top[] BY_ID = (GoblinVariant.Top[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Top::getId)).toArray((x$0) -> {
         return new GoblinVariant.Top[x$0];
      });
      private final int id;
      private final String location;

      private Top(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         return new ResourceLocation("tensura", "textures/entity/goblin/unisex/top/chest_" + entity.getTop().getLocation() + ".png");
      }

      public static GoblinVariant.Top byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Top)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Top[] $values() {
         return new GoblinVariant.Top[]{T_SHIRT, VEST, V_BROKEN_RIGHT, V_BROKEN_LEFT};
      }
   }

   public static enum Head {
      WHITE(0, "white"),
      WHITE_FULL(1, "full_white");

      private static final GoblinVariant.Head[] BY_ID = (GoblinVariant.Head[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Head::getId)).toArray((x$0) -> {
         return new GoblinVariant.Head[x$0];
      });
      private final int id;
      private final String location;

      private Head(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         return new ResourceLocation("tensura", "textures/entity/goblin/unisex/head/head_bandana_" + entity.getHead().getLocation() + ".png");
      }

      public static GoblinVariant.Head byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Head)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Head[] $values() {
         return new GoblinVariant.Head[]{WHITE, WHITE_FULL};
      }
   }

   public static enum Clothing {
      GREY(0, "_grey"),
      TAN(1, "_tan"),
      BROWN(2, "_brown"),
      BLACK(3, "_black"),
      DARK_GREY(4, "_dark_grey"),
      WHITE(5, "_white");

      private static final GoblinVariant.Clothing[] BY_ID = (GoblinVariant.Clothing[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Clothing::getId)).toArray((x$0) -> {
         return new GoblinVariant.Clothing[x$0];
      });
      private final int id;
      private final String location;

      private Clothing(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         String gender = entity.getGender().getLocation();
         return new ResourceLocation("tensura", "textures/entity/goblin/" + gender + "/clothing/loin_" + gender + entity.getClothing().getLocation() + ".png");
      }

      public static GoblinVariant.Clothing byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Clothing)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Clothing[] $values() {
         return new GoblinVariant.Clothing[]{GREY, TAN, BROWN, BLACK, DARK_GREY, WHITE};
      }
   }

   public static enum Hair {
      BANDANA_BLACK(0, "bandana_black_"),
      BANDANA_BROWN(1, "bandana_brown_"),
      BANDANA_GREY(2, "bandana_grey_"),
      BANDANA_WHITE(3, "bandana_white_"),
      LONG_BLACK(4, "long_black_"),
      LONG_BROWN(5, "long_brown_"),
      LONG_GREY(6, "long_grey_"),
      LONG_WHITE(7, "long_white_"),
      SHORT_BLACK(8, "short_black_"),
      SHORT_BROWN(9, "short_brown_"),
      SHORT_GREY(10, "short_grey_"),
      SHORT_WHITE(11, "short_white_");

      private static final GoblinVariant.Hair[] BY_ID = (GoblinVariant.Hair[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Hair::getId)).toArray((x$0) -> {
         return new GoblinVariant.Hair[x$0];
      });
      private final int id;
      private final String location;

      private Hair(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         String gender = entity.getGender().getLocation();
         return new ResourceLocation("tensura", "textures/entity/goblin/" + gender + "/hair/" + entity.getHair().getLocation() + gender + ".png");
      }

      public static GoblinVariant.Hair byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Hair)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Hair[] $values() {
         return new GoblinVariant.Hair[]{BANDANA_BLACK, BANDANA_BROWN, BANDANA_GREY, BANDANA_WHITE, LONG_BLACK, LONG_BROWN, LONG_GREY, LONG_WHITE, SHORT_BLACK, SHORT_BROWN, SHORT_GREY, SHORT_WHITE};
      }
   }

   public static enum Face {
      FACE_A(0, "unisex/face/face_a", GoblinVariant.Gender.OTHER),
      FACE_B(1, "unisex/face/face_b", GoblinVariant.Gender.OTHER),
      FACE_C(2, "male/face/face_c", GoblinVariant.Gender.MALE),
      FACE_D(3, "unisex/face/face_d", GoblinVariant.Gender.OTHER),
      FACE_E(4, "unisex/face/face_e", GoblinVariant.Gender.OTHER);

      private static final GoblinVariant.Face[] BY_ID = (GoblinVariant.Face[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Face::getId)).toArray((x$0) -> {
         return new GoblinVariant.Face[x$0];
      });
      private static final List<Integer> MALE_LIST = Arrays.stream(values()).filter((skin) -> {
         return skin.getGender() != GoblinVariant.Gender.FEMALE;
      }).map(GoblinVariant.Face::getId).toList();
      private static final List<Integer> FEMALE_LIST = Arrays.stream(values()).filter((skin) -> {
         return skin.getGender() != GoblinVariant.Gender.MALE;
      }).map(GoblinVariant.Face::getId).toList();
      private final int id;
      private final String location;
      private final GoblinVariant.Gender gender;

      private Face(int id, String name, GoblinVariant.Gender gender) {
         this.id = id;
         this.location = name;
         this.gender = gender;
      }

      public ResourceLocation getTextureLocation() {
         return new ResourceLocation("tensura", "textures/entity/goblin/" + this.getLocation() + ".png");
      }

      public static GoblinVariant.Face byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinVariant.Gender gender, GoblinEntity entity) {
         return gender.equals(GoblinVariant.Gender.FEMALE) ? (Integer)FEMALE_LIST.get(entity.m_217043_().m_188503_(FEMALE_LIST.size())) : (Integer)MALE_LIST.get(entity.m_217043_().m_188503_(MALE_LIST.size()));
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      public GoblinVariant.Gender getGender() {
         return this.gender;
      }

      // $FF: synthetic method
      private static GoblinVariant.Face[] $values() {
         return new GoblinVariant.Face[]{FACE_A, FACE_B, FACE_C, FACE_D, FACE_E};
      }
   }

   public static enum Skin {
      MEDIUM(0, "mid_"),
      LIGHT(1, "light_"),
      DARK(2, "dark_");

      private static final GoblinVariant.Skin[] BY_ID = (GoblinVariant.Skin[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Skin::getId)).toArray((x$0) -> {
         return new GoblinVariant.Skin[x$0];
      });
      private final int id;
      private final String location;

      private Skin(int id, String name) {
         this.id = id;
         this.location = name;
      }

      public static ResourceLocation getTextureLocation(GoblinEntity entity) {
         String gender = entity.getGender().getLocation();
         return new ResourceLocation("tensura", "textures/entity/goblin/" + gender + "/skin/" + entity.getSkin().getLocation() + gender + ".png");
      }

      public static GoblinVariant.Skin byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public static int getRandom(GoblinEntity entity) {
         return ((GoblinVariant.Skin)Util.m_214670_(values(), entity.m_217043_())).getId();
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Skin[] $values() {
         return new GoblinVariant.Skin[]{MEDIUM, LIGHT, DARK};
      }
   }

   public static enum Gender {
      MALE(0, "male"),
      FEMALE(1, "female"),
      OTHER(2, "unisex");

      private static final GoblinVariant.Gender[] BY_ID = (GoblinVariant.Gender[])Arrays.stream(values()).sorted(Comparator.comparingInt(GoblinVariant.Gender::getId)).toArray((x$0) -> {
         return new GoblinVariant.Gender[x$0];
      });
      private final int id;
      private final String location;

      private Gender(int id, String location) {
         this.id = id;
         this.location = location;
      }

      public static GoblinVariant.Gender byId(int id) {
         return BY_ID[id % BY_ID.length];
      }

      public int getId() {
         return this.id;
      }

      public String getLocation() {
         return this.location;
      }

      // $FF: synthetic method
      private static GoblinVariant.Gender[] $values() {
         return new GoblinVariant.Gender[]{MALE, FEMALE, OTHER};
      }
   }
}
