package com.github.b4ndithelps.tennogamenolife.item;

import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 连锁铲技能书物品
 * 右键使用后可获得连锁铲技能
 */
public class ChainShovelSkillBook extends Item {

    /**
     * 创建连锁铲技能书
     */
    public ChainShovelSkillBook() {
        this(new Item.Properties()
                .stacksTo(1) // 不可堆叠
                .rarity(Rarity.EPIC)); // 史诗品质（紫色）
    }
    
    /**
     * 使用指定属性创建连锁铲技能书
     * @param properties 物品属性
     */
    public ChainShovelSkillBook(Properties properties) {
        super(properties);
    }

    @Override
    public void appendHoverText(@NotNull ItemStack stack, @Nullable Level level, @NotNull List<Component> tooltip, @NotNull TooltipFlag flag) {
        tooltip.add(Component.translatable("item.tennogamenolife.chain_shovel_skill_book.tooltip")
                .withStyle(ChatFormatting.GRAY));
        tooltip.add(Component.translatable("item.tennogamenolife.chain_shovel_skill_book.tooltip2")
                .withStyle(ChatFormatting.AQUA));
    }

    @Override
    public @NotNull InteractionResultHolder<ItemStack> use(@NotNull Level level, @NotNull Player player, @NotNull InteractionHand hand) {
        ItemStack stack = player.getItemInHand(hand);
        
        if (!level.isClientSide) {
            // 获取连锁铲技能实例
            ManasSkill chainShovelSkill = TenreincarnationSkill.CHAIN_SHOVEL.get();
            
            // 检查玩家是否已经拥有连锁铲技能
            boolean hasSkill = SkillUtils.hasSkill(player, chainShovelSkill);
            
            if (hasSkill) {
                // 已经拥有技能，提示玩家
                player.displayClientMessage(
                    Component.translatable("item.tennogamenolife.chain_shovel_skill_book.already_learned")
                        .withStyle(ChatFormatting.RED),
                    true
                );
            } else {
                // 使用指令给玩家添加连锁铲技能
                if (player instanceof ServerPlayer serverPlayer) {
                    MinecraftServer server = serverPlayer.getServer();
                    if (server != null) {
                        String command = "tensura edit " + player.getName().getString() + " ability grant tennogamenolife:chain_shovel";
                        server.getCommands().performPrefixedCommand(server.createCommandSourceStack(), command);
                        
                        // 播放获得技能的音效
                        level.playSound(
                            null,
                            player.getX(), player.getY(), player.getZ(),
                            SoundEvents.PLAYER_LEVELUP,
                            SoundSource.PLAYERS,
                            0.75F, 1.0F
                        );
                        
                        // 提示玩家已获得技能
                        player.displayClientMessage(
                            Component.translatable("item.tennogamenolife.chain_shovel_skill_book.learned")
                                .withStyle(ChatFormatting.GREEN),
                            true
                        );
                        
                        // 使用后消耗物品
                        if (!player.isCreative()) {
                            stack.shrink(1);
                        }
                    }
                }
            }
        }
        
        return InteractionResultHolder.sidedSuccess(stack, level.isClientSide());
    }
} 