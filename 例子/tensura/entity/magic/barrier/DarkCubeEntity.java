package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class DarkCubeEntity extends BarrierEntity {
   private static final EntityDataAccessor<Boolean> TRUE;

   public DarkCubeEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.DARK_CUBE.get(), level);
      this.m_5602_(entity);
   }

   public DarkCubeEntity(EntityType<? extends DarkCubeEntity> entityType, Level level) {
      super(entityType, level);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(TRUE, false);
   }

   protected void m_7380_(@NotNull CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128379_("TrueDarkness", this.isTrueDarkness());
   }

   protected void m_7378_(@NotNull CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setTrueDarkness(pCompound.m_128471_("TrueDarkness"));
   }

   public boolean isTrueDarkness() {
      return (Boolean)this.m_20088_().m_135370_(TRUE);
   }

   public void setTrueDarkness(boolean trueDarkness) {
      this.m_20088_().m_135381_(TRUE, trueDarkness);
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public int getTickEachHit() {
      return this.isTrueDarkness() ? 20 : 10;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void applyEffect(LivingEntity entity) {
      Entity owner = this.m_37282_();
      if (owner == null || !entity.m_7307_(owner) && entity != owner) {
         if (this.isTrueDarkness()) {
            float spiritualDamage = this.getDamage();
            if (owner == null) {
               DamageSourceHelper.directSpiritualHurt(entity, (Entity)null, TensuraDamageSources.SOUL_SCATTER, spiritualDamage);
            } else {
               DamageSourceHelper.directSpiritualHurt(entity, owner, TensuraDamageSources.soulScatter(owner), spiritualDamage);
            }

            entity.m_7292_(new MobEffectInstance(MobEffects.f_216964_, 20, 9, false, false, false));
            if (this.f_19797_ % (this.getTickEachHit() * 3) == 0) {
               int insanityLevel = 0;
               MobEffectInstance insanity = entity.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
               if (insanity != null) {
                  insanityLevel = insanity.m_19564_() + 1;
               }

               SkillHelper.checkThenAddEffectSource(entity, owner, new MobEffectInstance((MobEffect)TensuraMobEffects.INSANITY.get(), 200, insanityLevel, false, false, false));
            }

         } else {
            DamageSource source = TensuraDamageSources.indirectElementalAttack("tensura.dark_attack", this, owner, true);
            if (entity.m_6469_(source, this.getDamage())) {
               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 20, 4, false, false, false));
            }

         }
      }
   }

   public static void spawnTrueCube(EntityType<? extends DarkCubeEntity> entityType, float damage, float radius, int life, boolean trueDarkness, Vec3 pos, LivingEntity owner, ManasSkillInstance instance, double cost, double increaseCost, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      Level level = owner.m_9236_();
      if (tag.m_128451_("BarrierID") == 0 && !SkillHelper.outOfMagicule(owner, cost)) {
         DarkCubeEntity cube = (DarkCubeEntity)entityType.m_20615_(level);
         if (cube == null) {
            return;
         }

         cube.setTrueDarkness(trueDarkness);
         cube.m_5602_(owner);
         cube.setDamage(damage);
         cube.setRadius(radius);
         cube.setLife(life);
         cube.m_146884_(pos);
         cube.setMpCost(cost);
         cube.setSkill(instance);
         owner.m_9236_().m_7967_(cube);
         owner.m_21011_(InteractionHand.MAIN_HAND, true);
         tag.m_128405_("BarrierID", cube.m_19879_());
      } else {
         Entity entity = owner.m_9236_().m_6815_(tag.m_128451_("BarrierID"));
         if (entity instanceof DarkCubeEntity) {
            DarkCubeEntity cube = (DarkCubeEntity)entity;
            if (heldTicks % 20 != 0 || !SkillHelper.outOfMagicule(owner, increaseCost)) {
               cube.increaseLife(1);
            }
         } else {
            tag.m_128405_("BarrierID", 0);
         }
      }

      instance.markDirty();
   }

   static {
      TRUE = SynchedEntityData.m_135353_(DarkCubeEntity.class, EntityDataSerializers.f_135035_);
   }
}
