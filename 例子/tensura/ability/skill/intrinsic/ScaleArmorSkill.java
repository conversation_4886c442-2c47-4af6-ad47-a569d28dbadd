package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.ForgeMod;

public class ScaleArmorSkill extends Skill {
   protected static final UUID SCALE_ARMOR = UUID.fromString("6965a482-3e6d-11ee-be56-0242ac120002");

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public ScaleArmorSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance swim = entity.m_21051_((Attribute)ForgeMod.SWIM_SPEED.get());
      AttributeModifier swimModifier = new AttributeModifier(SCALE_ARMOR, "Scale Armor", 1.0D, Operation.ADDITION);
      if (swim != null && !swim.m_22109_(swimModifier)) {
         swim.m_22125_(swimModifier);
      }

      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      AttributeModifier armorModifier = new AttributeModifier(SCALE_ARMOR, "Scale Armor", 6.0D, Operation.ADDITION);
      if (armor != null && !armor.m_22109_(armorModifier)) {
         armor.m_22125_(armorModifier);
      }

      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11672_, SoundSource.PLAYERS, 5.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance swim = entity.m_21051_((Attribute)ForgeMod.SWIM_SPEED.get());
      if (swim != null) {
         swim.m_22127_(SCALE_ARMOR);
      }

      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         armor.m_22127_(SCALE_ARMOR);
      }

   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
