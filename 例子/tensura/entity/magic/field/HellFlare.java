package com.github.manasmods.tensura.entity.magic.field;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;

public class HellFlare extends AreaField {
   public HellFlare(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public HellFlare(Level level, Entity entity) {
      this((EntityType)TensuraEntityTypes.HELL_FLARE.get(), level);
      this.m_5602_(entity);
   }

   public boolean isInstant() {
      return true;
   }

   public void applyEffect(LivingEntity target, boolean instant) {
      boolean var10000;
      Entity owner;
      label35: {
         owner = this.m_37282_();
         if (owner instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)owner;
            if (target.m_7307_(living) || target.m_7306_(living)) {
               var10000 = true;
               break label35;
            }
         }

         var10000 = false;
      }

      boolean isAlly = var10000;
      if (!isAlly) {
         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_7500_()) {
               return;
            }
         }

         if (instant) {
            DamageSource source = owner == null ? TensuraDamageSources.BLACK_FLAME : TensuraDamageSources.blackFlame(owner);
            DamageSourceHelper.dealSplitElementalDamage(target, DamageSourceHelper.addSkillAndCost(source, this.getMpCost(), this.getSkill()), 0.9F, this.getDamage());
         }

         SkillHelper.checkThenAddEffectSource(target, this.m_37282_(), (MobEffect)TensuraMobEffects.BLACK_BURN.get(), 200, 0, false, false, false);
      }
   }
}
