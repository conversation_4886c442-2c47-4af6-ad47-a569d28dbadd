package com.github.manasmods.tensura.entity.magic.lightning;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.block.BlackFireBlock;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import org.jetbrains.annotations.Nullable;

public class TenuraLightningBolt extends net.minecraft.world.entity.LightningBolt {
   private static final EntityDataAccessor<Integer> ADDITIONAL_VISUAL;
   @Nullable
   private Entity owner = null;
   private float tensuraDamage = 5.0F;
   private float radius = 2.0F;
   protected double mpCost = 0.0D;
   protected ManasSkillInstance skill = null;

   public TenuraLightningBolt(EntityType<? extends net.minecraft.world.entity.LightningBolt> type, Level pLevel) {
      super(type, pLevel);
      this.m_20874_(true);
   }

   public TenuraLightningBolt(EntityType<? extends net.minecraft.world.entity.LightningBolt> type, Level pLevel, Entity owner) {
      super(type, pLevel);
      this.setOwner(owner);
      this.m_20874_(true);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(ADDITIONAL_VISUAL, 0);
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("AdditionalVisual", this.getAdditionalVisual());
      pCompound.m_128347_("MPCost", this.getMpCost());
      if (this.skill != null) {
         pCompound.m_128365_("skill", this.skill.toNBT());
      }

   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setAdditionalVisual(pCompound.m_128451_("AdditionalVisual"));
      this.setMpCost(pCompound.m_128459_("MPCost"));
      if (pCompound.m_128441_("skill")) {
         Tag var3 = pCompound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

   }

   public int getAdditionalVisual() {
      return (Integer)this.f_19804_.m_135370_(ADDITIONAL_VISUAL);
   }

   public void setAdditionalVisual(int i) {
      this.f_19804_.m_135381_(ADDITIONAL_VISUAL, i);
   }

   public float getDamage() {
      return 0.0F;
   }

   protected BlockState getFireBlock() {
      return Blocks.f_50083_.m_49966_();
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         if (this.f_20860_ == 1) {
            this.doAoEDamage();
         }

      }
   }

   protected boolean canHit(Entity entity) {
      if (entity == this) {
         return false;
      } else if (entity == this.getOwner()) {
         return false;
      } else {
         return this.getOwner() != null && this.getOwner().m_7307_(entity) ? false : entity.m_6084_();
      }
   }

   protected void doAoEDamage() {
      float electricRadius = this.m_20072_() ? this.radius * 1.5F : this.radius;
      List<Entity> list = this.f_19853_.m_6249_(this, this.m_20191_().m_82400_((double)electricRadius), (entityx) -> {
         return this.canHit(entityx) && entityx.m_20270_(this) < electricRadius;
      });
      DamageSource damageSource = this.getOwner() == null ? DamageSource.f_19306_ : TensuraDamageSources.lightning(this.getOwner());
      Iterator var4 = list.iterator();

      while(var4.hasNext()) {
         Entity entity = (Entity)var4.next();
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), 1.0D);
         DamageSourceHelper.dealSplitElementalDamage(entity, DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), 0.9F, this.getTensuraDamage());
         Level var7 = this.f_19853_;
         if (var7 instanceof ServerLevel) {
            ServerLevel serverLevel = (ServerLevel)var7;
            entity.m_8038_(serverLevel, this);
         }
      }

      if (TensuraGameRules.canSkillGrief(this.f_19853_)) {
         this.placeFireAndMeltGlass(this.m_20182_(), (int)this.radius);
      }

   }

   protected void placeFireAndMeltGlass(Vec3 pos, int radius) {
      Level level = this.m_9236_();
      SkillHelper.launchBlock(this, this.m_20182_(), radius + 1, 1, (float)radius / 6.0F, (float)radius / 9.0F, (blockStatex) -> {
         return this.f_19796_.m_188503_(3) != 1 ? false : blockStatex.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
      }, (blockPos) -> {
         return true;
      }, this.getSkill());
      int yPos = Mth.m_14107_(pos.m_7098_()) - 1;
      int xPos = Mth.m_14107_(pos.m_7096_());
      int zPos = Mth.m_14107_(pos.m_7094_());
      boolean placedBlocks = false;
      boolean removedBlocks = false;
      boolean shouldPlaceFire = this.getOwner() instanceof Player;

      for(int j = -radius; j <= radius; ++j) {
         for(int k = -radius; k <= radius; ++k) {
            for(int i = -2; i <= 2; ++i) {
               int newYPos = yPos + i;
               int newXPos = xPos + j;
               int newZPos = zPos + k;
               BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
               if (!(blockpos.m_203193_(pos) > (double)(radius * radius)) && this.f_19796_.m_188503_(3) == 1) {
                  BlockState blockState = level.m_8055_(blockpos);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.getOwner(), this.getSkill(), blockpos);
                  if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                     if (blockState.m_204336_(net.minecraftforge.common.Tags.Blocks.SAND)) {
                        placedBlocks = level.m_46597_(blockpos, Blocks.f_50058_.m_49966_()) || placedBlocks;
                     } else {
                        if (blockState.m_60767_().m_76336_() && blockState.m_60819_().m_76178_()) {
                           BlockPos blockPosDown = blockpos.m_7495_();
                           BlockState blockStateDown = level.m_8055_(blockPosDown);
                           if (blockStateDown.m_60783_(level, blockPosDown, Direction.UP)) {
                              removedBlocks = level.m_7471_(blockpos, true) || removedBlocks;
                           }
                        }

                        if (shouldPlaceFire && BlackFireBlock.canBePlacedAt(level, blockpos)) {
                           placedBlocks = level.m_46597_(blockpos, this.getFireBlock()) || placedBlocks;
                           level.m_186460_(blockpos, blockState.m_60734_(), 20);
                        }

                        MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.getOwner(), this.getSkill(), blockpos));
                     }
                  }
               }
            }
         }
      }

      if (removedBlocks && this.getOwner() != null) {
         this.f_19853_.m_142346_(this.getOwner(), GameEvent.f_157792_, this.m_20183_());
      }

      if (placedBlocks && this.getOwner() != null) {
         this.f_19853_.m_142346_(this.getOwner(), GameEvent.f_157792_, this.m_20183_());
      }

   }

   @Nullable
   public Entity getOwner() {
      return this.owner;
   }

   public void setOwner(@Nullable Entity owner) {
      this.owner = owner;
   }

   public float getTensuraDamage() {
      return this.tensuraDamage;
   }

   public void setTensuraDamage(float tensuraDamage) {
      this.tensuraDamage = tensuraDamage;
   }

   public float getRadius() {
      return this.radius;
   }

   public void setRadius(float radius) {
      this.radius = radius;
   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   static {
      ADDITIONAL_VISUAL = SynchedEntityData.m_135353_(TenuraLightningBolt.class, EntityDataSerializers.f_135028_);
   }
}
