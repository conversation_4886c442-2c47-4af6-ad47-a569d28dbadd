package com.github.manasmods.tensura.registry.entity;

import com.github.manasmods.tensura.block.SmithingBenchBlock;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.google.common.collect.ImmutableSet;
import javax.annotation.Nullable;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.ai.village.poi.PoiType;
import net.minecraft.world.entity.npc.VillagerProfession;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraVillagers {
   public static final DeferredRegister<PoiType> POI;
   public static final DeferredRegister<VillagerProfession> registry;
   public static final RegistryObject<PoiType> GEARSMITH_POI;
   public static final RegistryObject<VillagerProfession> GEARSMITH;

   private static RegistryObject<VillagerProfession> register(String name, RegistryObject<PoiType> held, RegistryObject<PoiType> acquirable, @Nullable ImmutableSet<Item> requested, @Nullable ImmutableSet<Block> secondary, @Nullable SoundEvent sound) {
      ImmutableSet<Item> items = requested == null ? ImmutableSet.of() : requested;
      ImmutableSet<Block> blocks = secondary == null ? ImmutableSet.of() : secondary;
      return registry.register(name, () -> {
         return new VillagerProfession(name, (holder) -> {
            return holder.get() == held.get();
         }, (holder) -> {
            return holder.get() == acquirable.get();
         }, items, blocks, sound);
      });
   }

   public static void init(IEventBus iEventBus) {
      POI.register(iEventBus);
      registry.register(iEventBus);
   }

   static {
      POI = DeferredRegister.create(ForgeRegistries.POI_TYPES, "tensura");
      registry = DeferredRegister.create(ForgeRegistries.VILLAGER_PROFESSIONS, "tensura");
      GEARSMITH_POI = POI.register("gearsmith_poi", () -> {
         return new PoiType(ImmutableSet.copyOf(((SmithingBenchBlock)TensuraBlocks.SMITHING_BENCH.get()).m_49965_().m_61056_()), 1, 1);
      });
      GEARSMITH = register("gearsmith", GEARSMITH_POI, GEARSMITH_POI, (ImmutableSet)null, (ImmutableSet)null, SoundEvents.f_12574_);
   }
}
