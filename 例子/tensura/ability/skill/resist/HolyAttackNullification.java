package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.damagesource.DamageSource;

public class HolyAttackNullification extends ResistSkill {
   public HolyAttackNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isHoly(damageSource);
   }
}
