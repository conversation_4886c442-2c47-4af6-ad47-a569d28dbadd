package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.DynamicMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.controller.FlightMoveController;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.ItemTags;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.TemptGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.WebBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class ArmyWaspEntity extends FLyingTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public boolean hovering = false;

   public ArmyWaspEntity(EntityType<? extends ArmyWaspEntity> type, Level level) {
      super(type, level);
      this.f_21364_ = 10;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 60.0D).m_22268_(Attributes.f_22281_, 8.0D).m_22268_(Attributes.f_22284_, 4.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22280_, 0.30000001192092896D).m_22268_(Attributes.f_22278_, 0.1D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(3, new TamableFollowParentGoal(this, 1.25D));
      this.f_21345_.m_25352_(4, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new TemptGoal(this, 1.25D, Ingredient.m_204132_(ItemTags.f_13149_), false));
      this.f_21345_.m_25352_(7, new FLyingTamableEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21345_.m_25352_(4, new DynamicMeleeAttackGoal(this, List.of((self, target, goal) -> {
         float speed = 2.0F;
         goal.shouldMoveToTarget = !target.m_5842_();
         if (this.m_20270_(target) < 20.0F) {
            if ((double)this.m_20270_(target) < 4.0D) {
               this.m_7327_(target);
               this.setMiscAnimation(1);
               speed = 2.2F;
            } else {
               this.m_21391_(target, 70.0F, 70.0F);
            }
         }

         return speed;
      }, (self, target, goal) -> {
         return target instanceof Player ? 2.2F : 2.0F;
      })));
      this.f_21346_.m_25352_(5, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(6, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public void m_7334_(Entity pEntity) {
      if (!(pEntity instanceof ArmyWaspEntity)) {
         super.m_7334_(pEntity);
      }
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || super.m_6673_(source);
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!(pState.m_60734_() instanceof WebBlock)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.armyWaspSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      ArmyWaspEntity wasp = (ArmyWaspEntity)((EntityType)TensuraEntityTypes.ARMY_WASP.get()).m_20615_(pLevel);
      if (wasp == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            wasp.m_21816_(uuid);
            wasp.m_7105_(true);
         }

         return wasp;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(ItemTags.f_13149_);
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = pEntity.m_6469_(DamageSource.m_19364_(this), (float)((int)this.m_21133_(Attributes.f_22281_)));
      if (flag) {
         this.m_19970_(this, pEntity);
         if (pEntity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)pEntity;
            living.m_21321_(living.m_21235_() + 1);
            byte var10000;
            switch(this.f_19853_.m_46791_()) {
            case EASY:
               var10000 = 5;
               break;
            case NORMAL:
               var10000 = 10;
               break;
            case HARD:
               var10000 = 16;
               break;
            default:
               var10000 = 0;
            }

            int i = var10000;
            if (i > 0) {
               int level = living.m_21235_() / 3;
               living.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), i * 20, level), this);
               living.m_147207_(new MobEffectInstance(MobEffects.f_19604_, i * 20, level > 0 ? 1 : 0), this);
            }
         }

         this.m_5496_(SoundEvents.f_11692_, 1.0F, 1.0F);
      }

      return flag;
   }

   protected void switchNavigator(boolean onLand) {
      if (onLand) {
         this.f_21342_ = new MoveControl(this);
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      } else {
         this.f_21342_ = new FlightMoveController(this, 0.7F, true);
         this.f_21344_ = new StraightFlightNavigator(this, this.f_19853_);
         this.wasFlying = true;
         this.setMiscAnimation(3);
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 10) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (!this.f_19853_.f_46443_) {
         LivingEntity livingentity = this.m_5448_();
         if (livingentity != null && livingentity.m_6084_() && this.m_20280_(livingentity) > 3.0D) {
            this.setFlying(true);
         }
      }

   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(3.0F);
            this.setMiscAnimation(2);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.setMiscAnimation(2);
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   protected SoundEvent m_7515_() {
      return null;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_11741_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11740_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (!this.m_20096_() && !this.m_20069_() && !this.m_20077_() && this.getMiscAnimation() != 3) {
         if (event.isMoving()) {
            this.hovering = false;
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.fly", EDefaultLoopTypes.LOOP));
         } else {
            this.hovering = true;
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.hover", EDefaultLoopTypes.LOOP));
         }
      } else {
         this.hovering = false;
         if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.idle", EDefaultLoopTypes.LOOP));
         }
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.sting", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.army_wasp.take_off", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(ArmyWaspEntity.class, EntityDataSerializers.f_135028_);
   }
}
