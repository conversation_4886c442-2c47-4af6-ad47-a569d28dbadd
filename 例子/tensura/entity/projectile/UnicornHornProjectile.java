package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;

public class UnicornHornProjectile extends AbstractArrow {
   private int life;

   public UnicornHornProjectile(EntityType<? extends UnicornHornProjectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public UnicornHornProjectile(Level pLevel, LivingEntity pShooter) {
      super((EntityType)TensuraEntityTypes.UNICORN_HORN.get(), pShooter, pLevel);
      this.m_36781_(this.m_36789_() + 2.75D);
   }

   public UnicornHornProjectile(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.UNICORN_HORN.get(), pX, pY, pZ, pLevel);
      this.m_36781_(this.m_36789_() + 2.75D);
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("Life", this.life);
   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.life = pCompound.m_128451_("Life");
   }

   public ItemStack m_7941_() {
      return new ItemStack((ItemLike)TensuraMobDropItems.UNICORN_HORN.get());
   }

   protected float m_6882_() {
      return 0.55F;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.life == 0 && !this.m_20067_()) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11932_, SoundSource.AMBIENT, 3.0F, 1.0F);
      }

      ++this.life;
   }

   protected void m_5790_(EntityHitResult pResult) {
      if (!this.f_19853_.m_5776_()) {
         Entity var3 = pResult.m_82443_();
         LivingEntity var10001;
         if (var3 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var3;
            var10001 = living;
         } else {
            var10001 = null;
         }

         this.explode(var10001);
      }

      super.m_5790_(pResult);
   }

   protected void m_8060_(BlockHitResult pResult) {
      BlockPos blockpos = new BlockPos(pResult.m_82425_());
      this.f_19853_.m_8055_(blockpos).m_60682_(this.f_19853_, blockpos, this);
      if (!this.f_19853_.m_5776_()) {
         this.explode((LivingEntity)null);
      }

      super.m_8060_(pResult);
   }

   private void explode(@Nullable LivingEntity living) {
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11930_, SoundSource.AMBIENT, 3.0F, 1.0F);
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11934_, SoundSource.AMBIENT, 2.0F, 1.0F);
      this.f_19853_.m_7605_(this, (byte)17);
      this.m_146852_(GameEvent.f_157812_, this.m_37282_());
      this.dealExplosionDamage(living);
      this.m_146870_();
   }

   private void dealExplosionDamage(@Nullable LivingEntity living) {
      Vec3 vec3 = this.m_20182_();
      Iterator var3 = this.f_19853_.m_45976_(LivingEntity.class, this.m_20191_().m_82400_(5.0D)).iterator();

      while(true) {
         LivingEntity livingentity;
         do {
            do {
               if (!var3.hasNext()) {
                  return;
               }

               livingentity = (LivingEntity)var3.next();
            } while(!(this.m_20280_(livingentity) <= 25.0D));
         } while(livingentity == living);

         boolean flag = false;

         for(int i = 0; i < 2; ++i) {
            Vec3 vec31 = new Vec3(livingentity.m_20185_(), livingentity.m_20227_(0.5D * (double)i), livingentity.m_20189_());
            HitResult hitresult = this.f_19853_.m_45547_(new ClipContext(vec3, vec31, Block.COLLIDER, Fluid.NONE, this));
            if (hitresult.m_6662_() == Type.MISS) {
               flag = true;
               break;
            }
         }

         if (flag) {
            float f1 = 15.0F * (float)Math.sqrt((5.0D - (double)this.m_20270_(livingentity)) / 5.0D);
            livingentity.m_6469_(TensuraDamageSources.unicornHorn(this, this.m_37282_()), f1);
         }
      }
   }

   public void m_7822_(byte pId) {
      if (pId == 17 && this.f_19853_.f_46443_) {
         for(int i = 0; i < this.f_19796_.m_188503_(3) + 2; ++i) {
            this.f_19853_.m_7106_(ParticleTypes.f_123747_, this.m_20185_(), this.m_20186_(), this.m_20189_(), this.f_19796_.m_188583_() * 0.05D, 0.005D, this.f_19796_.m_188583_() * 0.05D);
         }
      }

      super.m_7822_(pId);
   }
}
