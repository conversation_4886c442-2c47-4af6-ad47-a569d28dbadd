package com.github.manasmods.tensura.entity.magic.misc;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.common.ThoughtCommunicationSkill;
import com.github.manasmods.tensura.ability.skill.unique.ThrowerSkill;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.entity.human.ShinjiTanimuraEntity;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.item.custom.KunaiItem;
import com.github.manasmods.tensura.item.custom.OrbOfDominationItem;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import javax.annotation.Nullable;
import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.BucketItem;
import net.minecraft.world.item.DispensibleContainerItem;
import net.minecraft.world.item.FireChargeItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.PotionItem;
import net.minecraft.world.item.alchemy.PotionUtils;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.block.BaseFireBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class ThrownItemProjectile extends AbstractArrow {
   private static final EntityDataAccessor<ItemStack> SOURCE_ITEM;
   protected static final EntityDataAccessor<Integer> LOYALTY_LEVEL;
   private float baseDamage;
   private boolean finishPiercing;
   private int piercingEntity = 0;
   private float weaponDamage;
   public int clientSideReturnKunaiTickCount;

   public ThrownItemProjectile(EntityType<? extends ThrownItemProjectile> type, Level level) {
      super(type, level);
   }

   public ThrownItemProjectile(Level worldIn, LivingEntity shooter, ItemStack pStack, boolean right, float baseDamage) {
      super((EntityType)TensuraEntityTypes.THROWN_ITEM.get(), shooter, worldIn);
      this.setSourceItem(pStack.m_41777_());
      this.baseDamage = baseDamage;
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public ItemStack m_7941_() {
      return this.getSourceItem();
   }

   @Nullable
   protected EntityHitResult m_6351_(Vec3 pStartVec, Vec3 pEndVec) {
      return this.finishPiercing ? null : super.m_6351_(pStartVec, pEndVec);
   }

   private boolean isAcceptableReturnOwner() {
      Entity entity = this.m_37282_();
      if (entity == null) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else {
         return !(entity instanceof ServerPlayer) || !entity.m_5833_();
      }
   }

   public void m_8119_() {
      Entity var2 = this.m_37282_();
      if (var2 instanceof Player) {
         Player owner = (Player)var2;
         BlockPos pos = ThrowerSkill.getHomingPos(owner);
         if (pos != null && !this.finishPiercing && this.m_6084_()) {
            this.homing(pos, ThrowerSkill.getHomingEntity(owner));
         }
      }

      if (this.f_36704_ > 5) {
         this.finishPiercing = true;
      }

      Entity owner = this.m_37282_();
      int level = this.getSourceItem().m_150930_(Items.f_41852_) ? 0 : this.getLoyaltyLevel();
      if (level > 0 && (this.finishPiercing || this.m_36797_()) && owner != null && this.m_6084_()) {
         if (!this.isAcceptableReturnOwner()) {
            if (!this.f_19853_.f_46443_ && this.f_36705_ == Pickup.ALLOWED) {
               this.m_5552_(this.m_7941_(), 0.1F);
            }

            this.m_146870_();
         } else {
            this.m_36790_(true);
            Vec3 vec3 = owner.m_146892_().m_82546_(this.m_20182_());
            this.m_20343_(this.m_20185_(), this.m_20186_() + vec3.f_82480_ * 0.015D * (double)level, this.m_20189_());
            if (this.f_19853_.f_46443_) {
               this.f_19791_ = this.m_20186_();
            }

            double d0 = 0.05D * (double)level;
            this.m_20256_(this.m_20184_().m_82490_(0.95D).m_82549_(vec3.m_82541_().m_82490_(d0)));
            if (this.clientSideReturnKunaiTickCount == 0) {
               this.m_5496_(SoundEvents.f_12516_, 10.0F, 1.0F);
            }

            ++this.clientSideReturnKunaiTickCount;
         }
      }

      super.m_8119_();
   }

   public void m_6901_() {
      int i = this.getLoyaltyLevel();
      if ((this.f_36705_ != Pickup.ALLOWED || i <= 0) && ++this.f_36697_ >= (Integer)TensuraConfig.INSTANCE.entitiesConfig.thrownItem.get()) {
         this.m_146870_();
      }

   }

   private void homing(@Nullable BlockPos pos, @Nullable LivingEntity living) {
      if (pos != null) {
         double posX = this.m_20185_();
         double posY = this.m_20186_();
         double posZ = this.m_20189_();
         double motionX = this.m_20184_().f_82479_;
         double motionY = this.m_20184_().f_82480_;
         double motionZ = this.m_20184_().f_82481_;
         if (pos.m_123341_() != 0 || pos.m_123342_() != 0 || pos.m_123343_() != 0) {
            Vec3 targetVector = new Vec3((double)pos.m_123341_() + 0.5D - posX, (double)pos.m_123342_() + 0.75D - posY, (double)pos.m_123343_() + 0.5D - posZ);
            double length = targetVector.m_82553_();
            targetVector = targetVector.m_82490_(0.3D / length);
            double weight = 0.0D;
            if (length <= 3.0D) {
               weight = (3.0D - length) * 0.3D;
            }

            motionX = (0.9D - weight) * motionX + (0.1D + weight) * targetVector.f_82479_;
            motionY = (0.9D - weight) * motionY + (0.1D + weight) * targetVector.f_82480_;
            motionZ = (0.9D - weight) * motionZ + (0.1D + weight) * targetVector.f_82481_;
         }

         posX += motionX;
         posY += motionY;
         posZ += motionZ;
         this.m_6034_(posX, posY, posZ);
         this.m_20334_(motionX, motionY, motionZ);
         if (living != null && (double)living.m_20270_(this) < 0.5D) {
            this.hitEntity(living);
         }

      }
   }

   protected void m_5790_(EntityHitResult pResult) {
      Entity entity = pResult.m_82443_();
      this.hitEntity(entity);
      ItemStack source = this.getSourceItem();
      if (source.m_41619_()) {
         this.m_146870_();
      }

      if (source.m_41783_() != null && source.m_41783_().m_128471_("FakeItem")) {
         this.m_146870_();
      }

   }

   public void hitEntity(Entity entity) {
      ItemStack sourceStack = this.getSourceItem();
      Item item = sourceStack.m_41720_();
      float damage = this.baseDamage;
      if (this.weaponDamage > 1.0F) {
         damage += this.weaponDamage;
      } else {
         damage += DamageSourceHelper.getWeaponBaseDamage(item);
      }

      if (entity instanceof LivingEntity) {
         LivingEntity livingentity = (LivingEntity)entity;
         damage += EnchantmentHelper.m_44833_(sourceStack, livingentity.m_6336_());
      }

      Entity ownerEntity = this.m_37282_();
      DamageSource damagesource = DamageSource.m_19361_(this, this.m_37282_());
      if (item instanceof HealingPotionItem || item instanceof OrbOfDominationItem) {
         damagesource = damagesource.m_181120_();
         damage = 0.0F;
      }

      if (entity.m_6469_(damagesource, damage)) {
         if (entity.m_6095_() == EntityType.f_20566_) {
            return;
         }

         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            if (this.applySpecialItemEffects(target)) {
               this.m_146870_();
            }

            if (ownerEntity instanceof LivingEntity) {
               LivingEntity owner = (LivingEntity)ownerEntity;
               item.m_7579_(sourceStack, target, owner);
               if (owner instanceof Player) {
                  Player player = (Player)owner;
                  item.m_6880_(sourceStack, player, target, InteractionHand.MAIN_HAND);
               }

               if (owner instanceof ShinjiTanimuraEntity) {
                  ShinjiTanimuraEntity shinji = (ShinjiTanimuraEntity)owner;
                  shinji.applyVirus(target);
               }

               EnchantmentHelper.m_44823_(target, owner);
               EnchantmentHelper.m_44896_(owner, target);
               EngravingEnchantment.doAdditionalAttack(sourceStack, owner, entity, damage);
               entity.m_20254_(3 * sourceStack.getEnchantmentLevel(Enchantments.f_44981_));
               if (this.getSourceItem().m_41613_() <= 0) {
                  this.m_146870_();
               }
            }

            this.m_7761_(target);
         }
      }

      ++this.piercingEntity;
      if (this.piercingEntity >= sourceStack.getEnchantmentLevel(Enchantments.f_44961_)) {
         this.finishPiercing = true;
         this.m_20256_(this.m_20184_().m_82542_(-0.01D, -0.1D, -0.01D));
         this.m_5496_(SoundEvents.f_12514_, 1.0F, 1.0F);
      }

   }

   protected boolean applySpecialItemEffects(LivingEntity target) {
      ItemStack sourceStack = this.getSourceItem();
      Item item = sourceStack.m_41720_();
      if (item.equals(Items.f_41863_)) {
         target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), 100));
         return true;
      } else if (!item.equals(TensuraBlocks.Items.STICKY_COBWEB.get()) && !item.equals(TensuraBlocks.Items.STICKY_STEEL_COBWEB.get())) {
         if (!(item instanceof PotionItem)) {
            if (item.equals(Items.f_42613_)) {
               target.m_20254_(5);
               return true;
            } else {
               return this.spawnEntityItem(sourceStack, this.f_19853_, this.m_20183_(), (BlockHitResult)null, target);
            }
         } else {
            if (!this.f_19853_.f_46443_) {
               Iterator var4 = PotionUtils.m_43547_(sourceStack).iterator();

               while(var4.hasNext()) {
                  MobEffectInstance mobeffectinstance = (MobEffectInstance)var4.next();
                  if (mobeffectinstance.m_19544_().m_8093_()) {
                     mobeffectinstance.m_19544_().m_19461_(this, this.m_37282_(), target, mobeffectinstance.m_19564_(), 1.0D);
                  } else {
                     target.m_7292_(new MobEffectInstance(mobeffectinstance));
                  }
               }
            }

            this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12436_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return true;
         }
      } else {
         target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), 200));
         return true;
      }
   }

   private boolean spawnEntityItem(ItemStack itemstack, Level pLevel, BlockPos blockpos, @Nullable BlockHitResult hitResult, @Nullable LivingEntity target) {
      Item var7 = itemstack.m_41720_();
      if (var7 instanceof DispensibleContainerItem) {
         DispensibleContainerItem item = (DispensibleContainerItem)var7;
         Entity var9 = this.m_37282_();
         Player var10000;
         if (var9 instanceof Player) {
            Player player = (Player)var9;
            var10000 = player;
         } else {
            var10000 = null;
         }

         Player owner = var10000;
         item.m_142131_(owner, pLevel, itemstack, blockpos);
         if (owner != null && target != null) {
            ThoughtCommunicationSkill.attackCommand(this, owner, target, 3.0D);
         }

         if (owner != null && !owner.m_7500_() && item instanceof BucketItem) {
            pLevel.m_7967_(new ItemEntity(pLevel, (double)blockpos.m_123341_(), (double)blockpos.m_123342_(), (double)blockpos.m_123343_(), BucketItem.m_40699_(itemstack, owner)));
         }

         if (!(Boolean)TensuraConfig.INSTANCE.skillsConfig.throwerLiquid.get()) {
            return true;
         } else if (!TensuraGameRules.canSkillGrief(this.f_19853_)) {
            return true;
         } else {
            SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), (ManasSkillInstance)null, blockpos);
            if (!MinecraftForge.EVENT_BUS.post(preGrief) && item.emptyContents(owner, pLevel, blockpos, hitResult, itemstack)) {
               if (owner != null) {
                  if (owner instanceof ServerPlayer) {
                     ServerPlayer serverPlayer = (ServerPlayer)owner;
                     CriteriaTriggers.f_10591_.m_59469_(serverPlayer, blockpos, itemstack);
                  }

                  owner.m_36246_(Stats.f_12982_.m_12902_(itemstack.m_41720_()));
               }

               MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), (ManasSkillInstance)null, blockpos));
            }

            return true;
         }
      } else {
         return false;
      }
   }

   protected void m_6532_(HitResult pResult) {
      Item item = this.getSourceItem().m_41720_();
      if (!item.equals(Items.f_41996_) && !item.equals(Items.f_42693_) && !item.equals(Items.f_42729_)) {
         super.m_6532_(pResult);
      } else {
         float radius = item.equals(Items.f_42729_) ? 3.0F : 2.0F;
         if (TensuraGameRules.canSkillGrief(this.f_19853_)) {
            SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), (ManasSkillInstance)null, this.m_20185_(), this.m_20186_(), this.m_20189_());
            if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
               this.f_19853_.m_46518_(this.m_37282_(), this.m_20185_(), this.m_20186_(), this.m_20189_(), radius, false, BlockInteraction.BREAK);
               MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), (ManasSkillInstance)null, this.m_20185_(), this.m_20186_(), this.m_20189_()));
            }
         } else {
            this.f_19853_.m_46518_(this.m_37282_(), this.m_20185_(), this.m_20186_(), this.m_20189_(), radius, false, BlockInteraction.NONE);
         }

         Type type = pResult.m_6662_();
         if (type == Type.ENTITY) {
            this.f_19853_.m_214171_(GameEvent.f_157777_, pResult.m_82450_(), Context.m_223719_(this, (BlockState)null));
         } else if (type == Type.BLOCK) {
            BlockHitResult blockhitresult = (BlockHitResult)pResult;
            BlockPos blockpos = blockhitresult.m_82425_();
            this.f_19853_.m_220407_(GameEvent.f_157777_, blockpos, Context.m_223719_(this, this.f_19853_.m_8055_(blockpos)));
         }

         this.m_146870_();
      }

   }

   protected void m_8060_(BlockHitResult pResult) {
      super.m_8060_(pResult);
      ItemStack source = this.getSourceItem();
      if (source.m_41619_()) {
         this.m_146870_();
      }

      if (source.m_41783_() != null && source.m_41783_().m_128471_("FakeItem")) {
         this.m_146870_();
      }

      if (source.m_41720_() instanceof PotionItem || source.m_41720_() instanceof HealingPotionItem) {
         this.m_146870_();
      }

      if (!this.f_19853_.m_5776_()) {
         BlockPos blockpos = pResult.m_82425_().m_121945_(pResult.m_82434_());
         if (source.m_41720_() instanceof FireChargeItem) {
            if (TensuraGameRules.canSkillGrief(this.f_19853_) && this.f_19853_.m_46859_(blockpos)) {
               SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.m_37282_(), (ManasSkillInstance)null, blockpos);
               if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                  this.f_19853_.m_46597_(blockpos, BaseFireBlock.m_49245_(this.f_19853_, blockpos));
                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.m_37282_(), (ManasSkillInstance)null, blockpos));
               }

               this.m_146870_();
            }
         } else if (this.spawnEntityItem(source, this.f_19853_, blockpos, pResult, (LivingEntity)null)) {
            this.m_146870_();
         }
      }

   }

   protected boolean m_142470_(Player pPlayer) {
      return super.m_142470_(pPlayer) || this.m_36797_() && pPlayer.m_150109_().m_36054_(this.m_7941_());
   }

   protected SoundEvent m_7239_() {
      return SoundEvents.f_12018_;
   }

   public ItemStack getSourceItem() {
      return (ItemStack)this.f_19804_.m_135370_(SOURCE_ITEM);
   }

   public void setSourceItem(ItemStack pStack) {
      this.f_19804_.m_135381_(SOURCE_ITEM, pStack);
   }

   public int getLoyaltyLevel() {
      return (Integer)this.f_19804_.m_135370_(LOYALTY_LEVEL);
   }

   public void setLoyaltyLevel(int level) {
      this.f_19804_.m_135381_(LOYALTY_LEVEL, level);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(LOYALTY_LEVEL, 0);
      this.f_19804_.m_135372_(SOURCE_ITEM, ((KunaiItem)TensuraToolItems.KUNAI.get()).m_7968_());
   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      if (pCompound.m_128425_("Source Item", 10)) {
         this.setSourceItem(ItemStack.m_41712_(pCompound.m_128469_("Source Item")));
      }

      this.setLoyaltyLevel(pCompound.m_128451_("Loyalty"));
      this.weaponDamage = pCompound.m_128457_("AttackDamage");
      this.finishPiercing = pCompound.m_128471_("DealtDamage");
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128379_("DealtDamage", this.finishPiercing);
      pCompound.m_128350_("AttackDamage", this.weaponDamage);
      pCompound.m_128365_("Source Item", this.getSourceItem().m_41739_(new CompoundTag()));
      pCompound.m_128405_("Loyalty", this.getLoyaltyLevel());
   }

   protected float m_6882_() {
      return 0.6F + (float)this.getSourceItem().getEnchantmentLevel(Enchantments.f_44956_) * 0.05F;
   }

   public void setWeaponDamage(float weaponDamage) {
      this.weaponDamage = weaponDamage;
   }

   static {
      SOURCE_ITEM = SynchedEntityData.m_135353_(ThrownItemProjectile.class, EntityDataSerializers.f_135033_);
      LOYALTY_LEVEL = SynchedEntityData.m_135353_(ThrownItemProjectile.class, EntityDataSerializers.f_135028_);
   }
}
