package com.github.manasmods.tensura.entity.magic.misc;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundAddEntityPacket;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.MovementEmission;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraftforge.common.MinecraftForge;

public class FusionistLandmineEntity extends Entity {
   private static final EntityDataAccessor<Integer> DATA_EXPLOSION_RADIUS;
   @Nullable
   private UUID ownerUUID;
   @Nullable
   private Entity cachedOwner;

   public FusionistLandmineEntity(EntityType<? extends FusionistLandmineEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19794_ = true;
   }

   public FusionistLandmineEntity(Level pLevel, double pX, double pY, double pZ, @Nullable LivingEntity pOwner) {
      this((EntityType)TensuraEntityTypes.LANDMINE.get(), pLevel);
      this.m_6034_(pX, pY, pZ);
      this.setOwner(pOwner);
      this.f_19794_ = true;
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(DATA_EXPLOSION_RADIUS, 2);
   }

   protected MovementEmission m_142319_() {
      return MovementEmission.NONE;
   }

   public boolean m_6087_() {
      return !this.m_213877_();
   }

   public boolean m_6094_() {
      return true;
   }

   public void m_8119_() {
      Entity var2 = this.getOwner();
      if (var2 instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)var2;
         double d0 = this.f_19796_.m_188583_() * 0.02D;
         double d1 = this.f_19796_.m_188583_() * 0.02D;
         double d2 = this.f_19796_.m_188583_() * 0.02D;
         TensuraParticleHelper.spawnParticlesToOnePLayer(player, DustParticleOptions.f_123656_, this.m_20208_(0.5D), this.m_20227_(1.5D * this.f_19796_.m_188500_() - 0.5D), this.m_20262_(0.5D), 0, d0, d1, d2, 1.0D, false);
      }
   }

   public void m_7334_(Entity pEntity) {
      if (this.targetNotAlly(pEntity)) {
         this.trigger(pEntity.m_20185_(), pEntity.m_20186_() + (double)(pEntity.m_20206_() / 2.0F), pEntity.m_20189_());
      }

   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      Entity pEntity = pSource.m_7639_();
      if (pEntity != null) {
         if (this.targetNotAlly(pEntity)) {
            this.trigger(pEntity.m_20185_(), pEntity.m_20186_() + (double)(pEntity.m_20206_() / 2.0F), pEntity.m_20189_());
         } else {
            Entity var5 = this.getOwner();
            if (var5 instanceof Player) {
               Player player = (Player)var5;
               if (pEntity == player) {
                  this.m_146870_();
                  player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11937_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      }

      return false;
   }

   public InteractionResult m_6096_(Player player, InteractionHand pHand) {
      if (this.targetNotAlly(player)) {
         this.trigger(player.m_20185_(), player.m_20186_() + (double)(player.m_20206_() / 2.0F), player.m_20189_());
      } else if (this.getOwner() == player) {
         this.m_146870_();
         player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11937_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

      return InteractionResult.SUCCESS;
   }

   public boolean targetNotAlly(Entity entity) {
      if (this.getOwner() == null) {
         return true;
      } else if (entity.m_7307_(this.getOwner())) {
         return false;
      } else {
         return entity != this.getOwner();
      }
   }

   public void trigger(double x, double y, double z) {
      BlockInteraction interaction = TensuraGameRules.canSkillGrief(this.f_19853_) ? BlockInteraction.BREAK : BlockInteraction.NONE;
      SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(this.getOwner(), (ManasSkillInstance)null, this.m_20183_());
      if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
         this.f_19853_.m_46511_(this.getOwner(), x, y, z, (float)this.getRadius(), interaction);
         this.f_19853_.m_46511_(this.getOwner(), x, y, z, (float)this.getRadius(), interaction);
         MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(this.getOwner(), (ManasSkillInstance)null, this.m_20183_()));
      }

      this.m_146870_();
   }

   protected void m_7380_(CompoundTag pCompound) {
      pCompound.m_128376_("Radius", (short)this.getRadius());
      if (this.ownerUUID != null) {
         pCompound.m_128362_("Owner", this.ownerUUID);
      }

   }

   protected void m_7378_(CompoundTag pCompound) {
      this.setRadius(pCompound.m_128448_("Radius"));
      if (pCompound.m_128403_("Owner")) {
         this.ownerUUID = pCompound.m_128342_("Owner");
      }

   }

   public void setRadius(int pLife) {
      this.f_19804_.m_135381_(DATA_EXPLOSION_RADIUS, pLife);
   }

   public int getRadius() {
      return (Integer)this.f_19804_.m_135370_(DATA_EXPLOSION_RADIUS);
   }

   public void setOwner(@Nullable Entity pOwner) {
      if (pOwner != null) {
         this.ownerUUID = pOwner.m_20148_();
         this.cachedOwner = pOwner;
      }

   }

   @Nullable
   public Entity getOwner() {
      if (this.cachedOwner != null && !this.cachedOwner.m_213877_()) {
         return this.cachedOwner;
      } else if (this.ownerUUID != null && this.f_19853_ instanceof ServerLevel) {
         this.cachedOwner = ((ServerLevel)this.f_19853_).m_8791_(this.ownerUUID);
         return this.cachedOwner;
      } else {
         return null;
      }
   }

   protected boolean ownedBy(Entity pEntity) {
      return pEntity.m_20148_().equals(this.ownerUUID);
   }

   public Packet<?> m_5654_() {
      Entity entity = this.getOwner();
      return new ClientboundAddEntityPacket(this, entity == null ? 0 : entity.m_19879_());
   }

   public void m_141965_(ClientboundAddEntityPacket pPacket) {
      super.m_141965_(pPacket);
      Entity entity = this.f_19853_.m_6815_(pPacket.m_131509_());
      if (entity != null) {
         this.setOwner(entity);
      }

   }

   static {
      DATA_EXPLOSION_RADIUS = SynchedEntityData.m_135353_(FusionistLandmineEntity.class, EntityDataSerializers.f_135028_);
   }
}
