package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.CaterpillarTryToCocoonGoal;
import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.template.ClimbingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.MothVariant;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.awt.Color;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.Registry;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedAttackGoal;
import net.minecraft.world.entity.ai.goal.TemptGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.predicate.BlockStatePredicate;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class HellCaterpillarEntity extends ClimbingEntity implements IAnimatable, SpittingRangedMonster {
   private static final EntityDataAccessor<Integer> COCOON_TYPE;
   private static final EntityDataAccessor<Integer> COCOON_TICKS;
   private static final EntityDataAccessor<Integer> GRASS_EATEN;
   private static final EntityDataAccessor<Boolean> DONE_COCOON;
   private static final EntityDataAccessor<Boolean> START_COCOON;
   private static final EntityDataAccessor<Boolean> SILK_SHOOTING;
   private static final EntityDataAccessor<Direction> FACE_ATTACHED;
   private static final Direction[] DIRECTIONS;
   protected boolean didSpit;
   private int startCocoon;
   public float attachChangeProgress = 0.0F;
   public float prevAttachChangeProgress = 0.0F;
   public Direction prevAttachDir;
   private final AnimationFactory factory;
   public static final AnimationBuilder IDLE;
   public static final AnimationBuilder WALKING;
   public static final AnimationBuilder COCOONED;
   public static final AnimationBuilder PRE_COCOON;
   public static final AnimationBuilder COCOONING;
   public static final AnimationBuilder SILK_SHOT;

   public HellCaterpillarEntity(EntityType<? extends HellCaterpillarEntity> type, Level level) {
      super(type, level);
      this.prevAttachDir = Direction.DOWN;
      this.factory = GeckoLibUtil.createFactory(this);
      this.f_21364_ = 10;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 8.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22281_, 1.0D).m_22268_(Attributes.f_22279_, 0.15000000596046448D).m_22268_(Attributes.f_22278_, 0.2D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new HellCaterpillarEntity.CocoonStickOnLeavesGoal(this));
      this.f_21345_.m_25352_(2, new CaterpillarTryToCocoonGoal(this, 1.0D, 16));
      this.f_21345_.m_25352_(3, new RangedAttackGoal(this, 1.25D, 40, 20.0F));
      this.f_21345_.m_25352_(3, new FloatGoal(this));
      this.f_21346_.m_25352_(4, new HellCaterpillarEntity.SpitThenGoGoal(this));
      this.f_21345_.m_25352_(5, new HellCaterpillarEntity.CaterpillarEatGrassGoal(this));
      this.f_21345_.m_25352_(6, new HellCaterpillarEntity.CaterpillarFollowFoodGoal(this, 1.0D));
      this.f_21345_.m_25352_(7, new HellCaterpillarEntity.CaterpillarWanderGoal(this, 40, 1.0D, 10, 7));
      this.f_21345_.m_25352_(8, new HellCaterpillarEntity.CaterpillarLookAroundGoal(this));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(FACE_ATTACHED, Direction.DOWN);
      this.f_19804_.m_135372_(START_COCOON, Boolean.FALSE);
      this.f_19804_.m_135372_(DONE_COCOON, Boolean.FALSE);
      this.f_19804_.m_135372_(COCOON_TYPE, 0);
      this.f_19804_.m_135372_(GRASS_EATEN, 0);
      this.f_19804_.m_135372_(COCOON_TICKS, 0);
      this.f_19804_.m_135372_(SILK_SHOOTING, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("StartCocoon", this.hasStartedCocoon());
      compound.m_128379_("Cocooned", this.isCocooned());
      compound.m_128405_("CocoonType", this.getCocoonType());
      compound.m_128405_("GrassEaten", this.getGrassEaten());
      compound.m_128405_("CocoonTicks", this.getCocoonTicks());
      compound.m_128379_("SilkShooting", this.isShootingSilk());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setStartCocoon(compound.m_128471_("StartCocoon"));
      this.setCocooned(compound.m_128471_("Cocooned"));
      this.setCocoonType(compound.m_128451_("CocoonType"));
      this.setGrassEaten(compound.m_128451_("GrassEaten"));
      this.setCocoonTick(compound.m_128451_("CocoonTicks"));
      this.setShootingSilk(compound.m_128471_("SilkShooting"));
   }

   public void m_7350_(EntityDataAccessor<?> accessor) {
      super.m_7350_(accessor);
      if (FACE_ATTACHED.equals(accessor)) {
         this.prevAttachChangeProgress = 0.0F;
         this.attachChangeProgress = 0.0F;
      } else if (DONE_COCOON.equals(accessor)) {
         this.m_6210_();
      }

   }

   public int getCocoonTicks() {
      return (Integer)this.f_19804_.m_135370_(COCOON_TICKS);
   }

   public void setCocoonTick(int cocoonTick) {
      this.f_19804_.m_135381_(COCOON_TICKS, cocoonTick);
   }

   public int getGrassEaten() {
      return (Integer)this.f_19804_.m_135370_(GRASS_EATEN);
   }

   public void setGrassEaten(int grassEaten) {
      this.f_19804_.m_135381_(GRASS_EATEN, grassEaten);
   }

   public int getCocoonType() {
      return (Integer)this.f_19804_.m_135370_(COCOON_TYPE);
   }

   public void setCocoonType(int cocoonType) {
      this.f_19804_.m_135381_(COCOON_TYPE, cocoonType);
   }

   public boolean hasStartedCocoon() {
      return (Boolean)this.f_19804_.m_135370_(START_COCOON);
   }

   public void setStartCocoon(boolean started) {
      this.f_19804_.m_135381_(START_COCOON, started);
   }

   public boolean isCocooned() {
      return (Boolean)this.f_19804_.m_135370_(DONE_COCOON);
   }

   public void setCocooned(boolean cocooned) {
      this.f_19804_.m_135381_(DONE_COCOON, cocooned);
   }

   public boolean isShootingSilk() {
      return (Boolean)this.f_19804_.m_135370_(SILK_SHOOTING);
   }

   public void setShootingSilk(boolean shootingSilk) {
      this.f_19804_.m_135381_(SILK_SHOOTING, shootingSilk);
   }

   public void makeCocoon() {
      this.setCocooned(Boolean.TRUE);
      ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22284_))).m_22100_(10.0D);
      ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22278_))).m_22100_(1.0D);
      ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22279_))).m_22100_(0.0D);
      if ((new Random()).nextInt(20) == 10) {
         this.setCocoonType(1);
      }

   }

   protected ResourceLocation m_7582_() {
      if (this.isCocooned()) {
         return this.getCocoonType() == 1 ? new ResourceLocation(Registry.f_122826_.m_7981_(this.m_6095_()).m_135827_(), "entities/gehenna_cocoon") : new ResourceLocation(Registry.f_122826_.m_7981_(this.m_6095_()).m_135827_(), "entities/hell_cocoon");
      } else {
         return super.m_7582_();
      }
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.isCocooned() ? EntityDimensions.m_20398_(entitydimensions.f_20377_, 2.0F) : entitydimensions;
   }

   public boolean m_5957_() {
      return false;
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      HellCaterpillarEntity hellCaterpillar = (HellCaterpillarEntity)((EntityType)TensuraEntityTypes.HELL_CATERPILLAR.get()).m_20615_(pLevel);
      if (hellCaterpillar == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            hellCaterpillar.m_21816_(uuid);
            hellCaterpillar.m_7105_(true);
         }

         return hellCaterpillar;
      }
   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      if (this.m_6898_(itemstack)) {
         if (!pPlayer.m_7500_()) {
            itemstack.m_41774_(1);
         }

         if (this.m_21223_() < this.m_21233_()) {
            this.m_5634_(1.0F);
         } else {
            this.m_8035_();
         }

         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.SMALL_CHEW.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setGrassEaten(this.getGrassEaten() + 1);
      this.m_5634_(5.0F);
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.CATERPILLAR_FOOD);
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean canTrample(BlockState state, BlockPos pos, float fallDistance) {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   protected void m_7840_(double y, boolean onGroundIn, BlockState state, BlockPos pos) {
   }

   protected float getClimbSpeedMultiplier() {
      return 0.5F;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || super.m_6673_(source);
   }

   public Direction getAttachmentFacing() {
      return (Direction)this.f_19804_.m_135370_(FACE_ATTACHED);
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!pState.m_204336_(TensuraTags.Blocks.WEB_BLOCKS)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected float m_6041_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_6041_();
   }

   protected float m_20098_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_20098_();
   }

   public static boolean checkCaterpillarSpawnRules(EntityType<HellCaterpillarEntity> pCaterpillar, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184232_) && m_217057_(pCaterpillar, pLevel, pSpawnType, pPos, pRandom);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.hellCaterpillarSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public boolean m_6469_(DamageSource source, float amount) {
      boolean hurt = super.m_6469_(source, amount);
      if (hurt) {
         Entity var5 = source.m_7639_();
         if (var5 instanceof LivingEntity) {
            LivingEntity damageSource = (LivingEntity)var5;
            if (!damageSource.m_6084_()) {
               return true;
            }

            if (damageSource instanceof Player) {
               Player player = (Player)damageSource;
               if (player.m_7500_() || player.m_5833_()) {
                  return true;
               }
            }

            List<HellMothEntity> list = this.f_19853_.m_6443_(HellMothEntity.class, this.m_20191_().m_82400_((double)(Integer)SpawnRateConfig.INSTANCE.hellMothAwarenessRange.get()), (entity) -> {
               return !entity.m_21824_();
            });
            if (!list.isEmpty()) {
               list.forEach((hellMoth) -> {
                  hellMoth.m_6710_(damageSource);
               });
            }
         }
      }

      return hurt;
   }

   protected SoundEvent m_7515_() {
      return (SoundEvent)TensuraSoundEvents.CATERPILLAR_AMBIENT.get();
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return (SoundEvent)TensuraSoundEvents.MOTH_HURT.get();
   }

   protected SoundEvent m_5592_() {
      return (SoundEvent)TensuraSoundEvents.MOTH_DEATH.get();
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.isCocooned() && this.m_6084_()) {
         this.setCocoonTick(this.getCocoonTicks() + 1);
         if (this.getCocoonTicks() >= 2400) {
            Level level = this.m_9236_();
            CompoundTag tag = this.serializeNBT();
            this.m_146870_();
            HellMothEntity moth = new HellMothEntity((EntityType)TensuraEntityTypes.HELL_MOTH.get(), level);
            moth.m_20258_(tag);
            if (level instanceof ServerLevel) {
               ServerLevel serverLevel = (ServerLevel)level;
               moth.m_6518_(serverLevel, level.m_6436_(moth.m_20183_()), MobSpawnType.CONVERSION, (SpawnGroupData)null, (CompoundTag)null);
            }

            if (this.getCocoonType() == 1) {
               moth.setVariant(MothVariant.GEHENNA);
            }

            RaceHelper.applyBaseAttribute((AttributeSupplier)HellMothEntity.setAttributes(), moth);
            moth.m_21153_(moth.m_21233_());
            RaceHelper.updateSpiritualHP(moth);
            RaceHelper.updateEntityEPCount(moth);
            moth.m_146762_(-24000);
            int randomSize = (new Random()).nextInt(10);
            moth.setBabySize(randomSize >= 8 ? 2 : 1);
            moth.setFlying(true);
            level.m_46796_(2009, moth.m_20183_(), (new Color(255, 255, 255)).getRGB());
            level.m_5594_((Player)null, moth.m_20183_(), SoundEvents.f_12639_, SoundSource.PLAYERS, 1.0F, 1.0F);
            level.m_7967_(moth);
            return;
         }
      }

      if (this.hasStartedCocoon()) {
         ++this.startCocoon;
         if (this.startCocoon >= 30) {
            this.setStartCocoon(Boolean.FALSE);
            this.startCocoon = 0;
         }
      }

      if (this.isShootingSilk()) {
         ++this.startCocoon;
         if (this.startCocoon >= 5) {
            this.setShootingSilk(Boolean.FALSE);
            this.startCocoon = 0;
         }
      }

      this.f_20883_ = Mth.m_14148_(this.f_20884_, this.f_20883_, (float)this.m_8085_());
      this.prevAttachChangeProgress = this.attachChangeProgress;
      if (this.prevAttachDir != this.getAttachmentFacing()) {
         if (this.attachChangeProgress < 5.0F) {
            ++this.attachChangeProgress;
         } else if (this.attachChangeProgress >= 5.0F) {
            this.prevAttachDir = this.getAttachmentFacing();
         }
      } else {
         this.attachChangeProgress = 5.0F;
      }

      Vec3 vector3d = this.m_20184_();
      if (!this.f_19853_.f_46443_) {
         this.setBesideClimbableBlock(collidingWall(this) || this.f_19863_ && !this.m_20096_());
         if (!this.m_20096_() && !this.m_20072_() && !this.m_20077_()) {
            if (this.f_19863_) {
               this.f_19804_.m_135381_(FACE_ATTACHED, Direction.UP);
            } else {
               Direction closestDirection = Direction.DOWN;
               double closestDistance = 100.0D;
               Direction[] var5 = DIRECTIONS;
               int var6 = var5.length;

               for(int var7 = 0; var7 < var6; ++var7) {
                  Direction dir = var5[var7];
                  BlockPos antPos = new BlockPos(Mth.m_14107_(this.m_20185_()), Mth.m_14107_(this.m_20186_()), Mth.m_14107_(this.m_20189_()));
                  BlockPos offsetPos = antPos.m_121945_(dir);
                  Vec3 offset = Vec3.m_82512_(offsetPos);
                  if (closestDistance > this.m_20182_().m_82554_(offset) && this.f_19853_.m_46578_(offsetPos, this, dir.m_122424_())) {
                     closestDistance = this.m_20182_().m_82554_(offset);
                     closestDirection = dir;
                  }
               }

               this.f_19804_.m_135381_(FACE_ATTACHED, closestDirection);
            }
         } else {
            this.f_19804_.m_135381_(FACE_ATTACHED, Direction.DOWN);
         }
      }

      if (this.getAttachmentFacing() == Direction.UP) {
         this.m_20242_(true);
         this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.1D, 0.0D));
      } else {
         this.m_20242_(false);
         if (this.getAttachmentFacing() != Direction.DOWN && vector3d.f_82480_ < 0.0D) {
            if (!this.f_19862_) {
               Vec3 vec = Vec3.m_82528_(this.getAttachmentFacing().m_122436_());
               this.m_20256_(this.m_20184_().m_82549_(vec.m_82541_().m_82542_(0.10000000149011612D, 0.10000000149011612D, 0.10000000149011612D)));
            }

            if (!this.f_19861_) {
               this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
               if (this.m_6147_()) {
                  this.m_20256_(vector3d.m_82542_(1.0D, 0.5D, 1.0D));
               }
            }
         }
      }

   }

   public void spitParticle(MonsterSpitProjectile projectile) {
      this.particleSpawning(projectile, ParticleTypes.f_123764_, 5);
   }

   public void m_6504_(@NotNull LivingEntity target, float pDistanceFactor) {
      this.setShootingSilk(Boolean.TRUE);
      MonsterSpitProjectile spit = new MonsterSpitProjectile(this.f_19853_, this);
      spit.m_7678_(this.m_20185_(), this.m_20186_() + 1.0D, this.m_20189_(), this.m_146908_(), this.m_146909_());
      double d0 = target.m_20186_();
      double d1 = target.m_20185_() - this.m_20185_();
      double d2 = d0 - spit.m_20186_();
      double d3 = target.m_20189_() - this.m_20189_();
      double f = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
      spit.m_6686_(d1, d2 + f, d3, 1.2F, 0.0F);
      this.f_19853_.m_7967_(spit);
   }

   public void spitHit(LivingEntity pTarget) {
      if (!(pTarget instanceof HellMothEntity)) {
         if (!(pTarget instanceof HellCaterpillarEntity)) {
            this.setDidSpit(true);
            if (pTarget.m_6469_(DamageSource.m_19370_(this), 5.0F)) {
               if (!(pTarget.m_20206_() <= 3.0F) && !(pTarget.m_20205_() <= 3.0F)) {
                  pTarget.m_147207_(new MobEffectInstance(MobEffects.f_19597_, 300, 0, false, false, true), this);
               } else {
                  pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), 300, 0, false, false, true), this);
                  pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SILENCE.get(), 300, 0, false, false, true), this);
               }
            }

         }
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.hasStartedCocoon()) {
         event.getController().setAnimation(PRE_COCOON);
      } else if (this.isCocooned()) {
         event.getController().setAnimation(COCOONED);
      } else if (!event.isMoving() && (this.getAttachmentFacing().equals(Direction.DOWN) || this.getAttachmentFacing().equals(Direction.UP))) {
         event.getController().setAnimation(IDLE);
      } else {
         event.getController().setAnimation(WALKING);
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState cocoonPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.hasStartedCocoon()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(COCOONING);
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState silkShootingPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.isShootingSilk()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(SILK_SHOT);
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "cocoonController", 0.0F, this::cocoonPredicate));
      data.addAnimationController(new AnimationController(this, "silkShootingController", 0.0F, this::silkShootingPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public void setDidSpit(boolean didSpit) {
      this.didSpit = didSpit;
   }

   static {
      COCOON_TYPE = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135028_);
      COCOON_TICKS = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135028_);
      GRASS_EATEN = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135028_);
      DONE_COCOON = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135035_);
      START_COCOON = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135035_);
      SILK_SHOOTING = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135035_);
      FACE_ATTACHED = SynchedEntityData.m_135353_(HellCaterpillarEntity.class, EntityDataSerializers.f_135040_);
      DIRECTIONS = new Direction[]{Direction.NORTH, Direction.EAST, Direction.SOUTH, Direction.WEST};
      IDLE = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.idle", EDefaultLoopTypes.LOOP);
      WALKING = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.walking", EDefaultLoopTypes.LOOP);
      COCOONED = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.cocooned", EDefaultLoopTypes.LOOP);
      PRE_COCOON = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.cocoon_pose", EDefaultLoopTypes.LOOP);
      COCOONING = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.cocooning", EDefaultLoopTypes.PLAY_ONCE);
      SILK_SHOT = (new AnimationBuilder()).addAnimation("animation.hell_caterpillar.silk_shot", EDefaultLoopTypes.PLAY_ONCE);
   }

   static class CocoonStickOnLeavesGoal extends Goal {
      private final HellCaterpillarEntity caterpillar;

      public CocoonStickOnLeavesGoal(HellCaterpillarEntity caterpillarEntity) {
         this.caterpillar = caterpillarEntity;
         this.m_7021_(EnumSet.of(Flag.MOVE));
      }

      public boolean m_8036_() {
         return this.caterpillar.isCocooned() && this.getLeavesBlock(this.caterpillar.m_20183_(), this.caterpillar.f_19853_);
      }

      public boolean m_183429_() {
         return true;
      }

      public void m_8037_() {
         this.caterpillar.f_19804_.m_135381_(HellCaterpillarEntity.FACE_ATTACHED, Direction.UP);
         this.caterpillar.m_20256_(this.caterpillar.m_20184_().m_82520_(0.0D, 0.10000000149011612D, 0.0D));
      }

      private boolean getLeavesBlock(BlockPos pPos, BlockGetter pLevel) {
         BlockPos[] pos = new BlockPos[]{pPos.m_7494_(), pPos.m_7494_().m_7494_(), pPos.m_7494_().m_7494_().m_7494_()};
         boolean flag = false;
         BlockPos[] var5 = pos;
         int var6 = pos.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            BlockPos blockpos = var5[var7];
            if (pLevel.m_8055_(blockpos).m_204336_(BlockTags.f_13035_) || pLevel.m_8055_(blockpos).m_204336_(BlockTags.f_13106_)) {
               flag = true;
            }
         }

         return flag;
      }
   }

   static class SpitThenGoGoal extends HurtByTargetGoal {
      protected final HellCaterpillarEntity hellCaterpillar;

      public SpitThenGoGoal(HellCaterpillarEntity caterpillar) {
         super(caterpillar, new Class[0]);
         this.hellCaterpillar = caterpillar;
      }

      public boolean m_8036_() {
         if (this.hellCaterpillar.hasStartedCocoon()) {
            return false;
         } else {
            return this.hellCaterpillar.isCocooned() ? false : super.m_8036_();
         }
      }

      public boolean m_8045_() {
         if (this.hellCaterpillar.didSpit) {
            this.hellCaterpillar.setDidSpit(false);
            return false;
         } else {
            return super.m_8045_();
         }
      }
   }

   static class CaterpillarEatGrassGoal extends EatBlockGoal {
      protected final HellCaterpillarEntity hellCaterpillar;

      public CaterpillarEatGrassGoal(HellCaterpillarEntity caterpillar) {
         super(caterpillar);
         this.hellCaterpillar = caterpillar;
      }

      public boolean m_8036_() {
         if (this.hellCaterpillar.m_217043_().m_188503_(200) != 0) {
            return false;
         } else {
            BlockPos blockpos = this.hellCaterpillar.m_20183_();
            return BlockStatePredicate.m_61287_(Blocks.f_50034_).test(this.hellCaterpillar.f_19853_.m_8055_(blockpos)) ? true : this.hellCaterpillar.f_19853_.m_8055_(blockpos.m_7495_()).m_60713_(Blocks.f_50440_);
         }
      }
   }

   static class CaterpillarFollowFoodGoal extends TemptGoal {
      HellCaterpillarEntity caterpillar;

      public CaterpillarFollowFoodGoal(HellCaterpillarEntity pMob, double pSpeedModifier) {
         super(pMob, pSpeedModifier, Ingredient.m_204132_(TensuraTags.Items.CATERPILLAR_FOOD), false);
         this.caterpillar = pMob;
      }

      public boolean m_8036_() {
         if (this.caterpillar.m_5448_() != null) {
            return false;
         } else if (this.caterpillar.m_21827_()) {
            return false;
         } else if (this.caterpillar.isCocooned()) {
            return false;
         } else if (this.caterpillar.hasStartedCocoon()) {
            return false;
         } else {
            return this.caterpillar.m_21826_() != null && this.f_25925_ != null && !this.caterpillar.m_21826_().equals(this.f_25925_) ? false : super.m_8036_();
         }
      }
   }

   static class CaterpillarWanderGoal extends TensuraTamableEntity.WanderAroundPosGoal {
      protected HellCaterpillarEntity hellCaterpillar;

      public CaterpillarWanderGoal(HellCaterpillarEntity mob, int interval, double speed, int xzRange, int yRange) {
         super(mob, interval, speed, xzRange, yRange);
         this.hellCaterpillar = mob;
      }

      public boolean m_8036_() {
         if (!this.hellCaterpillar.isCocooned() && !this.hellCaterpillar.hasStartedCocoon()) {
            return this.hellCaterpillar.getGrassEaten() >= 50 ? false : super.m_8036_();
         } else {
            return false;
         }
      }
   }

   static class CaterpillarLookAroundGoal extends RandomLookAroundGoal {
      private final HellCaterpillarEntity caterpillar;

      public CaterpillarLookAroundGoal(HellCaterpillarEntity pMob) {
         super(pMob);
         this.caterpillar = pMob;
      }

      public boolean m_8036_() {
         if (!this.caterpillar.isCocooned() && !this.caterpillar.hasStartedCocoon()) {
            return this.caterpillar.getGrassEaten() >= 50 ? false : super.m_8036_();
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         return !this.caterpillar.isCocooned() && !this.caterpillar.hasStartedCocoon() ? super.m_8045_() : false;
      }
   }
}
