package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LizardmanEntity;
import com.github.manasmods.tensura.entity.variant.LizardmanVariant;
import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ShieldItem;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.geo.render.built.GeoBone;
import software.bernie.geckolib3.renderers.geo.ExtendedGeoEntityRenderer;

public class LizardmanRenderer extends ExtendedGeoEntityRenderer<LizardmanEntity> {
   protected ItemStack mainHandItem;
   protected ItemStack offHandItem;
   protected ItemStack helmetItem;
   protected ItemStack chestplateItem;
   protected ItemStack leggingsItem;
   protected ItemStack bootsItem;

   public LizardmanRenderer(Context renderManager) {
      super(renderManager, new LizardmanModel());
      this.f_114477_ = 0.2F;
   }

   public ResourceLocation getTextureLocation(LizardmanEntity instance) {
      return (ResourceLocation)LizardmanVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public void renderEarly(LizardmanEntity lizardman, PoseStack poseStack, float partialTick, MultiBufferSource bufferSource, VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float partialTicks) {
      super.renderEarly(lizardman, poseStack, partialTick, bufferSource, buffer, packedLight, packedOverlay, red, green, blue, partialTicks);
      this.mainHandItem = lizardman.m_6844_(EquipmentSlot.MAINHAND);
      this.offHandItem = lizardman.m_6844_(EquipmentSlot.OFFHAND);
      this.helmetItem = lizardman.m_6844_(EquipmentSlot.HEAD);
      this.chestplateItem = lizardman.m_6844_(EquipmentSlot.CHEST);
      this.leggingsItem = lizardman.m_6844_(EquipmentSlot.LEGS);
      this.bootsItem = lizardman.m_6844_(EquipmentSlot.FEET);
      if (lizardman.m_6162_()) {
         poseStack.m_85841_(0.5F, 0.5F, 0.5F);
      }

   }

   protected ItemStack getHeldItemForBone(String boneName, LizardmanEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1569438897:
         if (boneName.equals("rightItem")) {
            var4 = 1;
         }
         break;
      case 1718105018:
         if (boneName.equals("leftItem")) {
            var4 = 0;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
         var10000 = currentEntity.m_21526_() ? this.mainHandItem : this.offHandItem;
         break;
      case 1:
         var10000 = currentEntity.m_21526_() ? this.offHandItem : this.mainHandItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected TransformType getCameraTransformForItemAtBone(ItemStack boneItem, String boneName) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1569438897:
         if (boneName.equals("rightItem")) {
            var4 = 1;
         }
         break;
      case 1718105018:
         if (boneName.equals("leftItem")) {
            var4 = 0;
         }
      }

      TransformType var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = TransformType.THIRD_PERSON_RIGHT_HAND;
         break;
      default:
         var10000 = TransformType.NONE;
      }

      return var10000;
   }

   protected void preRenderItem(PoseStack stack, ItemStack item, String boneName, LizardmanEntity currentEntity, IBone bone) {
      if (item == this.mainHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, -0.25D);
         }
      } else if (item == this.offHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, 0.25D);
            stack.m_85845_(Vector3f.f_122225_.m_122240_(180.0F));
         }
      }

   }

   protected void postRenderItem(PoseStack matrixStack, ItemStack item, String boneName, LizardmanEntity currentEntity, IBone bone) {
   }

   protected ItemStack getArmorForBone(String boneName, LizardmanEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1432759002:
         if (boneName.equals("leftBootArmor")) {
            var4 = 0;
         }
         break;
      case -1392428367:
         if (boneName.equals("rightBootArmor")) {
            var4 = 1;
         }
         break;
      case -357763848:
         if (boneName.equals("leftLegArmor")) {
            var4 = 2;
         }
         break;
      case -246526145:
         if (boneName.equals("headArmor")) {
            var4 = 7;
         }
         break;
      case 474821133:
         if (boneName.equals("rightLegArmor")) {
            var4 = 3;
         }
         break;
      case 770579818:
         if (boneName.equals("leftArmArmor")) {
            var4 = 6;
         }
         break;
      case 1225256125:
         if (boneName.equals("bodyArmor")) {
            var4 = 4;
         }
         break;
      case 1603164799:
         if (boneName.equals("rightArmArmor")) {
            var4 = 5;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = this.bootsItem;
         break;
      case 2:
      case 3:
         var10000 = this.leggingsItem;
         break;
      case 4:
      case 5:
      case 6:
         var10000 = this.chestplateItem;
         break;
      case 7:
         var10000 = this.helmetItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected EquipmentSlot getEquipmentSlotForArmorBone(String boneName, LizardmanEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1432759002:
         if (boneName.equals("leftBootArmor")) {
            var4 = 0;
         }
         break;
      case -1392428367:
         if (boneName.equals("rightBootArmor")) {
            var4 = 1;
         }
         break;
      case -357763848:
         if (boneName.equals("leftLegArmor")) {
            var4 = 2;
         }
         break;
      case -246526145:
         if (boneName.equals("headArmor")) {
            var4 = 7;
         }
         break;
      case 474821133:
         if (boneName.equals("rightLegArmor")) {
            var4 = 3;
         }
         break;
      case 770579818:
         if (boneName.equals("leftArmArmor")) {
            var4 = 5;
         }
         break;
      case 1225256125:
         if (boneName.equals("bodyArmor")) {
            var4 = 6;
         }
         break;
      case 1603164799:
         if (boneName.equals("rightArmArmor")) {
            var4 = 4;
         }
      }

      EquipmentSlot var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = EquipmentSlot.FEET;
         break;
      case 2:
      case 3:
         var10000 = EquipmentSlot.LEGS;
         break;
      case 4:
         var10000 = !currentEntity.m_21526_() ? EquipmentSlot.MAINHAND : EquipmentSlot.OFFHAND;
         break;
      case 5:
         var10000 = currentEntity.m_21526_() ? EquipmentSlot.MAINHAND : EquipmentSlot.OFFHAND;
         break;
      case 6:
         var10000 = EquipmentSlot.CHEST;
         break;
      case 7:
         var10000 = EquipmentSlot.HEAD;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected ModelPart getArmorPartForBone(String name, HumanoidModel<?> armorModel) {
      byte var4 = -1;
      switch(name.hashCode()) {
      case -1432759002:
         if (name.equals("leftBootArmor")) {
            var4 = 0;
         }
         break;
      case -1392428367:
         if (name.equals("rightBootArmor")) {
            var4 = 2;
         }
         break;
      case -357763848:
         if (name.equals("leftLegArmor")) {
            var4 = 1;
         }
         break;
      case -246526145:
         if (name.equals("headArmor")) {
            var4 = 7;
         }
         break;
      case 474821133:
         if (name.equals("rightLegArmor")) {
            var4 = 3;
         }
         break;
      case 770579818:
         if (name.equals("leftArmArmor")) {
            var4 = 5;
         }
         break;
      case 1225256125:
         if (name.equals("bodyArmor")) {
            var4 = 6;
         }
         break;
      case 1603164799:
         if (name.equals("rightArmArmor")) {
            var4 = 4;
         }
      }

      ModelPart var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = armorModel.f_102814_;
         break;
      case 2:
      case 3:
         var10000 = armorModel.f_102813_;
         break;
      case 4:
         var10000 = armorModel.f_102811_;
         break;
      case 5:
         var10000 = armorModel.f_102812_;
         break;
      case 6:
         var10000 = armorModel.f_102810_;
         break;
      case 7:
         var10000 = armorModel.f_102808_;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected BlockState getHeldBlockForBone(String boneName, LizardmanEntity currentEntity) {
      return null;
   }

   protected void preRenderBlock(PoseStack stack, BlockState block, String boneName, LizardmanEntity currentEntity) {
   }

   protected void postRenderBlock(PoseStack stack, BlockState block, String boneName, LizardmanEntity currentEntity) {
   }

   protected ResourceLocation getTextureForBone(String boneName, LizardmanEntity animatable) {
      return null;
   }

   protected boolean isArmorBone(GeoBone bone) {
      return bone.getName().endsWith("Armor");
   }
}
