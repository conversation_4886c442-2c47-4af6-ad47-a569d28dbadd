package com.github.b4ndithelps.tennogamenolife.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITeleportation;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.projectile.SpatialArrowProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.entity.human.OtherworlderEntity;
import com.github.manasmods.tensura.entity.human.IOtherworlder;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.b4ndithelps.tennogamenolife.registry.sound.TenreincarnationSounds;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.damagesource.DamageSource;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.StringTag;
import java.util.List;
import java.util.UUID;
import java.util.ArrayList;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.phys.Vec3;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.OwnerHurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.OwnerHurtTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.SpawnPlacements;
import net.minecraft.world.level.NaturalSpawner;
import net.minecraft.util.Mth;
import com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity;
import com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems;

import net.minecraftforge.common.ForgeMod;

/**
 * 休比
 */
public class XiubiEntity extends OtherworlderEntity implements ITeleportation {

    // 机铠动画状态管理
    private boolean hasWeapon = false;
    private boolean previousHasWeapon = false;
    private int weaponAnimationTimer = 0;
    private int cancelAnimationTimer = 0;
    private boolean isCancelAnimationPlaying = false;

    // 背包系统
    private SimpleContainer inventory;
    private boolean inCombat = false;
    private int combatCooldown = 0;
    private static final int COMBAT_DURATION = 200; // 10秒 (200 ticks)

    // 威胁类型记录系统
    private String threatType = ""; // 记录攻击自己的敌对生物类型
    private int threatClearTimer = 0; // 威胁清除计时器（5分钟后清除记录）
    private static final int THREAT_CLEAR_TIME = 6000; // 5分钟 = 6000 ticks

    // 同族组队系统
    private List<UUID> teamMembers = new ArrayList<>();
    private UUID teamLeader = null;
    private static final int MAX_TEAM_SIZE = 6;
    private static final double TEAM_RANGE = 16.0D;
    private static final double HELP_RANGE = 20.0D;

    // 飞行系统
    private boolean isFlying = false;
    private int flyingTicks = 0;
    private static final int MAX_FLYING_TIME = 1200; // 60秒
    private boolean isLanding = false;
    private int landingTicks = 0;

    // 编队飞行系统
    private Vec3 formationOffset = Vec3.ZERO;
    private boolean isFormationLeader = false;

    // 主动攻击和随时飞行系统
    private int lastFlightTime = 0;
    private static final int FLIGHT_COOLDOWN = 600; // 30秒飞行冷却
    private int hostileCheckTimer = 0;

    // 自适应速度系统
    private double adaptiveSpeedBoost = 0.0D;
    private int speedCheckTimer = 0;
    private static final int SPEED_CHECK_INTERVAL = 10; // 每10tick检查一次速度
    private static final double MAX_SPEED_BOOST = 1.0D; // 最大速度提升

    public XiubiEntity(EntityType<? extends XiubiEntity> pEntityType, Level pLevel) {
        super(pEntityType, pLevel);
        this.initInventory();
        this.initSkills();
        this.initTeamGoals();
    }
    public static AttributeSupplier setAttributes() {
        return Mob.createMobAttributes()
            .add(Attributes.ATTACK_DAMAGE, 25.0D)      // 攻击力
            .add(Attributes.ATTACK_SPEED, 30.0D)       // 攻击速度
            .add(Attributes.ARMOR, 40.0D)               // 护甲
            .add(Attributes.MAX_HEALTH, 840.0D)        // 生命值
            .add(Attributes.MOVEMENT_SPEED, 0.3D)      // 移动速度
            .add(Attributes.FOLLOW_RANGE, 64.0D)       // 跟随范围
            .add(Attributes.KNOCKBACK_RESISTANCE, 0.5D) // 击退抗性
            .add(ForgeMod.SWIM_SPEED.get(), 1.8D)      // 游泳速度
            .add(ForgeMod.ATTACK_RANGE.get(), 2.3D)    // 攻击范围
            .build();
    }

    @Override
    protected void registerGoals() {
        this.goalSelector.addGoal(0, new FloatGoal(this));
        this.goalSelector.addGoal(1, new SitWhenOrderedToGoal(this));
        this.goalSelector.addGoal(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
            return this.shouldHeal();
        }, 3.0F));

        // 智能战斗AI - 确保远程攻击优先于近战
        this.goalSelector.addGoal(1, new XiubiAerialCombatGoal(this, 1.0D, 10, 32.0F)); // 最高优先级：空中战斗
        this.goalSelector.addGoal(2, new XiubiSmartRangedAttackGoal(this, 1.0D, 10, 32.0F)); // 远程攻击
        this.goalSelector.addGoal(4, new XiubiSmartMeleeAttackGoal(this, 1.2D, false)); // 低优先级：近战攻击
        this.goalSelector.addGoal(5, new XiubiAvoidEnemyGoal(this, 1.3D, 6.0D, 8.0D));

        // 原有攻击AI - 降低优先级作为备用
        this.goalSelector.addGoal(5, new CrossbowAttackGoal(this, 1.2D, 20.0F));
        this.goalSelector.addGoal(5, new RangedBowAttackGoal<>(this, 1.0D, 20, 20.0F));
        this.goalSelector.addGoal(5, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
        this.goalSelector.addGoal(5, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 1.5D, true));
        this.goalSelector.addGoal(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
        this.goalSelector.addGoal(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));

        // 修改目标选择优先级：保护自己第一，消除隐患第二，保护队友第三
        this.targetSelector.addGoal(1, new XiubiHurtByTargetGoal(this)); // 最高优先级：反击攻击自己的生物并记录威胁类型
        this.targetSelector.addGoal(2, new XiubiThreatEliminationGoal(this, 60.0D)); // 第二优先级：消除隐患
        this.targetSelector.addGoal(3, new OwnerHurtByTargetGoal(this)); // 第三优先级：保护主人
        this.targetSelector.addGoal(4, new OwnerHurtTargetGoal(this)); // 第四优先级：攻击主人的敌人

        // 移除主动攻击原版怪物的目标，改为完全被动防御
        // this.targetSelector.addGoal(4, new NearestAttackableTargetGoal<>(this, Player.class, 10, true, false, (target) -> {
        //     return this.shouldAttackPlayer(target);
        // }));
        this.targetSelector.addGoal(8, new ResetUniversalAngerTargetGoal<>(this, true));
    }

    /**
     * 休比的材质位置
     */
    @Override
    public ResourceLocation getTextureLocation() {
        return new ResourceLocation("tennogamenolife", "textures/entity/otherworlder/xiu_bi.png");
    }

    /**
     * 刷新检查 - 按照异世界人标准
     */
    @Override
    public boolean checkSpawnObstruction(net.minecraft.world.level.LevelReader pLevel) {
        return super.checkSpawnObstruction(pLevel);
    }

    /**
     * 刷新规则检查 - 按照异世界人标准
     */
    public static boolean checkXiubiSpawnRules(EntityType<XiubiEntity> pXiubi, net.minecraft.world.level.LevelAccessor pLevel,
                                               net.minecraft.world.entity.MobSpawnType pSpawnType, BlockPos pPos,
                                               net.minecraft.util.RandomSource pRandom) {
        // 按照异世界人的刷新规则
        if (pSpawnType == net.minecraft.world.entity.MobSpawnType.SPAWNER) {
            return true;
        } else {
            // 检查是否在可刷新的方块上
            return !pLevel.getBlockState(pPos.below()).is(com.github.manasmods.tensura.data.TensuraTags.Blocks.MOBS_SPAWNABLE_ON) ?
                   false : Mob.checkMobSpawnRules(pXiubi, pLevel, pSpawnType, pPos, pRandom);
        }
    }

    /**
     * 刷新率检查 - 按照异世界人标准
     */
    @Override
    public boolean checkSpawnRules(net.minecraft.world.level.LevelAccessor pLevel, net.minecraft.world.entity.MobSpawnType pSpawnReason) {
        // 使用异世界人的刷新率配置
        return com.github.manasmods.tensura.config.SpawnRateConfig.rollSpawn(
            com.github.manasmods.tensura.config.SpawnRateConfig.INSTANCE.otherworlderSpawnRate.get(),
            this.random, pSpawnReason) && super.checkSpawnRules(pLevel, pSpawnReason);
    }

    /**
     * 休比拥有的独特技能
     */
    @Override
    public List<ManasSkill> getUniqueSkills() {
        return List.of((ManasSkill)UniqueSkills.TRAVELER.get());
    }

    /**
     * 实体生成后的初始化 - 实现群体刷新机制
     * 参考Folgen的召唤小弟能力，让XiubiEntity刷新时召唤其他休比
     */
    @Override
    public void onAddedToWorld() {
        super.onAddedToWorld();

        // 只有在服务器端且不是被召唤的情况下才召唤群体
        if (!this.level.isClientSide && !this.getTags().contains("summoned")) {
            this.addTag("summoned"); // 标记为已处理，避免重复召唤
            this.summonXiubiGroup();
        }
    }

    /**
     * 召唤休比群体 - 参考Folgen的召唤机制
     * 召唤3-6个其他休比实体
     */
    protected void summonXiubiGroup() {
        net.minecraft.world.level.Level level = this.level;
        if (level instanceof net.minecraft.server.level.ServerLevel) {
            net.minecraft.server.level.ServerLevel serverLevel = (net.minecraft.server.level.ServerLevel)level;

            // 随机召唤2-5个休比（包括自己总共3-6个）
            int groupSize = 2 + this.random.nextInt(4); // 2, 3, 4, 或 5

            int baseX = net.minecraft.util.Mth.floor(this.getX());
            int baseY = net.minecraft.util.Mth.floor(this.getY());
            int baseZ = net.minecraft.util.Mth.floor(this.getZ());

            for(int i = 0; i < groupSize; ++i) {
                XiubiEntity newXiubi = new XiubiEntity(com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities.XIUBI.get(), serverLevel);

                // 尝试50次找到合适的位置
                for(int attempt = 0; attempt < 50; ++attempt) {
                    // 在7格范围内随机位置
                    int x = baseX + net.minecraft.util.Mth.nextInt(this.random, 0, 7) * net.minecraft.util.Mth.nextInt(this.random, -1, 1);
                    int y = baseY + net.minecraft.util.Mth.nextInt(this.random, 0, 3) * net.minecraft.util.Mth.nextInt(this.random, -1, 1);
                    int z = baseZ + net.minecraft.util.Mth.nextInt(this.random, 0, 7) * net.minecraft.util.Mth.nextInt(this.random, -1, 1);

                    net.minecraft.core.BlockPos spawnPos = new net.minecraft.core.BlockPos(x, y, z);
                    EntityType<?> entityType = com.github.b4ndithelps.tennogamenolife.registry.entity.TenreincarnationEntities.XIUBI.get();
                    net.minecraft.world.entity.SpawnPlacements.Type spawnType = net.minecraft.world.entity.SpawnPlacements.getPlacementType(entityType);

                    // 检查位置是否合适
                    if (net.minecraft.world.level.NaturalSpawner.isSpawnPositionOk(spawnType, serverLevel, spawnPos, entityType) &&
                        net.minecraft.world.entity.SpawnPlacements.checkSpawnRules(entityType, serverLevel,
                        net.minecraft.world.entity.MobSpawnType.MOB_SUMMONED, spawnPos, serverLevel.getRandom())) {

                        newXiubi.moveTo((double)x, (double)y, (double)z);

                        // 检查实体是否可以放置在这个位置
                        if (serverLevel.noCollision(newXiubi) && serverLevel.isUnobstructed(newXiubi)) {
                            // 初始化新的休比
                            newXiubi.addTag("summoned"); // 标记为被召唤的，避免再次召唤群体
                            newXiubi.setPersistenceRequired();
                            serverLevel.addFreshEntity(newXiubi);
                            break;
                        }
                    }
                }
            }
        }
    }



    /**
     * 背包 - 限制性背包，只能装机铠种之械
     */
    private void initInventory() {
        // 创建限制性背包，只能装机铠种之械
        this.inventory = new SimpleContainer(9) {
            @Override
            public boolean canPlaceItem(int pIndex, ItemStack pStack) {
                // 只允许机铠种之械或空物品
                return pStack.isEmpty() || pStack.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get());
            }

            @Override
            public void setItem(int pSlot, ItemStack pStack) {
                // 检查物品是否允许放入
                if (pStack.isEmpty() || pStack.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                    super.setItem(pSlot, pStack);
                }
                // 其他物品直接忽略
            }
        };

        // 默认给予机铠种之械
        ItemStack exMachinasWeapon = new ItemStack(Tenreincarnationitems.EX_MACHINAS_WEAPON.get());
        this.inventory.setItem(0, exMachinasWeapon);
    }

    /**
     * 初始化技能系统
     */
    private void initSkills() {
        if (!this.level.isClientSide()) {
            // Information技能
            SkillUtils.learnSkill(this, ExtraSkills.MAGIC_SENSE.get());

            // Extra Skills
            SkillUtils.learnSkill(this, ExtraSkills.INFINITE_REGENERATION.get());
            SkillUtils.learnSkill(this, ExtraSkills.MAGIC_JAMMING.get());
            SkillUtils.learnSkill(this, ExtraSkills.BLACK_LIGHTNING.get());
            SkillUtils.learnSkill(this, ExtraSkills.GRAVITY_MANIPULATION.get());

            // Resistance Skills
            SkillUtils.learnSkill(this, ResistanceSkills.COLD_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.PAIN_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.PARALYSIS_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.WATER_ATTACK_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.FLAME_ATTACK_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.GRAVITY_ATTACK_NULLIFICATION.get());
            SkillUtils.learnSkill(this, ResistanceSkills.ABNORMAL_CONDITION_NULLIFICATION.get());
        }
    }

    /**
     * 初始化团队AI目标
     */
    private void initTeamGoals() {
        // 移除主动攻击敌对生物的目标，改为完全被动防御
        // this.targetSelector.addGoal(1, new NearestAttackableTargetGoal<>(this, net.minecraft.world.entity.monster.Monster.class, true) {
        //     @Override
        //     public boolean canUse() {
        //         return super.canUse() && !XiubiEntity.this.isTame();
        //     }
        // });

        // 添加休比互助目标 - 当任何休比受到攻击时会帮助（优先级低于保护自己）
        this.targetSelector.addGoal(4, new HurtByTargetGoal(this) {
            @Override
            public boolean canUse() {
                // 检查是否有任何休比需要帮助
                return super.canUse() || hasTeamMemberInDanger();
            }

            @Override
            public void start() {
                super.start();
                // 如果有休比在危险中，设置相同的目标
                XiubiEntity memberInDanger = getTeamMemberInDanger();
                if (memberInDanger != null && memberInDanger.getTarget() != null) {
                    setTarget(memberInDanger.getTarget());
                    // 进入战斗状态
                    XiubiEntity.this.inCombat = true;
                    XiubiEntity.this.combatCooldown = COMBAT_DURATION;
                    // 装备武器
                    if (!XiubiEntity.this.isHoldingExMachinasWeapon()) {
                        XiubiEntity.this.equipWeaponFromInventory();
                    }
                }
            }
        });

        // 添加跟随团队成员的目标 - 使用简单的AI目标
        this.goalSelector.addGoal(3, new net.minecraft.world.entity.ai.goal.Goal() {
            @Override
            public boolean canUse() {
                return getTeamLeaderEntity() != null && !isTeamLeader();
            }

            @Override
            public void tick() {
                XiubiEntity leader = getTeamLeaderEntity();
                if (leader != null && leader.distanceTo(XiubiEntity.this) > 7.0F) {
                    // 如果距离团队领袖太远，向其移动
                    XiubiEntity.this.getNavigation().moveTo(leader, 1.0D);
                }
            }
        });
    }

    /**
     * 检查是否持有机铠种之械
     */
    private boolean isHoldingExMachinasWeapon() {
        ItemStack mainHand = this.getMainHandItem();
        ItemStack offHand = this.getOffhandItem();
        return mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get()) ||
               offHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get());
    }

    /**
     * 从背包中装备机铠种之械
     */
    private void equipWeaponFromInventory() {
        if (this.inventory != null && !this.isHoldingExMachinasWeapon()) {
            for (int i = 0; i < this.inventory.getContainerSize(); i++) {
                ItemStack stack = this.inventory.getItem(i);
                if (stack.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                    // 将武器装备到主手
                    ItemStack currentMainHand = this.getMainHandItem();
                    this.setItemSlot(EquipmentSlot.MAINHAND, stack.copy());

                    // 只有当前主手物品是机铠种之械时才放回背包，其他物品丢弃
                    if (currentMainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                        this.inventory.setItem(i, currentMainHand);
                    } else {
                        this.inventory.setItem(i, ItemStack.EMPTY);
                        // 丢弃非法物品
                        if (!currentMainHand.isEmpty()) {
                            this.spawnAtLocation(currentMainHand);
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 将武器收回背包
     */
    private void storeWeaponInInventory() {
        ItemStack mainHand = this.getMainHandItem();
        if (mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
            // 找到空槽位或原来的位置
            for (int i = 0; i < this.inventory.getContainerSize(); i++) {
                ItemStack slotItem = this.inventory.getItem(i);
                if (slotItem.isEmpty()) {
                    this.inventory.setItem(i, mainHand.copy());
                    this.setItemSlot(EquipmentSlot.MAINHAND, ItemStack.EMPTY);
                    break;
                }
            }
        }
    }

    /**
     * 团队管理方法 - 扩展为所有休比互助
     */
    private boolean hasTeamMemberInDanger() {
        // 检查所有附近的休比，不仅仅是队友
        List<XiubiEntity> nearbyXiubi = this.level.getEntitiesOfClass(XiubiEntity.class,
            this.getBoundingBox().inflate(HELP_RANGE));

        for (XiubiEntity xiubi : nearbyXiubi) {
            if (xiubi != this && xiubi.getTarget() != null) {
                return true;
            }
        }
        return false;
    }

    private XiubiEntity getTeamMemberInDanger() {
        // 检查所有附近的休比，不仅仅是队友
        List<XiubiEntity> nearbyXiubi = this.level.getEntitiesOfClass(XiubiEntity.class,
            this.getBoundingBox().inflate(HELP_RANGE));

        for (XiubiEntity xiubi : nearbyXiubi) {
            if (xiubi != this && xiubi.getTarget() != null) {
                return xiubi;
            }
        }
        return null;
    }

    private XiubiEntity getXiubiById(UUID id) {
        List<XiubiEntity> nearbyXiubi = this.level.getEntitiesOfClass(XiubiEntity.class,
            this.getBoundingBox().inflate(HELP_RANGE));
        for (XiubiEntity xiubi : nearbyXiubi) {
            if (xiubi.getUUID().equals(id)) {
                return xiubi;
            }
        }
        return null;
    }

    private XiubiEntity getTeamLeaderEntity() {
        if (teamLeader != null) {
            return getXiubiById(teamLeader);
        }
        return null;
    }

    private boolean isTeamLeader() {
        return teamLeader != null && teamLeader.equals(this.getUUID());
    }

    private void tryFormTeam() {
        if (teamMembers.size() >= MAX_TEAM_SIZE) return;

        List<XiubiEntity> nearbyXiubi = this.level.getEntitiesOfClass(XiubiEntity.class,
            this.getBoundingBox().inflate(TEAM_RANGE));

        for (XiubiEntity other : nearbyXiubi) {
            if (other != this && !teamMembers.contains(other.getUUID()) && !other.teamMembers.contains(this.getUUID())) {

                boolean thisIsLeader = this.isTeamLeader();
                boolean otherIsLeader = other.isTeamLeader();
                boolean thisIsSolo = teamMembers.isEmpty();
                boolean otherIsSolo = other.teamMembers.isEmpty();

                // 组队逻辑
                if (thisIsSolo && otherIsSolo) {
                    // 两个都是单独的，当前实体成为领袖
                    createTeam(other);
                    break;
                } else if (thisIsSolo && !otherIsLeader) {
                    // 当前是单独的，对方不是领袖，加入对方团队
                    joinTeam(other);
                    break;
                } else if (otherIsSolo && !thisIsLeader) {
                    // 对方是单独的，当前不是领袖，对方加入当前团队
                    other.joinTeam(this);
                    break;
                } else if (thisIsLeader && otherIsLeader) {
                    // 两个领袖相遇，检查是否可以合并
                    int totalSize = teamMembers.size() + other.teamMembers.size();
                    if (totalSize <= MAX_TEAM_SIZE) {
                        // 合并团队，当前实体保持领袖地位
                        mergeTeams(other);
                        break;
                    }
                }
                // 其他情况不组队（领袖不加入其他团队）
            }
        }
    }

    private void mergeTeams(XiubiEntity otherLeader) {
        // 创建副本避免并发修改异常
        List<UUID> otherTeamMembersCopy = new ArrayList<>(otherLeader.teamMembers);

        // 将对方团队合并到当前团队
        for (UUID memberId : otherTeamMembersCopy) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null && !teamMembers.contains(memberId)) {
                member.teamLeader = this.getUUID();
                member.teamMembers.clear();
                member.teamMembers.addAll(this.teamMembers);
                member.teamMembers.add(memberId);

                if (!this.teamMembers.contains(memberId)) {
                    this.teamMembers.add(memberId);
                }
            }
        }

        // 创建副本避免并发修改异常
        List<UUID> currentTeamMembersCopy = new ArrayList<>(this.teamMembers);

        // 更新所有成员的成员列表
        for (UUID memberId : currentTeamMembersCopy) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null) {
                member.teamMembers.clear();
                member.teamMembers.addAll(this.teamMembers);
            }
        }
    }

    private void createTeam(XiubiEntity other) {
        // 当前实体成为领袖
        this.teamLeader = this.getUUID();
        this.teamMembers.clear();
        this.teamMembers.add(this.getUUID());
        this.teamMembers.add(other.getUUID());

        // 对方加入团队
        other.teamLeader = this.getUUID();
        other.teamMembers.clear();
        other.teamMembers.addAll(this.teamMembers);
    }

    private void joinTeam(XiubiEntity leader) {
        if (leader.teamMembers.size() >= MAX_TEAM_SIZE) return;

        // 加入领袖的团队
        UUID leaderUUID = leader.isTeamLeader() ? leader.getUUID() : leader.teamLeader;
        this.teamLeader = leaderUUID;
        this.teamMembers.clear();
        this.teamMembers.addAll(leader.teamMembers);
        this.teamMembers.add(this.getUUID());

        // 创建副本避免并发修改异常
        List<UUID> teamMembersCopy = new ArrayList<>(this.teamMembers);

        // 更新所有团队成员的成员列表
        for (UUID memberId : teamMembersCopy) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null && member != this) {
                if (!member.teamMembers.contains(this.getUUID())) {
                    member.teamMembers.add(this.getUUID());
                }
            }
        }
    }

    /**
     * 获取机铠动画状态
     */
    public ArmorAnimationState getArmorAnimationState() {
        if (isCancelAnimationPlaying) {
            return ArmorAnimationState.CANCEL_WEAPON;
        } else if (hasWeapon) {
            return ArmorAnimationState.WEAPON;
        } else {
            return ArmorAnimationState.IDLE;
        }
    }

    /**
     * 获取动画计时器
     */
    public int getAnimationTimer() {
        if (isCancelAnimationPlaying) {
            return cancelAnimationTimer;
        } else if (hasWeapon) {
            return weaponAnimationTimer;
        } else {
            return this.tickCount; // 待机动画使用实体tick计数
        }
    }

    /**
     * 每tick更新 - 实现智能传送逻辑和动画状态管理
     */
    @Override
    public void tick() {
        super.tick();

        // 更新战斗状态
        this.updateCombatState();

        // 更新威胁计时器
        this.updateThreatTimer();

        // 检查威胁目标死亡并立即搜索新威胁
        this.checkThreatTargetDeath();

        // 更新团队状态
        if (this.tickCount % 100 == 0) { // 每5秒检查一次
            this.tryFormTeam();
        }

        // 更新飞行状态
        this.updateFlying();

        // 更新自适应速度
        this.updateAdaptiveSpeed();

        // 清理背包中的非法物品
        if (this.tickCount % 100 == 0) { // 每5秒检查一次
            this.cleanInventory();
        }

        // 飞行时的战斗逻辑
        LivingEntity currentTarget = this.getTarget();
        if ((isFlying || isLanding) && currentTarget != null && currentTarget.isAlive()) {
            double distanceToTarget = this.distanceTo(currentTarget);
            // 在攻击范围内时尝试攻击
            if (distanceToTarget <= this.getAttributeValue(net.minecraft.world.entity.ai.attributes.Attributes.FOLLOW_RANGE)) {
                // 确保面向目标
                this.getLookControl().setLookAt(currentTarget, 30.0F, 30.0F);

                // 如果在近战范围内，尝试攻击
                if (distanceToTarget <= 3.0D && this.hasLineOfSight(currentTarget)) {
                    this.doHurtTarget(currentTarget);
                }
            }
        }

        // 更新武器状态
        this.previousHasWeapon = this.hasWeapon;
        this.hasWeapon = this.isHoldingExMachinasWeapon();

        // 处理动画状态转换
        if (this.hasWeapon && !this.previousHasWeapon) {
            // 开始拿武器 - 启动武器动画
            this.weaponAnimationTimer = 0;
            this.isCancelAnimationPlaying = false;
        } else if (!this.hasWeapon && this.previousHasWeapon) {
            // 放下武器 - 启动取消动画
            this.cancelAnimationTimer = 0;
            this.isCancelAnimationPlaying = true;
        }

        // 更新动画计时器
        if (this.hasWeapon) {
            this.weaponAnimationTimer++;
        }

        if (this.isCancelAnimationPlaying) {
            this.cancelAnimationTimer++;
            // 取消动画播放完毕 (0.5833秒 = 约12 ticks)
            if (this.cancelAnimationTimer >= 12) {
                this.isCancelAnimationPlaying = false;
                this.cancelAnimationTimer = 0;
            }
        }

        LivingEntity target = this.getTarget();
        if (target != null) {
            if (this.isTame()) {
                if (this.tickCount % 20 == 0) {
                    // 距离太近时传送到远处
                    if (target.distanceTo(this) <= 5.0F && this.getTraveler() != null) {
                        this.teleportTowards(this, target, 12.0D);
                    }
                    // 距离太远时传送到近处
                    else if (target.distanceTo(this) >= 20.0F && this.getTraveler() != null) {
                        this.teleportTowards(this, target, 20.0D);
                    }
                }
            }
        }
    }

    /**
     * 更新战斗状态
     */
    private void updateCombatState() {
        boolean wasInCombat = this.inCombat;

        // 检查是否有目标或最近受到攻击
        if (this.getTarget() != null || this.combatCooldown > 0) {
            this.inCombat = true;
            if (this.getTarget() != null) {
                this.combatCooldown = COMBAT_DURATION; // 重置战斗冷却
            }
        } else {
            this.inCombat = false;
        }

        // 战斗冷却递减
        if (this.combatCooldown > 0) {
            this.combatCooldown--;
        }

        // 进入战斗状态时装备武器
        if (this.inCombat && !wasInCombat) {
            this.equipWeaponFromInventory();
        }

        // 脱离战斗状态时收回武器
        if (!this.inCombat && wasInCombat) {
            this.storeWeaponInInventory();
        }
    }

    /**
     * 更新威胁计时器
     */
    private void updateThreatTimer() {
        if (this.threatClearTimer > 0) {
            this.threatClearTimer--;
            if (this.threatClearTimer <= 0) {
                this.threatType = ""; // 清除威胁记录
            }
        }
    }

    /**
     * 检查威胁目标死亡并立即搜索新威胁
     */
    private void checkThreatTargetDeath() {
        // 只有有威胁记录时才检查
        if (!this.hasThreatRecord()) {
            return;
        }

        LivingEntity currentTarget = this.getTarget();

        // 如果当前目标死亡或不存在，立即搜索新的威胁目标
        if (currentTarget == null || !currentTarget.isAlive()) {
            LivingEntity newThreat = this.findNearbyThreatEntity();
            if (newThreat != null) {
                this.setTarget(newThreat);
            }
        }
    }

    /**
     * 搜索附近的威胁实体
     */
    private LivingEntity findNearbyThreatEntity() {
        String threatType = this.getThreatType();
        if (threatType.isEmpty()) {
            return null;
        }

        // 搜索周围60格内的所有生物
        java.util.List<LivingEntity> nearbyEntities = this.level.getEntitiesOfClass(
            LivingEntity.class,
            this.getBoundingBox().inflate(60.0D),
            entity -> {
                // 检查是否是同类型的敌对生物
                if (entity == this || !entity.isAlive()) {
                    return false;
                }

                // 检查类型是否匹配
                String entityType = entity.getClass().getSimpleName();
                if (!entityType.equals(threatType)) {
                    return false;
                }

                // 检查是否是敌对的（怪物或者攻击过休比的生物）
                return entity instanceof net.minecraft.world.entity.monster.Monster ||
                       entity.getLastHurtByMob() == this ||
                       this.getLastHurtByMob() == entity;
            }
        );

        // 返回最近的威胁实体
        return nearbyEntities.stream()
            .min((e1, e2) -> Double.compare(
                this.distanceToSqr(e1),
                this.distanceToSqr(e2)
            ))
            .orElse(null);
    }

    /**
     * 重写受伤方法，受到攻击时进入战斗状态并呼叫援助
     */
    @Override
    public boolean hurt(DamageSource pSource, float pAmount) {
        boolean result = super.hurt(pSource, pAmount);

        if (result) {
            // 检查攻击者是否是创造模式玩家
            boolean isCreativePlayer = false;
            if (pSource.getEntity() instanceof Player player) {
                isCreativePlayer = player.isCreative();
            }

            // 创造模式玩家攻击不产生仇恨
            if (!isCreativePlayer) {
                // 受到攻击时进入战斗状态
                this.inCombat = true;
                this.combatCooldown = COMBAT_DURATION;

                // 立即装备武器
                if (!this.isHoldingExMachinasWeapon()) {
                    this.equipWeaponFromInventory();
                }

                // 呼叫附近所有休比援助
                this.callForHelp(pSource);

                // 记录威胁类型用于后续清理
                if (pSource.getEntity() instanceof LivingEntity attacker) {
                    String attackerType = attacker.getClass().getSimpleName();
                    this.setThreatType(attackerType);
                }
            }
        }

        return result;
    }

    /**
     * 呼叫附近所有休比援助
     */
    private void callForHelp(DamageSource pSource) {
        if (pSource.getEntity() instanceof LivingEntity attacker) {
            // 检查攻击者是否是创造模式玩家
            boolean isCreativePlayer = false;
            if (attacker instanceof Player player) {
                isCreativePlayer = player.isCreative();
            }

            // 创造模式玩家攻击不呼叫援助
            if (!isCreativePlayer) {
                List<XiubiEntity> nearbyXiubi = this.level.getEntitiesOfClass(XiubiEntity.class,
                    this.getBoundingBox().inflate(HELP_RANGE));

                for (XiubiEntity xiubi : nearbyXiubi) {
                    if (xiubi != this && xiubi.getTarget() == null) {
                        // 设置攻击者为目标
                        xiubi.setTarget(attacker);
                        // 进入战斗状态
                        xiubi.inCombat = true;
                        xiubi.combatCooldown = COMBAT_DURATION;
                        // 装备武器
                        if (!xiubi.isHoldingExMachinasWeapon()) {
                            xiubi.equipWeaponFromInventory();
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新自适应速度系统
     */
    private void updateAdaptiveSpeed() {
        speedCheckTimer++;

        if (speedCheckTimer >= SPEED_CHECK_INTERVAL) {
            speedCheckTimer = 0;

            LivingEntity target = this.getTarget();
            if (target != null && (isFlying || isLanding) && target.isAlive()) {
                // 检查目标是否是飞行生物
                if (!target.isOnGround()) {
                    // 计算目标的移动速度
                    Vec3 targetMovement = target.getDeltaMovement();
                    double targetSpeed = Math.sqrt(targetMovement.x * targetMovement.x +
                                                 targetMovement.z * targetMovement.z);

                    // 计算自己的移动速度
                    Vec3 myMovement = this.getDeltaMovement();
                    double mySpeed = Math.sqrt(myMovement.x * myMovement.x +
                                             myMovement.z * myMovement.z);

                    // 如果目标速度比自己快，增加速度提升
                    if (targetSpeed > mySpeed) {
                        double speedDifference = targetSpeed - mySpeed;
                        adaptiveSpeedBoost = Math.min(MAX_SPEED_BOOST,
                                                    adaptiveSpeedBoost + speedDifference * 0.1D);
                    } else {
                        // 逐渐减少速度提升
                        adaptiveSpeedBoost = Math.max(0.0D, adaptiveSpeedBoost - 0.05D);
                    }
                } else {
                    // 目标在地面，逐渐减少速度提升
                    adaptiveSpeedBoost = Math.max(0.0D, adaptiveSpeedBoost - 0.05D);
                }
            } else {
                // 没有目标或不在飞行，重置速度提升
                adaptiveSpeedBoost = Math.max(0.0D, adaptiveSpeedBoost - 0.1D);
            }
        }
    }

    /**
     * 飞行系统管理
     */
    private void updateFlying() {
        LivingEntity target = this.getTarget();

        // 更新降落状态
        if (isLanding) {
            landingTicks++;
            performLanding();

            // 降落时如果发现新的空中目标，重新开始飞行
            if (target != null && target.isAlive() && !target.isOnGround()) {
                double heightDifference = target.getY() - this.getY();
                if (heightDifference > 3.0D) {
                    // 取消降落，重新飞行
                    isLanding = false;
                    landingTicks = 0;
                    startFlying();
                    return;
                }
            }

            // 只有碰到地面才停止降落
            if (this.isOnGround()) {
                finishLanding();
            }
            return;
        }

        // 检查是否应该开始飞行
        if (!isFlying && !isLanding) {
            boolean shouldFly = false;

            // 检查是否是领袖
            if (isTeamLeader() || teamMembers.isEmpty()) {
                // 领袖或单独个体的飞行逻辑
                if (target != null) {
                    double distanceToTarget = this.distanceTo(target);
                    double heightDifference = target.getY() - this.getY();

                    // 如果目标在高处或距离较远，开始飞行
                    if (heightDifference > 3.0D || distanceToTarget > 15.0D) {
                        shouldFly = true;
                    }
                }
            } else {
                // 非领袖的跟随逻辑
                XiubiEntity leader = getTeamLeaderEntity();
                if (leader != null && (leader.isFlying || leader.isLanding)) {
                    // 领袖在飞行，跟随飞行
                    shouldFly = true;
                }
                // 如果领袖没有飞行，即使有目标也不飞行
            }

            if (shouldFly) {
                startFlying();
                lastFlightTime = this.tickCount;
            }
        }

        // 更新飞行状态
        if (isFlying) {
            flyingTicks++;

            // 检查是否应该降落
            boolean shouldLand = false;

            if (flyingTicks >= MAX_FLYING_TIME) {
                // 飞行时间超限
                shouldLand = true;
            } else if (target == null) {
                // 无目标时立即降落
                shouldLand = true;
            } else if (target != null) {
                // 有目标时的降落条件
                double distanceToTarget = this.distanceTo(target);
                double heightDifference = Math.abs(target.getY() - this.getY());

                // 只有当目标死亡时才降落
                if (!target.isAlive()) {
                    shouldLand = true;
                } else if (target.isOnGround() && distanceToTarget < 3.0D && heightDifference < 2.0D) {
                    shouldLand = true;
                }
                // 如果目标在天上，继续飞行战斗，不降落
            }

            // 检查团队成员是否应该跟随领袖降落
            if (!shouldLand && !isTeamLeader() && !teamMembers.isEmpty()) {
                XiubiEntity leader = getTeamLeaderEntity();
                if (leader != null && leader.isLanding) {
                    // 领袖在降落，跟随降落
                    shouldLand = true;
                }
            }

            if (shouldLand) {
                startLanding();
            } else {
                // 飞行移动逻辑
                performFlightMovement(target);
            }
        }
    }

    private void startFlying() {
        isFlying = true;
        flyingTicks = 0;
        isLanding = false;
        landingTicks = 0;
        this.setNoGravity(true);

        // 计算编队位置
        calculateFormationPosition();
    }

    private void startLanding() {
        isFlying = false;
        isLanding = true;
        landingTicks = 0;
        flyingTicks = 0;
        // 不立即关闭无重力，让降落过程更平滑
    }

    private void finishLanding() {
        isFlying = false;
        isLanding = false;
        landingTicks = 0;
        flyingTicks = 0;
        this.setNoGravity(false);
    }

    private void performLanding() {
        // 更快的降落速度
        Vec3 currentMovement = this.getDeltaMovement();
        double landingSpeed = -0.5D; // 进一步加快下降速度

        // 降落时移动到编队位置
        Vec3 targetHorizontalPos = calculateLandingPosition();
        if (targetHorizontalPos != null) {
            Vec3 currentPos = this.position();
            Vec3 horizontalDirection = new Vec3(
                targetHorizontalPos.x - currentPos.x,
                0,
                targetHorizontalPos.z - currentPos.z
            );

            double horizontalDistance = horizontalDirection.length();
            if (horizontalDistance > 1.0D) {
                horizontalDirection = horizontalDirection.normalize().scale(0.3D); // 水平移动速度
            } else {
                horizontalDirection = Vec3.ZERO;
            }

            Vec3 newMovement = new Vec3(
                horizontalDirection.x,
                landingSpeed,
                horizontalDirection.z
            );

            this.setDeltaMovement(newMovement);
        } else {
            // 没有目标位置，直接下降
            Vec3 newMovement = new Vec3(
                currentMovement.x * 0.5D,
                landingSpeed,
                currentMovement.z * 0.5D
            );

            this.setDeltaMovement(newMovement);
        }
    }

    private Vec3 calculateLandingPosition() {
        if (!teamMembers.isEmpty() && !isTeamLeader()) {
            // 非领袖回到领袖身边
            XiubiEntity leader = getTeamLeaderEntity();
            if (leader != null && !leader.isFlying && !leader.isLanding) {
                return leader.position().add(formationOffset.x, 0, formationOffset.z);
            }
        }
        return null; // 领袖或无团队时不需要特定位置
    }

    private void performFlightMovement(LivingEntity target) {
        Vec3 targetPos;
        Vec3 currentPos = this.position();

        // 战斗时优先击杀敌人，不考虑编队
        if (target != null) {
            performNormalFlightMovement(target, currentPos);
            return;
        }

        // 非战斗时的编队飞行
        if (isTeamLeader() || teamMembers.isEmpty()) {
            // 领袖或单独个体，执行正常飞行逻辑
            performNormalFlightMovement(target, currentPos);
            return;
        }

        // 非领袖的跟随逻辑（仅在非战斗时）
        XiubiEntity leader = getTeamLeaderEntity();
        if (leader != null && (leader.isFlying || leader.isLanding)) {
            // 跟随领袖飞行，应用编队偏移
            Vec3 leaderPos = leader.position();
            targetPos = leaderPos.add(formationOffset);

            // 面向飞行方向
            Vec3 direction = targetPos.subtract(currentPos);
            if (direction.length() > 0.1D) {
                this.getLookControl().setLookAt(currentPos.add(direction.normalize().scale(5)));
            }
        } else {
            // 领袖不在飞行，不应该飞行（这种情况理论上不应该发生）
            startLanding();
            return;
        }

        // 计算移动
        Vec3 direction = targetPos.subtract(currentPos);
        double distance = direction.length();

        if (distance > 0.5D) {
            direction = direction.normalize();
            double speed = 1.0D; // 基础飞行速度

            // 根据距离调整速度
            if (distance > 15.0D) {
                speed = 1.3D;
            } else if (distance < 3.0D) {
                speed = 0.5D;
            }

            // 应用自适应速度提升
            speed += adaptiveSpeedBoost;

            Vec3 movement = direction.scale(speed);
            this.setDeltaMovement(movement);
        }
    }

    private void performNormalFlightMovement(LivingEntity target, Vec3 currentPos) {
        Vec3 targetPos;

        if (target != null) {
            // 有目标时的飞行
            double distanceToTarget = this.distanceTo(target);

            if (distanceToTarget <= 4.0D) {
                // 接近目标时，保持在攻击范围内
                Vec3 baseTargetPos = target.position().add(0, 1, 0); // 稍微高于目标
                targetPos = baseTargetPos.add(formationOffset);
            } else {
                // 距离较远时，快速接近
                Vec3 baseTargetPos = target.position().add(0, 2, 0);
                targetPos = baseTargetPos.add(formationOffset);
            }

            // 面向目标
            this.getLookControl().setLookAt(target, 30.0F, 30.0F);
        } else {
            // 无目标时的随机飞行
            if (flyingTicks % 60 == 0) { // 每3秒改变一次方向
                // 在当前位置周围随机选择一个点
                double randomX = currentPos.x + (this.random.nextDouble() - 0.5) * 20;
                double randomY = currentPos.y + (this.random.nextDouble() - 0.5) * 10;
                double randomZ = currentPos.z + (this.random.nextDouble() - 0.5) * 20;
                targetPos = new Vec3(randomX, Math.max(randomY, currentPos.y), randomZ);
            } else {
                // 保持当前方向
                Vec3 currentMovement = this.getDeltaMovement();
                targetPos = currentPos.add(currentMovement.scale(10));
            }
        }

        Vec3 direction = targetPos.subtract(currentPos);
        double distance = direction.length();

        if (distance > 0.5D) { // 避免过度接近
            direction = direction.normalize();

            // 基础飞行速度
            double speed = 1.0D;

            if (target != null) {
                // 根据距离调整速度
                if (distance > 15.0D) {
                    speed = 1.3D;
                } else if (distance < 3.0D) {
                    speed = 0.5D;
                }

                // 应用自适应速度提升
                speed += adaptiveSpeedBoost;
            } else {
                // 无目标时保持稳定速度
                speed = 0.8D;
            }

            Vec3 movement = direction.scale(speed);
            this.setDeltaMovement(movement);
        }
    }

    /**
     * 计算编队位置
     */
    private void calculateFormationPosition() {
        List<XiubiEntity> allTeammates = getAllTeammates();
        int teamSize = allTeammates.size();

        if (teamSize <= 1) {
            formationOffset = Vec3.ZERO;
            isFormationLeader = true;
            return;
        }

        // 找到自己在团队中的索引
        int myIndex = -1;
        for (int i = 0; i < allTeammates.size(); i++) {
            if (allTeammates.get(i) == this) {
                myIndex = i;
                break;
            }
        }

        if (myIndex == -1) {
            formationOffset = Vec3.ZERO;
            return;
        }

        // 设置编队领袖
        isFormationLeader = (myIndex == 0);

        if (isFormationLeader) {
            formationOffset = Vec3.ZERO;
        } else {
            // 根据团队大小和索引计算编队位置
            formationOffset = calculateFormationOffset(myIndex, teamSize);
        }
    }

    /**
     * 计算编队偏移
     */
    private Vec3 calculateFormationOffset(int index, int teamSize) {
        double spacing = 3.0D; // 间距3格

        if (teamSize <= 3) {
            // 小队：一字排开
            return new Vec3((index - 1) * spacing, 0, 0);
        } else {
            // 大队：三角形编队
            if (index == 1 || index == 2) {
                // 第二排：左右两侧
                double side = (index == 1) ? -1 : 1;
                return new Vec3(side * spacing, 0, -spacing);
            } else if (index >= 3 && index <= 5) {
                // 第三排：三个位置
                double xOffset = (index - 4) * spacing; // -3, 0, 3
                return new Vec3(xOffset, 0, -spacing * 2);
            } else {
                // 额外成员：随机分散
                double angle = (index - 6) * Math.PI / 3; // 60度间隔
                return new Vec3(
                    Math.cos(angle) * spacing * 1.5,
                    0,
                    Math.sin(angle) * spacing * 1.5 - spacing
                );
            }
        }
    }

    /**
     * 获取所有队友（包括自己）
     */
    private List<XiubiEntity> getAllTeammates() {
        List<XiubiEntity> allTeammates = new ArrayList<>();

        for (UUID memberId : teamMembers) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null) {
                allTeammates.add(member);
            }
        }

        // 按UUID排序确保编队位置一致
        allTeammates.sort((a, b) -> a.getUUID().compareTo(b.getUUID()));

        return allTeammates;
    }

    /**
     * 获取正在飞行的队友
     */
    private List<XiubiEntity> getFlyingTeammates() {
        List<XiubiEntity> flyingTeammates = new ArrayList<>();

        for (UUID memberId : teamMembers) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null && (member.isFlying || member.isLanding)) {
                flyingTeammates.add(member);
            }
        }

        // 按UUID排序确保编队位置一致
        flyingTeammates.sort((a, b) -> a.getUUID().compareTo(b.getUUID()));

        return flyingTeammates;
    }

    /**
     * 获取附近的队友
     */
    private List<XiubiEntity> getNearbyTeammates() {
        List<XiubiEntity> nearbyTeammates = new ArrayList<>();

        for (UUID memberId : teamMembers) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null && member != this && member.distanceTo(this) <= TEAM_RANGE) {
                nearbyTeammates.add(member);
            }
        }

        return nearbyTeammates;
    }

    /**
     * 通知团队成员一起飞行
     */
    private void notifyTeamToFly() {
        for (UUID memberId : teamMembers) {
            XiubiEntity member = getXiubiById(memberId);
            if (member != null && member != this && member.distanceTo(this) <= TEAM_RANGE) {
                if (!member.isFlying && !member.isLanding) {
                    member.lastFlightTime = this.tickCount; // 同步飞行时间
                }
            }
        }
    }

    /**
     * 重写移动方法以支持飞行
     */
    @Override
    public void travel(Vec3 pTravelVector) {
        if (isFlying || isLanding) {
            // 飞行或降落时的移动逻辑
            if (this.isEffectiveAi() || this.isControlledByLocalInstance()) {
                // 飞行和降落时都保持AI活跃，可以攻击
                super.travel(pTravelVector);
            } else {
                super.travel(pTravelVector);
            }
        } else {
            super.travel(pTravelVector);
        }
    }

    /**
     * 重写攻击方法，确保飞行时也能攻击
     */
    @Override
    public boolean doHurtTarget(net.minecraft.world.entity.Entity pEntity) {
        // 飞行时也能正常攻击
        return super.doHurtTarget(pEntity);
    }

    /**
     * 获取旅行者技能实例
     */
    private ManasSkillInstance getTraveler() {
        java.util.Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.TRAVELER.get());
        if (skill.isEmpty()) {
            return null;
        } else {
            return !skill.get().canInteractSkill(this) ? null : skill.get();
        }
    }

    /**
     * 判断是否应该治疗
     */
    @Override
    protected boolean shouldHeal() {
        return this.getHealth() < this.getMaxHealth() / 2.0F;
    }

    /**
     * 执行弓箭攻击 - 使用空间箭矢
     */
    @Override
    protected void performBowAttack(ItemStack weapon, BowItem bow, LivingEntity pTarget, float distance) {
        if (this.getTraveler() == null) {
            super.performBowAttack(weapon, bow, pTarget, distance);
        } else if ((double)this.random.nextFloat() <= 0.3D) {
            // 30%概率使用星尘雨攻击
            this.stardustRain(pTarget);
        } else {
            // 普通空间箭矢攻击
            SpatialArrowProjectile arrow = new SpatialArrowProjectile(this.level, this);
            arrow.setLife(50);
            arrow.setDamage((float)this.getAttributeValue(Attributes.ATTACK_SPEED));
            arrow.setMpCost(150.0D);
            arrow.setSkill(this.getTraveler());
            
            if (this.getHealth() < this.getMaxHealth() / 2.0F) {
                // 血量低时从背后攻击
                arrow.shootFromBehind(pTarget, 2.0F, 0.0F);
            } else {
                // 正常射击
                arrow.setPos(this.getX(), this.getEyeY() - 0.1D, this.getZ());
                double d0 = pTarget.getX() - this.getX();
                double d1 = pTarget.getY(0.3333333333333333D) - arrow.getY();
                double d2 = pTarget.getZ() - this.getZ();
                double d3 = Math.sqrt(d0 * d0 + d2 * d2);
                arrow.shoot(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, (float)(14 - this.level.getDifficulty().getId() * 4));
            }
            
            this.level.addFreshEntity(arrow);
        }
    }

    /**
     * 星尘雨攻击 - 大范围空间箭矢攻击
     */
    private void stardustRain(LivingEntity target) {
        Vec3 pos = target.position().add(0.0D, (double)target.getBbHeight() + 6.0D, 0.0D);
        int arrowAmount = 8; // 8支箭矢

        for(int i = 0; i < arrowAmount; ++i) {
            Vec3 arrowPos = pos.add((new Vec3(0.0D, Math.random() - 0.5D, 0.6D)).normalize().scale((double)(target.getBbWidth() + 6.0F)).yRot(360.0F * (float)i * 0.017453292F / (float)arrowAmount));
            SpatialArrowProjectile arrow = new SpatialArrowProjectile(this.level, this);
            arrow.setSpeed(1.0F);
            arrow.setPos(arrowPos);
            arrow.shootFromRot(pos.subtract(arrowPos).normalize());
            arrow.setSkill(this.getTraveler());
            arrow.setLife(50);
            arrow.setDamage((float)this.getAttributeValue(Attributes.ATTACK_SPEED));
            arrow.setMpCost((double)(5000.0F / (float)arrowAmount));
            this.level.addFreshEntity(arrow);
            this.level.playSound((Player)null, arrow.getX(), arrow.getY(), arrow.getZ(), SoundEvents.ARROW_SHOOT, SoundSource.PLAYERS, 1.0F, 1.0F);
        }
    }

    /**
     * 检查是否可以装备物品
     */
    @Override
    public boolean canHoldItem(ItemStack pStack) {
        return super.canHoldItem(pStack);
    }

    /**
     * 检查是否可以拾取物品 - 禁止休比捡起任何物品
     */
    @Override
    public boolean canPickUpLoot() {
        return false; // 完全禁止拾取物品
    }

    /**
     * 检查是否可以替换装备
     */
    @Override
    protected boolean canReplaceCurrentItem(ItemStack pCandidate, ItemStack pExisting) {
        if (pExisting.getItem() instanceof BowItem) {
            return pCandidate.getItem() instanceof BowItem && super.canReplaceCurrentItem(pCandidate, pExisting);
        } else {
            return super.canReplaceCurrentItem(pCandidate, pExisting);
        }
    }

    /**
     * 获取装备掉落概率
     */
    @Override
    protected float getEquipmentDropChance(net.minecraft.world.entity.EquipmentSlot pSlot) {
        if (this.isTame()) {
            return 0.0F;
        } else if (pSlot.equals(net.minecraft.world.entity.EquipmentSlot.MAINHAND)) {
            return 0.1F;
        } else {
            return pSlot.equals(net.minecraft.world.entity.EquipmentSlot.OFFHAND) ? 0.3F : super.getEquipmentDropChance(pSlot);
        }
    }

    /**
     * 生成时的装备
     */
    @Override
    protected void populateDefaultEquipmentSlots(net.minecraft.util.RandomSource pRandom, net.minecraft.world.DifficultyInstance pDifficulty) {
        super.populateDefaultEquipmentSlots(pRandom, pDifficulty);
        // 默认不装备任何武器，机铠种之械在背包中
        this.setItemSlot(net.minecraft.world.entity.EquipmentSlot.MAINHAND, ItemStack.EMPTY);

        // 确保背包已初始化并包含机铠种之械
        if (this.inventory == null) {
            this.initInventory();
        }
    }

    @Override
    protected boolean removeWhenNoAction() {
        return false;
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return TenreincarnationSounds.XIUBI_AMBIENT.get();
    }

    @Override
    protected SoundEvent getHurtSound(net.minecraft.world.damagesource.DamageSource pDamageSource) {
        return TenreincarnationSounds.XIUBI_HURT.get();
    }

    /**
     * 重写攻击目标选择，同族不互相攻击，创造模式玩家不被攻击
     */
    @Override
    public boolean canAttack(LivingEntity pTarget) {
        // 不攻击同族
        if (pTarget instanceof XiubiEntity) {
            return false;
        }

        // 不攻击创造模式玩家
        if (pTarget instanceof Player player && player.isCreative()) {
            return false;
        }

        return super.canAttack(pTarget);
    }

    /**
     * 重写设置目标方法，确保不攻击同族和创造模式玩家
     */
    @Override
    public void setTarget(LivingEntity pTarget) {
        if (pTarget instanceof XiubiEntity) {
            return; // 不设置同族为攻击目标
        }

        if (pTarget instanceof Player player && player.isCreative()) {
            return; // 不设置创造模式玩家为攻击目标
        }

        super.setTarget(pTarget);
    }

    /**
     * 禁止进食 - 机铠种不需要食物
     */
    @Override
    public InteractionResult mobInteract(Player pPlayer, InteractionHand pHand) {
        ItemStack itemstack = pPlayer.getItemInHand(pHand);

        // 如果玩家试图喂食，直接拒绝
        if (itemstack.isEdible()) {
            return InteractionResult.PASS;
        }

        return super.mobInteract(pPlayer, pHand);
    }

    /**
     * 机铠种不会饥饿
     */
    public boolean canEat(ItemStack pStack) {
        return false;
    }



    /**
     * 禁止休比拿起掉落物
     */
    @Override
    protected void pickUpItem(net.minecraft.world.entity.item.ItemEntity pItemEntity) {
        // 完全禁止拾取物品
    }

    /**
     * 重写物品拾取检查
     */
    @Override
    public boolean wantsToPickUp(ItemStack pStack) {
        return false; // 不想拾取任何物品
    }

    /**
     * 获取背包，防止玩家放入其他物品
     */
    public SimpleContainer getInventory() {
        return this.inventory;
    }

    /**
     * 检查是否可以接受物品（只接受机铠种之械）
     */
    public boolean canAcceptItem(ItemStack pStack) {
        return pStack.isEmpty() || pStack.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get());
    }

    /**
     * 清理背包中的非法物品
     */
    private void cleanInventory() {
        if (this.inventory != null) {
            for (int i = 0; i < this.inventory.getContainerSize(); i++) {
                ItemStack stack = this.inventory.getItem(i);
                if (!stack.isEmpty() && !stack.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                    // 丢弃非法物品
                    this.spawnAtLocation(stack);
                    this.inventory.setItem(i, ItemStack.EMPTY);
                }
            }
        }

        // 同时检查装备槽位
        ItemStack mainHand = this.getMainHandItem();
        ItemStack offHand = this.getOffhandItem();

        // 如果副手有非法物品，丢弃
        if (!offHand.isEmpty() && !offHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
            this.spawnAtLocation(offHand);
            this.setItemSlot(EquipmentSlot.OFFHAND, ItemStack.EMPTY);
        }

        // 如果主手有非法物品且不在战斗中，丢弃
        if (!mainHand.isEmpty() && !mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get()) && !this.inCombat) {
            this.spawnAtLocation(mainHand);
            this.setItemSlot(EquipmentSlot.MAINHAND, ItemStack.EMPTY);
        }
    }

    /**
     * 保存额外数据到NBT
     */
    @Override
    public void addAdditionalSaveData(CompoundTag pCompound) {
        super.addAdditionalSaveData(pCompound);

        // 保存飞行状态
        pCompound.putBoolean("IsFlying", this.isFlying);
        pCompound.putBoolean("IsLanding", this.isLanding);
        pCompound.putInt("FlyingTicks", this.flyingTicks);
        pCompound.putInt("LandingTicks", this.landingTicks);

        // 保存团队信息
        if (this.teamLeader != null) {
            pCompound.putString("TeamLeader", this.teamLeader.toString());
        }

        ListTag teamMembersList = new ListTag();
        for (UUID memberId : this.teamMembers) {
            teamMembersList.add(StringTag.valueOf(memberId.toString()));
        }
        pCompound.put("TeamMembers", teamMembersList);

        // 保存编队信息
        pCompound.putBoolean("IsFormationLeader", this.isFormationLeader);
        pCompound.putDouble("FormationOffsetX", this.formationOffset.x);
        pCompound.putDouble("FormationOffsetY", this.formationOffset.y);
        pCompound.putDouble("FormationOffsetZ", this.formationOffset.z);

        // 保存自适应速度
        pCompound.putDouble("AdaptiveSpeedBoost", this.adaptiveSpeedBoost);
    }

    /**
     * 从NBT读取额外数据
     */
    @Override
    public void readAdditionalSaveData(CompoundTag pCompound) {
        super.readAdditionalSaveData(pCompound);

        // 读取飞行状态
        this.isFlying = pCompound.getBoolean("IsFlying");
        this.isLanding = pCompound.getBoolean("IsLanding");
        this.flyingTicks = pCompound.getInt("FlyingTicks");
        this.landingTicks = pCompound.getInt("LandingTicks");

        // 读取团队信息
        if (pCompound.contains("TeamLeader")) {
            this.teamLeader = UUID.fromString(pCompound.getString("TeamLeader"));
        }

        this.teamMembers.clear();
        ListTag teamMembersList = pCompound.getList("TeamMembers", 8); // 8 = StringTag
        for (int i = 0; i < teamMembersList.size(); i++) {
            this.teamMembers.add(UUID.fromString(teamMembersList.getString(i)));
        }

        // 读取编队信息
        this.isFormationLeader = pCompound.getBoolean("IsFormationLeader");
        if (pCompound.contains("FormationOffsetX")) {
            this.formationOffset = new Vec3(
                pCompound.getDouble("FormationOffsetX"),
                pCompound.getDouble("FormationOffsetY"),
                pCompound.getDouble("FormationOffsetZ")
            );
        }

        // 读取自适应速度
        this.adaptiveSpeedBoost = pCompound.getDouble("AdaptiveSpeedBoost");

        // 修复飞行状态
        if (this.isFlying || this.isLanding) {
            this.setNoGravity(true);
            // 如果重载时在飞行但没有目标，开始降落
            if (this.isFlying && this.getTarget() == null) {
                this.startLanding();
            }
        }
    }

    /**
     * 设置威胁类型
     */
    public void setThreatType(String threatType) {
        this.threatType = threatType;
        this.threatClearTimer = THREAT_CLEAR_TIME; // 重置计时器
    }

    /**
     * 获取威胁类型
     */
    public String getThreatType() {
        return this.threatType;
    }

    /**
     * 检查是否有威胁记录
     */
    public boolean hasThreatRecord() {
        return !this.threatType.isEmpty() && this.threatClearTimer > 0;
    }

    /**
     * 机铠动画状态枚举
     */
    public enum ArmorAnimationState {
        IDLE,           // 待机动画
        WEAPON,         // 拿武器动画
        CANCEL_WEAPON   // 取消武器动画
    }

    /**
     * 智能远程攻击AI
     * 当持有ExMachinasWeapon时使用远程攻击
     */
    public static class XiubiSmartRangedAttackGoal extends net.minecraft.world.entity.ai.goal.Goal {
        private final XiubiEntity xiubi;
        private final double speedModifier;
        private final int attackIntervalMin;
        private final float attackRadius;
        private int attackTime = -1;
        private int seeTime;
        private boolean strafingClockwise;
        private boolean strafingBackwards;
        private int strafingTime = -1;

        public XiubiSmartRangedAttackGoal(XiubiEntity xiubi, double speedModifier, int attackInterval, float attackRadius) {
            this.xiubi = xiubi;
            this.speedModifier = speedModifier;
            this.attackIntervalMin = attackInterval;
            this.attackRadius = attackRadius;
            this.setFlags(java.util.EnumSet.of(Flag.MOVE, Flag.LOOK));
        }

        @Override
        public boolean canUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            double distance = this.xiubi.distanceToSqr(target);

            // 距离5格以外时使用远程攻击，32格半径内
            if (distance > 25.0D && distance < this.attackRadius * this.attackRadius) {
                // 确保装备ExMachinasWeapon
                ItemStack mainHand = this.xiubi.getMainHandItem();
                if (!mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                    // 自动装备武器
                    this.xiubi.setItemSlot(EquipmentSlot.MAINHAND, new ItemStack(Tenreincarnationitems.EX_MACHINAS_WEAPON.get()));
                }
                return true;
            }

            return false;
        }

        @Override
        public boolean canContinueToUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            double distance = this.xiubi.distanceToSqr(target);
            // 只有在合适的距离范围内才继续远程攻击
            return distance > 25.0D && distance < this.attackRadius * this.attackRadius;
        }

        @Override
        public void stop() {
            this.xiubi.setAggressive(false);
            this.seeTime = 0;
            this.attackTime = -1;
            this.xiubi.stopUsingItem();
        }

        @Override
        public void tick() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null) return;

            double distance = this.xiubi.distanceToSqr(target);
            boolean canSee = this.xiubi.getSensing().hasLineOfSight(target);

            if (canSee) {
                ++this.seeTime;
            } else {
                --this.seeTime;
            }

            if (distance <= this.attackRadius * this.attackRadius && this.seeTime >= 20) {
                this.xiubi.getNavigation().stop();
                ++this.strafingTime;
            } else if (distance > this.attackRadius * this.attackRadius) {
                // 超出攻击范围时停止攻击，不主动接近
                this.xiubi.getNavigation().stop();
                this.strafingTime = -1;
            } else {
                // 在攻击范围内但看不见时，稍微调整位置
                this.xiubi.getNavigation().stop();
                this.strafingTime = -1;
            }

            if (this.strafingTime >= 20) {
                if (this.xiubi.getRandom().nextFloat() < 0.3F) {
                    this.strafingClockwise = !this.strafingClockwise;
                }
                if (this.xiubi.getRandom().nextFloat() < 0.3F) {
                    this.strafingBackwards = !this.strafingBackwards;
                }
                this.strafingTime = 0;
            }

            if (this.strafingTime > -1) {
                if (distance > 16.0D) {
                    this.strafingBackwards = false;
                } else if (distance < 9.0D) {
                    this.strafingBackwards = true;
                }

                this.xiubi.getMoveControl().strafe(this.strafingBackwards ? -0.5F : 0.5F,
                                                  this.strafingClockwise ? 0.5F : -0.5F);
                this.xiubi.lookAt(target, 30.0F, 30.0F);
            } else {
                this.xiubi.getLookControl().setLookAt(target, 30.0F, 30.0F);
            }

            if (canSee && this.seeTime >= 20 && distance < this.attackRadius * this.attackRadius) {
                if (this.attackTime <= 0) {
                    this.fireExMachinaBullet(target);
                    this.attackTime = this.attackIntervalMin;
                }
                --this.attackTime;
            }
        }

        private void fireExMachinaBullet(LivingEntity target) {
            if (this.xiubi.level.isClientSide) return;

            // 创建子弹实体
            ExMachinaBulletEntity bullet = new ExMachinaBulletEntity(this.xiubi.level, this.xiubi);

            // 计算发射方向
            Vec3 targetPos = target.position().add(0, target.getBbHeight() * 0.5, 0);
            Vec3 shooterPos = this.xiubi.position().add(0, this.xiubi.getBbHeight() * 0.8, 0);
            Vec3 direction = targetPos.subtract(shooterPos).normalize();

            double speed = 8.0D; // 休比的子弹速度更快，更容易击中快速移动的敌人
            bullet.setDeltaMovement(
                direction.x * speed,
                direction.y * speed,
                direction.z * speed
            );

            bullet.setPos(shooterPos.x, shooterPos.y, shooterPos.z);
            this.xiubi.level.addFreshEntity(bullet);
        }
    }

    /**
     * 智能近战攻击AI
     * 当敌人太近或没有武器时使用近战攻击，并实现拉扯战术
     */
    public static class XiubiSmartMeleeAttackGoal extends net.minecraft.world.entity.ai.goal.Goal {
        private final XiubiEntity xiubi;
        private final double speedModifier;
        private final boolean followingTargetEvenIfNotSeen;
        private int attackTime;
        private int retreatTime;
        private boolean isRetreating;

        public XiubiSmartMeleeAttackGoal(XiubiEntity xiubi, double speedModifier, boolean followingTargetEvenIfNotSeen) {
            this.xiubi = xiubi;
            this.speedModifier = speedModifier;
            this.followingTargetEvenIfNotSeen = followingTargetEvenIfNotSeen;
            this.setFlags(java.util.EnumSet.of(Flag.MOVE, Flag.LOOK));
        }

        @Override
        public boolean canUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            double distance = this.xiubi.distanceToSqr(target);

            // 只有当敌人非常近时（2格内）才使用近战
            if (distance <= 4.0D) {
                // 近战时收起武器，使用空手
                ItemStack mainHand = this.xiubi.getMainHandItem();
                if (mainHand.is(Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                    this.xiubi.setItemSlot(EquipmentSlot.MAINHAND, ItemStack.EMPTY);
                }
                return true;
            }

            return false;
        }

        @Override
        public boolean canContinueToUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            return this.followingTargetEvenIfNotSeen || this.xiubi.getSensing().hasLineOfSight(target);
        }

        @Override
        public void stop() {
            this.xiubi.getNavigation().stop();
            this.attackTime = 0;
            this.retreatTime = 0;
            this.isRetreating = false;
        }

        @Override
        public void tick() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null) return;

            double distance = this.xiubi.distanceToSqr(target);
            boolean canSee = this.xiubi.getSensing().hasLineOfSight(target);

            // 拉扯战术逻辑
            if (this.isRetreating) {
                this.retreatTime--;
                if (this.retreatTime <= 0) {
                    this.isRetreating = false;
                }

                // 向后退
                Vec3 retreatDirection = this.xiubi.position().subtract(target.position()).normalize();
                Vec3 retreatPos = this.xiubi.position().add(retreatDirection.scale(3.0));
                this.xiubi.getNavigation().moveTo(retreatPos.x, retreatPos.y, retreatPos.z, this.speedModifier * 1.2);
                this.xiubi.getLookControl().setLookAt(target, 30.0F, 30.0F);
                return;
            }

            // 攻击逻辑
            if (distance <= this.xiubi.getMeleeAttackRangeSqr(target) && canSee) {
                if (this.attackTime <= 0) {
                    this.xiubi.doHurtTarget(target);
                    this.attackTime = 20; // 攻击间隔

                    // 攻击后立即开始拉扯
                    this.isRetreating = true;
                    this.retreatTime = 15 + this.xiubi.getRandom().nextInt(10); // 后退0.75-1.25秒
                }
            } else if (distance <= 25.0D) {
                // 只有当敌人在5格内时才主动接近
                this.xiubi.getNavigation().moveTo(target, this.speedModifier);
            } else {
                // 敌人太远，停止移动，让远程攻击AI处理
                this.xiubi.getNavigation().stop();
            }

            this.xiubi.getLookControl().setLookAt(target, 30.0F, 30.0F);

            if (this.attackTime > 0) {
                this.attackTime--;
            }
        }
    }

    /**
     * 躲避敌人AI
     * 当敌人太近时主动躲避，防止被包围
     */
    public static class XiubiAvoidEnemyGoal extends net.minecraft.world.entity.ai.goal.Goal {
        private final XiubiEntity xiubi;
        private final double speedModifier;
        private final double avoidDistance;
        private final double maxDistance;
        private LivingEntity toAvoid;
        private final net.minecraft.world.entity.ai.util.DefaultRandomPos randomPos;

        public XiubiAvoidEnemyGoal(XiubiEntity xiubi, double speedModifier, double avoidDistance, double maxDistance) {
            this.xiubi = xiubi;
            this.speedModifier = speedModifier;
            this.avoidDistance = avoidDistance;
            this.maxDistance = maxDistance;
            this.randomPos = new net.minecraft.world.entity.ai.util.DefaultRandomPos();
            this.setFlags(java.util.EnumSet.of(Flag.MOVE));
        }

        @Override
        public boolean canUse() {
            // 寻找最近的敌对生物
            this.toAvoid = this.xiubi.level.getNearestEntity(
                LivingEntity.class,
                net.minecraft.world.entity.ai.targeting.TargetingConditions.forCombat().range(this.maxDistance),
                this.xiubi,
                this.xiubi.getX(),
                this.xiubi.getY(),
                this.xiubi.getZ(),
                this.xiubi.getBoundingBox().inflate(this.maxDistance, 3.0D, this.maxDistance)
            );

            if (this.toAvoid == null) {
                return false;
            }

            // 检查是否是敌对的（检查是否是怪物或者是否在攻击目标列表中）
            if (!(this.toAvoid instanceof net.minecraft.world.entity.monster.Monster) &&
                this.xiubi.getTarget() != this.toAvoid) {
                return false;
            }

            // 检查距离是否太近
            double distance = this.xiubi.distanceToSqr(this.toAvoid);
            return distance < this.avoidDistance * this.avoidDistance;
        }

        @Override
        public boolean canContinueToUse() {
            if (this.toAvoid == null || !this.toAvoid.isAlive()) {
                return false;
            }

            double distance = this.xiubi.distanceToSqr(this.toAvoid);
            return distance < this.avoidDistance * this.avoidDistance;
        }

        @Override
        public void start() {
            // 立即开始躲避
            this.findAvoidPath();
        }

        @Override
        public void tick() {
            // 每10tick重新计算躲避路径
            if (this.xiubi.tickCount % 10 == 0) {
                this.findAvoidPath();
            }
        }

        private void findAvoidPath() {
            if (this.toAvoid == null) return;

            // 计算远离敌人的方向
            Vec3 avoidDirection = this.xiubi.position().subtract(this.toAvoid.position()).normalize();

            // 尝试多个躲避位置
            Vec3 bestPos = null;
            double bestScore = -1.0;

            for (int i = 0; i < 8; i++) {
                double angle = (i * Math.PI * 2.0) / 8.0;
                double offsetX = Math.cos(angle) * 5.0;
                double offsetZ = Math.sin(angle) * 5.0;

                Vec3 testPos = this.xiubi.position().add(offsetX, 0, offsetZ);

                // 评分：距离敌人越远越好，同时考虑地形
                double distanceToEnemy = testPos.distanceTo(this.toAvoid.position());
                double score = distanceToEnemy;

                // 偏向远离敌人的方向
                Vec3 testDirection = testPos.subtract(this.toAvoid.position()).normalize();
                double directionScore = avoidDirection.dot(testDirection);
                score += directionScore * 2.0;

                if (score > bestScore) {
                    bestScore = score;
                    bestPos = testPos;
                }
            }

            if (bestPos != null) {
                this.xiubi.getNavigation().moveTo(bestPos.x, bestPos.y, bestPos.z, this.speedModifier);
            }
        }
    }

    /**
     * 空中战斗AI
     * 当敌人在空中时，休比会飞到空中进行战斗
     */
    public static class XiubiAerialCombatGoal extends net.minecraft.world.entity.ai.goal.Goal {
        private final XiubiEntity xiubi;
        private final double speedModifier;
        private final int attackIntervalMin;
        private final float attackRadius;
        private int attackTime = -1;
        private int seeTime;
        private boolean isFlying;
        private Vec3 targetFlightPos;

        public XiubiAerialCombatGoal(XiubiEntity xiubi, double speedModifier, int attackInterval, float attackRadius) {
            this.xiubi = xiubi;
            this.speedModifier = speedModifier;
            this.attackIntervalMin = attackInterval;
            this.attackRadius = attackRadius;
            this.setFlags(java.util.EnumSet.of(Flag.MOVE, Flag.LOOK));
        }

        @Override
        public boolean canUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            // 检查敌人是否比休比高12格以上
            double heightDifference = target.getY() - this.xiubi.getY();

            if (heightDifference > 12.0D) {
                double distance = this.xiubi.distanceToSqr(target);
                return distance < this.attackRadius * this.attackRadius;
            }

            return false;
        }

        @Override
        public boolean canContinueToUse() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            // 继续空中战斗直到高度差小于10格或距离太远
            double heightDifference = target.getY() - this.xiubi.getY();
            double distance = this.xiubi.distanceToSqr(target);

            return heightDifference > 10.0D && distance < this.attackRadius * this.attackRadius;
        }

        @Override
        public void start() {
            // 启用飞行能力
            this.xiubi.getNavigation().setCanFloat(true);
            this.isFlying = true;
            this.seeTime = 0;
            this.attackTime = -1;
        }

        @Override
        public void stop() {
            // 恢复正常移动
            this.xiubi.getNavigation().setCanFloat(false);
            this.isFlying = false;
            this.xiubi.setAggressive(false);
            this.seeTime = 0;
            this.attackTime = -1;
            this.targetFlightPos = null;
        }

        @Override
        public void tick() {
            LivingEntity target = this.xiubi.getTarget();
            if (target == null) return;

            double distance = this.xiubi.distanceToSqr(target);
            boolean canSee = this.xiubi.getSensing().hasLineOfSight(target);

            if (canSee) {
                ++this.seeTime;
            } else {
                --this.seeTime;
            }

            // 计算理想的飞行位置
            this.calculateFlightPosition(target, distance);

            // 移动到理想位置
            if (this.targetFlightPos != null) {
                this.xiubi.getNavigation().moveTo(this.targetFlightPos.x, this.targetFlightPos.y, this.targetFlightPos.z, this.speedModifier);
            }

            // 始终看向目标
            this.xiubi.getLookControl().setLookAt(target, 30.0F, 30.0F);

            // 攻击逻辑 - 空中敌人靠近3格内才近战
            if (canSee && this.seeTime >= 20) {
                if (distance <= 9.0D) { // 3格距离的平方
                    // 敌人靠近3格内时使用近战
                    if (this.attackTime <= 0) {
                        // 收起武器进行近战
                        ItemStack mainHand = this.xiubi.getMainHandItem();
                        if (mainHand.is(com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                            this.xiubi.setItemSlot(net.minecraft.world.entity.EquipmentSlot.MAINHAND, net.minecraft.world.item.ItemStack.EMPTY);
                        }
                        this.xiubi.doHurtTarget(target);
                        this.attackTime = 20;
                    }
                } else if (distance < this.attackRadius * this.attackRadius) {
                    // 其他距离使用远程攻击
                    if (this.attackTime <= 0) {
                        // 装备武器进行远程攻击
                        ItemStack mainHand = this.xiubi.getMainHandItem();
                        if (!mainHand.is(com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems.EX_MACHINAS_WEAPON.get())) {
                            this.xiubi.setItemSlot(net.minecraft.world.entity.EquipmentSlot.MAINHAND,
                                                 new net.minecraft.world.item.ItemStack(com.github.b4ndithelps.tennogamenolife.registry.item.Tenreincarnationitems.EX_MACHINAS_WEAPON.get()));
                        }
                        this.fireExMachinaBullet(target);
                        this.attackTime = this.attackIntervalMin;
                    }
                }

                if (this.attackTime > 0) {
                    --this.attackTime;
                }
            }
        }

        private void calculateFlightPosition(LivingEntity target, double distance) {
            // 计算理想的空中战斗位置
            Vec3 targetPos = target.position();
            Vec3 xiubiPos = this.xiubi.position();

            // 目标高度：比敌人高2-4格
            double targetHeight = targetPos.y + 2.0D + this.xiubi.getRandom().nextDouble() * 2.0D;

            if (distance > 100.0D) {
                // 距离太远，接近目标
                Vec3 direction = targetPos.subtract(xiubiPos).normalize();
                this.targetFlightPos = targetPos.subtract(direction.scale(8.0D)).add(0, targetHeight - targetPos.y, 0);
            } else if (distance < 25.0D) {
                // 距离太近，拉开距离
                Vec3 direction = xiubiPos.subtract(targetPos).normalize();
                this.targetFlightPos = targetPos.add(direction.scale(6.0D)).add(0, targetHeight - targetPos.y, 0);
            } else {
                // 距离合适，保持环绕
                double angle = this.xiubi.tickCount * 0.1D; // 缓慢环绕
                double radius = 8.0D;
                double offsetX = Math.cos(angle) * radius;
                double offsetZ = Math.sin(angle) * radius;
                this.targetFlightPos = new Vec3(targetPos.x + offsetX, targetHeight, targetPos.z + offsetZ);
            }
        }

        private void fireExMachinaBullet(LivingEntity target) {
            if (this.xiubi.level.isClientSide) return;

            // 创建子弹实体
            com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity bullet =
                new com.github.b4ndithelps.tennogamenolife.entity.projectile.ExMachinaBulletEntity(this.xiubi.level, this.xiubi);

            // 计算发射方向
            Vec3 targetPos = target.position().add(0, target.getBbHeight() * 0.5, 0);
            Vec3 shooterPos = this.xiubi.position().add(0, this.xiubi.getBbHeight() * 0.8, 0);
            Vec3 direction = targetPos.subtract(shooterPos).normalize();

            double speed = 8.0D; // 休比的空中子弹速度更快
            bullet.setDeltaMovement(
                direction.x * speed,
                direction.y * speed,
                direction.z * speed
            );

            bullet.setPos(shooterPos.x, shooterPos.y, shooterPos.z);
            this.xiubi.level.addFreshEntity(bullet);
        }
    }

    /**
     * 自定义受伤目标AI
     * 记录攻击者的类型，用于后续的威胁消除
     */
    public static class XiubiHurtByTargetGoal extends HurtByTargetGoal {
        private final XiubiEntity xiubi;

        public XiubiHurtByTargetGoal(XiubiEntity xiubi) {
            super(xiubi);
            this.xiubi = xiubi;
        }

        @Override
        public void start() {
            super.start();

            // 记录攻击者的类型
            LivingEntity attacker = this.xiubi.getLastHurtByMob();
            if (attacker != null) {
                String attackerType = attacker.getClass().getSimpleName();
                this.xiubi.setThreatType(attackerType);
            }
        }
    }

    /**
     * 威胁消除AI
     * 主动攻击周围60格内与威胁类型相同的敌对生物
     */
    public static class XiubiThreatEliminationGoal extends net.minecraft.world.entity.ai.goal.Goal {
        private final XiubiEntity xiubi;
        private final double searchRange;
        private LivingEntity targetThreat;
        private int searchCooldown;

        public XiubiThreatEliminationGoal(XiubiEntity xiubi, double searchRange) {
            this.xiubi = xiubi;
            this.searchRange = searchRange;
            this.setFlags(java.util.EnumSet.of(Flag.TARGET));
        }

        @Override
        public boolean canUse() {
            // 检查是否有威胁记录
            if (!this.xiubi.hasThreatRecord()) {
                return false;
            }

            // 搜索冷却
            if (this.searchCooldown > 0) {
                this.searchCooldown--;
                return false;
            }

            // 检查当前目标是否是威胁类型
            LivingEntity currentTarget = this.xiubi.getTarget();
            if (currentTarget != null && currentTarget.isAlive()) {
                String currentTargetType = currentTarget.getClass().getSimpleName();
                String threatType = this.xiubi.getThreatType();

                // 如果当前目标就是威胁类型，继续攻击
                if (currentTargetType.equals(threatType)) {
                    return false; // 让其他AI处理当前目标
                }
            }

            // 搜索周围60格内同类型的敌对生物
            this.targetThreat = this.findThreatEntity();

            if (this.targetThreat != null) {
                this.xiubi.setTarget(this.targetThreat);
                this.searchCooldown = 20; // 1秒搜索冷却，更频繁地搜索
                return true;
            }

            this.searchCooldown = 40; // 2秒搜索冷却
            return false;
        }

        @Override
        public boolean canContinueToUse() {
            // 如果没有威胁记录，停止
            if (!this.xiubi.hasThreatRecord()) {
                return false;
            }

            // 如果目标死亡，立即搜索新目标
            if (this.targetThreat == null || !this.targetThreat.isAlive()) {
                this.targetThreat = this.findThreatEntity();
                if (this.targetThreat != null) {
                    this.xiubi.setTarget(this.targetThreat);
                    return true;
                }
                return false;
            }

            // 检查距离
            return this.xiubi.distanceToSqr(this.targetThreat) < this.searchRange * this.searchRange;
        }

        @Override
        public void stop() {
            this.targetThreat = null;
        }

        private LivingEntity findThreatEntity() {
            String threatType = this.xiubi.getThreatType();
            if (threatType.isEmpty()) {
                return null;
            }

            // 搜索周围60格内的所有生物
            java.util.List<LivingEntity> nearbyEntities = this.xiubi.level.getEntitiesOfClass(
                LivingEntity.class,
                this.xiubi.getBoundingBox().inflate(this.searchRange),
                entity -> {
                    // 检查是否是同类型的敌对生物
                    if (entity == this.xiubi || !entity.isAlive()) {
                        return false;
                    }

                    // 检查类型是否匹配
                    String entityType = entity.getClass().getSimpleName();
                    if (!entityType.equals(threatType)) {
                        return false;
                    }

                    // 检查是否是敌对的（怪物或者攻击过休比的生物）
                    return entity instanceof net.minecraft.world.entity.monster.Monster ||
                           entity.getLastHurtByMob() == this.xiubi ||
                           this.xiubi.getLastHurtByMob() == entity;
                }
            );

            // 返回最近的威胁实体
            return nearbyEntities.stream()
                .min((e1, e2) -> Double.compare(
                    this.xiubi.distanceToSqr(e1),
                    this.xiubi.distanceToSqr(e2)
                ))
                .orElse(null);
        }
    }
}
