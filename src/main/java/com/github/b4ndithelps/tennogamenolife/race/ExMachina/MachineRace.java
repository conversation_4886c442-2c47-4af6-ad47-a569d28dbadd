package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

import com.github.b4ndithelps.tennogamenolife.registry.race.Tenreincarnationraces;
import com.github.b4ndithelps.tennogamenolife.registry.skill.TenreincarnationSkill;
import com.github.b4ndithelps.tennogamenolife.race.ExMachina.ExMachinaDecorativeManager;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;

import java.util.ArrayList;
import java.util.List;

/**
 * 机凯种是一个机械生命体种族
 * 具备完整的装饰建模功能
 */
public class MachineRace extends Race implements IExMachinaDecorativeRace {

    // 种族基础属性
    private double baseHealth = 40.0;          // 基础生命值
    private double baseAttackDamage = 2.0;     // 基础攻击伤害
    private double baseAttackSpeed = 4.0;      // 基础攻击速度
    private double knockbackResistance = 0.5;   // 击退抗性
    private double jumpHeight = 1.0;           // 跳跃高度
    private double movementSpeed = 0.1;        // 移动速度
    private double sprintSpeed = 0.13;         // 冲刺速度
    private double auraMin = 700.0;           // 最小灵气值
    private double auraMax = 1500.0;           // 最大灵气值
    private double startingMagiculeMin = 100.0; // 最小初始魔素值
    private double startingMagiculeMax = 200.0; // 最大初始魔素值

    private float playerSize = 2.0f;           // 玩家大小

    public MachineRace() {
        // 设置为困难种族
        super(Difficulty.HARD);
    }

    @Override
    public double getBaseHealth() {
        return baseHealth;
    }

    @Override
    public float getPlayerSize() {
        return playerSize;
    }

    @Override
    public double getBaseAttackDamage() {
        return baseAttackDamage;
    }

    @Override
    public double getBaseAttackSpeed() {
        return baseAttackSpeed;
    }

    @Override
    public double getKnockbackResistance() {
        return knockbackResistance;
    }

    @Override
    public double getJumpHeight() {
        return JumpPowerHelper.defaultPlayer(jumpHeight);
    }

    @Override
    public double getMovementSpeed() {
        return movementSpeed;
    }

    @Override
    public double getSprintSpeed() {
        return sprintSpeed;
    }

    @Override
    public Pair<Double, Double> getBaseAuraRange() {
        return Pair.of(auraMin, auraMax);
    }

    @Override
    public Pair<Double, Double> getBaseMagiculeRange() {
        return Pair.of(startingMagiculeMin, startingMagiculeMax);
    }

    @Override
    public List<TensuraSkill> getIntrinsicSkills(Player player) {
        List<TensuraSkill> list = new ArrayList<>();
        list.add(TenreincarnationSkill.BARRIER.get());
        list.add(ExtraSkills.MAGIC_SENSE.get());
        return list;
    }

    @Override
    public boolean isMajin() {
        return false;
    }

    @Override
    public boolean isSpiritual() {
        return false;
    }

    @Override
    public boolean isDivine() {
        return false;
    }

    @Override
    public void raceAbility(Player player) {
        if (!player.isSpectator() && !player.isCreative()) {
            if (player.getAbilities().mayfly) {
                // 关闭飞行
                player.getAbilities().mayfly = false;
                player.getAbilities().flying = false;
                player.onUpdateAbilities();
            } else {
                // 开启飞行
                player.getAbilities().mayfly = true;
                player.getAbilities().flying = true;
                player.onUpdateAbilities();
            }
        }
    }

    // 装饰功能由IExMachinaDecorativeRace接口提供默认实现

    @Override
    public List<Race> getNextEvolutions(Player player) {
        List<Race> list = new ArrayList<>();
        list.add(Tenreincarnationraces.BEFEHLER.get());
        list.add(Tenreincarnationraces.KAMPF.get());
        list.add(Tenreincarnationraces.ZEICHEN.get());
        list.add(Tenreincarnationraces.PRUEFER.get());
        list.add(Tenreincarnationraces.SEHER.get());
        return list;
    }

    @Override
    public Race getDefaultEvolution(Player player) {
        return Tenreincarnationraces.BEFEHLER.get();
    }
} 