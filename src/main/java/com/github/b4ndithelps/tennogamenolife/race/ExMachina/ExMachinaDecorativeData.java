package com.github.b4ndithelps.tennogamenolife.race.ExMachina;

/**
 * ExMachina种族装饰数据类
 * 存储单个玩家的装饰状态信息
 */
public class ExMachinaDecorativeData {
    
    // 武器状态
    private boolean hasWeapon = false;
    private boolean previousHasWeapon = false;
    
    // 动画状态
    private boolean isCancelAnimationPlaying = false;
    private int weaponAnimationTimer = 0;
    private int cancelAnimationTimer = 0;
    
    // 装饰显示状态
    private boolean decorativeEnabled = true;
    
    /**
     * 构造函数
     */
    public ExMachinaDecorativeData() {
        // 默认状态
    }
    
    // === 武器状态相关 ===
    
    public boolean hasWeapon() {
        return hasWeapon;
    }
    
    public void setHasWeapon(boolean hasWeapon) {
        this.previousHasWeapon = this.hasWeapon;
        this.hasWeapon = hasWeapon;
    }
    
    public boolean previousHasWeapon() {
        return previousHasWeapon;
    }
    
    // === 动画状态相关 ===
    
    public boolean isCancelAnimationPlaying() {
        return isCancelAnimationPlaying;
    }
    
    public void setCancelAnimationPlaying(boolean cancelAnimationPlaying) {
        this.isCancelAnimationPlaying = cancelAnimationPlaying;
    }
    
    public int getWeaponAnimationTimer() {
        return weaponAnimationTimer;
    }
    
    public void setWeaponAnimationTimer(int weaponAnimationTimer) {
        this.weaponAnimationTimer = weaponAnimationTimer;
    }
    
    public void incrementWeaponAnimationTimer() {
        this.weaponAnimationTimer++;
    }
    
    public int getCancelAnimationTimer() {
        return cancelAnimationTimer;
    }
    
    public void setCancelAnimationTimer(int cancelAnimationTimer) {
        this.cancelAnimationTimer = cancelAnimationTimer;
    }
    
    public void incrementCancelAnimationTimer() {
        this.cancelAnimationTimer++;
    }
    
    // === 装饰显示相关 ===
    
    public boolean isDecorativeEnabled() {
        return decorativeEnabled;
    }
    
    public void setDecorativeEnabled(boolean decorativeEnabled) {
        this.decorativeEnabled = decorativeEnabled;
    }
    
    // === 实用方法 ===
    
    /**
     * 重置所有动画状态
     */
    public void resetAnimations() {
        this.isCancelAnimationPlaying = false;
        this.weaponAnimationTimer = 0;
        this.cancelAnimationTimer = 0;
    }
    
    /**
     * 重置所有状态
     */
    public void reset() {
        this.hasWeapon = false;
        this.previousHasWeapon = false;
        this.resetAnimations();
        this.decorativeEnabled = true;
    }
    
    /**
     * 获取当前动画状态描述
     */
    public String getAnimationStateDescription() {
        if (isCancelAnimationPlaying) {
            return "CANCEL_WEAPON (Timer: " + cancelAnimationTimer + ")";
        } else if (hasWeapon) {
            return "WEAPON (Timer: " + weaponAnimationTimer + ")";
        } else {
            return "IDLE";
        }
    }
    
    /**
     * 获取数据状态描述
     */
    @Override
    public String toString() {
        return String.format(
            "ExMachinaDecorativeData{hasWeapon=%s, previousHasWeapon=%s, " +
            "isCancelAnimationPlaying=%s, weaponAnimationTimer=%d, " +
            "cancelAnimationTimer=%d, decorativeEnabled=%s}",
            hasWeapon, previousHasWeapon, isCancelAnimationPlaying,
            weaponAnimationTimer, cancelAnimationTimer, decorativeEnabled
        );
    }
}
