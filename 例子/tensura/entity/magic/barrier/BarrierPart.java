package com.github.manasmods.tensura.entity.magic.barrier;

import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.RandomSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.entity.PartEntity;
import org.jetbrains.annotations.NotNull;

public class BarrierPart extends PartEntity<BarrierEntity> {
   public final BarrierEntity barrier;
   public final String id;
   private final Direction direction;

   public BarrierPart(BarrierEntity barrier, String name, Direction direction) {
      super(barrier);
      this.barrier = barrier;
      this.id = name;
      this.direction = direction;
      this.f_19850_ = barrier.blockBuilding();
   }

   protected void m_8097_() {
   }

   protected void m_7378_(CompoundTag compoundTag) {
   }

   protected void m_7380_(CompoundTag compoundTag) {
   }

   public boolean m_7306_(Entity entity) {
      return this == entity || this.barrier == entity;
   }

   public EntityDimensions m_6972_(Pose pose) {
      return this.barrier.m_6972_(pose);
   }

   public boolean m_142391_() {
      return false;
   }

   public boolean m_7337_(@NotNull Entity entity) {
      if (this.barrier.canWalkThrough(entity)) {
         return false;
      } else {
         return (entity.m_5829_() || entity.m_6094_()) && !this.m_20365_(entity);
      }
   }

   public boolean m_5829_() {
      return !this.barrier.canWalkThrough();
   }

   public boolean m_6094_() {
      return this.barrier.shouldPush();
   }

   public void m_7334_(Entity pEntity) {
      if (this.barrier.m_37282_() != pEntity) {
         super.m_7334_(pEntity);
      }
   }

   public boolean m_6087_() {
      return !this.m_213877_();
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return this.barrier.m_6469_(pSource, pAmount);
   }

   public float getBarrierRadius() {
      return this.barrier.getRadius();
   }

   public void directionBoundingBox() {
      Direction opposite = this.direction.m_122424_();
      float minX = -0.15F;
      float minY = -0.15F;
      float minZ = -0.15F;
      float maxX = 0.15F;
      float maxY = 0.15F;
      float maxZ = 0.15F;
      switch(opposite) {
      case NORTH:
      case SOUTH:
         minX = -this.getBarrierRadius();
         maxX = this.getBarrierRadius();
         minY = -this.getBarrierRadius();
         maxY = this.getBarrierRadius() + this.barrier.getHeight();
         break;
      case EAST:
      case WEST:
         minZ = -this.getBarrierRadius();
         maxZ = this.getBarrierRadius();
         minY = -this.getBarrierRadius();
         maxY = this.getBarrierRadius() + this.barrier.getHeight();
         break;
      case UP:
      case DOWN:
         minX = -this.getBarrierRadius();
         maxX = this.getBarrierRadius();
         minZ = -this.getBarrierRadius();
         maxZ = this.getBarrierRadius();
      }

      this.m_20011_(new AABB(this.m_20185_() + (double)minX, this.m_20186_() + (double)minY, this.m_20189_() + (double)minZ, this.m_20185_() + (double)maxX, this.m_20186_() + (double)maxY, this.m_20189_() + (double)maxZ));
   }

   public void directionPosition() {
      Vec3 pos = this.barrier.m_20182_().m_82520_(0.0D, 0.5D, 0.0D);
      Direction opposite = this.direction.m_122424_();
      switch(opposite) {
      case NORTH:
         this.m_6034_(pos.f_82479_, pos.f_82480_ + (double)this.barrier.getRadius(), pos.f_82481_ + (double)this.barrier.getRadius());
         break;
      case SOUTH:
         this.m_6034_(pos.f_82479_, pos.f_82480_ + (double)this.barrier.getRadius(), pos.f_82481_ - (double)this.barrier.getRadius());
         break;
      case EAST:
         this.m_6034_(pos.f_82479_ + (double)this.barrier.getRadius(), pos.f_82480_ + (double)this.barrier.getRadius(), pos.f_82481_);
         break;
      case WEST:
         this.m_6034_(pos.f_82479_ - (double)this.barrier.getRadius(), pos.f_82480_ + (double)this.barrier.getRadius(), pos.f_82481_);
         break;
      case UP:
         this.m_6034_(pos.f_82479_, pos.f_82480_ + (double)this.barrier.getHeight() + (double)(this.barrier.getRadius() * 2.0F), pos.f_82481_);
         break;
      case DOWN:
         this.m_6034_(pos.f_82479_, pos.f_82480_, pos.f_82481_);
      }

      Vec3 vec3 = new Vec3(this.m_20185_(), this.m_20186_(), this.m_20189_());
      this.f_19854_ = vec3.f_82479_;
      this.f_19855_ = vec3.f_82480_;
      this.f_19856_ = vec3.f_82481_;
      this.f_19790_ = vec3.f_82479_;
      this.f_19791_ = vec3.f_82480_;
      this.f_19792_ = vec3.f_82481_;
   }

   public void addServerParticlesAroundSelf(ParticleOptions pParticleOption, double randomScale) {
      Level var5 = this.f_19853_;
      if (var5 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var5;
         RandomSource random = this.f_19796_;
         AABB aabb = this.m_20191_();

         for(int i = 0; i < 5; ++i) {
            double d0 = random.m_188583_() * 0.02D * randomScale;
            double d1 = random.m_188583_() * 0.02D * randomScale;
            double d2 = random.m_188583_() * 0.02D * randomScale;
            serverLevel.m_8767_(pParticleOption, this.m_20185_() + aabb.m_82362_() * this.getRandomScale(randomScale), this.m_20186_() + aabb.m_82376_() * this.getRandomScale(randomScale), this.m_20189_() + aabb.m_82385_() * this.getRandomScale(randomScale), 0, d0, d1, d2, 1.0D);
         }

      }
   }

   private double getRandomScale(double scale) {
      return (2.0D * this.f_19796_.m_188500_() - 1.0D) * scale;
   }
}
