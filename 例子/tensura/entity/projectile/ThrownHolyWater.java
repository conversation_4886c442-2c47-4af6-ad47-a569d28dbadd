package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.block.HolyFireBlock;
import com.github.manasmods.tensura.entity.HolyCowEntity;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.item.custom.HolyWaterItem;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.awt.Color;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.Cow;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.ThrowableItemProjectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.ForgeEventFactory;

public class ThrownHolyWater extends ThrowableItemProjectile {
   public ThrownHolyWater(EntityType<? extends ThrownHolyWater> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public ThrownHolyWater(Level pLevel, LivingEntity pShooter) {
      super((EntityType)TensuraEntityTypes.HOLY_WATER.get(), pShooter, pLevel);
   }

   public ThrownHolyWater(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.HOLY_WATER.get(), pX, pY, pZ, pLevel);
   }

   protected Item m_7881_() {
      return (Item)TensuraConsumableItems.HOLY_WATER.get();
   }

   protected float m_7139_() {
      return 0.05F;
   }

   protected void m_8060_(BlockHitResult pResult) {
      BlockState blockstate = this.f_19853_.m_8055_(pResult.m_82425_());
      blockstate.m_60669_(this.f_19853_, blockstate, pResult, this);
      if (!this.f_19853_.m_5776_()) {
         this.placeFire(this);
      }
   }

   protected void m_5790_(EntityHitResult pResult) {
      this.placeFire(pResult.m_82443_());
      if (!this.f_19853_.m_5776_()) {
         Entity var3 = pResult.m_82443_();
         if (var3 instanceof Cow) {
            Cow cow = (Cow)var3;
            this.convertCow(cow);
         }
      }
   }

   protected void convertCow(Cow cow) {
      if (cow.getClass().equals(Cow.class)) {
         if (cow.m_6162_()) {
            return;
         }

         HolyCowEntity holyCow = (HolyCowEntity)((EntityType)TensuraEntityTypes.HOLY_COW.get()).m_20615_(this.f_19853_);
         if (holyCow == null) {
            return;
         }

         holyCow.m_7678_(cow.m_20185_(), cow.m_20186_(), cow.m_20189_(), cow.m_146908_(), cow.m_146909_());
         holyCow.m_21557_(cow.m_21525_());
         if (cow.m_8077_()) {
            holyCow.m_6593_(cow.m_7770_());
            holyCow.m_20340_(cow.m_20151_());
         }

         holyCow.m_21530_();
         holyCow.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 100));
         ForgeEventFactory.onLivingConvert(cow, holyCow);
         this.f_19853_.m_46796_(2002, this.m_20183_(), (new Color(0, 255, 195)).getRGB());
         this.f_19853_.m_5594_((Player)null, this.m_20183_(), SoundEvents.f_12071_, SoundSource.PLAYERS, 1.0F, 1.0F);
         this.f_19853_.m_7967_(holyCow);
         cow.m_146870_();
      } else if (cow instanceof HolyCowEntity) {
         HolyCowEntity holyCow = (HolyCowEntity)cow;
         if (holyCow.m_21223_() < holyCow.m_21233_()) {
            holyCow.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 100));
            holyCow.m_21153_(holyCow.m_21233_());
         }
      }

   }

   protected void m_6532_(HitResult pResult) {
      Type hitResultType = pResult.m_6662_();
      if (hitResultType == Type.ENTITY) {
         this.m_5790_((EntityHitResult)pResult);
         this.f_19853_.m_214171_(GameEvent.f_157777_, pResult.m_82450_(), Context.m_223719_(this, (BlockState)null));
      } else if (hitResultType == Type.BLOCK) {
         BlockHitResult blockhitresult = (BlockHitResult)pResult;
         this.m_8060_(blockhitresult);
         BlockPos blockpos = blockhitresult.m_82425_();
         this.f_19853_.m_220407_(GameEvent.f_157777_, blockpos, Context.m_223719_(this, this.f_19853_.m_8055_(blockpos)));
      }

      if (!this.f_19853_.f_46443_) {
         this.applyHolyWater();
         this.f_19853_.m_46796_(2007, this.m_20183_(), (new Color(0, 255, 195)).getRGB());
         this.m_146870_();
      }
   }

   private void applyHolyWater() {
      Item var2 = this.m_7846_().m_41720_();
      if (var2 instanceof HolyWaterItem) {
         HolyWaterItem holyWaterItem = (HolyWaterItem)var2;
         int holyLevel = holyWaterItem.getHolyLevel();
         double holyRadius = holyLevel > 0 ? 4.0D + (double)(2 * holyLevel) : 4.0D;
         AABB aabb = this.m_20191_().m_82377_(holyRadius, holyRadius / 2.0D, holyRadius);
         List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entityx) -> {
            return RaceHelper.isAffectedByHolyCoat(entityx) || entityx.m_21023_((MobEffect)TensuraMobEffects.CURSE.get());
         });
         if (!list.isEmpty()) {
            Iterator var7 = list.iterator();

            while(var7.hasNext()) {
               LivingEntity entity = (LivingEntity)var7.next();
               if (this.m_20280_(entity) < 16.0D) {
                  if (RaceHelper.isAffectedByHolyCoat(entity)) {
                     MobEffect mobeffect = (MobEffect)TensuraMobEffects.HOLY_DAMAGE.get();
                     mobeffect.m_19461_(this, this.m_37282_(), entity, holyLevel, 0.0D);
                  } else if (entity.m_21023_((MobEffect)TensuraMobEffects.CURSE.get())) {
                     if (this.m_7846_().m_150930_((Item)TensuraConsumableItems.GREATER_HOLY_WATER.get())) {
                        entity.m_21195_((MobEffect)TensuraMobEffects.CURSE.get());
                     } else {
                        MobEffectInstance instance = entity.m_21124_((MobEffect)TensuraMobEffects.CURSE.get());
                        if (instance != null) {
                           int level = instance.m_19564_();
                           int duration = instance.m_19557_();
                           entity.m_21195_((MobEffect)TensuraMobEffects.CURSE.get());
                           if (level > holyLevel) {
                              entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.CURSE.get(), duration, level - (holyLevel + 1)));
                           }
                        }
                     }

                     this.f_19853_.m_5594_((Player)null, this.m_20183_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               }
            }

         }
      }
   }

   protected void placeFire(Entity entity) {
      Item var3 = this.m_7846_().m_41720_();
      if (var3 instanceof HolyWaterItem) {
         HolyWaterItem holyWaterItem = (HolyWaterItem)var3;
         int yPos = Mth.m_14107_(entity.m_20186_()) - 1;
         int xPos = Mth.m_14107_(entity.m_20185_());
         int zPos = Mth.m_14107_(entity.m_20189_());
         BlockState holyFire = (BlockState)((Block)TensuraBlocks.HOLY_FIRE.get()).m_49966_().m_61124_(HolyFireBlock.AGE, 0);
         int holyLevel = holyWaterItem.getHolyLevel();
         boolean placeFire = false;
         boolean removeBlock = false;

         for(int j = -1 - holyLevel; j <= 1 + holyLevel; ++j) {
            for(int k = -1 - holyLevel; k <= 1 + holyLevel; ++k) {
               for(int i = 0; i <= 2; ++i) {
                  int newYPos = yPos + i;
                  int newXPos = xPos + j;
                  int newZPos = zPos + k;
                  BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
                  BlockState blockState = this.f_19853_.m_8055_(blockpos);
                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, (ManasSkillInstance)null, blockpos);
                  if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                     if (blockState.m_60767_().m_76336_() && blockState.m_60819_().m_76178_()) {
                        BlockPos blockPosDown = blockpos.m_7495_();
                        BlockState blockStateDown = this.f_19853_.m_8055_(blockPosDown);
                        if (blockStateDown.m_60783_(this.f_19853_, blockPosDown, Direction.UP)) {
                           removeBlock = this.f_19853_.m_7471_(blockpos, true) || removeBlock;
                        }
                     }

                     if (HolyFireBlock.canBePlacedAt(this.f_19853_, blockpos)) {
                        placeFire = this.f_19853_.m_46597_(blockpos, holyFire) || placeFire;
                        this.f_19853_.m_186460_(blockpos, blockState.m_60734_(), HolyFireBlock.getFireTickDelay(this.f_19853_.f_46441_));
                     }

                     MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, (ManasSkillInstance)null, blockpos));
                  }
               }
            }
         }

         if (removeBlock && this.m_37282_() != null) {
            this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
         }

         if (placeFire && this.m_37282_() != null) {
            this.f_19853_.m_142346_(this.m_37282_(), GameEvent.f_157792_, this.m_20183_());
         }

      }
   }
}
