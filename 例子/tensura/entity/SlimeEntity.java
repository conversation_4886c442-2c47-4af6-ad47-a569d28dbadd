package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.controller.JumpingEntityMoveControl;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.IJumpingEntity;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraBiomeTags;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.SlimeType;
import com.github.manasmods.tensura.entity.variant.SlimeVariant;
import com.github.manasmods.tensura.event.EnergyRegenerateTickEvent;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.core.Registry;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.Bucketable;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.Slime;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.DyeColor;
import net.minecraft.world.item.DyeItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ItemUtils;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.storage.loot.BuiltInLootTables;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.Tags.Biomes;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.network.NetworkHooks;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class SlimeEntity extends TensuraTamableEntity implements HasCustomInventoryScreen, PlayerRideableJumping, Bucketable, IJumpingEntity, IAnimatable, IGiantMob {
   private static final EntityDataAccessor<Integer> ID_SIZE;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> SLIME_TYPE;
   private static final EntityDataAccessor<Boolean> FROM_BUCKET;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private static final EntityDataAccessor<Boolean> SUPPER_MASSIVE;
   private static final EntityDataAccessor<Boolean> CHILLED;
   public float squishAmount;
   public float squishFactor;
   public float prevSquishFactor;
   public boolean wasOnGround;
   public static final int MAX_SIZE = 30;
   public static final int DEFAULT_SIZE = 3;
   public static final int MASSIVE_SIZE = 18;
   public static final int MAX_PRODUCE = 3;
   public int slimeballTime;
   public int slimeProduceTime;
   public int producedTimes;
   public int selfRegen;
   public SimpleContainer slimeInventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   public int trappingCoolDown;
   public boolean canTrap;
   private boolean trappingTarget;
   public int hurtTicks;
   public int jumpTicks;
   private static final EntityDataAccessor<Boolean> HURT;
   private static final EntityDataAccessor<Boolean> JUMP;
   private final AnimationFactory factory;
   public static final AnimationBuilder IDLE;
   public static final AnimationBuilder WALK;
   public static final AnimationBuilder DAMAGED;
   public static final AnimationBuilder JUMPING;

   public SlimeEntity(EntityType<? extends SlimeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.slimeballTime = this.f_19796_.m_188503_(6000) + 6000;
      this.slimeProduceTime = this.f_19796_.m_188503_(12000) + 12000;
      this.producedTimes = 0;
      this.selfRegen = 20;
      this.hasChestVarChanged = false;
      this.trappingCoolDown = 120;
      this.canTrap = false;
      this.trappingTarget = false;
      this.hurtTicks = 0;
      this.jumpTicks = 0;
      this.factory = GeckoLibUtil.createFactory(this);
      this.f_21364_ = 1;
      this.f_19793_ = 2.0F;
      this.initSlimeInventory();
      this.f_21342_ = new JumpingEntityMoveControl(this, 0.05F);
      this.f_21344_ = new StraightFlightNavigator(this, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 5.0D).m_22268_(Attributes.f_22281_, 0.5D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22288_, 0.3D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_(Attributes.f_22279_, 0.44999998807907104D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 4.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new MeleeAttackGoal(this, 1.0D, false));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 0.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(3, new TensuraTamableEntity.WanderAroundPosGoal(this, 60, 1.0D, 10, 7));
      this.f_21345_.m_25352_(4, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Slime.class, false));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(ID_SIZE, 1);
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(SLIME_TYPE, 0);
      this.f_19804_.m_135372_(FROM_BUCKET, false);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
      this.f_19804_.m_135372_(SUPPER_MASSIVE, Boolean.FALSE);
      this.f_19804_.m_135372_(CHILLED, Boolean.FALSE);
      this.f_19804_.m_135372_(HURT, Boolean.FALSE);
      this.f_19804_.m_135372_(JUMP, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("Size", this.getSize() - 1);
      pCompound.m_128379_("wasOnGround", this.wasOnGround);
      pCompound.m_128405_("Variant", this.getTypeVariant());
      pCompound.m_128405_("Type", this.getTypeSlime());
      pCompound.m_128405_("RegenTime", this.selfRegen);
      pCompound.m_128405_("SlimeballTime", this.slimeballTime);
      pCompound.m_128405_("SlimeProduceTime", this.slimeProduceTime);
      pCompound.m_128405_("ProduceTimes", this.producedTimes);
      pCompound.m_128379_("FromBucket", this.m_27487_());
      pCompound.m_128379_("Saddled", this.isSaddled());
      pCompound.m_128379_("Chested", this.isChested());
      pCompound.m_128379_("Chilled", this.isChilled());
      pCompound.m_128379_("SuperMassive", this.isMassive());
      pCompound.m_128405_("TrappingCooldown", this.trappingCoolDown);
      pCompound.m_128379_("Hurt", this.isHurt());
      pCompound.m_128379_("Jump", this.jump());
      if (this.slimeInventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.slimeInventory.m_6643_(); ++i) {
            ItemStack itemstack = this.slimeInventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         pCompound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag pCompound) {
      this.setSize(pCompound.m_128451_("Size") + 1, false, false, false);
      super.m_7378_(pCompound);
      this.wasOnGround = pCompound.m_128471_("wasOnGround");
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, pCompound.m_128451_("Variant"));
      this.f_19804_.m_135381_(SLIME_TYPE, pCompound.m_128451_("Type"));
      if (pCompound.m_128441_("RegenTime")) {
         this.selfRegen = pCompound.m_128451_("RegenTime");
      }

      if (pCompound.m_128441_("SlimeballTime")) {
         this.slimeballTime = pCompound.m_128451_("SlimeballTime");
      }

      if (pCompound.m_128441_("SlimeProduceTime")) {
         this.slimeProduceTime = pCompound.m_128451_("SlimeProduceTime");
      }

      if (pCompound.m_128441_("ProduceTimes")) {
         this.producedTimes = pCompound.m_128451_("ProduceTimes");
      }

      this.setSaddled(pCompound.m_128471_("Saddled"));
      this.setChested(pCompound.m_128471_("Chested"));
      this.m_27497_(pCompound.m_128471_("FromBucket"));
      this.f_19804_.m_135381_(SUPPER_MASSIVE, pCompound.m_128471_("SuperMassive"));
      if (pCompound.m_128441_("TrappingCooldown")) {
         this.trappingCoolDown = pCompound.m_128451_("TrappingCooldown");
      }

      this.setChilled(pCompound.m_128471_("Chilled"));
      this.setHurt(pCompound.m_128471_("Hurt"));
      this.setJumpAnimation(pCompound.m_128471_("Jump"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.slimeInventory != null) {
         listTag = pCompound.m_128437_("Items", 10);
         this.initSlimeInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.slimeInventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = pCompound.m_128437_("Items", 10);
         this.initSlimeInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initSlimeInventory();
            this.slimeInventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public void setSize(int pSize, boolean pResetHealth) {
      this.setSize(pSize, true, true, true);
   }

   public void setSize(int pSize, boolean resetStat, boolean pResetHealth, boolean resetEP) {
      int oldSize = this.getSize();
      this.f_19804_.m_135381_(ID_SIZE, pSize);
      this.m_20090_();
      this.m_6210_();
      if (resetStat) {
         float difference = (float)(pSize - oldSize);
         this.addBaseValue(Attributes.f_22276_, 3.0F * difference);
         this.addBaseValue((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get(), 3.0F * difference);
         this.m_21051_(Attributes.f_22279_).m_22100_((double)(0.1F + 0.05F * (float)pSize));
         if (pSize <= 4) {
            this.addBaseValue(Attributes.f_22281_, 0.05F * difference);
            this.m_21051_(Attributes.f_22288_).m_22100_(0.6D);
         } else {
            this.addBaseValue(Attributes.f_22281_, 0.2F * difference);
            this.m_21051_(Attributes.f_22288_).m_22100_(0.15D * (double)pSize);
         }

         if (pResetHealth) {
            this.m_21153_(this.m_21233_());
         }

         if (!this.getSlimeType().equals(SlimeType.SUMMONED)) {
            this.f_21364_ = pSize * 3;
         }

         if (resetEP) {
            double currentEP = TensuraEPCapability.getEP(this);
            if (currentEP != 0.0D) {
               double EP = pSize < 4 && oldSize < 4 ? 250.0D : 1000.0D;
               TensuraEPCapability.setLivingEP(this, currentEP + EP * (double)difference);
            }
         }
      }
   }

   private void addBaseValue(Attribute attribute, float amount) {
      AttributeInstance instance = this.m_21051_(attribute);
      if (instance != null) {
         instance.m_22100_(instance.m_22115_() + (double)amount);
         if (attribute == Attributes.f_22281_ && instance.m_22115_() < 0.5D) {
            instance.m_22100_(0.5D);
         }

      }
   }

   public int getSize() {
      return (Integer)this.f_19804_.m_135370_(ID_SIZE);
   }

   public boolean isTiny() {
      return this.getSize() <= 1;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      return super.m_6972_(pPose).m_20388_(0.255F * (float)this.getSize());
   }

   public boolean isMassive() {
      return (Boolean)this.f_19804_.m_135370_(SUPPER_MASSIVE);
   }

   public void setMassive(boolean massive) {
      if (massive) {
         SkillAPI.getSkillsFrom(this).learnSkill((ManasSkill)ExtraSkills.ULTRASPEED_REGENERATION.get());
      }

      this.f_19804_.m_135381_(SUPPER_MASSIVE, massive);
   }

   public boolean isChilled() {
      return (Boolean)this.f_19804_.m_135370_(CHILLED);
   }

   public void setChilled(boolean chilled) {
      this.f_19804_.m_135381_(CHILLED, chilled);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public void m_8119_() {
      super.m_8119_();
      this.jumpingSquishTick();
      this.wasOnGround = this.f_19861_;
      if (this.setWantedTarget(this, 25.0D)) {
         this.wasOnGround = true;
      } else if (!this.setWantedOwner(this, 100.0D)) {
         this.wasOnGround = true;
      }

      if (!this.f_19853_.f_46443_ && this.m_6084_()) {
         if (--this.selfRegen <= 0 && this.m_21223_() < this.m_21233_()) {
            this.selfRegen();
         }

         this.massiveSlimeTick();
         switch(this.getSlimeType()) {
         case PRODUCE:
            this.produceSlimeTick();
            break;
         case SUMMONED:
            this.summonedSlimeTick();
         }

         LivingEntity controller = this.getControllingPassenger();
         if (!this.m_21824_() || controller != null && this.m_21830_(controller)) {
            SimpleContainer container = this.isChested() ? this.slimeInventory : null;
            if (this.getSize() >= 15) {
               this.breakBlocks(this, 1.0F, false, container);
            }
         }
      }

      this.slimeInventory();
      this.animationTick();
   }

   private void jumpingSquishTick() {
      this.squishFactor += (this.squishAmount - this.squishFactor) * 0.5F;
      this.prevSquishFactor = this.squishFactor;
      if (this.f_19861_ && !this.wasOnGround) {
         for(int j = 0; j < 8; ++j) {
            float f = this.f_19796_.m_188501_() * 6.2831855F;
            float f1 = this.f_19796_.m_188501_() * 0.5F + 0.5F;
            float f2 = Mth.m_14031_(f) * 0.5F * f1;
            float f3 = Mth.m_14089_(f) * 0.5F * f1;
            this.f_19853_.m_7106_(ParticleTypes.f_123753_, this.m_20185_() + (double)f2, this.m_20186_(), this.m_20189_() + (double)f3, 0.0D, 0.0D, 0.0D);
         }

         this.m_5496_(this.getSquishSound(), this.getJumpSoundVolume(), ((this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F) / 0.8F);
         this.squishAmount = -0.35F;
      } else if (!this.f_19861_ && this.wasOnGround) {
         this.squishAmount = 2.0F;
      }

      this.alterSquishAmount();
   }

   protected void selfRegen() {
      if (this.isMassive()) {
         this.m_5634_(10.0F);
      } else {
         this.m_5634_(2.0F);
      }

      this.selfRegen = 20;
   }

   private void massiveSlimeTick() {
      if (this.isMassive()) {
         if (this.trappingTarget && (this.m_5448_() == null || this.m_5448_() != null && this.m_20280_(this.m_5448_()) > 4.0D)) {
            this.trappingTarget = false;
         }

         if (--this.trappingCoolDown <= 0) {
            if (!this.canTrap) {
               boolean hasThrownAwayTarget = false;
               Iterator var2 = this.m_20197_().iterator();

               while(var2.hasNext()) {
                  Entity passenger = (Entity)var2.next();
                  if (!passenger.equals(this.getControllingPassenger()) && this.m_20160_() && !(passenger instanceof Player)) {
                     passenger.m_8127_();
                     this.trappingCoolDown = 120;
                     hasThrownAwayTarget = true;
                     Vec3 throwVec = this.m_20252_(10.0F);
                     LivingEntity var7 = this.m_21826_();
                     if (var7 instanceof Player) {
                        Player owner = (Player)var7;
                        var7 = this.getControllingPassenger();
                        if (var7 instanceof Player) {
                           Player controller = (Player)var7;
                           if (controller.equals(owner)) {
                              throwVec = owner.m_20252_(10.0F);
                           }
                        }
                     }

                     passenger.m_20256_(passenger.m_20184_().m_82520_(throwVec.m_7096_(), 1.0D + throwVec.m_7098_(), throwVec.m_7094_()));
                  }
               }

               if (!hasThrownAwayTarget) {
                  this.canTrap = true;
               }

            }
         }
      }
   }

   private void summonedSlimeTick() {
      if (--this.slimeProduceTime <= 0) {
         LivingEntity var2 = this.m_21826_();
         if (var2 instanceof Player) {
            Player player = (Player)var2;
            player.m_5661_(Component.m_237115_("tensura.message.slime.despawn").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN).m_131136_(true)), false);
         }

         this.m_5496_(SoundEvents.f_12411_, 0.8F, 0.8F);
         this.m_142467_(RemovalReason.DISCARDED);
      }
   }

   private void produceSlimeTick() {
      if (this.isMassive()) {
         this.massiveSlimeTick();
      } else {
         if (--this.slimeballTime <= 0) {
            this.setJumpAnimation(Boolean.TRUE);
            this.m_5496_(SoundEvents.f_11752_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
            this.m_19998_(Items.f_42518_);
            this.slimeballTime = this.f_19796_.m_188503_(6000) + 6000;
         }

         if (--this.slimeProduceTime <= 0 && this.producedTimes < 3 && this.m_21824_()) {
            this.setJumpAnimation(Boolean.TRUE);
            this.m_5496_(SoundEvents.f_11752_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
            if (this.getSize() < 3) {
               this.setSize(this.getSize() + 1, true);
            } else {
               SlimeEntity slime = new SlimeEntity((EntityType)TensuraEntityTypes.SLIME.get(), this.m_9236_());
               slime.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
               slime.setVariant(this.getVariant());
               slime.setType(SlimeType.byId(this.f_19796_.m_188503_(2)));
               if (this.m_21824_()) {
                  LivingEntity var3 = this.m_21826_();
                  if (var3 instanceof Player) {
                     Player owner = (Player)var3;
                     slime.m_21828_(owner);
                     slime.m_21839_(this.m_21827_());
                     slime.setWandering(this.isWandering());
                     slime.setWanderPos(this.getWanderPos());
                  }
               }

               this.m_9236_().m_7967_(slime);
               ++this.producedTimes;
            }

            this.slimeProduceTime = this.f_19796_.m_188503_(12000) + 12000;
         }

      }
   }

   private void slimeInventory() {
      if (!this.f_19853_.m_5776_() && this.f_19797_ % 10 == 0) {
         MinecraftForge.EVENT_BUS.post(new EnergyRegenerateTickEvent(this));
      }

      if (this.hasChestVarChanged && this.slimeInventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.slimeInventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.slimeInventory.m_8020_(i), 1.0F);
               }

               this.slimeInventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   private void animationTick() {
      if (this.isHurt()) {
         ++this.hurtTicks;
         if (this.hurtTicks >= 2) {
            this.setHurt(Boolean.FALSE);
            this.hurtTicks = 0;
         }
      }

      if (this.jump()) {
         ++this.jumpTicks;
         if (this.jumpTicks >= 3) {
            this.setJumpAnimation(Boolean.FALSE);
            this.jumpTicks = 0;
         }
      }

   }

   public boolean m_6094_() {
      return this.m_6084_();
   }

   public static boolean isSlime(Entity entity) {
      return entity instanceof SlimeEntity || entity instanceof MetalSlimeEntity || entity instanceof Slime;
   }

   public boolean isSameOwner(TamableAnimal entity) {
      return entity.m_21824_() && this.m_21824_() && Objects.equals(this.m_21826_(), entity.m_21826_());
   }

   private boolean isSameSizeMergeSlime(SlimeEntity slime) {
      if (!slime.getSlimeType().equals(SlimeType.MERGE)) {
         return false;
      } else if (!this.getSlimeType().equals(SlimeType.MERGE)) {
         return false;
      } else if (slime.getSize() != this.getSize()) {
         return false;
      } else {
         return this.getSize() <= 30;
      }
   }

   public void m_6123_(Player pEntity) {
      if (!this.m_21830_(pEntity)) {
         if (this.m_6142_()) {
            this.damageCollidedEntity(pEntity);
            if (this.getControllingPassenger() instanceof Player && !pEntity.equals(this.getControllingPassenger())) {
               pEntity.m_147207_(new MobEffectInstance(MobEffects.f_19599_, 30, 1, false, false, false), this);
               pEntity.m_147207_(new MobEffectInstance(MobEffects.f_19613_, 30, 1, false, false, false), this);
               pEntity.m_147207_(new MobEffectInstance(MobEffects.f_19610_, 30, 0, false, false, false), this);
            }

            if (!pEntity.m_7500_() && !pEntity.m_5833_() && this.isMassive() && (double)this.m_20270_(pEntity) < 3.5D) {
               pEntity.m_147207_(new MobEffectInstance(MobEffects.f_19597_, 20, 2, false, false, false), this);
               if (pEntity.equals(this.m_5448_())) {
                  this.trappingTarget = true;
               }
            }

         }
      }
   }

   public void m_7334_(Entity pEntity) {
      super.m_7334_(pEntity);
      if (this.m_6142_()) {
         if (!isSlime(pEntity) && pEntity.m_20184_().m_7098_() < 0.0D) {
            pEntity.m_183634_();
            pEntity.m_20256_(pEntity.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
         }

         if (!this.canSlimeMerge(pEntity)) {
            if (!isSlime(pEntity) || this.m_5448_() == pEntity) {
               if (!this.m_7307_(pEntity)) {
                  if (pEntity instanceof LivingEntity) {
                     LivingEntity living = (LivingEntity)pEntity;
                     if (this.m_21830_(living)) {
                        return;
                     }
                  }

                  this.damageCollidedEntity(pEntity);
                  this.supermassiveTrapping(pEntity);
               }
            }
         }
      }
   }

   protected boolean canSlimeMerge(Entity pEntity) {
      if (pEntity instanceof SlimeEntity) {
         SlimeEntity slime = (SlimeEntity)pEntity;
         if (this.isSameSizeMergeSlime(slime) && (this.isSameOwner(slime) || !this.m_21824_() && !slime.m_21824_())) {
            if (TensuraEPCapability.getEP(this) >= TensuraEPCapability.getEP(slime)) {
               this.mergeSlime(this, slime);
            } else {
               this.mergeSlime(slime, this);
            }

            return true;
         }
      }

      return false;
   }

   private void mergeSlime(SlimeEntity remain, SlimeEntity remove) {
      remove.m_142467_(RemovalReason.DISCARDED);
      remain.setJumpAnimation(Boolean.TRUE);
      int oldSize = remain.getSize();
      remain.setSize(oldSize + 1, true);
      if (oldSize < 18 && remain.getSize() >= 18) {
         remain.setMassive(true);
      }

      LivingEntity var5 = remain.m_21826_();
      if (var5 instanceof Player) {
         Player player = (Player)var5;
         player.m_5661_(Component.m_237115_("tensura.message.slime.merge").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN).m_131136_(true)), false);
         if (remain.isMassive() && oldSize < 18) {
            player.m_5661_(Component.m_237115_("tensura.message.slime.massive").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD).m_131136_(true)), false);
         }
      }

   }

   protected void supermassiveTrapping(Entity entity) {
      if (entity.equals(this.m_5448_())) {
         if (!this.trappingTarget) {
            this.trappingTarget = true;
            if (!(this.m_20270_(entity) > 5.0F)) {
               if (this.canTrap) {
                  if (this.trappingCoolDown <= 0) {
                     entity.m_7998_(this, true);
                     this.setHurt(Boolean.TRUE);
                     this.canTrap = false;
                     this.trappingCoolDown = 120;
                  }
               }
            }
         }
      }
   }

   protected void damageCollidedEntity(Entity entity) {
      float damage = entity == this.m_5448_() ? (float)this.m_21133_(Attributes.f_22281_) : (this.isTiny() ? 0.0F : 0.5F);
      this.dealDamage(entity, damage, entity == this.m_5448_());
   }

   protected boolean dealDamage(Entity entity, float damage, boolean aggro) {
      if (damage < 0.0F) {
         return false;
      } else if (!this.m_6084_()) {
         return false;
      } else {
         DamageSource damageSource = TensuraDamageSources.corrosion(this);
         if (!aggro) {
            damageSource = damageSource.m_181120_();
         }

         if (entity.m_20202_() == this) {
            damage *= 2.0F;
         }

         if (this.m_20270_(entity) < this.m_20205_() / 2.0F + 1.0F && entity.m_6469_(damageSource, damage)) {
            this.m_5496_(SoundEvents.f_12384_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
            this.m_19970_(this, entity);
            return true;
         } else {
            return false;
         }
      }
   }

   public boolean isTamingFood(ItemStack stack) {
      return stack.m_204117_(TensuraTags.Items.SLIME_TAMING_FOOD);
   }

   public boolean m_6898_(ItemStack stack) {
      return stack.m_204117_(TensuraTags.Items.SLIME_FOOD);
   }

   public boolean isDyeable() {
      return true;
   }

   private float getHealAmount(Item item) {
      float healAmount = 2.0F;
      if (item.equals(TensuraMobDropItems.SLIME_CHUNK.get())) {
         healAmount = 3.0F;
      }

      if (item.equals(TensuraMaterialItems.MAGIC_ORE.get())) {
         healAmount = 20.0F;
      }

      return healAmount;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      Item item = itemstack.m_41720_();
      if (item instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else if (this.f_19853_.f_46443_) {
         boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack) || this.m_6898_(itemstack);
         return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
      } else {
         if (this.m_21824_()) {
            if (this.m_21830_(player) && this.m_6084_()) {
               if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
                  this.m_5634_(this.getHealAmount(item));
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.SUCCESS;
               }

               ItemStack helmet;
               if (this.getSize() <= 4 && item == Items.f_42446_ && this.getSlimeType() != SlimeType.SUMMONED) {
                  this.m_5496_(this.m_142623_(), 1.0F, 1.0F);
                  helmet = this.m_28282_();
                  this.m_6872_(helmet);
                  ItemStack filledBucket = ItemUtils.m_41817_(itemstack, player, helmet, false);
                  player.m_21008_(hand, filledBucket);
                  Level level = this.f_19853_;
                  if (!level.f_46443_) {
                     CriteriaTriggers.f_10576_.m_38772_((ServerPlayer)player, helmet);
                  }

                  this.m_146870_();
                  if (player instanceof ServerPlayer) {
                     ServerPlayer serverPlayer = (ServerPlayer)player;
                     TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.GET_BUCKETED);
                  }

                  return InteractionResult.SUCCESS;
               }

               if (this.getSize() < 30 && item == TensuraMobDropItems.SLIME_CORE.get() && !this.getSlimeType().equals(SlimeType.SUMMONED) && this.getClass() != MetalSlimeEntity.class) {
                  this.setJumpAnimation(Boolean.TRUE);
                  this.setSize(this.getSize() + 1, true);
                  ServerPlayer serverPlayer;
                  if (player instanceof ServerPlayer) {
                     serverPlayer = (ServerPlayer)player;
                     TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.GROW_A_SLIME);
                  }

                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.m_5496_((SoundEvent)TensuraSoundEvents.EATING.get(), 1.0F, 0.8F);
                  if (this.getSize() >= 25) {
                     this.f_19793_ = 3.0F;
                  }

                  if (this.getSize() >= 18) {
                     if (player instanceof ServerPlayer) {
                        serverPlayer = (ServerPlayer)player;
                        TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.KING_SLIME);
                     }

                     if (!this.isMassive()) {
                        player.m_5661_(Component.m_237115_("tensura.message.slime.massive").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD).m_131136_(true)), false);
                        this.setMassive(true);
                     }
                  } else if (this.getSize() >= 30) {
                     this.f_19793_ = 4.0F;
                     player.m_5661_(Component.m_237115_("tensura.message.slime.maxsize").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD).m_131136_(true)), false);
                  }

                  return InteractionResult.SUCCESS;
               }

               if (this.isDyeable() && item instanceof DyeItem) {
                  DyeItem dyeItem = (DyeItem)item;
                  if (!((DyeColor)SlimeVariant.DYE_BY_VARIANT.get(this.getVariant())).equals(dyeItem.m_41089_())) {
                     SlimeVariant.setVariantFromColor(this, dyeItem.m_41089_());
                     this.setHurt(Boolean.TRUE);
                     if (!player.m_150110_().f_35937_) {
                        itemstack.m_41774_(1);
                     }

                     return InteractionResult.SUCCESS;
                  }
               }

               if (item instanceof ArmorItem) {
                  ArmorItem armorItem = (ArmorItem)item;
                  if (armorItem.m_40402_().equals(EquipmentSlot.HEAD)) {
                     helmet = this.m_6844_(EquipmentSlot.HEAD);
                     if (!helmet.m_41619_()) {
                        this.m_19983_(helmet);
                     }

                     this.m_8061_(EquipmentSlot.HEAD, itemstack.m_41777_());
                     if (!player.m_150110_().f_35937_) {
                        itemstack.m_41774_(1);
                     }

                     this.setHurt(Boolean.TRUE);
                     this.m_5496_(SoundEvents.f_11675_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                     return InteractionResult.SUCCESS;
                  }
               }

               if (!this.m_6844_(EquipmentSlot.HEAD).m_41619_() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19983_(this.m_6844_(EquipmentSlot.HEAD));
                  this.m_8061_(EquipmentSlot.HEAD, ItemStack.f_41583_);
                  this.setHurt(Boolean.TRUE);
                  return InteractionResult.SUCCESS;
               }

               if ((player.m_36341_() || !this.isMassive()) && item != TensuraMobDropItems.SLIME_CORE.get()) {
                  if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
                     this.commanding(player);
                     return InteractionResult.SUCCESS;
                  }

                  return InteractionResult.PASS;
               }

               if (this.isChested() && item.equals(TensuraToolItems.SLIME_STAFF.get())) {
                  this.openGUI(player);
               } else if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.setJumpAnimation(Boolean.TRUE);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               } else if (!this.isChested() && itemstack.m_204117_(net.minecraftforge.common.Tags.Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.setJumpAnimation(Boolean.TRUE);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }
               } else if (this.isChested() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.slimeInventory.m_6643_(); ++i) {
                     this.m_19983_(this.slimeInventory.m_8020_(i));
                  }

                  this.slimeInventory.m_6211_();
                  this.setChested(false);
                  this.setHurt(Boolean.TRUE);
               } else if (this.isSaddled() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  this.setHurt(Boolean.TRUE);
               } else if (this.isSaddled()) {
                  player.m_7998_(this, true);
               } else if (this.isChested()) {
                  this.m_213583_(player);
               }

               return InteractionResult.SUCCESS;
            }
         } else if (this.isTamingFood(itemstack)) {
            if (!player.m_150110_().f_35937_) {
               itemstack.m_41774_(1);
            }

            if (this.f_19796_.m_188503_(9) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
               this.m_21828_(player);
               this.f_21344_.m_26573_();
               this.m_6710_((LivingEntity)null);
               this.m_21839_(true);
               this.setJumpAnimation(Boolean.TRUE);
               this.f_19853_.m_7605_(this, (byte)7);
            } else {
               this.setHurt(Boolean.TRUE);
               this.f_19853_.m_7605_(this, (byte)6);
            }

            return InteractionResult.SUCCESS;
         }

         return super.m_6071_(player, hand);
      }
   }

   private void initSlimeInventory() {
      SimpleContainer slimeChest = this.slimeInventory;
      this.slimeInventory = new SimpleContainer(45) {
         public boolean m_6542_(Player player) {
            return SlimeEntity.this.m_6084_() && !SlimeEntity.this.f_19817_;
         }
      };
      if (slimeChest != null) {
         int i = Math.min(slimeChest.m_6643_(), this.slimeInventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = slimeChest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.slimeInventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.slimeInventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void openGUI(Player playerEntity) {
      if (!this.f_19853_.f_46443_) {
         if (!this.m_20363_(playerEntity)) {
            NetworkHooks.openScreen((ServerPlayer)playerEntity, this.getMenu());
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39961_, menu, inventory, SlimeEntity.this.slimeInventory, 5);
            }

            public Component m_5446_() {
               return Component.m_237115_("tensura.message.slime.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.f_46443_) {
         if (this.slimeInventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.slimeInventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.slimeInventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.f_19853_.f_46443_) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.slimeInventory.m_6643_(); ++i) {
               this.m_19983_(this.slimeInventory.m_8020_(i));
            }
         }

         this.slimeInventory.m_6211_();
         this.setChested(false);
      }

   }

   protected float m_21519_(EquipmentSlot pSlot) {
      return this.m_21824_() ? 1.0F : super.m_21519_(pSlot);
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         double extraX;
         double extraZ;
         double yOffset;
         label14: {
            passenger.m_183634_();
            float radius = -0.25F;
            float angle = 0.017453292F * this.f_20883_;
            extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
            extraZ = (double)(radius * Mth.m_14089_(angle));
            yOffset = this.m_20186_() + 0.5D + this.m_6048_() + passenger.m_6049_();
            if (passenger instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)passenger;
               if (this.m_21830_(living)) {
                  break label14;
               }
            }

            yOffset -= 3.0D;
         }

         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public double m_6048_() {
      float f = Math.min(0.25F, this.f_20924_);
      float f1 = this.f_20925_;
      return (double)this.m_20206_() + (double)(0.12F * Mth.m_14089_(f1 * 0.7F) * 0.7F * f);
   }

   public boolean m_6146_() {
      return true;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean m_7132_() {
      return this.isSaddled() && this.m_20096_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.m_5496_(SoundEvents.f_12387_, 0.4F, 1.0F);
      }
   }

   public void m_8012_() {
      this.m_5496_(SoundEvents.f_12388_, 0.4F, 1.0F);
   }

   public void m_7888_(int pJumpPower) {
      if (this.isSaddled()) {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         if (pJumpPower >= 90) {
            this.playerJumpPendingScale = 1.0F;
         } else {
            this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
         }

      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null && this.isSaddled()) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               this.setJumpAnimation(Boolean.TRUE);
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               if (!this.isInFluidType()) {
                  if (this.m_20096_()) {
                     speed /= 6.0F;
                  }

                  this.m_7910_(speed);
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               } else {
                  if (this.isInFluidType((fluidType, height) -> {
                     return height > this.m_20204_();
                  }) && controller.f_20899_) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.07D, 0.0D));
                  } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.07D, 0.0D));
                  }

                  AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
                  if (instance != null) {
                     instance.m_22100_(controller.m_20142_() ? 7.0D : 5.0D);
                  }

                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
            if (instance != null && instance.m_22115_() != 4.0D) {
               instance.m_22100_(4.0D);
            }

            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }

      }
   }

   @Nonnull
   public ItemStack m_28282_() {
      ItemStack stack = new ItemStack((ItemLike)TensuraMaterialItems.SLIME_IN_A_BUCKET.get());
      if (this.m_8077_()) {
         stack.m_41714_(this.m_7770_());
      }

      return stack;
   }

   public boolean m_27487_() {
      return (Boolean)this.f_19804_.m_135370_(FROM_BUCKET);
   }

   public void m_27497_(boolean pBoolean) {
      this.f_19804_.m_135381_(FROM_BUCKET, pBoolean);
   }

   public boolean m_8023_() {
      return super.m_8023_() || this.m_27487_() || this.m_21824_();
   }

   public void m_6872_(@Nonnull ItemStack bucket) {
      if (this.m_8077_()) {
         bucket.m_41714_(this.m_7770_());
      }

      CompoundTag compound = bucket.m_41784_();
      compound.m_128365_("SlimeData", this.serializeNBT());
      compound.m_128379_("MetalSlime", this.getClass() == MetalSlimeEntity.class);
      compound.m_128379_("Supermassive", this.getClass() == SupermassiveSlimeEntity.class);
   }

   public void m_142278_(@Nonnull CompoundTag pCompound) {
      if (pCompound.m_128441_("SlimeData")) {
         this.m_20258_(pCompound.m_128469_("SlimeData"));
      }
   }

   @Nonnull
   public SoundEvent m_142623_() {
      return SoundEvents.f_12469_;
   }

   public SlimeVariant getVariant() {
      return SlimeVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(SlimeVariant variant) {
      if (this.isDyeable()) {
         this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
      }
   }

   public SlimeType getSlimeType() {
      return SlimeType.byId(this.getTypeSlime() & 255);
   }

   private int getTypeSlime() {
      return (Integer)this.f_19804_.m_135370_(SLIME_TYPE);
   }

   public void setType(SlimeType type) {
      this.f_19804_.m_135381_(SLIME_TYPE, type.getId() & 255);
   }

   public int getJumpDelay() {
      return this.f_19796_.m_188503_(15);
   }

   protected void alterSquishAmount() {
      this.squishAmount *= 0.6F;
   }

   public void m_6210_() {
      double d0 = this.m_20185_();
      double d1 = this.m_20186_();
      double d2 = this.m_20189_();
      super.m_6210_();
      this.m_6034_(d0, d1, d2);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (ID_SIZE.equals(pKey)) {
         this.m_6210_();
         this.m_146922_(this.f_20885_);
         this.f_20883_ = this.f_20885_;
         if (this.m_20069_() && this.f_19796_.m_188503_(20) == 0) {
            this.m_5841_();
         }
      }

      super.m_7350_(pKey);
   }

   protected void m_6135_() {
      Vec3 vec3 = this.m_20184_();
      this.m_20334_(vec3.f_82479_, (double)this.m_6118_() * 1.5D, vec3.f_82481_);
      this.f_19812_ = true;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   protected ResourceLocation m_7582_() {
      if (this.getSlimeType().equals(SlimeType.SUMMONED)) {
         return BuiltInLootTables.f_78712_;
      } else {
         return this.isChilled() ? new ResourceLocation(Registry.f_122826_.m_7981_(this.m_6095_()).m_135827_(), "entities/chilled_slime") : this.m_6095_().m_20677_();
      }
   }

   protected void m_7625_(DamageSource pDamageSource, boolean pAttackedRecently) {
      if (this.getClass() == SlimeEntity.class) {
         for(int i = 0; i < this.getSize(); ++i) {
            if (this.f_19796_.m_188503_(10) < 8) {
               super.m_7625_(pDamageSource, pAttackedRecently);
            }
         }
      } else {
         super.m_7625_(pDamageSource, pAttackedRecently);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (this.isMassive() && this.getClass() != SupermassiveSlimeEntity.class) {
         this.m_19998_((ItemLike)TensuraMobDropItems.SLIME_CORE.get());
      }

   }

   public int m_8132_() {
      return 0;
   }

   public boolean isPushedByFluid(FluidType type) {
      return false;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return false;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19315_ || source == DamageSource.f_19312_ || source == DamageSource.f_19311_ || source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_146703_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return this.isTiny() ? SoundEvents.f_12468_ : SoundEvents.f_12386_;
   }

   protected SoundEvent m_5592_() {
      return this.isTiny() ? SoundEvents.f_12467_ : SoundEvents.f_12385_;
   }

   protected SoundEvent getSquishSound() {
      return this.isTiny() ? SoundEvents.f_12470_ : SoundEvents.f_12388_;
   }

   public float getJumpSoundVolume() {
      return 0.4F * (float)this.getSize();
   }

   public SoundEvent getJumpSound() {
      return this.isTiny() ? SoundEvents.f_12469_ : SoundEvents.f_12387_;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.slimeSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      int variant = this.f_19796_.m_188503_(6);
      this.setVariant(SlimeVariant.byId(variant));
      if (pLevel.m_204166_(this.m_20097_()).m_203656_(Biomes.IS_COLD_OVERWORLD)) {
         this.setChilled(Boolean.TRUE);
      }

      if (this.getClass() == SlimeEntity.class && !pReason.equals(MobSpawnType.BUCKET)) {
         int type;
         if (this.canSpawnSpecialVariant(pReason)) {
            if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.slimeMetalChance.get(), pLevel.m_213780_())) {
               MetalSlimeEntity slime = new MetalSlimeEntity((EntityType)TensuraEntityTypes.METAL_SLIME.get(), this.m_9236_());
               slime.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
               this.m_9236_().m_7967_(slime);
               this.m_142467_(RemovalReason.DISCARDED);
               return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
            }

            if (this.f_19853_.m_204166_(this.m_20183_()).m_203656_(TensuraBiomeTags.IS_FLAT_LAND) && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.slimeSupermassiveChance.get(), pLevel.m_213780_())) {
               SupermassiveSlimeEntity slime = new SupermassiveSlimeEntity((EntityType)TensuraEntityTypes.SUPERMASSIVE_SLIME.get(), this.m_9236_());
               slime.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
               slime.setVariant(this.getVariant());
               slime.setChilled(this.isChilled());
               this.m_9236_().m_7967_(slime);
               this.m_142467_(RemovalReason.DISCARDED);
               return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
            }

            type = this.f_19796_.m_188503_(2);
            this.setType(SlimeType.byId(type));
         }

         type = this.f_19796_.m_188503_(4);
         if (type < 2 && this.f_19796_.m_188501_() < 0.5F * pDifficulty.m_19057_()) {
            ++type;
         }

         int j = 1 << type;
         this.setSize(j, true);
      }

      if (this.getSlimeType().equals(SlimeType.SUMMONED)) {
         this.f_21364_ = 0;
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.jump()) {
         event.getController().clearAnimationCache();
         return PlayState.STOP;
      } else if (event.isMoving() && (this.m_20096_() || this.m_20072_() || this.m_20077_() || this.f_19798_) && !this.isHurt()) {
         event.getController().setAnimation(WALK);
         return PlayState.CONTINUE;
      } else {
         if (this.m_20096_()) {
            event.getController().setAnimation(IDLE);
         }

         return PlayState.CONTINUE;
      }
   }

   private <T extends IAnimatable> PlayState hurtPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.isHurt()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(DAMAGED);
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState jumpPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.jump()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(JUMPING);
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "hurtController", 0.0F, this::hurtPredicate));
      data.addAnimationController(new AnimationController(this, "crouchController", 0.0F, this::jumpPredicate));
   }

   public boolean isHurt() {
      return (Boolean)this.f_19804_.m_135370_(HURT);
   }

   public void setHurt(boolean hurt) {
      this.f_19804_.m_135381_(HURT, hurt);
   }

   public boolean jump() {
      return (Boolean)this.f_19804_.m_135370_(JUMP);
   }

   public void setJumpAnimation(boolean jump) {
      this.f_19804_.m_135381_(JUMP, jump);
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      ID_SIZE = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135028_);
      SLIME_TYPE = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135028_);
      FROM_BUCKET = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      SADDLED = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      SUPPER_MASSIVE = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      CHILLED = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      HURT = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      JUMP = SynchedEntityData.m_135353_(SlimeEntity.class, EntityDataSerializers.f_135035_);
      IDLE = (new AnimationBuilder()).addAnimation("animation.slime.idle", EDefaultLoopTypes.LOOP);
      WALK = (new AnimationBuilder()).addAnimation("animation.slime.walk", EDefaultLoopTypes.LOOP);
      DAMAGED = (new AnimationBuilder()).addAnimation("animation.slime.hurt", EDefaultLoopTypes.PLAY_ONCE);
      JUMPING = (new AnimationBuilder()).addAnimation("animation.slime.jump", EDefaultLoopTypes.PLAY_ONCE);
   }
}
