package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.entity.template.TensuraBoatEntity;
import com.github.manasmods.tensura.entity.template.TensuraChestBoatEntity;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.custom.BattlewillManualItem;
import com.github.manasmods.tensura.item.custom.ElementCoreItem;
import com.github.manasmods.tensura.item.custom.HipokuteFlowerItem;
import com.github.manasmods.tensura.item.custom.MarionetteHeartItem;
import com.github.manasmods.tensura.item.custom.ResetScrollItem;
import com.github.manasmods.tensura.item.custom.ShadowStorageItem;
import com.github.manasmods.tensura.item.custom.SlimeBucketItem;
import com.github.manasmods.tensura.item.custom.TensuraFireChargeItem;
import com.github.manasmods.tensura.item.templates.custom.TensuraBoatItem;
import com.github.manasmods.tensura.item.templates.custom.TensuraChestBoatItem;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.fluids.TensuraFluids;
import com.github.manasmods.tensura.sound.TensuraSounds;
import com.github.manasmods.tensura.util.TensuraRarity;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.BucketItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemNameBlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.RecordItem;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@GenerateItemModels
public class TensuraMaterialItems {
   private static final DeferredRegister<Item> registry;
   @SingleTextureModel
   public static final RegistryObject<Item> MUSIC_DISC_NANODA;
   public static final RegistryObject<Item> SLIME_IN_A_BUCKET;
   @SingleTextureModel
   public static final RegistryObject<Item> SHADOW_STORAGE;
   @SingleTextureModel
   public static final RegistryObject<Item> HOT_SPRING_WATER_BUCKET;
   @SingleTextureModel
   public static final RegistryObject<Item> THATCH;
   @SingleTextureModel
   public static final RegistryObject<Item> HIPOKUTE_SEEDS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIPOKUTE_GRASS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIPOKUTE_FLOWER;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_SADDLE;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_SILVER;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> MAGIC_ORE;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_NUGGET;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_INGOT;
   @SingleTextureModel
   public static final RegistryObject<Item> MAGIC_STONE;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_EMPTY;
   @SingleTextureModel
   public static final RegistryObject<Item> EARTH_ELEMENTAL_SHARD;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_EARTH;
   @SingleTextureModel
   public static final RegistryObject<Item> FIRE_ELEMENTAL_SHARD;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_FIRE;
   @SingleTextureModel
   public static final RegistryObject<Item> SPACE_ELEMENTAL_SHARD;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_SPACE;
   @SingleTextureModel
   public static final RegistryObject<Item> WATER_ELEMENTAL_SHARD;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_WATER;
   @SingleTextureModel
   public static final RegistryObject<Item> WIND_ELEMENTAL_SHARD;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENT_CORE_WIND;
   @SingleTextureModel
   public static final RegistryObject<Item> BLACK_FIRE_CHARGE;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_FIRE_CHARGE;
   @SingleTextureModel
   public static final RegistryObject<Item> BRONZE_COIN;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_COIN;
   @SingleTextureModel
   public static final RegistryObject<Item> GOLD_COIN;
   @SingleTextureModel
   public static final RegistryObject<Item> STELLAR_GOLD_COIN;
   @SingleTextureModel
   public static final RegistryObject<Item> PALM_BOAT;
   @SingleTextureModel
   public static final RegistryObject<Item> PALM_CHEST_BOAT;
   @SingleTextureModel
   public static final RegistryObject<Item> SAKURA_BOAT;
   @SingleTextureModel
   public static final RegistryObject<Item> SAKURA_CHEST_BOAT;
   @SingleTextureModel
   public static final RegistryObject<Item> MARIONETTE_HEART;
   @SingleTextureModel
   public static final RegistryObject<Item> BATTLEWILL_MANUAL;
   @SingleTextureModel
   public static final RegistryObject<Item> RACE_RESET_SCROLL;
   @SingleTextureModel
   public static final RegistryObject<Item> SKILL_RESET_SCROLL;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARACTER_RESET_SCROLL;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      MUSIC_DISC_NANODA = registry.register("music_disc_nanoda", () -> {
         return new RecordItem(15, TensuraSounds.NANODA, (new Properties()).m_41487_(1).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41497_(Rarity.RARE).m_41486_(), 32);
      });
      SLIME_IN_A_BUCKET = registry.register("slime_in_a_bucket", () -> {
         return new SlimeBucketItem((new Properties()).m_41487_(1).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      SHADOW_STORAGE = registry.register("shadow_storage", () -> {
         return new ShadowStorageItem((new Properties()).m_41487_(1).m_41486_());
      });
      HOT_SPRING_WATER_BUCKET = registry.register("hot_spring_water_bucket", () -> {
         return new BucketItem(TensuraFluids.SOURCE_HOT_SPRING_WATER, (new Properties()).m_41487_(1).m_41491_(TensuraCreativeTab.MISCELLANEOUS)) {
            public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
               pTooltipComponents.add(Component.m_237115_("tooltip.tensura.coming_soon").m_130940_(ChatFormatting.RED));
            }
         };
      });
      THATCH = registry.register("thatch", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      HIPOKUTE_SEEDS = registry.register("hipokute_seeds", () -> {
         return new ItemNameBlockItem((Block)TensuraBlocks.HIPOKUTE_GRASS.get(), (new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      HIPOKUTE_GRASS = registry.register("hipokute_grass", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      HIPOKUTE_FLOWER = registry.register("hipokute_flower", () -> {
         return new HipokuteFlowerItem(TensuraBlocks.POTTED_HIPOKUTE_FLOWER, (new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      MONSTER_SADDLE = registry.register("monster_saddle", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(1));
      });
      RAW_SILVER = registry.register("raw_silver", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      SILVER_NUGGET = registry.register("silver_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      MAGIC_ORE = registry.register("magic_ore_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      SILVER_INGOT = registry.register("silver_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      LOW_MAGISTEEL_NUGGET = registry.register("low_magisteel_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      HIGH_MAGISTEEL_NUGGET = registry.register("high_magisteel_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      MITHRIL_NUGGET = registry.register("mithril_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      ORICHALCUM_NUGGET = registry.register("orichalcum_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      PURE_MAGISTEEL_NUGGET = registry.register("pure_magisteel_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      ADAMANTITE_NUGGET = registry.register("adamantite_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      HIHIIROKANE_NUGGET = registry.register("hihiirokane_nugget", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      LOW_MAGISTEEL_INGOT = registry.register("low_magisteel_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      HIGH_MAGISTEEL_INGOT = registry.register("high_magisteel_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      MITHRIL_INGOT = registry.register("mithril_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      ORICHALCUM_INGOT = registry.register("orichalcum_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      PURE_MAGISTEEL_INGOT = registry.register("pure_magisteel_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      ADAMANTITE_INGOT = registry.register("adamantite_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      HIHIIROKANE_INGOT = registry.register("hihiirokane_ingot", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      MAGIC_STONE = registry.register("magic_stone", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      ELEMENT_CORE_EMPTY = registry.register("element_core_empty", ElementCoreItem::new);
      EARTH_ELEMENTAL_SHARD = registry.register("earth_elemental_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(64).m_41497_(Rarity.UNCOMMON).m_41486_());
      });
      ELEMENT_CORE_EARTH = registry.register("element_core_earth", () -> {
         return new ElementCoreItem(1);
      });
      FIRE_ELEMENTAL_SHARD = registry.register("fire_elemental_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(64).m_41497_(Rarity.UNCOMMON).m_41486_());
      });
      ELEMENT_CORE_FIRE = registry.register("element_core_fire", () -> {
         return new ElementCoreItem(2);
      });
      SPACE_ELEMENTAL_SHARD = registry.register("space_elemental_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(64).m_41497_(Rarity.UNCOMMON).m_41486_());
      });
      ELEMENT_CORE_SPACE = registry.register("element_core_space", () -> {
         return new ElementCoreItem(4);
      });
      WATER_ELEMENTAL_SHARD = registry.register("water_elemental_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(64).m_41497_(Rarity.UNCOMMON).m_41486_());
      });
      ELEMENT_CORE_WATER = registry.register("element_core_water", () -> {
         return new ElementCoreItem(5);
      });
      WIND_ELEMENTAL_SHARD = registry.register("wind_elemental_shard", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41487_(64).m_41497_(Rarity.UNCOMMON).m_41486_());
      });
      ELEMENT_CORE_WIND = registry.register("element_core_wind", () -> {
         return new ElementCoreItem(6);
      });
      BLACK_FIRE_CHARGE = registry.register("black_fire_charge", () -> {
         return new TensuraFireChargeItem(TensuraBlocks.BLACK_FIRE, (new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41497_(TensuraRarity.UNIQUE).m_41486_());
      });
      HOLY_FIRE_CHARGE = registry.register("holy_fire_charge", () -> {
         return new TensuraFireChargeItem(TensuraBlocks.HOLY_FIRE, (new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41497_(TensuraRarity.UNIQUE).m_41486_());
      });
      BRONZE_COIN = registry.register("bronze_coin", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      SILVER_COIN = registry.register("silver_coin", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      GOLD_COIN = registry.register("gold_coin", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS));
      });
      STELLAR_GOLD_COIN = registry.register("stellar_gold_coin", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41486_());
      });
      PALM_BOAT = registry.register("palm_boat", () -> {
         return new TensuraBoatItem((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS), TensuraBoatEntity.Type.PALM);
      });
      PALM_CHEST_BOAT = registry.register("palm_chest_boat", () -> {
         return new TensuraChestBoatItem((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS), TensuraChestBoatEntity.Type.PALM);
      });
      SAKURA_BOAT = registry.register("sakura_boat", () -> {
         return new TensuraBoatItem((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS), TensuraBoatEntity.Type.SAKURA);
      });
      SAKURA_CHEST_BOAT = registry.register("sakura_chest_boat", () -> {
         return new TensuraChestBoatItem((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS), TensuraChestBoatEntity.Type.SAKURA);
      });
      MARIONETTE_HEART = registry.register("marionette_heart", MarionetteHeartItem::new);
      BATTLEWILL_MANUAL = registry.register("battlewill_manual", BattlewillManualItem::new);
      RACE_RESET_SCROLL = registry.register("race_reset_scroll", () -> {
         return new ResetScrollItem(ResetScrollItem.ResetType.RESET_RACE);
      });
      SKILL_RESET_SCROLL = registry.register("skill_reset_scroll", () -> {
         return new ResetScrollItem(ResetScrollItem.ResetType.RESET_SKILL);
      });
      CHARACTER_RESET_SCROLL = registry.register("character_reset_scroll", () -> {
         return new ResetScrollItem(ResetScrollItem.ResetType.RESET_ALL);
      });
   }
}
