package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.IfritCloneEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.SalamanderEntity;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.EntityHitResult;
import org.jetbrains.annotations.NotNull;

public class FlameOrbProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/flame_orb/flame_orb_6.png")};

   public FlameOrbProjectile(EntityType<? extends FlameOrbProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(0.5F);
   }

   public FlameOrbProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.FLAME_ORB.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(0.5F);
   }

   public String getMagic() {
      return "tensura.fire_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void m_5790_(@NotNull EntityHitResult result) {
      if (!(result.m_82443_() instanceof IfritEntity) && !(result.m_82443_() instanceof IfritCloneEntity) && !(result.m_82443_() instanceof SalamanderEntity)) {
         super.m_5790_(result);
      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
