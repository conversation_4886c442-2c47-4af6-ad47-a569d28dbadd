package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import org.jetbrains.annotations.NotNull;

public class MegiddoBubbleEntity extends BarrierEntity {
   private static final EntityDataAccessor<Integer> CHARGE;
   private static final EntityDataAccessor<Integer> CHAIN_CHARGE;
   private static final EntityDataAccessor<Float> START_POS_X;
   private static final EntityDataAccessor<Float> START_POS_Y;
   private static final EntityDataAccessor<Float> START_POS_Z;
   private static final EntityDataAccessor<Float> TARGET_POS_X;
   private static final EntityDataAccessor<Float> TARGET_POS_Y;
   private static final EntityDataAccessor<Float> TARGET_POS_Z;
   private int beamTick;
   private int chargeChain;

   public MegiddoBubbleEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.MEGIDDO_BUBBLE.get(), level);
      this.m_5602_(entity);
   }

   public MegiddoBubbleEntity(EntityType<? extends MegiddoBubbleEntity> entityType, Level level) {
      super(entityType, level);
      this.beamTick = 0;
      this.chargeChain = 0;
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(CHARGE, 10);
      this.f_19804_.m_135372_(CHAIN_CHARGE, 0);
      this.f_19804_.m_135372_(START_POS_X, 0.0F);
      this.f_19804_.m_135372_(START_POS_Y, 0.0F);
      this.f_19804_.m_135372_(START_POS_Z, 0.0F);
      this.f_19804_.m_135372_(TARGET_POS_X, 0.0F);
      this.f_19804_.m_135372_(TARGET_POS_Y, 0.0F);
      this.f_19804_.m_135372_(TARGET_POS_Z, 0.0F);
   }

   protected void m_7380_(@NotNull CompoundTag pCompound) {
      super.m_7380_(pCompound);
      pCompound.m_128405_("Charge", this.getCharge());
      pCompound.m_128405_("ChainCharge", this.getChainCharge());
      pCompound.m_128350_("xStart", (float)this.getStartBeamOffset().m_7096_());
      pCompound.m_128350_("yStart", (float)this.getStartBeamOffset().m_7098_());
      pCompound.m_128350_("zStart", (float)this.getStartBeamOffset().m_7094_());
      pCompound.m_128350_("xTarget", (float)this.getTargetPos().m_7096_());
      pCompound.m_128350_("yTarget", (float)this.getTargetPos().m_7098_());
      pCompound.m_128350_("zTarget", (float)this.getTargetPos().m_7094_());
   }

   protected void m_7378_(@NotNull CompoundTag pCompound) {
      super.m_7378_(pCompound);
      this.setCharge(pCompound.m_128451_("Charge"));
      this.setChainCharge(pCompound.m_128451_("ChainCharge"));
      this.setStartBeamOffset(pCompound.m_128457_("xStart"), pCompound.m_128457_("yStart"), pCompound.m_128457_("zStart"));
      this.setTargetPos(pCompound.m_128457_("xTarget"), pCompound.m_128457_("yTarget"), pCompound.m_128457_("zTarget"));
   }

   public int getCharge() {
      return (Integer)this.m_20088_().m_135370_(CHARGE);
   }

   public void setCharge(int charge) {
      this.m_20088_().m_135381_(CHARGE, charge);
   }

   public int getChainCharge() {
      return (Integer)this.m_20088_().m_135370_(CHAIN_CHARGE);
   }

   public void setChainCharge(int charge) {
      this.m_20088_().m_135381_(CHAIN_CHARGE, charge);
      this.chargeChain = charge;
   }

   public void setStartBeamOffset(float x, float y, float z) {
      this.m_20088_().m_135381_(START_POS_X, x);
      this.m_20088_().m_135381_(START_POS_Y, y);
      this.m_20088_().m_135381_(START_POS_Z, z);
   }

   public Vec3 getStartBeamOffset() {
      return new Vec3((double)(Float)this.m_20088_().m_135370_(START_POS_X), (double)(Float)this.m_20088_().m_135370_(START_POS_Y), (double)(Float)this.m_20088_().m_135370_(START_POS_Z));
   }

   public void setTargetPos(float x, float y, float z) {
      this.m_20088_().m_135381_(TARGET_POS_X, x);
      this.m_20088_().m_135381_(TARGET_POS_Y, y);
      this.m_20088_().m_135381_(TARGET_POS_Z, z);
   }

   public Vec3 getTargetPos() {
      return new Vec3((double)(Float)this.m_20088_().m_135370_(TARGET_POS_X), (double)(Float)this.m_20088_().m_135370_(TARGET_POS_Y), (double)(Float)this.m_20088_().m_135370_(TARGET_POS_Z));
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean blockBuilding() {
      return false;
   }

   public boolean isMultipartEntity() {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public boolean m_6783_(double pDistance) {
      return super.m_6783_(pDistance) || pDistance < 16384.0D;
   }

   public void m_8119_() {
      super.m_8119_();
      Entity owner = this.m_37282_();
      List list;
      if (owner != null && this.getChainCharge() <= 0) {
         this.m_6034_(owner.m_20185_(), owner.m_20186_() + 20.0D, owner.m_20189_());
         list = this.m_9236_().m_6443_(MegiddoBubbleEntity.class, this.m_20191_(), (entityData) -> {
            return entityData.m_37282_() == owner && entityData != this;
         });
         if (!list.isEmpty() || !owner.m_6084_()) {
            this.m_146870_();
         }
      }

      if (!this.getTargetPos().equals(Vec3.f_82478_)) {
         ++this.beamTick;
         if (this.beamTick == 1) {
            Vec3 source = this.getStartBeamOffset();
            Vec3 targetPos = this.getTargetPos();
            Vec3 offSetToTarget = targetPos.m_82546_(source);
            Vec3 normalizes = offSetToTarget.m_82541_();

            for(int i = 0; i < Mth.m_14107_(offSetToTarget.m_82553_()) + 1; ++i) {
               Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)i));
               AABB aabb = new AABB(particlePos.f_82479_ + 0.5D, particlePos.f_82480_ + 0.5D, particlePos.f_82481_ + 0.5D, particlePos.f_82479_ - 0.5D, particlePos.f_82480_ - 0.5D, particlePos.f_82481_ - 0.5D);
               List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entityData) -> {
                  return entityData != this.m_37282_() && entityData.m_6084_();
               });
               Iterator var10 = list.iterator();

               while(var10.hasNext()) {
                  LivingEntity living = (LivingEntity)var10.next();
                  DamageSource damagesource = TensuraDamageSources.megiddo((Entity)(this.m_37282_() != null ? this.m_37282_() : this));
                  if (living.m_6469_(DamageSourceHelper.addSkillAndCost(damagesource, 3000.0D, new ManasSkillInstance((ManasSkill)SpiritualMagics.MEGIDDO.get())), 150.0F)) {
                     TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123796_);
                  }
               }
            }

            this.f_19853_.m_6263_((Player)null, targetPos.m_7096_(), targetPos.m_7098_(), targetPos.m_7094_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 0.5F, 1.0F);
         } else if (this.beamTick >= 10) {
            if (this.getChainCharge() > 0) {
               list = this.getTargetList(this.getTargetPos(), 20.0F);
               if (!list.isEmpty()) {
                  if (this.chargeChain <= 0) {
                     this.chargeChain = this.getChainCharge();
                     this.startNewBeam((LivingEntity)list.get(this.f_19796_.m_188503_(list.size())));
                  } else {
                     LivingEntity target = (LivingEntity)list.get(this.f_19796_.m_188503_(list.size()));
                     this.setStartBeamOffset((float)this.getTargetPos().m_7096_(), (float)this.getTargetPos().m_7098_(), (float)this.getTargetPos().m_7094_());
                     this.setTargetPos((float)target.m_20185_(), (float)(target.m_20186_() + (double)(target.m_20206_() / 2.0F)), (float)target.m_20189_());
                  }

                  --this.chargeChain;
               } else {
                  this.setTargetPos(0.0F, 0.0F, 0.0F);
               }
            } else {
               this.setTargetPos(0.0F, 0.0F, 0.0F);
            }

            this.beamTick = 0;
         }
      } else if (this.getChainCharge() > 0 && this.f_19797_ % 200 == 0) {
         list = this.getTargetList(this.m_20182_().m_82520_(0.0D, -40.0D, 0.0D), 40.0F);
         if (!list.isEmpty()) {
            this.startNewBeam((LivingEntity)list.get(this.f_19796_.m_188503_(list.size())));
         }
      }

      if (this.getCharge() <= 0 || this.f_19853_.m_6042_().f_63857_()) {
         BarrierPart[] var14 = this.parts;
         int var16 = var14.length;

         for(int var17 = 0; var17 < var16; ++var17) {
            BarrierPart part = var14[var17];
            part.addServerParticlesAroundSelf(ParticleTypes.f_123796_, 0.5D);
            part.addServerParticlesAroundSelf(ParticleTypes.f_123796_, 0.5D);
            part.addServerParticlesAroundSelf(ParticleTypes.f_123796_, 0.5D);
         }

         this.m_146870_();
      }

   }

   public void startNewBeam(LivingEntity target) {
      if (this.hasSunlight()) {
         this.setCharge(this.getCharge() - 1);
         this.setTargetPos((float)target.m_20185_(), (float)(target.m_20186_() + (double)(target.m_20206_() / 2.0F)), (float)target.m_20189_());
         float radius = this.getRadius() - 1.0F;
         float randomX = (float)(this.m_20185_() + ((double)this.f_19796_.m_188501_() - 0.5D) * (double)radius);
         float randomZ = (float)(this.m_20189_() + ((double)this.f_19796_.m_188501_() - 0.5D) * (double)radius);
         this.setStartBeamOffset(randomX, (float)(this.m_20186_() + (double)(this.getRadius() * 2.0F)), randomZ);
      }
   }

   private boolean hasSunlight() {
      Level level = this.m_9236_();
      if (!level.m_46471_() && !level.m_46470_()) {
         return !level.m_45527_(this.m_20183_()) ? false : level.m_46461_();
      } else {
         return false;
      }
   }

   public List<LivingEntity> getTargetList(Vec3 pos, float radius) {
      AABB box = (new AABB(new BlockPos(pos))).m_82400_((double)radius);
      return this.m_9236_().m_6443_(LivingEntity.class, box, this::shouldTarget);
   }

   protected boolean shouldTarget(LivingEntity entity) {
      if (!entity.f_19853_.m_46749_(entity.m_20183_())) {
         return false;
      } else if (this.m_37282_() != null && entity.m_7307_(this.m_37282_())) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_() || player.m_5833_()) {
               return false;
            }
         }

         return this.hasLineOfSight(entity);
      }
   }

   public boolean hasLineOfSight(Entity pEntity) {
      if (pEntity.f_19853_ != this.f_19853_) {
         return false;
      } else {
         Vec3 vec3 = new Vec3(this.m_20185_(), this.m_20186_(), this.m_20189_());
         Vec3 vec31 = new Vec3(pEntity.m_20185_(), pEntity.m_20188_(), pEntity.m_20189_());
         return this.f_19853_.m_45547_(new ClipContext(vec3, vec31, Block.COLLIDER, Fluid.NONE, this)).m_6662_() == Type.MISS;
      }
   }

   static {
      CHARGE = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135028_);
      CHAIN_CHARGE = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135028_);
      START_POS_X = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
      START_POS_Y = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
      START_POS_Z = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
      TARGET_POS_X = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
      TARGET_POS_Y = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
      TARGET_POS_Z = SynchedEntityData.m_135353_(MegiddoBubbleEntity.class, EntityDataSerializers.f_135029_);
   }
}
