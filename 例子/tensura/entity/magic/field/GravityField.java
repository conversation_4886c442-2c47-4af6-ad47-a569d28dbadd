package com.github.manasmods.tensura.entity.magic.field;

import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class GravityField extends AreaField {
   public GravityField(EntityType<? extends Projectile> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public GravityField(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.GRAVITY_FIELD.get(), level);
      this.m_5602_(entity);
   }

   public void applyEffect(LivingEntity target, boolean instant) {
      Entity var4 = this.m_37282_();
      if (var4 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var4;
         if (target.m_7307_(owner) || target == owner) {
            target.m_147207_(new MobEffectInstance(MobEffects.f_19596_, 100, 1, false, true), this.m_37282_());
            target.m_147207_(new MobEffectInstance(MobEffects.f_19591_, 100, 0, false, true), this.m_37282_());
            return;
         }
      }

      if (target instanceof Player) {
         Player player = (Player)target;
         if (player.m_7500_()) {
            return;
         }
      }

      target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.BURDEN.get(), 100, 0, false, true), this.m_37282_());
      target.m_147207_(new MobEffectInstance(MobEffects.f_19597_, 100, 1, false, true), this.m_37282_());
   }

   public void ambientParticles() {
      if (this.getVisualRadius() != this.getRadius() || this.f_19797_ % 5 == 0) {
         Level level = this.m_9236_();
         float radius = this.getVisualRadius();
         Vec3 pos = this.m_20182_().m_82520_(0.0D, (double)this.getRadius(), 0.0D);
         int ySteps = (int)(3.2D * (double)radius);
         int xSteps = (int)(6.4D * (double)radius);
         float yDeg = 180.0F / (float)ySteps * 0.017453292F;
         float xDeg = 360.0F / (float)xSteps * 0.017453292F;

         for(int x = 0; x < xSteps; ++x) {
            for(int y = -ySteps; y < ySteps; ++y) {
               double dx = level.f_46441_.m_188500_() * 0.05D - 0.05D;
               double dy = level.f_46441_.m_188500_() * 0.05D - 0.05D;
               double dz = level.f_46441_.m_188500_() * 0.05D - 0.05D;
               Vec3 offset = (new Vec3(0.0D, 0.0D, (double)radius)).m_82524_((float)y * yDeg).m_82496_((float)x * xDeg).m_82535_(-1.5707964F).m_82542_(1.0D, 0.8500000238418579D, 1.0D);
               level.m_7106_(ParticleTypes.f_123783_, pos.f_82479_ + offset.f_82479_ + dx, pos.f_82480_ + 1.0D + offset.f_82480_ + dy, pos.f_82481_ + offset.f_82481_ + dz, 0.0D, 0.0D, 0.0D);
               level.m_7106_(ParticleTypes.f_123790_, pos.f_82479_ + offset.f_82479_ + dx, pos.f_82480_ + 1.0D + offset.f_82480_ + dy, pos.f_82481_ + offset.f_82481_ + dz, 0.0D, 0.0D, 0.0D);
            }
         }

      }
   }
}
