package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.github.manasmods.tensura.entity.magic.field.cloud.BloodMistCloud;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;

public class BloodMistSkill extends Skill {
   public BloodMistSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (TensuraPlayerCapability.isSpiritualForm(player)) {
            return false;
         }
      }

      return super.canInteractSkill(instance, entity);
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (instance.getMode() == 1) {
         if (!instance.isMastered(entity)) {
            return 0;
         } else {
            return TensuraEPCapability.getEP(entity) < 500000.0D ? 0 : 2;
         }
      } else {
         return 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.default");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.blood_mist.ray");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 2 ? 10000.0D : 500.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 2) {
         return false;
      } else {
         if (heldTicks % 10 == 0) {
            label24: {
               if (SkillHelper.outOfMagicule(entity, instance)) {
                  return false;
               }

               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  if (player.m_7500_()) {
                     break label24;
                  }
               }

               entity.m_6469_(TensuraDamageSources.BLOOD_DRAIN.m_19382_().m_19381_(), 10.0F);
            }
         }

         double cost = this.magiculeCost(entity, instance);
         BeamProjectile.spawnLastingBeam((EntityType)TensuraEntityTypes.BLOOD_RAY.get(), 50.0F, 0.2F, 21, 40.0F, 0.0F, entity.m_146892_(), entity, instance, cost, cost, heldTicks);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 0.8F, 0.5F);
         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      if (instance.getMode() == 2) {
         tag.m_128405_("BeamID", 0);
         instance.markDirty();
      } else {
         if (tag.m_128441_("mistY")) {
            AABB aabb = (new AABB(new BlockPos(tag.m_128459_("mistX"), tag.m_128459_("mistY"), tag.m_128459_("mistZ")))).m_82400_(0.5D);
            List<BloodMistCloud> list = level.m_6443_(BloodMistCloud.class, aabb, (mistx) -> {
               return mistx.m_37282_() == entity;
            });
            if (!list.isEmpty()) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               Iterator var11 = list.iterator();

               while(var11.hasNext()) {
                  BloodMistCloud mist = (BloodMistCloud)var11.next();
                  mist.bloodExplosion();
               }

               tag.m_128473_("mistX");
               tag.m_128473_("mistY");
               tag.m_128473_("mistZ");
               instance.markDirty();
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
               instance.setCoolDown(instance.isMastered(entity) ? 2 : 3);
               return;
            }
         }

         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 10.0D);
         BlockPos pos = result.m_82425_();
         this.addMasteryPoint(instance, entity);
         BloodMistCloud mist = new BloodMistCloud(entity.m_9236_(), entity);
         mist.setDamage(10.0F);
         mist.setRadius(5.0F);
         mist.setHeight(1.0F);
         mist.setSkill(instance);
         mist.setMpCost(500.0D);
         mist.m_6034_((double)pos.m_123341_() + 0.5D, (double)(pos.m_123342_() + 1), (double)pos.m_123343_() + 0.5D);
         level.m_7967_(mist);
         entity.m_6469_(TensuraDamageSources.BLOOD_DRAIN.m_19382_(), 20.0F);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         tag.m_128347_("mistX", mist.m_20185_());
         tag.m_128347_("mistY", mist.m_20186_());
         tag.m_128347_("mistZ", mist.m_20189_());
         instance.markDirty();
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }
   }
}
