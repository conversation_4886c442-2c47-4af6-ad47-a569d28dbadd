package com.github.b4ndithelps.tennogamenolife.client.renderer.layer;

import com.github.b4ndithelps.tennogamenolife.client.model.XiubiArmorModel;
import com.github.b4ndithelps.tennogamenolife.entity.human.XiubiEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;

/**
 * 休比机铠装饰渲染层
 * 在休比实体上渲染机铠装饰效果
 */
@SuppressWarnings("rawtypes")
public class XiubiArmorLayer extends RenderLayer {
    
    private static final ResourceLocation ARMOR_TEXTURE = 
        new ResourceLocation("tennogamenolife", "textures/entity/xiubi/armor.png");
    
    private final XiubiArmorModel armorModel;

    @SuppressWarnings("unchecked")
    public XiubiArmorLayer(RenderLayerParent renderer, XiubiArmorModel armorModel) {
        super(renderer);
        this.armorModel = armorModel;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void render(PoseStack poseStack, MultiBufferSource buffer, int packedLight, 
                      net.minecraft.world.entity.Entity entity, float limbSwing, float limbSwingAmount, 
                      float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
        
        if (!(entity instanceof XiubiEntity xiubiEntity)) {
            return;
        }
        
        // 设置机铠装饰动画
        this.armorModel.setupAnim(xiubiEntity, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch);
        
        // 渲染机铠装饰
        this.armorModel.renderToBuffer(
            poseStack, 
            buffer.getBuffer(RenderType.entityCutoutNoCull(ARMOR_TEXTURE)), 
            packedLight, 
            OverlayTexture.NO_OVERLAY, 
            1.0F, 1.0F, 1.0F, 1.0F  // 完全不透明
        );
    }
}
