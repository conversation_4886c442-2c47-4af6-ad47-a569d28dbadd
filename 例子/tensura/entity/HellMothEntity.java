package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.block.MothEggBlock;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.MothVariant;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Registry;
import net.minecraft.core.particles.DustParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.tags.BlockTags;
import net.minecraft.tags.ItemTags;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.ExperienceOrb;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MoveToBlockGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.TemptGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.Spider;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.predicate.BlockStatePredicate;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class HellMothEntity extends FLyingTamableEntity implements IAnimatable, SpittingRangedMonster, IRanking {
   private static final EntityDataAccessor<Boolean> HAS_EGG;
   private static final EntityDataAccessor<Integer> BABY_SIZE;
   private static final EntityDataAccessor<Integer> ATTACK_MODE;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public float attackTicks;
   public boolean isWet;
   public static final AnimationBuilder POWDER_ATTACK;
   public static final AnimationBuilder IDLE_GROUND;
   public static final AnimationBuilder IDLE_FLY;
   public static final AnimationBuilder FLY_AGRO;
   public static final AnimationBuilder FLY;
   public static final AnimationBuilder WALK;
   public static final AnimationBuilder WATER_SHAKE;
   public static final AnimationBuilder CLEAN_ANTENNA;
   public static final AnimationBuilder HATCH;

   public HellMothEntity(EntityType<? extends HellMothEntity> type, Level level) {
      super(type, level);
      this.f_21364_ = 20;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 40.0D).m_22268_(Attributes.f_22280_, 0.6000000238418579D).m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22277_, 20.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22278_, 0.3D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(0, new HellMothEntity.MothLayEggGoal(this, 1.0D));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(1, new HellMothEntity.MothBreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21346_.m_25352_(4, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21345_.m_25352_(6, new HellMothEntity.MothEatOrCleanGoal(this));
      this.f_21345_.m_25352_(7, new HellMothEntity.MothFollowLamp(this, 1.0D));
      this.f_21345_.m_25352_(7, new FLyingTamableEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21345_.m_25352_(3, new HellMothEntity.MothAttackGoal(this, 1.25D, 20, 20.0F));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21345_.m_25352_(5, new HellMothEntity.MothTargetGoal(this, Player.class, (Predicate)null));
      this.f_21345_.m_25352_(5, new HellMothEntity.MothTargetGoal(this, Spider.class, (Predicate)null));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(HAS_EGG, false);
      this.f_19804_.m_135372_(BABY_SIZE, 0);
      this.f_19804_.m_135372_(ATTACK_MODE, 0);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("HasEgg", this.hasEgg());
      compound.m_128405_("BabySize", this.getBabySize());
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("AttackMode", this.getAttackMode());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setHasEgg(compound.m_128471_("HasEgg"));
      this.setAttackMode(compound.m_128451_("AttackMode"));
      this.f_19804_.m_135381_(BABY_SIZE, compound.m_128451_("BabySize"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getBabySize() {
      return (Integer)this.f_19804_.m_135370_(BABY_SIZE);
   }

   public void setBabySize(int pSize) {
      this.f_19804_.m_135381_(BABY_SIZE, pSize);
      this.m_20090_();
      this.m_6210_();
      ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22276_))).m_22100_((double)(10 * pSize));
      ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22281_))).m_22100_((double)(1.5F * (float)pSize));
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions dimensions = super.m_6972_(pPose);
      return this.m_6162_() ? super.m_6972_(pPose).m_20388_(0.5F * (float)this.getBabySize()) : dimensions;
   }

   public int getAttackMode() {
      return (Integer)this.f_19804_.m_135370_(ATTACK_MODE);
   }

   public void setAttackMode(int attackTicks) {
      this.f_19804_.m_135381_(ATTACK_MODE, attackTicks);
   }

   public MothVariant getVariant() {
      return MothVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(MothVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || super.m_6673_(source);
   }

   protected boolean m_8028_() {
      return false;
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!pState.m_204336_(TensuraTags.Blocks.WEB_BLOCKS)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected float m_6041_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_6041_();
   }

   protected float m_20098_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_20098_();
   }

   public boolean hasEgg() {
      return (Boolean)this.f_19804_.m_135370_(HAS_EGG);
   }

   void setHasEgg(boolean pHasEgg) {
      this.f_19804_.m_135381_(HAS_EGG, pHasEgg);
   }

   public boolean m_5957_() {
      return super.m_5957_() && !this.hasEgg();
   }

   @Nullable
   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      HellMothEntity moth = (HellMothEntity)((EntityType)TensuraEntityTypes.HELL_MOTH.get()).m_20615_(pLevel);
      if (moth == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            moth.m_21816_(uuid);
            moth.m_7105_(true);
         }

         int randomSize = (new Random()).nextInt(10);
         if (randomSize >= 8) {
            moth.setBabySize(2);
         } else {
            moth.setBabySize(1);
         }

         return moth;
      }
   }

   public void evolve() {
      if ((double)this.f_19796_.m_188501_() <= 0.1D) {
         this.setVariant(MothVariant.GEHENNA);
         TensuraEPCapability.setLivingEP(this, TensuraEPCapability.getEP(this) + 1000.0D);
      }

   }

   public void m_8119_() {
      super.m_8119_();
      this.animationTicking();
      if (this.m_5448_() != null && this.getAttackMode() == 0) {
         ++this.attackTicks;
         if (this.attackTicks > 500.0F) {
            this.setAttackMode(1);
            this.attackTicks = 0.0F;
         }
      }

      if (this.getAttackMode() == 1 && this.m_21660_()) {
         float radius = this.m_20205_();
         double x = this.m_20185_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;
         double y = this.m_20186_() - 1.0D + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius * 0.75D;
         double z = this.m_20189_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;

         for(int i = 0; i < 6; ++i) {
            for(int j = 0; j < 10; ++j) {
               double newX = x + this.m_217043_().m_188583_() / 2.0D;
               double newY = y + this.m_217043_().m_188583_() / 2.0D;
               double newZ = z + this.m_217043_().m_188583_() / 2.0D;
               this.f_19853_.m_7106_(new DustParticleOptions(new Vector3f(Vec3.m_82501_(14733312)), 1.0F), newX, newY - (double)i, newZ, 0.0D, -0.1D, 0.0D);
            }
         }
      }

   }

   protected void animationTicking() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 15) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.m_20071_()) {
         this.isWet = true;
         if (this.getMiscAnimation() == 2 && !this.f_19853_.f_46443_) {
            this.f_19853_.m_7605_(this, (byte)56);
            this.setMiscAnimation(0);
         }
      } else if (this.isWet && this.m_20096_()) {
         this.isWet = false;
         this.setMiscAnimation(2);
         this.m_5496_(SoundEvents.f_12623_, this.m_6121_(), (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
         this.m_146850_(GameEvent.f_223710_);
      }

   }

   public void m_8107_() {
      super.m_8107_();
      if (!this.f_19853_.f_46443_) {
         if (this.m_5448_() != null) {
            if (!this.m_6162_() || this.getBabySize() > 1) {
               LivingEntity target = this.m_5448_();
               if (this.m_20270_(target) > 6.0F && (this.m_21826_() == null || !(this.m_20270_(this.m_21826_()) > 12.0F)) || this.getAttackMode() == 1) {
                  Vec3 vec3 = this.m_20184_().m_82542_(1.0D, 0.6D, 1.0D);
                  if (this.getAttackMode() == 1) {
                     vec3 = this.m_20184_().m_82542_(1.2D, 0.6D, 1.2D);
                  }

                  double d0 = vec3.f_82480_;
                  if (this.m_20186_() < target.m_20186_() + 7.0D) {
                     d0 = Math.max(0.0D, d0);
                     d0 += 0.3D - d0 * 0.6000000238418579D;
                  }

                  vec3 = new Vec3(vec3.f_82479_, d0, vec3.f_82481_);
                  Vec3 vec31 = new Vec3(target.m_20185_() - this.m_20185_(), 0.0D, target.m_20189_() - this.m_20189_());
                  if (vec31.m_165925_() > 10.0D) {
                     Vec3 vec32 = vec31.m_82541_();
                     vec3 = vec3.m_82520_(vec32.f_82479_ * 0.3D - vec3.f_82479_ * 0.6D, 0.0D, vec32.f_82481_ * 0.3D - vec3.f_82481_ * 0.6D);
                  }

                  this.m_20256_(vec3);
                  if (vec3.m_165925_() > 0.05D) {
                     this.m_146922_((float)Mth.m_14136_(vec3.f_82481_, vec3.f_82479_) * 57.295776F - 90.0F);
                  }
               }

            }
         }
      }
   }

   public void m_146762_(int pAge) {
      if (this.getBabySize() == 1 && pAge >= 0) {
         this.m_146762_(-24000);
         this.setBabySize(2);
      } else {
         super.m_146762_(pAge);
      }

   }

   public float m_5610_(BlockPos pPos, LevelReader pLevel) {
      return MothEggBlock.onEggCanPlace(pLevel, pPos) ? 10.0F : super.m_5610_(pPos, pLevel);
   }

   public void m_6504_(@NotNull LivingEntity pTarget, float pDistanceFactor) {
      MonsterSpitProjectile spit = new MonsterSpitProjectile(this.f_19853_, this);
      spit.m_7678_(this.m_20185_(), this.m_20186_(), this.m_20189_(), this.m_146908_(), this.m_146909_());
      double d1 = pTarget.m_20185_() - this.m_20185_();
      double d2 = pTarget.m_20188_() - 1.100000023841858D - spit.m_20186_();
      double d3 = pTarget.m_20189_() - this.m_20189_();
      double d4 = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
      spit.m_6686_(d1, d2 + d4, d3, 1.6F, 1.0F);
      if (!this.m_20067_()) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, this.m_5720_(), 1.0F, 1.0F + (this.m_217043_().m_188501_() - this.m_217043_().m_188501_()) * 0.2F);
      }

      this.f_19853_.m_7967_(spit);
   }

   public void spitHit(LivingEntity pTarget) {
      if (!(pTarget instanceof HellMothEntity) && !(pTarget instanceof HellCaterpillarEntity)) {
         if (pTarget.m_6469_(DamageSource.m_19370_(this), 8.0F)) {
            int level = this.f_19796_.m_188503_(10) >= 8 ? 1 : 0;
            if (this.hasParalysis()) {
               pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 300, level, false, false, true), this);
            }

            pTarget.m_147207_(new MobEffectInstance(MobEffects.f_19614_, 200, level, false, false, true), this);
         }

      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(5.0F);
      if (this.m_6162_()) {
         this.m_146758_(60);
      }

   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(ItemTags.f_13167_);
   }

   public boolean hasParalysis() {
      return SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)CommonSkills.PARALYSIS.get()).isPresent();
   }

   public static boolean checkMothSpawnRules(EntityType<HellMothEntity> moth, ServerLevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184232_) && Monster.m_219009_(pLevel, pPos, pRandom) && m_217057_(moth, pLevel, pSpawnType, pPos, pRandom);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.hellMothSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if ((new Random()).nextInt(20) == 10) {
         this.setVariant(MothVariant.GEHENNA);
         TensuraEPCapability.setLivingEP(this, TensuraEPCapability.getEP(this) + 1000.0D);
      }

      this.setMiscAnimation(1);
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected ResourceLocation m_7582_() {
      return this.getVariant().equals(MothVariant.GEHENNA) ? new ResourceLocation(Registry.f_122826_.m_7981_(this.m_6095_()).m_135827_(), "entities/gehenna_moth") : super.m_7582_();
   }

   public void m_6667_(DamageSource pCause) {
      this.isWet = false;
      this.setMiscAnimation(0);
      super.m_6667_(pCause);
   }

   protected SoundEvent m_7515_() {
      return (SoundEvent)TensuraSoundEvents.MOTH_AMBIENT.get();
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return (SoundEvent)TensuraSoundEvents.MOTH_HURT.get();
   }

   protected SoundEvent m_5592_() {
      return (SoundEvent)TensuraSoundEvents.MOTH_DEATH.get();
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() == 1) {
         return PlayState.CONTINUE;
      } else {
         if (!this.m_20096_() && !this.m_20069_() && !this.m_20077_()) {
            if (event.isMoving()) {
               if (this.m_21660_()) {
                  if (this.getAttackMode() == 1) {
                     event.getController().setAnimation(POWDER_ATTACK);
                  } else {
                     event.getController().setAnimation(FLY_AGRO);
                  }
               } else {
                  event.getController().setAnimation(FLY);
               }
            } else {
               event.getController().setAnimation(IDLE_FLY);
            }
         } else if (event.isMoving()) {
            event.getController().setAnimation(WALK);
         } else if (this.getMiscAnimation() == 0) {
            event.getController().setAnimation(IDLE_GROUND);
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         if (this.getMiscAnimation() == 1) {
            event.getController().markNeedsReload();
            event.getController().setAnimation(HATCH);
         } else if (this.getMiscAnimation() == 2) {
            event.getController().markNeedsReload();
            event.getController().setAnimation(WATER_SHAKE);
         } else if (this.getMiscAnimation() == 3) {
            event.getController().markNeedsReload();
            event.getController().setAnimation(CLEAN_ANTENNA);
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      HAS_EGG = SynchedEntityData.m_135353_(HellMothEntity.class, EntityDataSerializers.f_135035_);
      BABY_SIZE = SynchedEntityData.m_135353_(HellMothEntity.class, EntityDataSerializers.f_135028_);
      ATTACK_MODE = SynchedEntityData.m_135353_(HellMothEntity.class, EntityDataSerializers.f_135028_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(HellMothEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(HellMothEntity.class, EntityDataSerializers.f_135028_);
      POWDER_ATTACK = (new AnimationBuilder()).addAnimation("animation.hell_moth.powder_attack", EDefaultLoopTypes.LOOP);
      IDLE_GROUND = (new AnimationBuilder()).addAnimation("animation.hell_moth.idle_ground", EDefaultLoopTypes.LOOP);
      IDLE_FLY = (new AnimationBuilder()).addAnimation("animation.hell_moth.idle_fly", EDefaultLoopTypes.LOOP);
      FLY_AGRO = (new AnimationBuilder()).addAnimation("animation.hell_moth.fly_agro", EDefaultLoopTypes.LOOP);
      FLY = (new AnimationBuilder()).addAnimation("animation.hell_moth.fly_passive", EDefaultLoopTypes.LOOP);
      WALK = (new AnimationBuilder()).addAnimation("animation.hell_moth.walking", EDefaultLoopTypes.LOOP);
      WATER_SHAKE = (new AnimationBuilder()).addAnimation("animation.hell_moth.water_shaking", EDefaultLoopTypes.PLAY_ONCE);
      CLEAN_ANTENNA = (new AnimationBuilder()).addAnimation("animation.hell_moth.cleaning", EDefaultLoopTypes.PLAY_ONCE);
      HATCH = (new AnimationBuilder()).addAnimation("animation.hell_moth.hatch", EDefaultLoopTypes.PLAY_ONCE);
   }

   static class MothLayEggGoal extends MoveToBlockGoal {
      private final HellMothEntity moth;

      MothLayEggGoal(HellMothEntity moth, double pSpeedModifier) {
         super(moth, pSpeedModifier, 20);
         this.moth = moth;
      }

      public boolean m_8036_() {
         return this.moth.hasEgg() && super.m_8036_() && !this.moth.m_21827_();
      }

      public void m_8037_() {
         super.m_8037_();
         BlockPos blockpos = this.moth.m_20183_();
         if (this.m_25625_()) {
            Level level = this.moth.f_19853_;
            level.m_5594_((Player)null, blockpos, SoundEvents.f_12486_, SoundSource.BLOCKS, 0.3F, 0.9F + level.f_46441_.m_188501_() * 0.2F);
            level.m_46796_(2001, blockpos, Block.m_49956_(level.m_8055_(blockpos.m_7495_())));
            level.m_7731_(this.f_25602_.m_7494_(), (BlockState)((MothEggBlock)TensuraBlocks.MOTH_EGG.get()).m_49966_().m_61124_(MothEggBlock.EGGS, this.moth.f_19796_.m_188503_(4) + 1), 3);
            this.moth.setHasEgg(false);
            this.moth.m_27601_(600);
         } else {
            this.moth.setFlying(Boolean.TRUE);
         }

      }

      protected BlockPos m_6669_() {
         BlockPos newPos = new BlockPos((double)this.f_25602_.m_123341_() + 0.5D, (double)this.f_25602_.m_123342_(), (double)this.f_25602_.m_123343_() + 0.5D);
         return newPos.m_7494_();
      }

      protected boolean m_6465_(LevelReader pLevel, BlockPos pPos) {
         return pLevel.m_46859_(pPos.m_7494_()) && MothEggBlock.canPlaceEgg(pLevel, pPos);
      }
   }

   static class MothBreedGoal extends BreedGoal {
      private final HellMothEntity moth;

      MothBreedGoal(HellMothEntity moth, double pSpeedModifier) {
         super(moth, pSpeedModifier);
         this.moth = moth;
      }

      public boolean m_8036_() {
         return super.m_8036_() && !this.moth.hasEgg();
      }

      protected void m_8026_() {
         ServerPlayer serverplayer = this.f_25113_.m_27592_();
         if (serverplayer == null && this.f_25115_ != null && this.f_25115_.m_27592_() != null) {
            serverplayer = this.f_25115_.m_27592_();
         }

         if (serverplayer != null && this.f_25115_ != null) {
            serverplayer.m_36220_(Stats.f_12937_);
            CriteriaTriggers.f_10581_.m_147278_(serverplayer, this.f_25113_, this.f_25115_, (AgeableMob)null);
         }

         this.moth.setHasEgg(true);
         this.f_25113_.m_27594_();
         this.f_25115_.m_27594_();
         RandomSource randomsource = this.f_25113_.m_217043_();
         if (this.f_25114_.m_46469_().m_46207_(GameRules.f_46135_)) {
            this.f_25114_.m_7967_(new ExperienceOrb(this.f_25114_, this.f_25113_.m_20185_(), this.f_25113_.m_20186_(), this.f_25113_.m_20189_(), randomsource.m_188503_(7) + 1));
         }

      }
   }

   static class MothEatOrCleanGoal extends EatBlockGoal {
      protected final HellMothEntity moth;

      public MothEatOrCleanGoal(HellMothEntity hellMothEntity) {
         super(hellMothEntity);
         this.moth = hellMothEntity;
      }

      public boolean m_8036_() {
         if (this.moth.m_217043_().m_188503_(200) != 0) {
            return false;
         } else {
            BlockPos blockpos = this.moth.m_20183_();
            boolean shouldEat = false;
            if (BlockStatePredicate.m_61287_(Blocks.f_50034_).test(this.moth.f_19853_.m_8055_(blockpos))) {
               shouldEat = true;
            } else if (this.moth.f_19853_.m_8055_(blockpos.m_7495_()).m_60713_(Blocks.f_50440_)) {
               shouldEat = true;
            }

            if (shouldEat && this.moth.m_217043_().m_188503_(10) > 5) {
               return true;
            } else {
               this.moth.setMiscAnimation(3);
               return false;
            }
         }
      }
   }

   static class MothFollowLamp extends TemptGoal {
      HellMothEntity moth;

      public MothFollowLamp(HellMothEntity pMob, double pSpeedModifier) {
         super(pMob, pSpeedModifier, Ingredient.m_204132_(TensuraTags.Items.MOTH_LAMP), false);
         this.moth = pMob;
      }

      public boolean m_8036_() {
         if (this.moth.hasEgg()) {
            return false;
         } else if (this.moth.m_27593_()) {
            return false;
         } else if (this.moth.m_5448_() != null) {
            return false;
         } else if (this.moth.m_21827_()) {
            return false;
         } else if (this.moth.m_213856_() >= 0.5F) {
            return false;
         } else {
            return this.moth.m_21826_() != null && this.f_25925_ != null && !this.moth.m_21826_().equals(this.f_25925_) ? false : super.m_8036_();
         }
      }
   }

   static class MothAttackGoal extends RangedAttackGoal {
      HellMothEntity moth;

      public MothAttackGoal(HellMothEntity moth, double pSpeedModifier, int pAttackInterval, float pAttackRadius) {
         super(moth, pSpeedModifier, pAttackInterval, pAttackRadius);
         this.moth = moth;
      }

      public boolean m_8036_() {
         return this.moth.m_6162_() && this.moth.getBabySize() <= 1 ? false : super.m_8036_();
      }

      public void m_8037_() {
         LivingEntity target = this.moth.m_5448_();
         if (target != null) {
            if (this.moth.getAttackMode() == 1) {
               AABB aabb = new AABB(this.moth.m_20183_().m_6625_(2), this.moth.m_20183_().m_6625_(5));
               List<LivingEntity> list = this.moth.f_19853_.m_6443_(LivingEntity.class, aabb.m_82400_(2.0D), (entityx) -> {
                  return !entityx.m_7307_(this.moth) && !(entityx instanceof HellCaterpillarEntity) && !(entityx instanceof HellMothEntity);
               });
               if (list.isEmpty()) {
                  return;
               }

               Iterator var4 = list.iterator();

               while(var4.hasNext()) {
                  LivingEntity entity = (LivingEntity)var4.next();
                  if (entity.equals(target)) {
                     this.moth.setAttackMode(0);
                  }

                  entity.m_147207_(new MobEffectInstance(MobEffects.f_19614_, 200, 0), this.moth);
                  if (this.moth.hasParalysis()) {
                     entity.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 300, entity.m_21023_((MobEffect)TensuraMobEffects.PARALYSIS.get()) ? 2 : 1), this.moth);
                  }
               }
            } else {
               super.m_8037_();
            }

         }
      }

      public void m_8041_() {
         super.m_8041_();
         this.moth.setAttackMode(0);
      }
   }

   static class MothTargetGoal<T extends LivingEntity> extends NonTameRandomTargetGoal<T> {
      public MothTargetGoal(HellMothEntity mothEntity, Class<T> pEntityTypeToTarget, @Nullable Predicate<LivingEntity> pTargetPredicate) {
         super(mothEntity, pEntityTypeToTarget, true, pTargetPredicate);
      }

      public boolean m_8036_() {
         return this.f_26135_.m_213856_() < 0.5F && super.m_8036_();
      }
   }
}
