package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.spike.PillarEntity;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Arrays;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.Direction.Axis;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.DeadBushBlock;
import net.minecraft.world.level.block.SeagrassBlock;
import net.minecraft.world.level.block.TallGrassBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class EarthDominationSkill extends Skill {
   public EarthDominationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.isSkillMastered(entity, (ManasSkill)ExtraSkills.EARTH_MANIPULATION.get())) {
         return false;
      } else {
         return newEP > 400000.0D;
      }
   }

   public double learningCost() {
      return 20.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.earth_manipulation.wall");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.earth_manipulation.break");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.earth_manipulation.pit");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 5.0D;
         break;
      case 2:
         var10000 = 10.0D;
         break;
      case 3:
         var10000 = 20.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (DamageSourceHelper.isEarthDamage(e.getSource())) {
            e.setAmount(e.getAmount() * 4.0F);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity player) {
      Level level = player.m_9236_();
      if (!SkillHelper.outOfMagicule(player, instance)) {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, player, Fluid.NONE, 5.0D);
         switch(instance.getMode()) {
         case 1:
            this.placeWall(instance, player, level, result);
            break;
         case 2:
            this.breakWall(instance, player, level, result);
            break;
         case 3:
            this.pit(instance, player, level);
         }

      }
   }

   private void pit(ManasSkillInstance instance, LivingEntity entity, Level level) {
      if (TensuraGameRules.canSkillGrief(level)) {
         BlockPos pos = entity.m_20097_();
         int radius = 2;
         boolean success = false;

         for(int x = -radius; x <= radius; ++x) {
            for(int z = -radius; z <= radius; ++z) {
               for(int y = -radius * 2; y <= 0; ++y) {
                  BlockPos newPos = pos.m_5487_(Axis.Y, y).m_5487_(Axis.X, x).m_5487_(Axis.Z, z);
                  success = this.breakBlock(level, newPos, entity, instance) || success;
               }
            }
         }

         if (success) {
            this.addMasteryPoint(instance, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         }

      }
   }

   private void breakWall(ManasSkillInstance instance, LivingEntity entity, Level level, BlockHitResult result) {
      if (result.m_6662_() == Type.BLOCK) {
         BlockPos pos = result.m_82425_();
         if (!level.m_8055_(pos).m_204336_(TensuraTags.Blocks.EARTH_DOMINATING)) {
            return;
         }

         if (!TensuraGameRules.canSkillGrief(level)) {
            return;
         }

         int radius = 1;
         boolean sucess = false;
         Axis playerAxis = result.m_82434_().m_122434_();
         Axis[] axes = (Axis[])Arrays.stream(Axis.values()).filter((a) -> {
            return a != playerAxis;
         }).toArray((x$0) -> {
            return new Axis[x$0];
         });

         for(int x = -radius; x <= radius; ++x) {
            for(int y = -radius; y <= radius; ++y) {
               BlockPos newPos = pos.m_5487_(axes[0], x).m_5487_(axes[1], y);
               sucess = this.breakBlock(level, newPos, entity, instance) || sucess;
            }
         }

         if (sucess) {
            this.addMasteryPoint(instance, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         }
      }

   }

   private boolean breakBlock(Level level, BlockPos pos, LivingEntity entity, ManasSkillInstance instance) {
      if (level.m_8055_(pos).m_204336_(TensuraTags.Blocks.EARTH_DOMINATING)) {
         SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
         if (MinecraftForge.EVENT_BUS.post(preGrief)) {
            return false;
         } else {
            boolean success = level.m_46961_(pos, true);
            MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
            return success;
         }
      } else {
         return false;
      }
   }

   private void placeWall(ManasSkillInstance instance, LivingEntity entity, Level level, BlockHitResult result) {
      if (result.m_6662_() == Type.BLOCK) {
         BlockPos clickPos = result.m_82425_();
         if (clickPos.m_123342_() > entity.m_20097_().m_123342_() + 1) {
            return;
         }

         Block block = level.m_8055_(clickPos).m_60734_();
         if (block instanceof TallGrassBlock || block instanceof SeagrassBlock || block instanceof DeadBushBlock) {
            clickPos = clickPos.m_7495_();
         }

         int side = 2;
         Direction direction = entity.m_6350_();
         boolean success = this.placePillars(level, clickPos, entity, instance);
         BlockPos blockPos = clickPos;

         for(int w = 0; w < side; ++w) {
            blockPos = blockPos.m_121945_(direction.m_122427_());
            success = this.placePillars(level, blockPos, entity, instance) || success;
         }

         BlockPos blockPosCounter = clickPos;

         for(int w = 0; w < side; ++w) {
            blockPosCounter = blockPosCounter.m_121945_(direction.m_122428_());
            success = this.placePillars(level, blockPosCounter, entity, instance) || success;
         }

         if (success) {
            this.addMasteryPoint(instance, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
         }
      }

   }

   private boolean placePillars(Level level, BlockPos pos, LivingEntity entity, ManasSkillInstance instance) {
      BlockState state = level.m_8055_(pos);
      if (!state.m_204336_(TensuraTags.Blocks.EARTH_DOMINATING)) {
         return false;
      } else {
         Vec3 spawnPos = Vec3.m_82512_(pos.m_7494_()).m_82520_(0.0D, -0.5D, 0.0D);
         List<PillarEntity> list = level.m_6443_(PillarEntity.class, AABB.m_165882_(spawnPos, 1.0D, 1.0D, 1.0D), (pillarx) -> {
            return pillarx.getOwner() == entity && pillarx.getSkill().getSkill() == instance.getSkill();
         });
         if (!list.isEmpty()) {
            return false;
         } else {
            PillarEntity pillar = new PillarEntity(level, entity);
            pillar.m_146884_(spawnPos);
            pillar.setDamage(5.0F);
            pillar.setLife(1200);
            pillar.setBlockState(state);
            pillar.setExtendingTick(10);
            pillar.setHeight(4.0F);
            pillar.setMpCost(this.magiculeCost(entity, instance));
            pillar.setSkill(instance);
            entity.m_9236_().m_7967_(pillar);
            TensuraParticleHelper.spawnServerParticles(level, new BlockParticleOption(ParticleTypes.f_123794_, state), (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_() + 0.5D, (double)pos.m_123343_() + 0.5D, 10, 0.08D, 0.08D, 0.08D, 0.1D, false);
            level.m_6263_((Player)null, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_(), state.m_60734_().m_49962_(state).m_56777_(), SoundSource.PLAYERS, 1.0F, 1.0F);
            return true;
         }
      }
   }
}
