package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.awt.Color;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.common.ForgeSpawnEggItem;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraSpawnEggs {
   private static final DeferredRegister<Item> registry;
   public static final RegistryObject<Item> AKASH;
   public static final RegistryObject<Item> AQUA_FROG;
   public static final RegistryObject<Item> ARMOURSAURUS;
   public static final RegistryObject<Item> ARMY_WASP;
   public static final RegistryObject<Item> BARGHEST;
   public static final RegistryObject<Item> BEAST_GNOME;
   public static final RegistryObject<Item> BLACK_SPIDER;
   public static final RegistryObject<Item> BLADE_TIGER;
   public static final RegistryObject<Item> BULLDEER;
   public static final RegistryObject<Item> CHARYBDIS;
   public static final RegistryObject<Item> DIREWOLF;
   public static final RegistryObject<Item> DRAGON_PEACOCK;
   public static final RegistryObject<Item> ELEMENTAL_COLOSSUS;
   public static final RegistryObject<Item> EVIL_CENTIPEDE;
   public static final RegistryObject<Item> FEATHERED_SERPENT;
   public static final RegistryObject<Item> GIANT_ANT;
   public static final RegistryObject<Item> GIANT_BAT;
   public static final RegistryObject<Item> GIANT_BEAR;
   public static final RegistryObject<Item> GIANT_COD;
   public static final RegistryObject<Item> GIANT_SALMON;
   public static final RegistryObject<Item> GOBLIN;
   public static final RegistryObject<Item> HOLY_COW;
   public static final RegistryObject<Item> HELL_CATERPILLAR;
   public static final RegistryObject<Item> HELL_MOTH;
   public static final RegistryObject<Item> HORNED_BEAR;
   public static final RegistryObject<Item> HORNED_RABBIT;
   public static final RegistryObject<Item> HOUND_DOG;
   public static final RegistryObject<Item> HOVER_LIZARD;
   public static final RegistryObject<Item> IFRIT;
   public static final RegistryObject<Item> KNIGHT_SPIDER;
   public static final RegistryObject<Item> LANDFISH;
   public static final RegistryObject<Item> LEECH_LIZARD;
   public static final RegistryObject<Item> LIZARDMAN;
   public static final RegistryObject<Item> MEGALODON;
   public static final RegistryObject<Item> ONE_EYED_OWL;
   public static final RegistryObject<Item> ORC;
   public static final RegistryObject<Item> ORC_LORD;
   public static final RegistryObject<Item> ORC_DISASTER;
   public static final RegistryObject<Item> OTHERWORLDER_FOLGEN;
   public static final RegistryObject<Item> OTHERWORLDER_HINATA_SAKAGUCHI;
   public static final RegistryObject<Item> OTHERWORLDER_KIRARA_MIZUTANI;
   public static final RegistryObject<Item> OTHERWORLDER_KYOYA_TACHIBANA;
   public static final RegistryObject<Item> OTHERWORLDER_MAI_FURUKI;
   public static final RegistryObject<Item> OTHERWORLDER_MARK_LAUREN;
   public static final RegistryObject<Item> OTHERWORLDER_SHINJI_TANIMURA;
   public static final RegistryObject<Item> OTHERWORLDER_SHIN_RYUSEI;
   public static final RegistryObject<Item> OTHERWORLDER_SHIZU;
   public static final RegistryObject<Item> OTHERWORLDER_SHOGO_TAGUCHI;
   public static final RegistryObject<Item> SALAMANDER;
   public static final RegistryObject<Item> SISSIE;
   public static final RegistryObject<Item> SLIME;
   public static final RegistryObject<Item> METAL_SLIME;
   public static final RegistryObject<Item> SUPERMASSIVE_SLIME;
   public static final RegistryObject<Item> SPEAR_TORO;
   public static final RegistryObject<Item> SYLPHIDE;
   public static final RegistryObject<Item> TEMPEST_SERPENT;
   public static final RegistryObject<Item> UNDINE;
   public static final RegistryObject<Item> UNICORN;
   public static final RegistryObject<Item> WAR_GNOME;
   public static final RegistryObject<Item> WINGED_CAT;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      AKASH = registry.register("akash_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.AKASH, (new Color(211, 211, 211)).getRGB(), (new Color(113, 192, 178)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      AQUA_FROG = registry.register("aqua_frog_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.AQUA_FROG, (new Color(105, 204, 214)).getRGB(), (new Color(234, 101, 47)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ARMOURSAURUS = registry.register("armoursaurus_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ARMOURSAURUS, (new Color(65, 99, 155)).getRGB(), (new Color(21, 86, 32)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ARMY_WASP = registry.register("army_wasp_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ARMY_WASP, (new Color(220, 199, 50)).getRGB(), (new Color(23, 23, 24)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      BARGHEST = registry.register("barghest_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.BARGHEST, (new Color(60, 56, 66)).getRGB(), (new Color(25, 84, 185)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      BEAST_GNOME = registry.register("beast_gnome_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.BEAST_GNOME, (new Color(186, 117, 139)).getRGB(), (new Color(212, 181, 182)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      BLACK_SPIDER = registry.register("black_spider_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.BLACK_SPIDER, (new Color(164, 162, 21)).getRGB(), (new Color(33, 29, 44)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      BLADE_TIGER = registry.register("blade_tiger_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.BLADE_TIGER, (new Color(179, 65, 65)).getRGB(), (new Color(34, 44, 58)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      BULLDEER = registry.register("bulldeer_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.BULLDEER, (new Color(164, 112, 68)).getRGB(), (new Color(121, 97, 66)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      CHARYBDIS = registry.register("charybdis_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.CHARYBDIS, (new Color(30, 87, 142)).getRGB(), (new Color(23, 60, 131)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      DIREWOLF = registry.register("direwolf_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.DIREWOLF, (new Color(73, 76, 111)).getRGB(), (new Color(24, 25, 37)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      DRAGON_PEACOCK = registry.register("dragon_peacock_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.DRAGON_PEACOCK, (new Color(113, 209, 155)).getRGB(), (new Color(248, 97, 122)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ELEMENTAL_COLOSSUS = registry.register("elemental_colossus_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ELEMENTAL_COLOSSUS, (new Color(186, 104, 116)).getRGB(), (new Color(172, 172, 195)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      EVIL_CENTIPEDE = registry.register("evil_centipede_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.EVIL_CENTIPEDE, (new Color(96, 41, 41)).getRGB(), (new Color(45, 46, 49)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      FEATHERED_SERPENT = registry.register("feathered_serpent_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.FEATHERED_SERPENT, (new Color(131, 196, 116)).getRGB(), (new Color(174, 58, 40)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GIANT_ANT = registry.register("giant_ant_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GIANT_ANT, (new Color(149, 50, 43)).getRGB(), (new Color(71, 21, 19)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GIANT_BAT = registry.register("giant_bat_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GIANT_BAT, (new Color(68, 68, 69)).getRGB(), (new Color(30, 30, 31)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GIANT_BEAR = registry.register("giant_bear_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GIANT_BEAR, (new Color(63, 63, 64)).getRGB(), (new Color(160, 160, 161)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GIANT_COD = registry.register("giant_cod_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GIANT_COD, (new Color(173, 145, 121)).getRGB(), (new Color(86, 73, 61)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GIANT_SALMON = registry.register("giant_salmon_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GIANT_SALMON, (new Color(133, 36, 33)).getRGB(), (new Color(65, 65, 46)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      GOBLIN = registry.register("goblin_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.GOBLIN, (new Color(144, 157, 110)).getRGB(), (new Color(94, 99, 68)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HOLY_COW = registry.register("holy_cow_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HOLY_COW, (new Color(215, 214, 214)).getRGB(), (new Color(72, 61, 43)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HELL_CATERPILLAR = registry.register("hell_caterpillar_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HELL_CATERPILLAR, (new Color(236, 236, 237)).getRGB(), (new Color(176, 214, 21)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HELL_MOTH = registry.register("hell_moth_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HELL_MOTH, (new Color(187, 180, 172)).getRGB(), (new Color(35, 18, 13)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HORNED_BEAR = registry.register("horned_bear_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HORNED_BEAR, (new Color(100, 69, 70)).getRGB(), (new Color(160, 134, 121)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HORNED_RABBIT = registry.register("horned_rabbit_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HORNED_RABBIT, (new Color(234, 231, 228)).getRGB(), (new Color(200, 175, 160)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HOUND_DOG = registry.register("hound_dog_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HOUND_DOG, (new Color(40, 32, 47)).getRGB(), (new Color(93, 63, 56)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      HOVER_LIZARD = registry.register("hover_lizard_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HOVER_LIZARD, (new Color(121, 114, 73)).getRGB(), (new Color(226, 219, 198)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      IFRIT = registry.register("ifrit_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.IFRIT, (new Color(112, 81, 60)).getRGB(), (new Color(197, 66, 33)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      KNIGHT_SPIDER = registry.register("knight_spider_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.KNIGHT_SPIDER, (new Color(88, 66, 70)).getRGB(), (new Color(21, 28, 62)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      LANDFISH = registry.register("landfish_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.LANDFISH, (new Color(84, 125, 129)).getRGB(), (new Color(43, 78, 77)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      LEECH_LIZARD = registry.register("leech_lizard_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.LEECH_LIZARD, (new Color(77, 94, 61)).getRGB(), (new Color(37, 47, 29)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      LIZARDMAN = registry.register("lizardman_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.LIZARDMAN, (new Color(63, 86, 81)).getRGB(), (new Color(177, 173, 156)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      MEGALODON = registry.register("megalodon_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.MEGALODON, (new Color(113, 141, 174)).getRGB(), (new Color(87, 109, 134)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ONE_EYED_OWL = registry.register("one_eyed_owl_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ONE_EYED_OWL, (new Color(217, 217, 217)).getRGB(), (new Color(136, 187, 134)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ORC = registry.register("orc_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ORC, (new Color(147, 114, 65)).getRGB(), (new Color(192, 190, 168)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ORC_LORD = registry.register("orc_lord_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ORC_LORD, (new Color(105, 78, 61)).getRGB(), (new Color(74, 42, 80)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      ORC_DISASTER = registry.register("orc_disaster_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.ORC_DISASTER, (new Color(105, 78, 61)).getRGB(), (new Color(51, 51, 51)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_FOLGEN = registry.register("folgen_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.FOLGEN, (new Color(171, 171, 171)).getRGB(), (new Color(157, 143, 102)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_HINATA_SAKAGUCHI = registry.register("hinata_sakaguchi_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.HINATA_SAKAGUCHI, (new Color(185, 184, 186)).getRGB(), (new Color(246, 196, 16)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_KIRARA_MIZUTANI = registry.register("kirara_mizutani_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.KIRARA_MIZUTANI, (new Color(150, 125, 129)).getRGB(), (new Color(72, 58, 56)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_KYOYA_TACHIBANA = registry.register("kyoya_tachibana_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.KYOYA_TACHIBANA, (new Color(60, 64, 72)).getRGB(), (new Color(113, 105, 97)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_MAI_FURUKI = registry.register("mai_furuki_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.MAI_FURUKI, (new Color(33, 34, 38)).getRGB(), (new Color(190, 156, 137)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_MARK_LAUREN = registry.register("mark_lauren_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.MARK_LAUREN, (new Color(181, 137, 113)).getRGB(), (new Color(51, 58, 69)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_SHINJI_TANIMURA = registry.register("shinji_tanimura_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SHINJI_TANIMURA, (new Color(202, 202, 202)).getRGB(), (new Color(78, 64, 93)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_SHIN_RYUSEI = registry.register("shin_ryusei_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SHIN_RYUSEI, (new Color(165, 165, 165)).getRGB(), (new Color(61, 58, 63)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_SHIZU = registry.register("shizu_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SHIZU, (new Color(51, 50, 68)).getRGB(), (new Color(168, 58, 65)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      OTHERWORLDER_SHOGO_TAGUCHI = registry.register("shogo_taguchi_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SHOGO_TAGUCHI, (new Color(99, 107, 106)).getRGB(), (new Color(50, 49, 54)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SALAMANDER = registry.register("salamander_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SALAMANDER, (new Color(124, 50, 41)).getRGB(), (new Color(251, 80, 4)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SISSIE = registry.register("sissie_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SISSIE, (new Color(65, 65, 65)).getRGB(), (new Color(180, 180, 180)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SLIME = registry.register("slime_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SLIME, (new Color(156, 186, 195)).getRGB(), (new Color(114, 142, 174)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      METAL_SLIME = registry.register("metal_slime_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.METAL_SLIME, (new Color(139, 141, 141)).getRGB(), (new Color(78, 78, 80)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SUPERMASSIVE_SLIME = registry.register("supermassive_slime_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SUPERMASSIVE_SLIME, (new Color(124, 174, 232)).getRGB(), (new Color(93, 129, 201)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SPEAR_TORO = registry.register("spear_toro_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SPEAR_TORO, (new Color(27, 69, 145)).getRGB(), (new Color(204, 195, 225)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      SYLPHIDE = registry.register("sylphide_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.SYLPHIDE, (new Color(195, 207, 134)).getRGB(), (new Color(101, 150, 150)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      TEMPEST_SERPENT = registry.register("tempest_serpent_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.TEMPEST_SERPENT, (new Color(59, 58, 63)).getRGB(), (new Color(86, 93, 125)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      UNDINE = registry.register("undine_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.UNDINE, (new Color(47, 111, 193)).getRGB(), (new Color(158, 186, 190)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      UNICORN = registry.register("unicorn_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.UNICORN, (new Color(245, 242, 242)).getRGB(), (new Color(173, 153, 143)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      WAR_GNOME = registry.register("war_gnome_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.WAR_GNOME, (new Color(73, 73, 73)).getRGB(), (new Color(170, 52, 18)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
      WINGED_CAT = registry.register("winged_cat_spawn_egg", () -> {
         return new ForgeSpawnEggItem(TensuraEntityTypes.WINGED_CAT, (new Color(112, 92, 61)).getRGB(), (new Color(168, 165, 157)).getRGB(), (new Properties()).m_41491_(TensuraCreativeTab.SPAWN_EGGS));
      });
   }
}
