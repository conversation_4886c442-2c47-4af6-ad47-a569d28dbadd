package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.UndineEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class UndineModel extends AnimatedGeoModel<UndineEntity> {
   public ResourceLocation getModelResource(UndineEntity object) {
      return new ResourceLocation("tensura", "geo/undine.geo.json");
   }

   public ResourceLocation getTextureResource(UndineEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/undine/undine.png");
   }

   public ResourceLocation getAnimationResource(UndineEntity undine) {
      return new ResourceLocation("tensura", "animations/undine.animation.json");
   }

   public void setCustomAnimations(UndineEntity undine, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(undine, instanceId, customPredicate);
      if (!undine.m_20096_() || undine.stayingTicks < 1200) {
         if (undine.shouldStand()) {
            EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
            IBone head = this.getAnimationProcessor().getBone("Head");
            if (head != null) {
               head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
               head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
            }

         }
      }
   }
}
