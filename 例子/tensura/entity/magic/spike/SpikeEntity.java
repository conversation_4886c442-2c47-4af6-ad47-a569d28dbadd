package com.github.manasmods.tensura.entity.magic.spike;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import java.util.Iterator;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundAddEntityPacket;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntitySelector;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class SpikeEntity extends Entity {
   private static final EntityDataAccessor<Integer> TICK_COUNT;
   private static final EntityDataAccessor<Integer> EXTENDING_TICK;
   private static final EntityDataAccessor<Integer> LIFE;
   private static final EntityDataAccessor<Float> HEIGHT;
   protected double mpCost;
   protected float damage;
   protected ManasSkillInstance skill;
   @Nullable
   private Entity owner;
   @Nullable
   private UUID ownerUUID;

   public SpikeEntity(EntityType<? extends SpikeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.mpCost = 0.0D;
      this.damage = 0.0F;
      this.skill = null;
   }

   public SpikeEntity(EntityType<? extends SpikeEntity> pEntityType, Level pLevel, LivingEntity pOwner) {
      this(pEntityType, pLevel);
      this.setOwner(pOwner);
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(TICK_COUNT, 0);
      this.f_19804_.m_135372_(EXTENDING_TICK, 20);
      this.f_19804_.m_135372_(LIFE, 40);
      this.f_19804_.m_135372_(HEIGHT, 4.0F);
   }

   protected void m_7378_(CompoundTag pCompound) {
      this.setTickCount(pCompound.m_128451_("TickCount"));
      this.setExtendingTick(pCompound.m_128451_("ExtendingTick"));
      this.setLife(pCompound.m_128451_("Life"));
      this.setHeight(pCompound.m_128457_("Height"));
      if (pCompound.m_128403_("Owner")) {
         this.ownerUUID = pCompound.m_128342_("Owner");
      }

      pCompound.m_128347_("MPCost", this.getMpCost());
      pCompound.m_128350_("Damage", this.getDamage());
      if (this.skill != null) {
         pCompound.m_128365_("skill", this.skill.toNBT());
      }

   }

   protected void m_7380_(CompoundTag pCompound) {
      pCompound.m_128405_("TickCount", this.getTickCount());
      pCompound.m_128405_("ExtendingTick", this.getExtendingTick());
      pCompound.m_128405_("Life", this.getLife());
      pCompound.m_128350_("Height", this.getHeight());
      if (this.ownerUUID != null) {
         pCompound.m_128362_("Owner", this.ownerUUID);
      }

      this.setMpCost(pCompound.m_128459_("MPCost"));
      this.setDamage(pCompound.m_128457_("Damage"));
      if (pCompound.m_128441_("skill")) {
         Tag var3 = pCompound.m_128423_("skill");
         if (var3 instanceof CompoundTag) {
            CompoundTag tag = (CompoundTag)var3;
            this.skill = ManasSkillInstance.fromNBT(tag);
         }
      }

   }

   public void setTickCount(int tick) {
      this.f_19804_.m_135381_(TICK_COUNT, tick);
   }

   public int getTickCount() {
      return (Integer)this.f_19804_.m_135370_(TICK_COUNT);
   }

   public void setExtendingTick(int tick) {
      this.f_19804_.m_135381_(EXTENDING_TICK, tick);
   }

   public int getExtendingTick() {
      return (Integer)this.f_19804_.m_135370_(EXTENDING_TICK);
   }

   public void setLife(int tick) {
      this.f_19804_.m_135381_(LIFE, tick);
   }

   public int getLife() {
      return (Integer)this.f_19804_.m_135370_(LIFE);
   }

   public void setHeight(float height) {
      this.f_19804_.m_135381_(HEIGHT, height);
      this.m_6210_();
   }

   public float getHeight() {
      return (Float)this.f_19804_.m_135370_(HEIGHT);
   }

   public void setOwner(@Nullable Entity pOwner) {
      this.owner = pOwner;
      this.ownerUUID = pOwner == null ? null : pOwner.m_20148_();
   }

   @Nullable
   public Entity getOwner() {
      if (this.owner == null && this.ownerUUID != null && this.f_19853_ instanceof ServerLevel) {
         Entity entity = ((ServerLevel)this.f_19853_).m_8791_(this.ownerUUID);
         if (entity instanceof LivingEntity) {
            this.owner = entity;
         }
      }

      return this.owner;
   }

   public boolean m_6060_() {
      return false;
   }

   public boolean shouldPushUp() {
      return false;
   }

   public boolean m_7337_(@NotNull Entity entity) {
      Entity owner = this.getOwner();
      if (owner != null) {
         if (owner.m_6144_()) {
            return false;
         }

         if (entity.m_7307_(owner)) {
            return false;
         }
      }

      return (entity.m_5829_() || entity.m_6094_()) && !this.m_20365_(entity);
   }

   public boolean m_5829_() {
      return true;
   }

   public boolean m_6087_() {
      return !this.m_213877_();
   }

   public EntityDimensions m_6972_(Pose pose) {
      EntityDimensions dimensions = super.m_6972_(pose);
      int extendingTick = Math.min(this.getTickCount(), this.getExtendingTick());
      return dimensions.m_20390_(1.0F, this.getHeight() * (float)extendingTick / (float)this.getExtendingTick());
   }

   public void m_8119_() {
      super.m_8119_();
      this.setTickCount(this.getTickCount() + 1);
      if (this.getTickCount() >= this.getLife()) {
         this.onBreak();
      }

      this.m_6210_();
      if (this.shouldPushUp() && this.getTickCount() < this.getExtendingTick()) {
         Iterator var1 = this.f_19853_.m_6249_(this, this.m_20191_().m_82386_(0.0D, 0.5D, 0.0D), EntitySelector.f_20408_.and((entity) -> {
            return !entity.m_20365_(this);
         })).iterator();

         while(var1.hasNext()) {
            Entity target = (Entity)var1.next();
            if (!target.f_19794_ && !(target instanceof SpikeEntity)) {
               target.m_6478_(MoverType.SHULKER, new Vec3(0.0D, (double)Direction.UP.m_122430_(), 0.0D));
               if (target instanceof LivingEntity) {
                  LivingEntity living = (LivingEntity)target;
                  this.applyEffect(living);
               }
            }
         }
      }

   }

   public boolean m_6094_() {
      return true;
   }

   public void m_7334_(Entity pEntity) {
      if (this.getOwner() != pEntity) {
         if (pEntity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)pEntity;
            this.applyEffect(target);
         }

      }
   }

   public void applyEffect(LivingEntity target) {
   }

   public void onBreak() {
      this.m_146870_();
   }

   public Packet<?> m_5654_() {
      Entity entity = this.getOwner();
      return new ClientboundAddEntityPacket(this, entity == null ? 0 : entity.m_19879_());
   }

   public void m_141965_(ClientboundAddEntityPacket pPacket) {
      super.m_141965_(pPacket);
      Entity entity = this.f_19853_.m_6815_(pPacket.m_131509_());
      if (entity != null) {
         this.setOwner(entity);
      }

   }

   public double getMpCost() {
      return this.mpCost;
   }

   public void setMpCost(double mpCost) {
      this.mpCost = mpCost;
   }

   public float getDamage() {
      return this.damage;
   }

   public void setDamage(float damage) {
      this.damage = damage;
   }

   public ManasSkillInstance getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkillInstance skill) {
      this.skill = skill;
   }

   static {
      TICK_COUNT = SynchedEntityData.m_135353_(SpikeEntity.class, EntityDataSerializers.f_135028_);
      EXTENDING_TICK = SynchedEntityData.m_135353_(SpikeEntity.class, EntityDataSerializers.f_135028_);
      LIFE = SynchedEntityData.m_135353_(SpikeEntity.class, EntityDataSerializers.f_135028_);
      HEIGHT = SynchedEntityData.m_135353_(SpikeEntity.class, EntityDataSerializers.f_135029_);
   }
}
