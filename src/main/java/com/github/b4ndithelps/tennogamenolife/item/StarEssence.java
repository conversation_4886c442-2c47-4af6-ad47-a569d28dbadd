package com.github.b4ndithelps.tennogamenolife.item;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.Item;

public class StarEssence extends Item {
    public StarEssence(Properties properties) {
        super(properties
            .tab(TensuraCreativeTab.MISCELLANEOUS)
            .food(new FoodProperties.Builder()
                .nutrition(1)
                .saturationMod(0.1f)
                .alwaysEat()
                .effect(() -> new MobEffectInstance(MobEffects.WEAKNESS, 600, 0), 1.0f)
                .build()));
    }
} 