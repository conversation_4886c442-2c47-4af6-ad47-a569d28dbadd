package com.github.b4ndithelps.tennogamenolife.ability.skill.ultimate;

import com.github.b4ndithelps.tennogamenolife.Tenreincarnation;
import com.github.b4ndithelps.tennogamenolife.client.render.ChargeBarRenderer;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.level.Explosion;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;

/**
 * 崩哮（Far Cry）
 */
public class FarCryUltimateSkill extends Skill {
    private final double skillCastCost = 300000.0;     // 施法消耗的魔素值
    private final double learnCost = 5.0;              // 学习难度
    private static final long CHARGE_TIME = 10000;     // 蓄力时间（10秒）
    private static final double EXPLOSION_RANGE = 250.0; // 爆炸范围（250格）
    private boolean isCharging = false;                 // 是否正在蓄力
    private long chargeStartTime = 0;                  // 开始蓄力的时间
    private static final ResourceLocation FORBIDDEN_DIMENSION = new ResourceLocation("tensura", "labyrinth");

    public FarCryUltimateSkill() {
        super(SkillType.ULTIMATE);
    }

    @Override
    public ResourceLocation getSkillIcon() {
        return new ResourceLocation(Tenreincarnation.MODID, "textures/skill/ultimate/far_cry.png");
    }

    @Override
    public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
        return skillCastCost;
    }

    @Override
    public double learningCost() {
        return learnCost;
    }

    @Override
    public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
        if (isInForbiddenDimension(entity)) {
            if (entity instanceof Player player) {
                player.displayClientMessage(Component.translatable("skill.far_cry.forbidden_dimension"), true);
            }
            return;
        }

        if (SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance))) {
            return;
        }

        isCharging = true;
        chargeStartTime = System.currentTimeMillis();
        ChargeBarRenderer.startCharging("far_cry");
    }

    @Override
    public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
        if (isCharging && isInForbiddenDimension(entity)) {
            isCharging = false;
            ChargeBarRenderer.stopCharging();
            if (entity instanceof Player player) {
                player.displayClientMessage(Component.translatable("skill.far_cry.forbidden_dimension"), true);
            }
            return false;
        }
        return isCharging;
    }
    
    /**
     * 检查实体是否在禁止使用的维度
     */
    private boolean isInForbiddenDimension(LivingEntity entity) {
        ResourceLocation dimensionLocation = entity.level.dimension().location();
        return FORBIDDEN_DIMENSION.equals(dimensionLocation);
    }

    @Override
    public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
        if (isCharging) {
            long chargeTime = System.currentTimeMillis() - chargeStartTime;

            if (chargeTime >= CHARGE_TIME) {
                this.triggerFarCryExplosion(instance, entity);
                instance.setCoolDown(1600);
            }
            isCharging = false;
            ChargeBarRenderer.stopCharging();
        }
    }

    @Override
    public void onTick(ManasSkillInstance instance, LivingEntity entity) {
        if (isCharging && entity instanceof Player player) {
            long currentTime = System.currentTimeMillis();
            long chargeTime = currentTime - chargeStartTime;

            if (chargeTime % 2000 < 50) {
                player.level.playSound(null, player.getX(), player.getY(), player.getZ(),
                    SoundEvents.ENDER_DRAGON_GROWL, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
        }
    }

    /**
     * 获取目标方向
     */
    private Vec3 getTargetDirection(LivingEntity entity) {
        if (!(entity instanceof Player player)) return Vec3.ZERO;
        return player.getViewVector(1.0F).normalize();
    }

    /**
     * 使用Tensura伤害系统对范围内实体造成伤害
     */
    private void dealTensuraDamage(Level level, Vec3 center, int distance, double maxRange, LivingEntity attacker) {
        double progress = (double) distance / maxRange;
        double smoothProgress = progress * progress * (3.0 - 2.0 * progress);
        double damage = 100.0 + smoothProgress * 1440.0;
        double damageRadius = Math.max(3.0, 3.0 + smoothProgress * 17.0);
        level.getEntitiesOfClass(LivingEntity.class,
            new net.minecraft.world.phys.AABB(
                center.x - damageRadius, center.y - damageRadius, center.z - damageRadius,
                center.x + damageRadius, center.y + damageRadius, center.z + damageRadius
            )).forEach(target -> {

                double distanceToTarget = target.position().distanceTo(center);
                if (distanceToTarget <= damageRadius) {

                    double distanceMultiplier = 1.0 - (distanceToTarget / damageRadius);
                    double finalDamage = damage * distanceMultiplier;

                    TensuraEntityDamageSource farCryDamage = (TensuraEntityDamageSource) new TensuraEntityDamageSource("tensura.far_cry", attacker)
                        .setNoKnock()
                        .setNotTensuraMagic();

                    target.hurt(farCryDamage, (float)finalDamage);
                }
            });
    }

    /**
     * 触发崩哮爆炸 - 从玩家位置开始连续平滑扩散
     */
    private void triggerFarCryExplosion(ManasSkillInstance instance, LivingEntity entity) {
        Vec3 explosionDirection = getTargetDirection(entity);
        Level level = entity.getLevel();
        Vec3 startPos = entity.position();
        level.playSound(
            null, startPos.x, startPos.y, startPos.z,
            SoundEvents.GENERIC_EXPLODE, SoundSource.PLAYERS,
            2.0F, 0.5F
        );

        createSmoothExplosionChain(level, startPos, explosionDirection, EXPLOSION_RANGE, entity);
    }

    /**
     * 创建平滑的爆炸链 - 统一处理所有方向
     */
    private void createSmoothExplosionChain(Level level, Vec3 startPos, Vec3 direction, double range, LivingEntity attacker) {

        for (int distance = 0; distance <= range; distance += 3) {
            Vec3 currentPos;


            double yDirection = direction.y;
            if (yDirection < -0.7) {

                currentPos = startPos.add(0, -distance, 0);
            } else if (yDirection > 0.7) {

                currentPos = startPos.add(0, distance, 0);
            } else {

                currentPos = startPos.add(direction.scale(distance));
            }

            BlockPos blockPos = new BlockPos((int)currentPos.x, (int)currentPos.y, (int)currentPos.z);


            float explosionPower = calculateExplosionPower(distance, range);
            int destructionRadius = calculateDestructionRadius(distance, range);


            level.explode(null, currentPos.x, currentPos.y, currentPos.z,
                explosionPower, Explosion.BlockInteraction.DESTROY);

            dealTensuraDamage(level, currentPos, distance, range, attacker);


            createRadialExplosions(level, blockPos, distance, range);

            if (distance % 15 == 0) { // 每15格添加一次粒子效果
                createExplosionParticles(level, currentPos);
            }
        }
    }

    /**
     * 创建径向爆炸效果 - 实现真正的"向外扩大"
     */
    private void createRadialExplosions(Level level, BlockPos center, int distance, double maxRange) {

        double progress = (double) distance / maxRange;
        double smoothProgress = progress * progress * (3.0 - 2.0 * progress);
        int radialRadius = (int) Math.max(2, 2 + smoothProgress * 18);


        radialRadius = Math.min(radialRadius, 15);


        int explosionRings = Math.min(radialRadius / 3, 4);

        for (int ring = 1; ring <= explosionRings; ring++) {
            int ringRadius = ring * radialRadius / explosionRings;
            int explosionsInRing = Math.min(ringRadius * 2, 12);

            for (int i = 0; i < explosionsInRing; i++) {
                double angle = (2.0 * Math.PI * i) / explosionsInRing;
                int offsetX = (int)(Math.cos(angle) * ringRadius);
                int offsetZ = (int)(Math.sin(angle) * ringRadius);

                BlockPos explosionPos = center.offset(offsetX, 0, offsetZ);


                float ringPower = 1.5f + (ring * 0.8f);
                level.explode(null, explosionPos.getX(), explosionPos.getY(), explosionPos.getZ(),
                    ringPower, Explosion.BlockInteraction.DESTROY);
            }
        }
    }

    /**
     * 计算爆炸威力
     * @param distance 当前距离
     * @param maxRange 最大范围
     * @return 爆炸威力（限制在合理范围内）
     */
    private float calculateExplosionPower(int distance, double maxRange) {

        double progress = (double) distance / maxRange;


        double smoothProgress = progress * progress * (3.0 - 2.0 * progress);
        double explosionPower = 1.0 + smoothProgress * 7.0;

        return (float) explosionPower;
    }

    /**
     * 计算破坏半径
     * @param distance 当前距离
     * @param maxRange 最大范围
     * @return 破坏半径
     */
    private int calculateDestructionRadius(int distance, double maxRange) {

        double progress = (double) distance / maxRange;
        double smoothProgress = progress * progress * (3.0 - 2.0 * progress);
        int directRadius = (int) Math.max(2, 2 + smoothProgress * 10);

        return directRadius;
    }
    private void createExplosionParticles(Level level, Vec3 pos) {
        for (int i = 0; i < 100; i++) {
            double angle = level.random.nextDouble() * Math.PI * 2;
            double distance = level.random.nextDouble() * 20;
            double x = pos.x + Math.cos(angle) * distance;
            double y = pos.y + level.random.nextDouble() * 20;
            double z = pos.z + Math.sin(angle) * distance;
            level.addParticle(ParticleTypes.EXPLOSION_EMITTER, x, y, z, 0, 0, 0);
        }
    }
}
