package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.effect.InsanityEffect;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.MOD
)
public class ConfigHandler {
   @SubscribeEvent
   public static void clientConfig(FMLClientSetupEvent event) {
      InsanityEffect.loadConfig();
   }
}
