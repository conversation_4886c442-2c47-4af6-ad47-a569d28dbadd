package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OneEyedOwlEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class OneEyedOwlModel extends AnimatedGeoModel<OneEyedOwlEntity> {
   public ResourceLocation getModelResource(OneEyedOwlEntity object) {
      return new ResourceLocation("tensura", "geo/one_eyed_owl.geo.json");
   }

   public ResourceLocation getTextureResource(OneEyedOwlEntity object) {
      return new ResourceLocation("tensura", "textures/entity/one_eyed_owl/one_eyed_owl.png");
   }

   public ResourceLocation getAnimationResource(OneEyedOwlEntity animatable) {
      return new ResourceLocation("tensura", "animations/one_eyed_owl.animation.json");
   }
}
