package com.github.manasmods.tensura.entity.client.player;

import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.race.RaceHelper;
import com.mojang.blaze3d.vertex.PoseStack;
import javax.annotation.Nullable;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.multiplayer.ClientPacketListener;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class CloneRenderer extends PlayerLikeRenderer<CloneEntity> {
   private final boolean slim;

   public CloneRenderer(Context pContext, boolean slim) {
      super(pContext, new PlayerLikeModel(pContext.m_174023_(slim ? ModelLayers.f_171166_ : ModelLayers.f_171162_), slim), 0.5F);
      this.m_115326_(new HumanoidArmorLayer(this, new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171167_ : ModelLayers.f_171164_)), new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171168_ : ModelLayers.f_171165_))));
      this.slim = slim;
   }

   protected void scale(CloneEntity clone, PoseStack pMatrixStack, float pPartialTickTime) {
      float scale = 0.9375F * clone.getHeight() * RaceHelper.getSkillSizeMultiplier(clone);
      pMatrixStack.m_85841_(scale, scale, scale);
      this.f_114477_ = 0.5F * clone.getHeight() * RaceHelper.getSkillSizeMultiplier(clone);
   }

   public ResourceLocation getTextureLocation(CloneEntity clone) {
      LivingEntity var3 = clone.m_21826_();
      if (var3 instanceof Player) {
         Player player = (Player)var3;
         PlayerInfo info = this.getPlayerInfo(player);
         return info == null ? this.getDefaultSkin() : info.m_105337_();
      } else {
         return this.getDefaultSkin();
      }
   }

   private ResourceLocation getDefaultSkin() {
      if (this.slim) {
         new ResourceLocation("textures/entity/alex.png");
      }

      return new ResourceLocation("textures/entity/steve.png");
   }

   @Nullable
   protected PlayerInfo getPlayerInfo(Player player) {
      ClientPacketListener listener = Minecraft.m_91087_().m_91403_();
      return listener == null ? null : listener.m_104949_(player.m_20148_());
   }

   protected double getSittingYOffset(CloneEntity clone) {
      return super.getSittingYOffset(clone) * (double)clone.getHeight() * (double)RaceHelper.getSkillSizeMultiplier(clone);
   }
}
