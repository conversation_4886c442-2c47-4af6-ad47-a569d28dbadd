package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.AkashEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class AkashModel extends AnimatedGeoModel<AkashEntity> {
   public ResourceLocation getModelResource(AkashEntity object) {
      return new ResourceLocation("tensura", "geo/akash.geo.json");
   }

   public ResourceLocation getTextureResource(AkashEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/akash/akash.png");
   }

   public ResourceLocation getAnimationResource(AkashEntity akash) {
      return new ResourceLocation("tensura", "animations/akash.animation.json");
   }
}
